// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-app')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-device')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-geolocation')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-push-notifications')
    implementation project(':capacitor-share')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':capacitor-toast')
    implementation project(':capawesome-capacitor-badge')
    implementation project(':capgo-nativegeocoder')
    implementation project(':pantrist-capacitor-date-picker')
    implementation project(':capacitor-razorpay')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
