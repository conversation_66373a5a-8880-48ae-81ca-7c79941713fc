PODS:
  - Capacitor (6.1.1):
    - Capacitor<PERSON>ordova
  - CapacitorApp (6.0.0):
    - Capacitor
  - CapacitorCamera (6.1.2):
    - Capacitor
  - CapacitorCordova (6.1.1)
  - CapacitorDevice (6.0.2):
    - Capacitor
  - CapacitorFilesystem (6.0.3):
    - Capacitor
  - CapacitorGeolocation (6.1.0):
    - Capacitor
  - CapacitorHaptics (6.0.0):
    - Capacitor
  - CapacitorKeyboard (6.0.1):
    - Capacitor
  - CapacitorPushNotifications (6.0.4):
    - Capacitor
  - CapacitorRazorpay (1.2.0):
    - Capacitor
    - razorpay-pod
  - CapacitorShare (6.0.3):
    - Capacitor
  - CapacitorSplashScreen (6.0.3):
    - Capacitor
  - CapacitorStatusBar (6.0.0):
    - Capacitor
  - CapacitorToast (6.0.3):
    - Capacitor
  - CapawesomeCapacitorBadge (6.0.0):
    - Capacitor
  - CapgoNativegeocoder (6.1.32):
    - Capacitor
  - PantristCapacitorDatePicker (6.0.0):
    - Capacitor
  - razorpay-pod (1.3.14)

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCamera (from `../../node_modules/@capacitor/camera`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../node_modules/@capacitor/device`)"
  - "CapacitorFilesystem (from `../../node_modules/@capacitor/filesystem`)"
  - "CapacitorGeolocation (from `../../node_modules/@capacitor/geolocation`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorPushNotifications (from `../../node_modules/@capacitor/push-notifications`)"
  - CapacitorRazorpay (from `../../node_modules/capacitor-razorpay`)
  - "CapacitorShare (from `../../node_modules/@capacitor/share`)"
  - "CapacitorSplashScreen (from `../../node_modules/@capacitor/splash-screen`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"
  - "CapacitorToast (from `../../node_modules/@capacitor/toast`)"
  - "CapawesomeCapacitorBadge (from `../../node_modules/@capawesome/capacitor-badge`)"
  - "CapgoNativegeocoder (from `../../node_modules/@capgo/nativegeocoder`)"
  - "PantristCapacitorDatePicker (from `../../node_modules/@pantrist/capacitor-date-picker`)"

SPEC REPOS:
  trunk:
    - razorpay-pod

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCamera:
    :path: "../../node_modules/@capacitor/camera"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../node_modules/@capacitor/device"
  CapacitorFilesystem:
    :path: "../../node_modules/@capacitor/filesystem"
  CapacitorGeolocation:
    :path: "../../node_modules/@capacitor/geolocation"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorPushNotifications:
    :path: "../../node_modules/@capacitor/push-notifications"
  CapacitorRazorpay:
    :path: "../../node_modules/capacitor-razorpay"
  CapacitorShare:
    :path: "../../node_modules/@capacitor/share"
  CapacitorSplashScreen:
    :path: "../../node_modules/@capacitor/splash-screen"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"
  CapacitorToast:
    :path: "../../node_modules/@capacitor/toast"
  CapawesomeCapacitorBadge:
    :path: "../../node_modules/@capawesome/capacitor-badge"
  CapgoNativegeocoder:
    :path: "../../node_modules/@capgo/nativegeocoder"
  PantristCapacitorDatePicker:
    :path: "../../node_modules/@pantrist/capacitor-date-picker"

SPEC CHECKSUMS:
  Capacitor: 7b38922425a542e0b5936c8f72ea89d876dd4df1
  CapacitorApp: 30145f2ea2311e4f3744472119ec87d2ddf4c0a7
  CapacitorCamera: 9bc7b005d0e6f1d5f525b8137045b60cffffce79
  CapacitorCordova: 8f2cc8d8d3619c566e9418fe8772064a94266106
  CapacitorDevice: 9efd479d71d1baad74b75df531184c3f730eaa48
  CapacitorFilesystem: 59270a63c60836248812671aa3b15df673fbaf74
  CapacitorGeolocation: 3f6c960e6d9d11c83fcdd181c4912604857afb51
  CapacitorHaptics: 0cea833e6a8bf489dd6acaaebf6d953b90086c59
  CapacitorKeyboard: f38d730356be5569d72eb87ad9c8166947728c36
  CapacitorPushNotifications: b3f20973cb167cb8fee622e309ec521134798241
  CapacitorRazorpay: 14e950b756da4e21df43ef89c78a3a62b5e5df68
  CapacitorShare: d2a742baec21c8f3b92b361a2fbd2401cdd8288e
  CapacitorSplashScreen: fd8bf1bf9081d9aa8817b7cd37d740d1bdaf2fb2
  CapacitorStatusBar: 129c68650d3f950e080e8e7e03d69c3b361dbe52
  CapacitorToast: 325d1815e2c2b556257909df7ef1e25eb96cd0c5
  CapawesomeCapacitorBadge: 4df4bd752f7fcf0cedfc0748bca1cbe8a0e29794
  CapgoNativegeocoder: e026dfdf3380a0f118766a18a24a88bccf490642
  PantristCapacitorDatePicker: 4657b2869d0a15ccd9671bbc4028e075b4a19af6
  razorpay-pod: ffee2198928e2a5ed2556863af4db6b79722cea3

PODFILE CHECKSUM: a074fc2ef35d03ac2a2e182acfbb4e927b5231b2

COCOAPODS: 1.16.2
