<?xml version='1.0' encoding='utf-8'?>
<plist version="1.0">
    <dict>
        <key>CFBundleDevelopmentRegion</key>
        <string>en</string>
        <key>CFBundleDisplayName</key>
        <string>ZFORB</string>
        <key>CFBundleExecutable</key>
        <string>$(EXECUTABLE_NAME)</string>
        <key>CFBundleIdentifier</key>
        <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
        <key>CFBundleInfoDictionaryVersion</key>
        <string>6.0</string>
        <key>CFBundleName</key>
        <string>$(PRODUCT_NAME)</string>
        <key>CFBundlePackageType</key>
        <string>APPL</string>
        <key>CFBundleShortVersionString</key>
        <string>$(MARKETING_VERSION)</string>
        <key>CFBundleVersion</key>
        <string>$(CURRENT_PROJECT_VERSION)</string>
        <key>ITSAppUsesNonExemptEncryption</key>
        <false />
        <key>LSApplicationCategoryType</key>
        <string />
        <key>LSRequiresIPhoneOS</key>
        <true />
        <key>NSCameraUsageDescription</key>
        <string>We need access to your camera to allow you to update your profile picture.</string>
        <key>NSLocationWhenInUseUsageDescription</key>
        <string>We use your location to provide nearby service recommendations.</string>
        <key>NSMicrophoneUsageDescription</key>
        <string>We need access to your microphone to allow you to record message.</string>
        <key>NSPhotoLibraryAddUsageDescription</key>
        <string>We need access to your photo library to allow you to update your profile picture.</string>
        <key>NSPhotoLibraryUsageDescription</key>
        <string>We need access to your photo library to allow you to update your profile picture.</string>
        <key>NSPrivacyAccessedAPICategoryUserDefaults</key>
        <string>We need to store your preferences to provide you with a personalized experience.</string>
        <key>NSUserDefaultsUsageDescription</key>
        <string>We need to store your preferences to provide you with a personalized experience.</string>
        <key>UILaunchStoryboardName</key>
        <string>LaunchScreen</string>
        <key>UIMainStoryboardFile</key>
        <string>Main</string>
        <key>UIRequiredDeviceCapabilities</key>
        <array>
            <string>armv7</string>
        </array>
        <key>UISupportedInterfaceOrientations</key>
        <array>
            <string>UIInterfaceOrientationPortrait</string>
            <string>UIInterfaceOrientationLandscapeLeft</string>
            <string>UIInterfaceOrientationLandscapeRight</string>
        </array>
        <key>UISupportedInterfaceOrientations~ipad</key>
        <array>
            <string>UIInterfaceOrientationPortrait</string>
            <string>UIInterfaceOrientationPortraitUpsideDown</string>
            <string>UIInterfaceOrientationLandscapeLeft</string>
            <string>UIInterfaceOrientationLandscapeRight</string>
        </array>
        <key>UIViewControllerBasedStatusBarAppearance</key>
        <true />
        <key>NSAppTransportSecurity</key>
        <dict>
            <key>NSAllowsArbitraryLoads</key>
            <true />
        </dict>
    </dict>
</plist>
