body::-webkit-scrollbar {
  display: none;
}

.margin-top-2 {
  margin-top: 2px !important;
}

.margin-top-3 {
  margin-top: 3px;
}

.margin-top-4 {
  margin-top: 4px !important;
}

.margin-top-5 {
  margin-top: 5px !important;
}

.margin-top-6 {
  margin-top: 6px !important;
}

.margin-top-7 {
  margin-top: 7px !important;
}

.margin-top-8 {
  margin-top: 8px !important;
}

.margin-top-10 {
  margin-top: 10px !important;
}

.margin-top-12 {
  margin-top: 12px !important;
}

.margin-top-15 {
  margin-top: 15px !important;
}

.margin-top-20 {
  margin-top: 20px !important;
}

.margin-top-25 {
  margin-top: 25px !important;
}

.margin-top-30 {
  margin-top: 30px !important;
}

.margin-top-35 {
  margin-top: 35px !important;
}

.margin-top-40 {
  margin-top: 40px !important;
}

.margin-top-45 {
  margin-top: 45px !important;
}

.margin-top-50 {
  margin-top: 50px !important;
}

.margin-top-55 {
  margin-top: 55px !important;
}

.margin-top-60 {
  margin-top: 60px !important;
}

.margin-top-70 {
  margin-top: 70px !important;
}

.margin-top-80 {
  margin-top: 80px !important;
}

.margin-top-110 {
  margin-top: 110px !important;
}

.margin-top-15vw {
  margin-top: 15vw !important;
}

/*------------*/

.margin-bottom-5 {
  margin-bottom: 5px !important;
}

.margin-bottom-8 {
  margin-bottom: 8px !important;
}

.margin-bottom-10 {
  margin-bottom: 10px !important;
}

.margin-bottom-15 {
  margin-bottom: 15px !important;
}

.margin-bottom-20 {
  margin-bottom: 20px !important;
}

.margin-bottom-25 {
  margin-bottom: 25px !important;
}

.margin-bottom-30 {
  margin-bottom: 30px !important;
}

.margin-bottom-35 {
  margin-bottom: 35px !important;
}

.margin-bottom-40 {
  margin-bottom: 40px !important;
}

.margin-bottom-45 {
  margin-bottom: 45px !important;
}

.margin-bottom-50 {
  margin-bottom: 50px !important;
}

.margin-bottom-55 {
  margin-bottom: 55px !important;
}

.margin-bottom-60 {
  margin-bottom: 60px !important;
}

.margin-bottom-70 {
  margin-bottom: 70px !important;
}

.margin-bottom-80 {
  margin-bottom: 80px !important;
}

/*--------------*/

.margin-left-5 {
  margin-left: 5px !important;
}

.margin-left-7 {
  margin-left: 7px !important;
}

.margin-left-8 {
  margin-left: 8px !important;
}

.margin-left-10 {
  margin-left: 10px !important;
}

.margin-left-12 {
  margin-left: 12px !important;
}

.margin-left-15 {
  margin-left: 15px !important;
}

.margin-left-20 {
  margin-left: 20px !important;
}

.margin-left-25 {
  margin-left: 25px !important;
}

.margin-left-30 {
  margin-left: 30px !important;
}

.margin-left-35 {
  margin-left: 35px !important;
}

.margin-left-40 {
  margin-left: 40px !important;
}

.margin-left-45 {
  margin-left: 45px !important;
}

.margin-left-50 {
  margin-left: 50px !important;
}

.margin-left-55 {
  margin-left: 55px !important;
}

.margin-left-60 {
  margin-left: 60px !important;
}

/*--------------*/

.margin-right-5 {
  margin-right: 5px !important;
}

.margin-right-7 {
  margin-right: 7px !important;
}

.margin-right-10 {
  margin-right: 10px !important;
}

.margin-right-12 {
  margin-right: 12px !important;
}

.margin-right-15 {
  margin-right: 15px !important;
}

.margin-right-20 {
  margin-right: 20px !important;
}

.margin-right-25 {
  margin-right: 25px !important;
}

.margin-right-30 {
  margin-right: 30px !important;
}

.margin-right-35 {
  margin-right: 35px !important;
}

.margin-right-40 {
  margin-right: 40px !important;
}

.margin-right-45 {
  margin-right: 45px !important;
}

.margin-right-50 {
  margin-right: 50px !important;
}

.margin-right-55 {
  margin-right: 55px !important;
}

.margin-right-60 {
  margin-right: 60px !important;
}

/*------------*/

.no-margin-left {
  margin-left: 0px !important;
}

.no-margin-bottom {
  margin-bottom: 0px !important;
}

.no-margin-top {
  margin-top: 0px !important;
}

.no-margin-right {
  margin-right: 0px !important;
}

.no-margin {
  margin: 0px !important;
}

/*End Margin Classes*/

/*Padding Classes*/

.no-padding {
  padding: 0px !important;
}

.no-padding-left {
  padding-left: 0px !important;
}

.no-padding-right {
  padding-right: 0px !important;
}

.no-padding-top {
  padding-top: 0px !important;
}

.no-padding-bottom {
  padding-bottom: 0px !important;
}

.padding-5 {
  padding: 5px;
}

.padding-10 {
  padding: 10px;
}

.padding-15 {
  padding: 15px;
}

.padding-20 {
  padding: 20px !important;
}

.padding-left-5 {
  padding-left: 5px;
}

.padding-left-10 {
  padding-left: 10px !important;
}

.padding-left-15 {
  padding-left: 15px;
}

.padding-left-20 {
  padding-left: 20px;
}

.padding-right-5 {
  padding-right: 5px;
}

.padding-right-10 {
  padding-right: 10px !important;
}

.padding-right-15 {
  padding-right: 15px;
}

.padding-right-20 {
  padding-right: 20px;
}

.padding-top-3 {
  padding-top: 3px;
}

.padding-top-5 {
  padding-top: 5px;
}

.padding-top-10 {
  padding-top: 10px !important;
}

.padding-top-15 {
  padding-top: 15px !important;
}

.padding-top-20 {
  padding-top: 20px !important;
}

.padding-bottom-3 {
  padding-bottom: 3px;
}


.padding-bottom-5 {
  padding-bottom: 5px;
}

.padding-bottom-10 {
  padding-bottom: 10px !important;
}

.padding-bottom-15 {
  padding-bottom: 15px;
}

.padding-bottom-20 {
  padding-bottom: 20px;
}

.disable {
  display: none;
}

.no-border-bottom {
  border-bottom: 0px !important;
}

// customize width
.full-width {
  width: 100%;
}

.compaign-list-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.new-membership-button-container {
  &.add-package-btn-padding {
    .ios & {
      padding-bottom: 0px;
    }
  }
}

.site-full-rounded-button {
  min-height: 54px;
  border-radius: 100px;

  &.less-border-radius {
    --border-radius: 12px;
    font-size: 13px;
    --background-activated: #29385b;
  }

  &.text-capitalize {
    text-transform: uppercase;
  }

  &.add-money-button {
    min-height: 52px;
    --border-radius: 12px;
    font-size: 13px;
    --background-activated: #29385b;
    width: 80%;
    --background: #29385b !important;
    text-transform: uppercase;
  }

  &.compaign-list-button {
    --border-radius: 12px;
    width: 60%;
    min-height: 50px;
    --background: #29385b !important;
    text-align: center;
    font-size: 12px;
    --background-activated: #29385b;
  }

  &.half-width {
    width: calc(50% - 4px);
  }

  &.one-third-width {
    max-width: calc(100vw - 135px);
    width: 100%;
  }

  &.min-width-120 {
    min-width: 120px;
  }

  &.min-width-150 {
    min-width: 150px;
  }

  &.remaining-width {
    max-width: calc(100vw - 171px);
    width: 100%;
  }

  &.small-button {
    min-height: 38px;
  }

  &.full-width {
    width: 100%;
  }

  &.close-button-hotel {
    --background: #999 !important;
  }

  &.search-booking-filter {
    --background: #29385b !important;
    text-transform: uppercase;

    .ios & {
      margin-bottom: 110px !important;
    }
  }

  &.primary-button {
    margin-top: 15px;
    --background: #29385b !important;

    &.membership-button {
      font-size: 10px;
      font-weight: 700;
      --border-radius: 12px;
      //  min-height: 48px;
      border-radius: 15px;
      --background: #C29133 !important;
      --background-activated: #C29133 !important;
    }

    &.text-capitalize {
      text-transform: uppercase;
    }

    &.expire-holiday {
      border: 2px solid #7f8186;
      --background: #29385b !important;
      --background-activated: #29385b !important;
    }

    &.add-package {
      border: 2px dotted #7f8186;
      --background: #e8ecf5 !important;
      --color: #29385b;
      --background-activated: #e8ecf5 !important;
    }

    &.membership {
      border: 2px #7f8186;
      --background: #29385b !important;
      --color: #e8ecf5;
      --background-activated: #e8ecf5 !important;
    }

    &.no-membership-button {
      border: 2px solid #7f8186;
      font-size: 14px;
      font-weight: 700;
      --border-radius: 12px;
      border-radius: 15px;
      --background-activated: #29385b !important;
    }

    &.notification-list-no-button {
      margin-top: 0px;
      --background: #db524e !important;
      --background-activated: #db524e;
      width: 32%;
    }

    &.notification-list-yes-button {
      margin-top: 0px;
      --background: green !important;
      --background-activated: green;
      width: 32%;
    }
  }

  &.emi-tab-button {
    margin-top: 10px;
    margin-bottom: 70px;
    --background: #29385b !important;
  }

  &.green-button {
    --background: #07944f !important;
    --background-activated: #07944f !important;
    --background-focused: #07944f !important;
    --background-hover: #07944f !important;
  }

  &.default-button {
    --background: #e8e8e8 !important;
    --background-activated: #e8e8e8 !important;
    --background-focused: #e8e8e8 !important;
    --background-hover: #e8e8e8 !important;
    --color: #0d0d0d !important;
  }

  &.danger-button {
    --background: #fa4856 !important;
    --background-activated: #fa4856 !important;
    --background-focused: #fa4856 !important;
    --background-hover: #fa4856 !important;
  }

  &.button-disabled {
    --background: #cfd2d3 !important;
    --color: #858c8f;
  }

  &.medium-text {
    font-size: 17px;
    font-weight: 500;
  }
}

.refund-container {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  gap: 10px;

  .refund-section-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .refund-section-item {
    display: flex;
    align-items: center;
    flex-direction: column;
  }

  .refund-checkbox-container {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .refund-text {
    font-size: 15px;
    font-weight: 600;
  }

  .wallet-fast-use {
    font-size: 11px;
  }

  .refund-info-text {
    margin: 25px 25px 0px 25px;
    font-size: 11px;
    color: black;
    margin-top: 5px;
    line-height: 1.6;
    font-weight: 500;
  }

  .refund-note-container {
    display: flex;
  }

  .note-text {
    font-size: 12px;
    font-weight: 600;
  }
}

.main-modal-dismiss {
  display: flex;
  justify-content: center;

  &.button-gap {
    gap: 20px;
  }
}

.remind-later-container {
  display: flex;
  justify-content: center;

  .remind-later-text {
    font-size: 15px;
    text-decoration: underline;
  }
}

.site-less-rounded-button {
  min-height: 54px;
  --border-radius: 8px !important;
  width: 100%;

  &.primary {
    --background: #0d0d0d !important;
  }

  &.button-disabled {
    --background: #cfd2d3 !important;
    --color: #858c8f;
  }
}

.add-new-button-container {
  text-align: center;
  margin-top: 10px;
}

.custom-modal-notifications {
  text-align: center;
  margin: 10px 0px 10px 0px;
  font-size: 17px;
}

.add-new-button {
  --background: #fdb606;
  --color: white;
  min-height: 50px;
  border-radius: 70px;
  padding: 5px 10px;

  &.change-profile {
    --background: #29385b;
    padding: 0px;
  }

  &.text-capitalize {
    text-transform: uppercase;
  }
}

.document-items {
  position: relative;
  margin-top: 20px;
  display: flex;
  flex-direction: column;

  &.attachment-con {
    margin-top: 0px;
  }

  &.generate-key {
    margin-top: 5px;
  }

  .document-upload-container {
    border: 2px dotted #4c64a0;
    border-radius: 8px;
    padding: 22px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    cursor: pointer;
    background-color: #f3f5f9;
    transition: background-color 0.3s ease;
    margin-left: 5px;
    margin-right: 5px;
    margin-top: 10px;
    position: relative;
    height: 150px;
    // overflow: hidden;

    &.generate-key-container {
      height: auto;
      background-color: #f6f3ee;
      border: 2px dotted #b48429;
      padding: 18px;
    }
  }

  .code-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .code-text {
    font-size: 16px;
    color: #b48429;
    font-weight: 600;
    margin: 0;
    padding: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    text-align: left;
    overflow-wrap: break-word;
    max-width: 100%;
  }

  .copy-clipboard {
    cursor: pointer;
    font-size: 16px;
    color: black;
    font-weight: bold;
    margin: 5px;
    padding: 0;
  }

  .copy-clipboard-clicked {
    color: #4caf50;
  }

  .upload-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
  }

  .uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    position: absolute;
    top: 0;
    left: 0;
  }

  .upload-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .upload-adhar {
    display: block;
    font-size: 16px;
    color: black;
    margin-bottom: 5px;
    font-weight: bold;
  }

  .delete-icon {
    position: absolute;
    top: 0px;
    right: -5px;
    background-color: #29385b;
    color: #fff;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    font-size: 16px;
  }

  .delete-icon img {
    width: 16px;
    height: 16px;
  }

  .document-upload-container.missing-image {
    border: 2px solid var(--ion-color-warning-dark-red);
    background-color: #f3f5f9;
  }
}

.site-form-control {
  --border-width: 1px !important;
  --border-radius: 6px;
  --inner-padding-start: 0px;
  --inner-padding-end: 0px;
  --padding-start: 16px;
  --padding-end: 16px;

  &.is-invalid {
    --border-color: #dc3545 !important;
  }

  ion-input {
    font-size: 14px;
    --padding-start: 0px !important;
    --padding-end: 0px;
    --padding-top: 3px;
    --padding-bottom: 0px;
    font-weight: 500;

    &.has-start-icon {
      --padding-start: 45px !important;
    }

    &.no-font-weight {
      font-weight: 400;
    }
  }

  ion-select[disabled] {
    color: black;
    opacity: 1;
    pointer-events: none;
  }

  ion-input[disabled] {
    color: black;
    opacity: 1;
    pointer-events: none;
  }

  .start-icon {
    margin-right: 13px;
    font-size: 20px;
  }

  &.mobile-form-control {
    --padding-top: 6px;
    --padding-bottom: 6px;
  }
}

.site-form-floating-control {
  border-radius: 6px;
  --inner-padding-start: 16px;
  --inner-padding-end: 16px;
  --inner-padding-top: 2px;
  --inner-padding-bottom: 0px;
  --padding-start: 0px;
  --padding-bottom: 0px;
  border: 2px solid var(--ion-color-input-border);
  --min-height: 60px !important;
  height: 65px;

  &.child-section {
    margin-top: 10px;
  }

  &.padding-start {
    // --inner-padding-top: 2px;
    --padding-start: 16px;
    --inner-padding-start: 0px;
    --inner-padding-end: 0px;
    height: 60px;
  }

  &::part(native) {
    border-radius: 12px !important;
  }

  ion-input {
    border-radius: 12px;
    font-size: 15px;
  }

  ion-textarea {
    border-radius: 12px;
    font-size: 15px;
  }

  &.large-font {
    ion-input {
      border-radius: 12px;
      font-size: 17px;
    }
  }

  ion-label {
    margin-top: 0px !important;
  }

  .label-floating {
    transform: translate(0, 19px);
  }

  &.is-invalid {
    border: 2px solid var(--ion-color-warning-dark-red) !important;
  }

  .caret-down-icon {
    position: absolute;
    right: 5px;
    top: 50%;
    margin-top: -13.5px;
    cursor: pointer;
    z-index: 9999;
  }

  .value-display-label {
    margin-bottom: 0px !important;
    font-size: 15px;
    padding: 12px 0px;
    padding-right: 10px;
    max-width: calc(100% - 20px);
  }

  .slot-start {
    margin-inline: 0px;
    margin-top: 19px;
    margin-left: 15px;
    margin-bottom: 19px;
    font-size: 22px;
  }

  .custom-select-float-placeholder {
    font-size: 17px;
    margin-bottom: 0px !important;
    padding: 12px 0px;
    padding-right: 10px;
    max-width: calc(100% - 20px);
  }

  .start-icon {
    margin-top: 20px;
  }
}

.fixed-search {
  position: fixed;
  top: 69px;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
  border-top: 1px solid #ddd;

  &.country-city-margin {
    top: 55px;
    box-shadow: unset;
  }

  .padding-top {
    padding-top: 0;
  }
}

.search-container ion-item {
  width: 100%;
  /* Ensure it takes full width */
}

.site-input-group {
  padding: 0px 16px;
  border-radius: 12px;
  border: 2px solid var(--ion-color-input-border);

  .site-form-floating-control {
    border-radius: 0px !important;
    border: 0px !important;
    --inner-padding-start: 0px;
    --inner-padding-end: 0px;
    --inner-padding-top: 7px;
    --inner-padding-bottom: 7px;
    border-bottom: 1px solid var(--ion-color-input-border) !important;

    &:last-child {
      border: 0px !important;
    }

    .edit-input-label {
      margin-top: 18px;
    }
  }
}

.custom-form-control {
  position: relative;
  font-size: 17px;
  border: 2px solid var(--ion-color-input-border);
  border-radius: 12px;
  padding: 19px 16px;
  background-color: var(--ion-color-primary-contrast);
  display: flex;
  justify-content: center;
  flex-direction: column;

  .caret-down-icon {
    position: absolute;
    right: 15px;
    top: 15px;
    cursor: pointer;
    z-index: 9999;
    font-size: 27.2px;
  }

  &.has-content {
    font-size: 13.94px !important;
    padding: 8px 16px !important;
  }

  &.is-invalid {
    border: 2px solid var(--ion-color-warning-dark-red) !important;
  }

  .custom-value-display-value {
    padding-top: 8px;
    font-size: 15px;
  }
}

.error-message {
  text-align: left;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 13px;
  color: var(--ion-color-warning-dark-red);
  font-weight: 600;
}

.item-has-focus.label-floating.sc-ion-label-ios-h,
.item-has-focus .label-floating.sc-ion-label-ios-h,
.item-has-placeholder.sc-ion-label-ios-h:not(.item-input).label-floating,
.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-ios-h,
.item-has-value.label-floating.sc-ion-label-ios-h,
.item-has-value .label-floating.sc-ion-label-ios-h {
  transform: translate(0, 8px) scale(0.82);
}

ion-modal {
  --box-shadow: 0 28px 48px rgba(0, 0, 0, 0.4) !important;
  --backdrop-opacity: var(--ion-backdrop-opacity, 0.05) !important;
}

.green-text {
  color: var(--ion-color-green-text) !important;
}

.secondary-text {
  color: var(--ion-color-secondary-text) !important;
}

.grey-text {
  color: var(--ion-color-custom-note) !important;
}

.red-text {
  color: var(--ion-color-red) !important;
}

// end above css for customer layout tabs design according to adobe design //
.customer-forbcorp-navigation {
  ion-tab-bar {
    height: 80px;
    border: none;
    --background: #fff !important;
    //border-top: 2px solid #f7f7f7;

    ion-tab-button {
      height: 100%;
      --ripple-color: transparent;

      .icon-container-forbcorp {
        width: 42px;
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.4rem;
        background: var(--ion-color-primary-contrast);
        border-radius: 50%;

        .non-selected-icon {
          display: block;
        }

        .selected-icon {
          display: none !important;
        }
      }

      ion-label {
        font-size: 12px;
        font-weight: 500;
        color: var(--ion-color-privacy-anchor-tag);
        margin-top: 5px;
        margin-bottom: 0px;

        @media screen and (max-width: 375px) {
          font-size: 10px !important;
        }
      }

      &.tab-selected {
        .icon-container-forbcorp {
          background-color: var(--ion-color-primary);
          font-size: 1.4rem;

          .non-selected-icon {
            display: none !important;
          }

          .selected-icon {
            display: block !important;
            color: white;
          }
        }

        ion-label {
          color: var(--ion-color-primary-shade);
        }
      }
    }
  }

  &.hide-tab-bar {
    ion-tab-bar.tabs {
      display: none !important;
    }
  }
}

// start leads-details-tabs css //
ion-tab-bar {
  &.lead-detail-container {
    --background: white;
    border: none;
    padding: 15px;

    ion-tab-button {
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 50px;
      margin-left: 5px;
      margin-right: 5px;
      transition: background-color 0.3s ease;
      border: 1px solid var(--ion-color-primary);
      background: var(--ion-color-primary-contrast);
      color: var(--ion-color-heading);

      &.tab-selected {
        background: var(--ion-color-detail-tab);
        border: 1px solid var(--ion-color-detail-tab);

        .tab-label {
          color: var(--ion-color-primary-contrast) !important;
          font-size: 14px;
        }
      }
    }

    .tab-content {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      width: auto;

      ion-icon {
        font-size: 20px;
        margin-right: 6px;
        color: var(--ion-color-heading) !important;
      }

      .tab-label {
        font-size: 14px;
        color: var(--ion-color-primary);
      }
    }
  }
}

// end leads-details-tabs css //

// side-menu css code //
.sale-toolbar {
  --background: white;
  --box-shadow: none;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .sale-menu-button {
    margin: 0;
    margin-left: 15px;
  }

  .sale-menu-button ion-icon {
    font-size: 30px;
  }

  .sales-custom-title {
    color: var(--ion-color-warning-contrast);
    font-size: 21px;
    text-align: left;
    margin-left: 0;
  }

  .sale-toolbar-icon-container {
    margin-top: 15px;
    display: flex;
    margin-right: 15px;
    align-items: center;

    .sale-toolbar-icon-backgrounds {
      background-color: var(--ion-color-primary-contrast);
      padding: 11px;
      margin-bottom: 9px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .sale-toolbar-icon {
        font-size: 20px;
        color: var(--ion-color-heading);
      }
    }
  }
}

.sale-toolbar-show-title-and-icon {
  --background: white;
  --box-shadow: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;

  .back-button {
    margin-left: 15px;
  }

  .sales-custom-title {
    color: var(--ion-color-warning-contrast);
    font-size: 21px;
    text-align: left;
    margin-left: 0;
    //  display: flex;
    //  margin-right: 0px;
  }
}

.sales-custom-toolbar {
  height: 60px;
  --background: #29385b;
  display: flex;
  justify-content: center;

  .custom-menu-button {
    height: 60px;
    color: var(--ion-color-primary-contrast);
    display: flex;
  }

  .side-menu-custom-title {
    color: var(--ion-color-primary-contrast);
    font-size: 20px;
    text-align: center;
  }
}

.side-menu-item {
  display: flex;
  align-items: center;
  --border-color: transparent;
  border: none;
  --inner-border-width: 0;
  margin-top: 5px;

  .side-menu-icon-container {
    margin-right: 10px;
  }
}

.logout-card {
  margin: 16px;
  background: var(--ion-color-primary);
  border-radius: 10px;

  .logout-text {
    text-align: center;
    color: var(--ion-color-primary-contrast);
    font-weight: bold;
    cursor: pointer;
    font-size: 17px;
  }
}

// end side-menu css code //

.white-background {
  background-color: var(--ion-color-primary-contrast);
}

.custom-note {
  font-size: 13px;
  color: var(--ion-color-custom-note) !important;
}

.custom-loading-container {
  img {
    border-radius: 50%;
  }
}

.site-button {
  margin-bottom: 16px;
  --background: var(--ion-color-primary);
  min-height: 54px;
  --box-shadow: 0px 3px 6px var(--ion-color-box-shadow);
  --border-radius: 5px;
  text-transform: uppercase;

  &:last-child {
    margin-bottom: 0px !important;
  }
}

.site-custom-popup {
  --height: auto !important;
  align-items: flex-end;
  --box-shadow: none !important;
  --backdrop-opacity: var(--ion-backdrop-opacity, 0.32) !important;
  --max-height: 98vh;

  &.ios {
    --max-height: calc(98vh - var(--ion-safe-area-top)) !important;

    .guest-preview-padding-bottom {
      padding-bottom: 80px !important;
    }
  }

  .convenience-text {
    font-size: 10px;
    color: #7f8186;
  }

  .site-custom-popup-container {
    height: 100%;

    &.min-height {
      min-height: 250px;
    }
  }

  .site-custom-popup-header {
    text-align: center;
    position: relative;
    padding: 16px;

    &.skip-header {
      text-align: end;
    }

    &.heading-setting {
      display: flex;
      justify-content: center;

    }

    .skip-text {
      font-size: 17px;
      font-weight: bold;
      text-decoration: underline;
    }

    i-feather {
      position: absolute;
      top: 16px;
      left: 16px;
    }

    .header-text {
      text-rendering: auto;
      color: #0b1920;
      font-size: 17px;
      font-weight: 600;
    }

    &.no-header-text {
      padding: 28px;
    }

    &.less-padding {
      padding: 25px;
    }

    &.book-now-padding {
      padding: 20px;
    }
  }

  .site-custom-popup-body {
    max-height: calc(100vh - 73px);
    position: relative;
    background-color: #fff;
    overflow: auto;

    &.full-screen {
      height: 100vh !important;
    }

    &.scroll-able {
      overflow-x: scroll;
    }

    .popup-large-heading {
      font-weight: 700;
      font-size: 28px;
      margin-bottom: 6px;
      font-family: "Oswald", sans-serif;

      &.text-center {
        text-align: center;
      }

      &.book-now-heading {
        font-size: 25px;
      }
    }

    .popup-medium-heading {
      font-size: 16px;
    }

    .book-now-medium-heading {
      font-size: 17px;
      color: black;
      font-weight: 400;
      text-align: left;

      .ios & {
        font-size: 18px;
      }
    }

    .popup-expire-holiday-heading {
      font-size: 18px;
    }

    .popup-normal-heading {
      font-size: 14px;

      &.bold-text {
        font-weight: 500;
      }
    }

    .amount-selection-container-wallet-modal {
      display: flex;
      gap: 10px;

      .amount-selection-modal {
        background: white;
        border-radius: 14px;
        padding: 20px 15px;
        width: 75px;
        height: 50px;
        display: flex;
        align-items: center;
        cursor: pointer;
        border: 2px solid #E6E6E6AF;
        transition: all 0.3s ease-in-out;

        &.selected-amount {
          background-color: white;
          color: #1B2338;
          font-weight: 500;
          border-color: #1B2338;
        }

        &.unselected-amount {
          background-color: white; // Yellow for unselected options
        }

        .amount-text {
          font-size: 14px;
        }
      }
    }

    .dismiss-button-container {
      position: absolute;
      bottom: 10px;
      left: 16px;
      width: calc(100% - 32px);
    }

    .popup-text-container {
      min-height: 120px;

      &.medium {
        min-height: 95px;
      }

      &.small {
        min-height: 70px;
      }

      &.auto {
        min-height: auto !important;
      }
    }

    .resolution-con {
      .title {
        font-size: 18px;
        font-weight: 600;
        margin: 5px 0;
        font-family: "Oswald", sans-serif;
      }

      .desc {
        font-size: 12px;
        margin-bottom: 20px;
      }

      .image-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        /* 3 images per row */
        gap: 10px;
        /* Space between images */
        padding: 10px;
      }

      .image-item {
        display: flex;
        height: 100%;
        align-items: center;
      }

      .image-item img {
        width: 100%;
        height: auto;
        border-radius: 8px;
        /* Optional: Add rounded corners */
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        /* Optional: Add a shadow effect */
      }

    }

    // grab deal modal design //
    .campaign-card {
      border-radius: 5px;
      margin-bottom: 15px;
      // height: 200px;
      position: relative;

      .ios & {
        margin-bottom: 20px;
      }

      .campaign-card-header {
        position: relative;

        &.success-modal-card-header {
          background: transparent linear-gradient(177deg, rgba(165, 181, 219, 0.56) 0%, #29385B 100%) 0% 0% no-repeat padding-box;
          height: 104px;
          display: flex;

        }

        .campaign-image-slide {
          position: relative;

          &.success-modal-card-image-slide {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
          }

          img {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-radius: 0px;
            margin: 0px 0px -4px 0px;
          }

          .close-icon {
            // position: absolute;
            // top: 18px;
            // right: 27px;
            // color: #c0c9bde8;
            // border-radius: 50%;
            // cursor: pointer;
            // width: 30px;
            position: absolute;
            top: 15px;
            right: 15px;
            color: black;
            border-radius: 50%;
            cursor: pointer;
            width: 30px;
            height: 30px;
            background: white;
            padding: 5px 5px;

            &.white-icon {
              //  color: white;
            }
          }
        }
      }

      .coin-container {
        margin-top: -59px;
        display: flex;
        justify-content: center;

        &.request-coin-container {
          margin-top: -60px;
        }

        .balance-icon {
          height: 100px;
          width: 300px;

          &.request-bal-coin {
            height: 100px;
          }
        }

        .reward-overlay {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 150px;
          /* Make it slightly larger than the coin */
          height: 150px;
          transform: translate(-50%, -50%);
          pointer-events: none;
          /* Ensures it doesn't interfere with interactions */
        }
      }

      .campaign-detail-container {
        display: flex;
        flex-direction: column;
        align-items: center;

        .campaign-detail-title {
          font-size: 20px;
          color: #29385B;
          font-weight: bold;
          text-align: center;
          max-height: 110px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;

          &.random-campaign-title {
            -webkit-line-clamp: 16;
            overflow: auto;
            text-overflow: ellipsis;
            word-break: break-word;
            margin: 10px;
          }

          &.success-text {
            font-size: 22px;
          }
        }

        .campaign-detail-rewards {
          font-size: 15px;
          margin: 4px;
          text-align: center;
          color: #000000;
          font-weight: bold;
        }

        .campaign-detail-desc {
          font-size: 12px;
          text-align: center;
          margin: 8px 20px 0px;
        }

        .text {
          max-height: 110px;
          overflow: hidden;
          display: -webkit-box;
          //  -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          transition: max-height 0.3s ease-in-out;
          text-align: justify;
        }

        .rewards {
          max-height: 120px;
          overflow: hidden;
          //  display: -webkit-box;
          //  -webkit-line-clamp: 6;
          //  -webkit-box-orient: vertical;
          text-align: justify;
          margin: 10px 15px;
          font-size: 14px;
          color: #000000;
          overflow-y: auto;
        }

        .text-container {
          max-width: 400px;
        }

        .text-container:has(.see-more.clicked) .text {
          max-height: none;
          /* Expand when clicked */
          -webkit-line-clamp: unset;
        }

        .see-more {
          color: blue;
          cursor: pointer;
          font-size: 13px;
        }

        .expanded {
          max-height: none;
          -webkit-line-clamp: unset;
        }

        // css for success modal //
        .success-container {
          text-align: center;
          font-size: 13px;
          font-weight: 500;
          color: #333;
          display: flex;
          flex-direction: column;
        }

        .earned-text {
          font-size: 17px;
        }

        .earned-my-cash {
          color: black;
          font-weight: 400;
          font-size: 13px;

          &.my-cash {
            color: #cda60ef2;
            font-weight: bold;
            font-size: 19px;
          }

          .cash-label {
            font-size: 19px;
          }
        }

        .earned-promo-cash {
          color: #007bff;
          font-weight: bold;
          font-size: 15px;
        }
      }

      .detail-button {
        display: flex;
        justify-content: center;
        margin-top: 15px;

        .detail-text {
          font-size: 16px;
          text-decoration: underline;
          color: #29385B;
          font-weight: 600;

          .ios & {
            margin-bottom: 10px;
          }
        }
      }

      .grab-deal-button {
        --border-radius: 11px;
        height: 55px;
        margin: 16px 16px 0px 16px;

        &.random-compaign-button {
          margin: 7px 16px 20px 16px;
        }

        .grab-deal-container {
          display: flex;
          align-items: center;
          gap: 5px;

          .grab-deal-text {
            font-size: 12px;
            text-transform: uppercase;
          }
        }
      }
    }

  }

  &.job-invitation-popup {
    .site-custom-popup-body {
      height: calc(100% - 68px);

      &.random-campaign-border-radius {
        border-radius: 12px;
      }

      .img-cont {
        display: flex;
        justify-content: left;
      }

      img {
        margin: 20px 0;
        height: 100px;
        border-radius: 8px;
      }
    }
  }

  &.adults-selection-popup {}

  &.filter-popup {
    .site-custom-popup-body {
      background-color: #f7f7f7;
      overflow: auto;
      height: calc(100vh - 165px);
    }

    &.ios {
      .site-custom-popup-body {
        height: calc(100vh - (165px + var(--ion-safe-area-top))) !important;
      }
    }
  }

  .site-custom-popup-footer {
    padding: 16px;
  }

  &.full-screen-popup {
    --max-height: 100vh !important;

    .site-custom-popup-container {
      height: 100vh;
    }
  }
}

ion-spinner {
  &.dots {
    height: 50px;
    width: 50px;
  }
}

.custom-mobile-input {
  border: 0px !important;
  outline: none !important;
  height: 40px !important;
  font-size: 14px;
  font-weight: 500;
  color: rgb(11, 25, 32);
  background-color: transparent !important;

  &::placeholder {
    color: rgb(11, 25, 32);
    opacity: 1;
  }

  &::-ms-input-placeholder {
    color: rgb(11, 25, 32);
    opacity: 1;
  }
}

.swiper-pagination {
  position: absolute;
  justify-content: center;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10;
  width: 114px !important;
}

.swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background-color: #ccc;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.swiper-pagination-bullet-active {
  background-color: #29385B;
}

.swiper-pagination-bullet:hover {
  background-color: #B0B8C1;
}