.login-page {
  z-index: 999999999;
  min-height: 0vh !important;
  --height: auto !important;
  height: auto !important;
  max-height: 100% !important;
  contain: content;
  display: block;

  .custom-form {
    height: 100%;
  }

  .login-page-container {
    z-index: 999999999;
    padding: 25px 20px 30px;
    text-align: center;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    background-color: var(--ion-color-primary-contrast);
    min-height: 100%;

    &.for-login-height {
      height: auto !important;
      max-height: 100% !important;
      flex: none !important;
      display: block;
    }

    .back-button {
      --padding-start: 0px;
      --padding-end: 7px;
      margin-inline-start: -17px;
    }

    .login-buttons {
      margin-bottom: 16px;
      --background: var(--ion-color-primary);
      min-height: 54px;
      --box-shadow: 0px 3px 6px var(--ion-color-box-shadow);
      --border-radius: 5px;
      text-transform: uppercase;
      --border-radius: 40px;
    }

    .login-container {
      ion-img {
        max-width: 105px;
        margin: 0 auto;
        margin-bottom: 24px;
      }

      h2 {
        font-size: 24px;
        letter-spacing: 0;
        color: var(--ion-color-warning-contrast);
        font-weight: 300;

        span {
          font-weight: 600;
        }
      }

      .page-heading-title {
        font-size: 14px;
        margin: 20px;
      }
    }

    .form-container {
      min-height: 250px;

      .forgot-password {
        font-size: 14px;
        letter-spacing: 0;
        color: var(--ion-color-primary);
        font-weight: 600;
        text-align: right;
        margin-right: 5px;
      }

      ion-button {
        margin-bottom: 16px;
        --background: var(--ion-color-primary);
        min-height: 54px;
        --box-shadow: 0px 3px 6px var(--ion-color-box-shadow);
        --border-radius: 5px;
        text-transform: uppercase;

        &:last-child {
          margin-bottom: 0px !important;
        }
      }

      &.text-left {
        text-align: left !important;
      }

      .login-type-section-container {
        padding: 10px 0px;

        .login-type-section-item {
          display: inline-block;
          vertical-align: middle;
          margin-right: 15px;

          &:last-child {
            margin-right: 0px;
          }

          ion-checkbox,
          span {
            display: inline-block;
            vertical-align: middle;
          }

          span {
            padding-left: 4px;
            font-size: 13px;
            font-weight: 500;
            margin-top: -3px;
          }
        }
      }
    }

    .resend-container {
      font-size: 14px;
      letter-spacing: 0;
      color: var(--ion-color-primary);
      font-weight: 600;
    }

    /* Add this to your existing CSS */

    .register-container {
      text-align: center;
      margin-top: 10px;

      p {
        font-size: 14px;
        color: var(--ion-color-primary);
        font-weight: 600;
      }

      a {
        color: var(--ion-color-primary);
        font-weight: 600;
        text-decoration: underline;
      }
    }

    .privacy-container {
      color: var(--ion-color-privacy-container);
      margin: 0px;
      font-size: 14px;
      text-align: center;

      a {
        color: var(--ion-color-privacy-anchor-tag);
        font-weight: 500;
      }
    }
  }
}

// start otp screen css code //
.otp-validate-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0px auto;
  margin-top: 10px;

  &.disabled {
    pointer-events: none;
    opacity: 0.5;
    /* Make it look disabled */
  }

  ion-input {
    display: inline-block;
    max-width: 50px;
    height: 50px;
    --padding-start: 8px;
    border: 2px solid var(--ion-color-input-border);
    border-radius: 8px;
    text-align: center;
    --padding-top: 15px;
    --padding-bottom: 15px;
    --padding-end: 8px;

    .input-wrapper {
      .input-highlight {
        height: unset;
      }
    }

    &.is-invalid {
      border: 2px solid var(--ion-color-warning-dark-red) !important;
    }
  }
}

.splash-container {
  --background: black;

  ion-img {
    position: absolute;
    left: 9.5%;
    top: 30%;
    width: 80%;
    object-fit: cover;
    object-position: top;
  }
}


// end otp screen css code //