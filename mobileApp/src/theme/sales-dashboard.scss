.sales-dashboard-container {
    padding: 5px;
    height: auto;
    margin-bottom: 100px;

    .dashboard-main-container {
        margin-left: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .heading {
            color: var(--ion-color-heading);
            font-size: 19px;
        }

        .dashboard-icon-container {
            display: flex;
            margin-right: 15px;
            align-items: center;
        }

        .dashboard-icon-background {
            background-color: var(--ion-color-primary-contrast);
            padding: 11px;
            margin-bottom: 9px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

            .dashboard-icon {
                font-size: 20px;
                color: var(--ion-color-heading);
            }
        }
    }

    .dashboard-container {
        padding: 10px;

        .dashboard-card-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;

            .dashboard-card {
                position: relative;
                flex: 1;
                margin: 4px;
                border-radius: 15px;
                padding: 2px;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #f0f0f0;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                .icon-left {
                    position: absolute;
                    top: 8px;
                    left: 8px;
                    font-size: 24px;
                    color: var(--ion-color-heading);
                }

                .icon-left-total-revenue {
                    position: absolute;
                    top: 5px;
                    left: 1px;
                    font-size: 40px;
                    color: var(--ion-color-heading);
                }

                .icon-right {
                    position: absolute;
                    top: 10px;
                    right: 10px;
                    font-size: 15px;
                }

                .dashboard-card-content {
                    text-align: center;
                    margin-top: 20px;
                    padding: 15px;

                    h2,
                    .heading-title,
                    .page-heading,
                    .page-heading-total-revenue {
                        margin: 0;
                        padding: 0;
                        color: var(--ion-color-primary-contrast);
                    }

                    h2 {
                        font-size: 32px;
                        color: var(--ion-color-dashboard-heading);
                        font-weight: bold;
                        letter-spacing: 0;
                    }

                    .page-heading {
                        font-size: 11px;
                        color: var(--ion-color-dashboard-heading);
                        letter-spacing: 0;
                        font-weight: 500;
                    }

                    .page-heading-total-revenue {
                        font-size: 12px;
                        color: var(--ion-color-primary-contrast);
                        letter-spacing: 0;
                        font-weight: 500;
                    }
                }
            }

            .total-pending-leads {
                background-color: var(--ion-color-pending-leads);
            }

            .total-assigned-leads-today {
                background-color: var(--ion-color-assigned-leads);
            }

            .today-revenue {
                background-color: var(--ion-color-today-revenue);
            }

            .total-revenue {
                background-color: var(--ion-color-total-revenue);
            }

            .pending-payments {
                background-color: var(--ion-color-pending-payments);
            }

            .customer-pending-approvals {
                background-color: var(--ion-color-customer-pending-approvals);
            }
        }

    }

    .dashboard-chart-container {
        padding: 1px 18px 20px;
    }

    .apex-chart {
        height: 300px;
        background-color: var(--ion-color-primary-contrast);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid var(--ion-color-input-border);
        border-radius: 25px;

        &.monthly-chart {
            .apexcharts-toolbar {
                margin-right: 28px !important;
                margin-top: 7px !important;
            }
        }
    }

    .custom-toolbar-icon {
        background-color: var(--ion-color-toolbar);
        border-radius: 12px;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        position: relative;
        z-index: 1;
    }

    .custom-toolbar-icon img {
        height: 25px;
        display: block;
    }

    // start leads screen css
    .form-container {
        margin-top: 10px;
        margin-left: 20px;
        margin-right: 20px;

        &.text-left {
            text-align: left !important;
        }

        .margin-bottom-15 {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .input-container {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 5px;
            }

            .site-form-control {
                --border-width: 1px !important;
                --border-radius: 8px;
                --inner-padding-start: 0px;
                --inner-padding-end: 0px;
                --padding-start: 16px;
                --padding-end: 16px;
                --padding-top: 0px;
                --padding-bottom: 0px;
                --border-color: #F1F3F7;
            }

            .filter-icon-container {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 11px;
                margin-bottom: 5px;

                .filter-icon {
                    cursor: pointer;
                    background-color: var(--ion-color-primary);
                    padding: 5px;
                    border-radius: 10px;
                    color: var(--ion-color-primary-contrast);
                    width: 37px;
                    height: 37px;
                    margin-top: 5px;
                }
            }

            .no-data-message {
                text-align: center;
                font-size: 16px;
                color: #888;
                padding: 20px;
            }
        }

        /* Card View Styling */
        .custom-card {
            background-color: var(--ion-color-primary-contrast);
            border: 1px solid;
            border-color: var(--ion-color-toolbar);
            border-radius: 10px;
            // box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            padding: 0;
            margin: 0;
            margin-top: 15px;
            display: flex;

            .card-content {
                padding: 10px 16px 10px;
            }

            .full-name {
                font-weight: bold;
                font-size: 22px;
                margin-bottom: 8px;
                color: var(--ion-color-warning-contrast);
            }

            .phone-container {
                display: flex;
                align-items: center;
                color: var(--ion-color-total-revenue);
                margin-bottom: 12px;
                margin-top: 5px;
                width: 100%;
                box-sizing: border-box;

                .phone-icon {
                    margin-right: 8px;
                    font-size: 20px;
                }

                .phone-number {
                    font-weight: bold;
                    font-size: 14px;
                    color: var(--ion-color-total-revenue);
                    margin: 0;
                }
            }

            .separator-line {
                width: 100%;
                height: 1px;
                background-color: var(--ion-color-toolbar);
                //    margin: 16px 0;
            }

            .address {
                display: block;
                color: var(--ion-color-address);
            }

            .button-container {
                display: flex;
                flex-direction: row;

                .action-button {
                    --border-radius: 5px;
                    --padding: 10px;
                    --color: white;
                    height: 41px;
                }

                .blue-button {
                    --background: #29385B;
                    font-size: 12px;
                }

                .white-button {
                    --background: #fff;
                    --color: #29385B;
                    --border-color: #29385B;
                    border: 2px solid var(--ion-color-primary);
                    border-radius: 7px;
                    font-size: 12px;
                    font-weight: 600;
                }
            }
        }

        @media (min-width: 415px) {
            .action-button {
                text-wrap: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    // end leads screen css // 
}


// start basic-details-tab css //
.basic-details-container {
    display: flex;
    justify-content: center;
    padding: 0;
    margin-bottom: 100px;

    .form-container {
        margin-top: 0;
        margin-bottom: 100px;
        margin-left: 6px;
        margin-right: 6px;

        .contact-info {
            margin-top: 0;
            margin-bottom: 10px;
            margin-left: 20px;
            margin-right: 20px;

            .full-name {
                font-weight: bold;
                font-size: 22px;
                margin-bottom: 8px;
                color: var(--ion-color-warning-contrast);
            }

            .phone-container {
                display: flex;
                align-items: center;
                color: var(--ion-color-total-revenue);
                margin-bottom: 12px;
                margin-top: 5px;
                width: 100%;
                box-sizing: border-box;

                .phone-icon {
                    margin-right: 8px;
                    font-size: 20px;
                }

                .phone-number {
                    font-weight: bold;
                    font-size: 14px;
                    color: var(--ion-color-total-revenue);
                    margin: 0;
                }
            }
        }

        .separator-line {
            width: calc(100% - 32px);
            height: 1px;
            background-color: var(--ion-color-toolbar);
            margin: 16px 16px;
        }

        .info-sections {
            display: flex;
            align-items: flex-start;

            .info-column {
                flex: 1;
                padding: 0 15px;
            }

            .email-section .email-container {
                margin-top: 0;
            }

            .email-section .other-email-container {
                margin-top: 20px;
            }

            .email-label {
                font-weight: bold;
                font-size: 12px;
                color: var(--ion-color-email-label);
                margin-bottom: 8px;
                display: block;
            }

            .email-value {
                font-size: 15px;
                color: var(--ion-color-email-value);
                font-weight: bold;
                border-radius: 5px;
                display: block;

                &.large-email {
                    white-space: nowrap;
                    width: 114px;
                    overflow-wrap: break-word;
                }
            }

            .calling-status-section .calling-status-container {
                margin-top: 60px;
            }

            .calling-status-section .other-calling-status-container {
                margin-top: 20px;
            }

            .status-label {
                font-weight: bold;
                font-size: 12px;
                color: var(--ion-color-email-label);
                // margin-bottom: 8px;
                display: block;
            }

            .status-value {
                color: var(--ion-color-detail-tab);
                font-size: 15px;
                font-weight: bold;
                padding: 7px 5px;
                border-radius: 5px;
                display: block;
            }

            .separator-line-vertical {
                width: 1px;
                height: auto;
                background-color: var(--ion-color-toolbar);
                margin: 0 20px;
            }

            .status-section {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                width: auto;
                background-color: var(--ion-color-status-background);
                padding: 8px 2px;
                border-radius: 5px;
                // margin-top: 7px;

                .status-icon {
                    font-size: 20px;
                    margin-right: 6px;
                }

                .status-label {
                    color: var(--ion-color-green-color);
                    font-size: 11px;
                }
            }
        }

        .tele-caller-comment-container {
            margin-top: 30px;
            margin-left: 15px;
            margin-right: 15px;
        }

        .tele-caller-comment-other-container {
            margin-top: 20px;
            margin-left: 15px;
            margin-right: 15px;
        }

        .comment-label {
            font-weight: bold;
            font-size: 12px;
            color: var(--ion-color-email-label);
            //  margin-bottom: 8px;
            display: block;
        }

        .comment-value {
            font-size: 15px;
            color: var(--ion-color-email-value);
            font-weight: bold;
            padding: 7px 0;
            border-radius: 5px;
            display: block;
        }
    }
}

// end basic-details-tab css //