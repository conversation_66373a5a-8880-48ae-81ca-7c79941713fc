.onboarding-page {
  z-index: 999999999;

  &.ios {
    min-height: calc(100vh - (80px + var(--ion-safe-area-top))) !important;
    --overflow: auto !important;
  }

  .onboarding-page-container {
    z-index: 999999999;
    padding: 25px 20px 30px;
    text-align: center;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    background-color: var(--ion-color-primary-contrast);
    min-height: 100%;

    &.ios {
      min-height: calc(100vh - (80px + var(--ion-safe-area-top))) !important;
      // --padding-top: var(--ion-safe-area-top);
    }

    &.no-padding-bottom {
      padding-bottom: 0px !important;
    }


    .payment-header-container {
      padding: 25px 20px 12px;
    }

    .register-container {
      ion-img {
        max-width: 105px;
        margin: 0 auto;
        margin-bottom: 24px;
      }

      h2 {
        font-size: 24px;
        letter-spacing: 0;
        color: var(--ion-color-warning-contrast);
        font-weight: 300;

        span {
          font-weight: 600;
        }
      }

      .page-heading {
        font-family: "<PERSON>", sans-serif;
        font-optical-sizing: auto;
        font-weight: 700;
        font-size: 31px;
      }

      .page-heading-title {
        font-size: 12px;
        margin-top: 5px !important;
      }
    }

    .form-container {
      height: 70vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    ion-input[disabled] {
      opacity: 0.5;
      pointer-events: none;
    }

    .header-container {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      &.package-selection-header {
        align-items: flex-end;
      }

      &.request-form-container {
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
        }

        .page-heading {
          font-size: 19px;
          font-weight: bold;
          margin-top: 10px;
        }

        .close-button {
          margin: 5px;
          font-size: 24px;
        }
      }

      .icon-step-container {
        display: flex;
        align-items: center;
        border-radius: 8px;
        width: 100%;
        gap: 5px;
        margin-top: 8px;

        .icon-right {
          width: 25px;
          height: 25px;
          background-color: #cfd6e5;
          border: 1px solid #bcc3d3;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 4px;
        }

        .step-indicator-container {
          display: flex;
          align-items: center;
          margin-left: 5px;
          justify-content: space-between;
          width: 100%;
          font-size: 14px;

          .step-indicator {
            display: flex;

            font-weight: 600;
            color: #333;

            .current-step {
              margin-right: 4px;
            }

            .total-steps {
              color: black;
              font-weight: bold;
            }
          }
        }
      }
    }

    .page-heading {
      font-size: 37px;
      font-weight: bold;
      font-family: "Oswald", sans-serif;
    }

    .page-sub-heading {
      color: #485358;
      font-size: 14px;
      font-weight: 400;
      text-align: justify;
      margin-top: 5px;
      margin-bottom: 5px;

      &.small-font-size {
        font-size: 13px;
      }
    }

    .onboarding-progress {
      width: 100%;
      background-color: #e6e6e6af;
      border-radius: 100px;
      height: 8px;
      margin-top: 5px;
      margin-bottom: 12px;

      .onboarding-progress-completed {
        background-color: #29385b;
        border-radius: 100px;
        height: 8px;
      }
    }

    .back-button-container {
      display: flex;
      align-items: center;
      margin: 0;

      &.align-back-skip-btn {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      }

      .skip-text {
        font-size: 17px;
        font-weight: bold;
        text-decoration: underline;

        &.co-applicant-skip {
          font-size: 13px;
        }
      }

      ion-icon {
        font-size: 1.8rem;
      }

      .back-icon {
        cursor: pointer;
        font-size: 24px;
        margin-right: 20px;
      }
    }

    .profile-picture-wrapper {
      position: relative;
      max-width: 150px;
      margin: 15px auto;

      .profile-picture-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 150px;
        height: 150px;
        border-radius: 50%;
        overflow: hidden;
        background-color: #f0f0f0;
        padding: 8px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;
        }
      }

      .action-icon {
        background-color: #29385b;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        position: absolute;
        z-index: 1;
        padding: 12px;
        bottom: 0;
        right: -4px;

        img {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .privacy-container {
    color: var(--ion-color-privacy-container);
    margin: 0px;
    font-size: 14px;
    text-align: center;

    &.pay-button-margin {
      margin: 5px 20px 20px 20px;
    }

    a {
      color: var(--ion-color-privacy-anchor-tag);
      font-weight: 500;
    }

    &.position-fixed {
      position: fixed;
      width: 100%;
      bottom: 0px;
      left: 0px;
      padding: 10px;
    }
  }
}

.register-action-button-container {
  position: absolute;
  width: 100%;
  bottom: 0px;
  left: 0px;
  text-align: right;
  padding: 20px;

  .dont-have-text {
    color: #29385b;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
  }

  .register-contiune-button {
    --border-radius: 8px;
  }
}

.resend-otp-text {
  color: #29385b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.package-card-container {
  max-height: calc(100vh - 234px);
  overflow-x: auto;

  &.max-height-package {
    max-height: calc(100vh - 150px) !important;
    padding: 10px !important;
  }



  .no-data-available {
    width: 100%;
    text-align: center;
    padding: 15px;
    font-size: 14px;
    font-weight: 500;
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    min-height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
  }


  .package-card {
    background: linear-gradient(210deg, var(--package-color) 16%, #374156 71%, #2e384d);
    border-radius: 14px;
    padding: 20px 25px;
    position: relative;
    margin-bottom: 10px;
    color: white;
    cursor: pointer;
    border: 4px solid #fff;

    &:last-child {
      margin-bottom: 0px;
    }

    &.selected {
      border-color: #b48429 !important;
    }

    .card-header {
      position: relative;

      .card-header-content {
        .card-title {
          font-size: 21px;
          color: white;
          font-family: "Oswald", sans-serif;
        }
      }
    }

    .package-item {
      padding: 16px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .package-selection-checkbox {
        margin-right: 10px;
        display: inline-block;
        vertical-align: middle;
      }

      .package-selection-label {
        width: 100%;
        font-size: 14px;
        font-weight: 500;
        display: inline-block;
        vertical-align: middle;
        margin-top: -3px;
        text-align: left;
      }
    }

    .card-body {
      .package-details {
        .package-detail-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0px;

          .detail-label {
            font-size: 14px;
            font-weight: 100;
          }

          .detail-value {
            color: white;
            font-size: 14px;
          }
        }
      }

      .separator-line {
        border: 0;
        border-top: 1px solid #ddd;
        margin: 16px 0px;
      }

      .info-text {
        color: white;
        font-size: 12px;
        font-weight: 100;
        line-height: 20px;

        strong {
          font-weight: bold;
        }
      }
    }

    .info-image {
      width: 23px;
      height: 23px;
      margin-right: 8px;
    }

    .additional-info {
      display: flex;
      align-items: center;
    }

    .offer-image {
      width: 35px;
      height: 35px;
      margin-right: 8px;
    }
  }


  .package-card-without-gradient {
    background-color: #fff;
    border-radius: 14px;
    padding: 8px 25px;
    position: relative;
    margin-bottom: 10px;
    color: #29385b;
    cursor: pointer;
    border: 2px solid #ddd;

    &:last-child {
      margin-bottom: 0px;
    }

    &.selected {
      border-color: #b48429 !important;
    }

    .card-header {
      position: relative;

      .card-header-content {
        .card-title {
          font-size: 21px;
          color: white;
          font-family: "Oswald", sans-serif;
        }

        .card-title-color {
          font-size: 21px;
          color: #29385b !important;
          font-family: "Oswald", sans-serif;
        }
      }
    }

    .package-item {
      padding: 16px;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .package-selection-checkbox {
        margin-right: 10px;
        display: inline-block;
        vertical-align: middle;
      }

      .package-selection-label {
        width: 100%;
        font-size: 14px;
        font-weight: 500;
        display: inline-block;
        vertical-align: middle;
        margin-top: -3px;
        text-align: left;
      }
    }

    .card-body {
      .package-details {
        .package-detail-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0px;

          .detail-label {
            font-size: 14px;
            font-weight: 500;
          }

          .detail-value {
            // color: white;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }

      .separator-line {
        border: 0;
        border-top: 1px solid #ddd;
        margin: 16px 0px;
      }

      .info-common {
        font-size: 12px;
        font-weight: 500;
        line-height: 20px;

        strong {
          font-weight: bold;
        }
      }

      .info-text {
        color: white;
      }

      .info-color {
        color: #29385b !important;
      }
    }

    .info-image {
      width: 23px;
      height: 23px;
      margin-right: 8px;
    }

    .additional-info {
      display: flex;
      align-items: center;
    }

    .offer-image {
      width: 35px;
      height: 35px;
      margin-right: 8px;
    }
  }

  .highlighted-payment {
    background-color: #29385b !important;
    // border: 2px solid #ddd !important;
    color: #f8f8f8 !important;
    border: 2px solid #fff !important;

  }
}

.ios .package-card-container {
  max-height: calc(100vh - 283px) !important;
}

.ios .package-card-container.max-height-package {
  max-height: calc(100vh - 196px) !important;
}

.payment-divider {
  height: 10px;
  background-color: #b48429 !important;
}

.payment-detail-container {
  .payment-body-container {
    padding: 10px 0px;
  }

  .package-card {
    border-radius: 8px;
    padding: 10px 0px;
    position: relative;
    cursor: pointer;
    border: 2px solid #b48429;
    max-width: 80%;
    margin: 0px auto;
    background: linear-gradient(210deg, var(--package-color) 16%, #374156 71%, #2e384d);
    margin-bottom: 10px;

    .card-header {
      .card-header-content {
        .card-title {
          font-size: 18px;
          color: #fff;
          font-family: "Oswald", sans-serif;
        }
      }
    }
  }

  .payment-option-tab-container {
    display: flex;
    justify-content: center;
    align-items: center;

    .payment-option-tab {
      padding: 15px;
      width: 50%;
      border: 1px solid #ddd;
      border-top: 0px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;

      &.selected {
        background-color: #29385b;
        color: #fff;
      }

      &:nth-child(even) {
        border-left: 0px;
      }
    }
  }

  .price-detail-section {
    .price-detail-title {
      background-color: #ddd;
      font-size: 14px;
      font-weight: 500;
      padding: 10px 16px;
    }

    .price-detail-rows {
      .price-detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 9px;

        &:last-child {
          border-top: 1px dashed #ddd;
        }

        .price-detail-label {
          font-size: 14px;
          font-weight: 500;
          width: 50%;
          text-align: left;
        }

        .price-detail-value {
          font-size: 14px;
          font-weight: 500;
          width: 50%;
          text-align: right;

          i-feather {
            margin-left: 4px;
          }

          span,
          i-feather {
            display: inline-block;
            vertical-align: middle;
          }

          .line-through {
            text-decoration: line-through;
          }
        }
      }
    }
  }

  .want-to-choose-other-option {
    font-size: 13px;
    font-weight: 500;
    color: #29385b;
  }

  .emi-items {
    .emi-item {
      padding: 16px;
      border-top: 1px solid #ddd;
      display: flex;
      justify-content: flex-start;
      align-items: center;

      &:last-child {
        border-bottom: 1px solid #ddd;
      }

      .emi-selection-checkbox {
        margin-right: 10px;
        display: inline-block;
        vertical-align: middle;
      }

      .emi-selection-label {
        width: 100%;
        font-size: 14px;
        font-weight: 500;
        display: inline-block;
        vertical-align: middle;
        margin-top: -3px;
        text-align: left;

        .emi-downpayment-text {
          font-size: 11px;
          color: #aaa;
        }
      }
    }
  }

  .emi-items-loading-container {
    min-height: 237px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .asf-amount-detail {
    max-width: 80%;
    margin: 0 auto;
    font-size: 12px;
    color: #788185;

    span {
      font-weight: 500;
    }
  }
}