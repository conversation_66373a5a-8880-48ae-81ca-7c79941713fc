// For information on how to create your own theme, please see:
// http://ionicframework.com/docs/theming/

/** Ionic CSS Variables **/
:root {
  /** primary **/
  --ion-color-primary: #29385B;
  --ion-color-primary-rgb: 56, 128, 255;
  --ion-color-primary-contrast: white;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #0d0d0d;
  --ion-color-primary-tint: #0d0d0d;
  --ion-color-box-shadow: #00000029;

  /** secondary **/
  --ion-color-secondary: #29385B;
  --ion-color-secondary-rgb: 61, 194, 255;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #36abe0;
  --ion-color-secondary-tint: #50c8ff;

  /** tertiary **/
  --ion-color-tertiary: #5260ff;
  --ion-color-tertiary-rgb: 82, 96, 255;
  --ion-color-tertiary-contrast: #ffffff;
  --ion-color-tertiary-contrast-rgb: 255, 255, 255;
  --ion-color-tertiary-shade: #4854e0;
  --ion-color-tertiary-tint: #6370ff;

  /** success **/
  --ion-color-success: #2dd36f;
  --ion-color-success-rgb: 45, 211, 111;
  --ion-color-success-contrast: #ffffff;
  --ion-color-success-contrast-rgb: 255, 255, 255;
  --ion-color-success-shade: #28ba62;
  --ion-color-success-tint: #42d77d;

  /** warning **/
  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb: 255, 196, 9;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;
  --ion-color-warning-dark-red: #dc3545;

  /** danger **/
  --ion-color-danger: #eb445a;
  --ion-color-danger-rgb: 235, 68, 90;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #cf3c4f;
  --ion-color-danger-tint: #ed576b;

  /** dark **/
  --ion-color-dark: #222428;
  --ion-color-dark-rgb: 34, 36, 40;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;

  /** medium **/
  --ion-color-medium: #92949c;
  --ion-color-medium-rgb: 146, 148, 156;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #808289;
  --ion-color-medium-tint: #9d9fa6;

  /** light **/
  --ion-color-light: #f4f5f8;
  --ion-color-light-rgb: 244, 245, 248;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;

  --ion-text-color: #0b1920;
  --ion-font-family: "Montserrat", sans-serif;
  --app-light-color: #ffffff;
  --app-background-color: #e5e5e5;

    /** other **/
    --package-color:#56ad5e;
   --ion-color-red:#fa4856;
   --ion-color-secondary-text:#485358;
   --ion-color-green-text:#07944f;
   --ion-color-custom-note:#858c8f;
   --ion-color-privacy-container: #565656;
   --ion-color-privacy-anchor-tag: #263941;
   --ion-color-input-border: #e7e8e9;
   --ion-color-heading : black;
   --ion-color-dashboard-heading: #151F37;
   --ion-color-pending-leads: #E5E7EB;
   --ion-color-assigned-leads: #D1D4DB;
   --ion-color-today-revenue: #BBC0CA;
   --ion-color-total-revenue: #29385B;
   --ion-color-pending-payments: #DBDEE3;
   --ion-color-customer-pending-approvals: #B3BCD2;
   --ion-color-toolbar: #F1F3F7;
   --ion-color-address:#061428;
   --ion-color-detail-tab:#B48429;
   --ion-color-email-label:#50636D;
   --ion-color-email-value:#231F1F;
   --ion-color-green-color:#07C526;
   --ion-color-status-background:#F9F9F9;
   --ion-color-site-main-heading: #f6be00;
   --ion-color-common:#555;
}
