/* Container for the customer dashboard */
.customer-dashboard-page {
  --background: linear-gradient(to bottom, #ffffff 40%, #dfe3ef 70%);
  height: 100%;

  &.ios {
    min-height: calc(100vh - (70px + var(--ion-safe-area-top))) !important;

    .scroll {
      --overflow: auto !important;

    }

    .guest-preview-padding-bottom {
      --padding-bottom: 95px !important;
    }
  }

  .offer-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

  }

  .non-refundable-text {
    font-size: 11px;
    color: #060607;
    font-weight: 500;
  }

  .max-width-icon {
    max-width: 16px;
  }

  .locate-link {
    white-space: nowrap;
    color: blue;
    text-decoration: underline;
    display: inline-block;
  }

  .offer-title {
    font-size: 1.2rem;
    font-weight: bold;
  }

  .call-locate-now {
    display: flex;
    margin: 3px 0px;
    font-size: 12px;
  }

  .invoice-booking-detail {
    display: flex;
    justify-content: space-between;
    font-weight: 550;
  }

  .reward-card {
    background-color: #fff;
    display: flex;
    box-shadow:
      rgba(0, 0, 0, 0.1) 0px 0px 5px 0px,
      rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
    border: 1px solid #eee;
    width: 100%;
    height: auto;
    // margin-bottom: 15px;
    border-radius: 10px;
    padding: 15px 5px 15px 5px;
  }

  .reward-text {
    font-size: 11px;
    color: #07090e;
    font-weight: 600;
  }

  .reward-icon {
    height: 24px;
  }

  .card-content {
    padding: 0px !important;
  }

  .referal-font {
    font-size: 14px !important;
  }

  .customer-header-section {
    height: 70px;
    border-bottom: 1px solid #ddd;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2),
      0 4px 5px 0 rgba(0, 0, 0, 0.14),
      0 1px 10px 0 rgba(0, 0, 0, 0.12) !important;

    .my-packages-header {
      // background-color: #f0f0f0;
      padding: 4px 8px;
      // border-radius: 10px;
      // font-size: 10px;
      // color: #0f0f0f;
      // width: 75px;
      // margin-top: 5px;
      // text-align: center;
      // font-weight: 700;
    }

    &.ios {
      min-height: calc(100vh - (70px + var(--ion-safe-area-top))) !important;
    }



    .customer-header-container {
      display: flex;
      align-items: center;
      padding: 12.5px 20px;
      background: white;

      .dashboard-heading {
        text-align: start;
        font-family: "Oswald", sans-serif;
        font-optical-sizing: auto;
        flex: 1;

        &.inner-page-heading {
          text-align: center;
          margin-top: -7px;
        }

        .dashboard-heading-text {
          font-size: 14px;
          color: #999;
          line-height: 11px;
        }

        .dashboard-subheading-text {
          font-size: 21px;
          color: #000;
          text-transform: capitalize;
          white-space: normal;
        }

      }
    }

    .dashboard-icon-container {
      display: flex;
      align-items: center;

      .dashboard-icon-background {
        background-color: #fff;
        width: 45px;
        height: 45px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        cursor: pointer;

        &:last-child {
          margin-right: 0px;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;

          &.notification-mark-all-image {
            width: 35px;
            height: 35px;
          }
        }

        .notification-count {
          position: absolute;
          top: 6px;
          right: 15px;
          background-color: red;
          color: white;
          font-size: 12px;
          font-weight: bold;
          width: 23px;
          height: 23px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 20px;
        }

      }
    }
  }

  .customer-body-section {
    height: 100vh;
    padding: 12px;
    overflow-y: auto;

    &.has-no-padding {
      padding: 0px;
    }

    &.no-bottom-tab {
      height: calc(100vh - 80px);
    }

    &.has-header-section {
      height: calc(100vh - 150px);

      &.no-bottom-tab {
        height: calc(100vh - 70px);
      }
    }

    &.has-header-section-change-profile {
      height: calc(100% - 70px);
    }

    &.campaign-background-scroll {
      background: #e1e7f3;
      height: calc(100vh - 1px);
      padding-bottom: 30px !important;

      .ios & {
        padding-bottom: 70px !important;
        margin-top: 20px;
      }
    }

    &.has-header-section-package-detail {
      height: calc(100% - 90px);
    }

    &.no-height {
      height: unset;
    }

    .no-bookings-container {
      //  display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      color: black;
      height: 100%;
      padding: 16px;

      &.no-height {
        height: unset;
      }

      .no-bookings-icon {
        font-size: 60px;
        color: black;
        margin-bottom: 20px;
      }

      .no-bookings-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 8px;
        color: black;
      }

      .no-bookings-message {
        font-size: 16px;
        color: black;
        font-weight: 500;
        margin-bottom: 20px;
      }
    }

    .slider-container {
      .slide {
        min-width: calc(100% - 40px);
        position: relative;
        transition: opacity 0.5s ease;
        height: 200px;

        .slide-inner-container {
          //  background-color: rgba(0, 0, 0, 0.5);
          border-radius: 15px;
          position: absolute;
          top: 0px;
          width: 100%;
          height: 100%;
        }

        img {
          width: 100%;
          //  border-radius: 15px;
          height: 210px;
        }

        .image-info {
          position: absolute;
          bottom: 30px;
          left: 27px;
          color: white;
          width: calc(100% - 56px);

          .image-info-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-direction: row;
          }

          .text-container {
            display: flex;
            flex-direction: column;
          }

          .image-text {
            margin-top: 5px;

            &.main-heading {
              font-size: 16px;
              font-weight: bold;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              width: 200px;
            }

            &.inner-heading {
              font-size: 11px;
              font-weight: 500;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              width: 200px;
            }
          }

          .rating-card {
            display: flex;
            align-items: center;
            background-color: var(--ion-color-primary-contrast);
            border-radius: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 5px 10px;
            font-weight: bold;
            font-size: 15px;
            color: black;

            .star-icon {
              width: 20px;
              height: 20px;
              margin-right: 5px;
            }
          }
        }
      }
    }

    .balance-card {
      position: relative;
      background: linear-gradient(to bottom, #ffffff 40%, #dfe3ef 70%);
      border-radius: 15px;
      border: 1px solid #0E1629;
      color: black;
      width: 90%;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      z-index: 10;
      margin: 8px auto;
      margin-top: -10px;

      .balance-card-inner-container {
        align-items: center;
        display: flex;
        justify-content: space-between;
      }

      .balance-card-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
      }

      .vertical-separator {
        width: 2px;
        height: 20px;
        background-color: #222;
        margin-top: 3px;
        margin-bottom: 4px;
      }

      .bal-text {
        font-size: 11px;
        font-weight: 500;
        display: flex;
        justify-content: center;
        gap: 4px;
        align-items: center;

        .bal-amount {
          font-size: 13px;
        }
      }

      .bal-info {
        font-size: 11px;
      }
    }

    .star-rating-outer {
      display: flex;
      align-items: center;
      height: 100%;
    }

    .star-rating {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f4f5f9;
      padding: 10px 15px;
      border-radius: 8px;
      width: fit-content;
    }

    .star-rating-content {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .star-icon {
      color: gold;
    }

    .rating-text {
      font-size: 13px;
      color: #333;
      margin-top: 5px;
      font-weight: bold;
    }

    .holiday-selection-container {
      width: 89%;
      margin: 0 auto 20px;

      .ios & {
        margin-bottom: 40px;
      }

      .membership-card-wrapper {
        position: relative;
        width: 100%;
        margin-top: 12px;

        .membership-package {
          width: 55%;
          text-align: center;
          font-size: 11px;
          font-weight: bold;
          color: white;
          z-index: 1;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
          //  background: linear-gradient(210deg, var(--package-color) 16%, #374156 71%, #2e384d);
          background: linear-gradient(210deg, var(--package-color, #374156) 16%, #374156 71%, #2e384d);

          padding: 4px;
          color: white;
          --f: .5em;
          --r: .8em;
          position: absolute;
          top: 20px;
          left: calc(-1 * var(--f));
          padding-inline: 2.8em;
          line-height: 2.0;
          border-bottom: var(--f) solid rgba(0, 0, 0, 0.3333333333);
          clip-path: polygon(calc(98% - var(--r)) 0, 0 0, 0 calc(98% - var(--f)), var(--f) 100%, var(--f) calc(100% - var(--f)),
              calc(100% - var(--r)) calc(100% - var(--f)), 100% calc(90% - var(--f) / 2));
          //  border-right: var(--r) solid #0000;          
        }

        .membership-holiday-selection-card {
          background: #1b397f;
          background-image: url("/assets/images/svg/background.svg");
          background-repeat: no-repeat;
          background-size: cover;
          color: white;
          border-radius: 25px;
          //  padding: 55px 21px 55px;
          padding: 55px 21px 70px;
          text-align: center;

          .membership-title {
            font-size: 14px;
            margin: 6px 0;
            text-align: right;

            &.holiday {
              margin-right: 14px;
            }
          }

          .offers-text {
            margin-top: 16px;
            font-size: 10px;
            text-align: right;
          }

          .click-here {
            text-decoration: underline;
            cursor: pointer;
          }
        }
      }

      .active-campaign-container {
        .active-campaign {
          //  border-radius: 15px;
          //  background-color: #ffffff;
          position: relative;

          &.no-campaign-background {
            background: unset;
          }
        }

        .slider-active-campaign {
          //  min-height: 210px; // Only applied if there are more than 1 campaigns
        }

        .active-campaign-card-header {
          position: relative;
        }

        .active-campaign-image-slide {
          width: 100%;
          height: 166px;
          border-top-left-radius: 20px;
          border-top-right-radius: 20px;
          margin-bottom: -4px;
          object-fit: cover;
        }

        .active-campaign-detail-container {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px;
          background: white;
          border-bottom-left-radius: 20px;
          border-bottom-right-radius: 20px;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .active-campaign-text-container {
          display: flex;
          gap: 3px;
          align-items: center;
        }

        .active-campaign-icon {
          font-size: 30px;
        }

        .active-campaign-text {
          font-size: 12px;
          font-weight: 600;
          margin-left: 3px;
          color: #29385B;
        }

        .active-campaign-vertical-separator {
          width: 1px;
          height: 17px;
          background-color: gray;
          margin: 3px;
        }

        .active-campaign-count {
          font-size: 16px;
          font-weight: bold;
          color: #29385B;
        }

        .additional-info {
          display: flex;
          justify-content: space-between;
          color: #29385B;

          .campaign-container {
            display: flex;
            align-items: center;
            gap: 5px;
          }

          .offer-text {
            font-size: 12px;
            text-align: justify;
            font-weight: 600;
          }

          strong {
            font-weight: 100;
          }

          .buy-voucher-container {
            background: #29385B;
            border-radius: 23px;
            display: flex;

            .voucher-icon {
              font-size: 18px;
              color: white;
            }
          }
        }

        .swiper-pagination {
          position: relative;
          justify-content: end;
          top: 30px;
          left: 78%;
          transform: translateX(-50%);
          display: flex;
          gap: 10px;
          z-index: 10;
          width: 114px !important;
        }
      }

      .other-holiday-selection-card {
        background: #212D47;
        background-size: cover;
        color: white;
        border-radius: 25px;
        padding: 64px 21px 64px;
        text-align: center;
        position: relative;
        overflow: hidden;

        .other-holiday-selection-card-container {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .book-your-hotel-container {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            font-size: 10px;
            font-weight: 400;
          }

          .book-hotel-text {
            font-size: 12px;
            font-weight: 600;
          }

          .exclusive-hotel-detail {
            color: gray;
          }
        }

        .book-hotel-icon {
          position: absolute;
          bottom: 48px;
          right: 10px;
          max-width: 110px;
          height: auto;
        }

        .offers-text {
          margin-top: 16px;
          font-size: 11px;
          text-align: right;
        }

        .click-here {
          cursor: pointer;
          color: gray;

          .strong-text {
            text-decoration: underline;
            font-weight: 500;
            color: white;
          }
        }
      }

      .package-subscribe-card {
        background: #C29133;
        color: white;
        border-radius: 25px;
        padding: 21px 21px 21px;
        text-align: center;
        position: relative;
        overflow: hidden;

        .package-subscribe-card-container {
          // display: flex;
          // justify-content: space-between;
          align-items: center;

          .package-subscribe-container {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            font-size: 9px;
            font-weight: 400;
            gap: 5px;
            width: 100%;

            @media (min-width:400px) {
              width: 58%;
            }

            @media (min-width:450px) {
              width: 62%;
            }
          }

          .subscribe-text {
            font-size: 13px;
            font-weight: 600;
          }

          .get-price-text {
            text-align: left;

            &.other-text {
              white-space: normal;
              word-break: break-word;
              display: inline-block;
              max-width: 160px;
            }
          }

          .subscribe-heading-text {
            font-size: 14px;
            text-align: left;
          }
        }

        .view-detail-button {
          --border-radius: 11px;
          height: 40px;
          margin: 14px 0px 0px 0px;
          max-width: 140px;
          width: 100%;

          .view-detail-button-container {
            display: flex;
            align-items: center;
            gap: 3px;
            white-space: nowrap;

            .view-detail-text {
              font-size: 8px;
              text-transform: uppercase;
              white-space: nowrap;
            }
          }
        }

        .package-subscribe-icon {
          position: absolute;
          bottom: 0;
          right: 0px;
          max-width: 165px;
          height: auto;

          @media (min-width:400px) {
            max-width: 115px;
          }
        }
      }

      .travel-card {
        background: white;
        color: black;
        border-radius: 25px;
        //  padding: 14px 21px 20px;
        padding: 29px 21px 33px;
        text-align: center;
        position: relative;
        overflow: hidden;

        .travel-card-container {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .travel-container {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            font-size: 11px;
            font-weight: 400;
            gap: 5px;
          }

          .travel-feature-container {
            display: flex;
            align-items: center;
            gap: 3px;

            .flight-icon {
              font-size: 20px;
            }

            .travel-text {
              font-size: 13px;
              font-weight: bold;
            }
          }

          .travel-working-text {
            font-size: 10px;
            text-align: left;
            white-space: normal;
            word-break: break-word;
            display: inline-block;
            max-width: 160px;
          }

          .travel-coming-text {
            font-size: 11px;
            font-weight: bold;
            color: #29385B;
          }
        }

        .travel-icon {
          position: absolute;
          bottom: 0;
          right: 0px;
          max-width: 165px;
          height: auto;
        }
      }

      // .other-holiday-selection-card {
      //   border: 3px dotted #29385B54;
      //   background-color: #ffffff;
      //   color: white;
      //   border-radius: 15px;
      //   padding: 14px;
      //   align-items: center;
      //   display: flex;
      //   justify-content: center;

      //   &.skeleton-less-padding {
      //     padding: 5px;
      //   }

      //   .service-icon {
      //     width: 24px;
      //     height: 24px;
      //     margin-right: 10px;
      //   }

      //   .service-text {
      //     font-size: 13px;
      //     font-weight: 600;
      //     color: #29385b;
      //   }
      // }
    }

    .search-bar {
      // padding: 15px 8px 15px 8px;
      // background-color: #f4f4f4;
      // border-radius: 6px;
      // display: flex;
      // align-items: center;
      // cursor: pointer;
      // min-height: 68px;
      // gap: 10px;
      padding: 10px 7px 12px 7px;
      background-color: #f4f4f4;
      border-radius: 6px;
      display: flex;
      align-items: center;
      cursor: pointer;
      min-height: 50px;
      color: black;
    }
  }

  &.ios {
    .customer-body-section {
      &.no-bottom-tab {
        height: calc(100vh - (80px + 25pt));
      }

      &.has-header-section {
        height: calc(100vh - (150px + 25pt));

        &.no-bottom-tab {
          height: calc(100vh - (70px + 25pt));
        }
      }

      &.has-header-section-change-profile {
        //  height: calc(100vh - 70px);
        height: calc(100% - (60px + 25pt));
      }
    }
  }

  &.my-notification-list {
    .customer-body-section {
      .notification-container {
        padding: 0px 12px 12px 12px;

        .no-bookings-container {
          //  display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          color: black;
          height: 100%;
          padding: 16px;

          .no-bookings-icon {
            font-size: 60px;
            color: black;
            margin-bottom: 20px;
          }

          .no-bookings-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
            color: black;
          }

          .no-bookings-message {
            font-size: 16px;
            color: black;
            font-weight: 500;
            margin-bottom: 20px;
          }
        }

        .unread-count-container {
          text-align: center;
          margin: 16px 0;
          padding: 12px;
          background-color: #f8f9fa;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .unread-count-text {
          font-size: 16px;
          color: #333;
        }

        .unread-count {
          font-size: 18px;
          font-weight: bold;
          color: #0078d4;
          margin-left: 5px;
        }

        .notification-card {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          padding: 11px 7px;
          border: 1px solid #ddd;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          background-color: #fff;
          max-width: 400px;
          margin: 10px auto;
          font-family: Arial, sans-serif;
          transition: background-color 0.3s ease;

          &.unread-notification {
            background-color: #e0f0ff; // Light blue for unread notifications
          }

          &.read-notification {
            background-color: white; // Yellow for read notifications
          }
        }

        .notification-image {
          flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 30px;
          height: 50px;
          margin-right: 8px;

          img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
          }
        }

        .notification-content {
          flex-grow: 1;
          //  margin-left: 8px;
        }

        .notification-heading {
          font-size: 14px;
          font-weight: bold;
          color: #333;
        }

        .notification-description {
          font-size: 11px;
          color: #666;
          margin-top: 4px;
          max-height: 110px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          transition: max-height 0.3sease-in-out;
        }

        .notification-time {
          font-size: 13px;
          color: #999;
          margin-bottom: 15px;
          white-space: nowrap;

          .date-text {
            margin-top: 6px;
          }

          .time-text {
            text-align: center;
            margin-top: 3px;
          }
        }
      }
    }
  }

  &.my-membership-page {
    .customer-body-section {

      // &.background-white {
      //   background-color: #fff;
      // }
      .my-membership-container {
        .new-holiday-selection-container {
          width: 94%;
          margin: 0 auto;
          height: calc(100vh - 100px);

          /* Adjust based on header/footer space */
          .membership-holiday-selection-card {
            background-color: #29385b;
            background-repeat: no-repeat;
            background-size: cover;
            //  background-image: url("/assets/images/svg/background.svg");
            color: white;
            border-radius: 25px;
            padding: 20px 21px 25px;
            text-align: center;
            margin: 4px 4px 15px 4px;

            .package-heading-container {
              display: flex;
              justify-content: space-between;
              padding: 16px 0px 0px 0px;

              &.no-membership-container {
                justify-content: center;
              }

              .membership-text {
                text-align: left;

                .membership-package-name {
                  font-family: "Oswald", sans-serif;
                  font-optical-sizing: auto;
                  font-size: 16px;
                  text-transform: uppercase;
                  width: 100%;

                  &.no-membership-text {
                    font-size: 20px;
                  }
                }

                .membership-puch-line-text {
                  font-size: 10px;
                  font-weight: 400;
                  font-style: normal;
                  width: 100%;
                }
              }

              .package-icon-container {
                display: flex;

                .package-icon {
                  font-size: 47px;
                }
              }
            }

            .new-overall-night-detail {
              padding: 12px 0px;
              font-weight: 500;
              text-align: left;
              font-size: 12px;
            }

            .new-membership-holiday-balance-container {
              display: flex;
              //   justify-content: center;
              align-items: center;
              gap: 10px;
              color: #29385b;

              .membership-holiday-balance-item {
                width: 70%;
                background: white;
                text-align: center;
                border-radius: 20px;
                padding: 10px 5px 13px 4px;

                &.bal-current-year {
                  background: #29385b;
                  color: white;
                  border: 2px solid rgba(240, 239, 238, 0.10);
                }

                &.current-year {
                  width: 23%;
                }

                .balance-count {
                  font-size: 26px;
                  font-weight: bold;
                  font-family: "Oswald", sans-serif;
                }

                .balance-title {
                  font-size: 9px;
                  font-weight: 400;
                }
              }
            }

            .new-membership-button-container {
              display: flex;
              justify-content: center;
              gap: 10px;
            }

            .new-membership-package-expiry {
              border-radius: 6px;
              text-align: center;

              .membership-package-text {
                font-size: 14px;
                font-weight: 500;
              }

              &.active {
                //  background-color: #28a745;
                //  border-color: #28a745;
                //  color: #fff;
              }

              &.expired {}
            }

            .offer-container {
              text-align: left;
              display: flex;
              align-items: center;
              margin-top: 10px;

              .redeem-btn {
                border-radius: 7px;
                padding: 7px;
                font-weight: 600;
                font-size: 10px;
                text-decoration: underline;
                cursor: pointer;
              }

              .redeem-btn-not-underline {
                font-weight: 600;
                font-size: 11px;
                color: white;
                background: #f0efee38;
                border-radius: 13px;
                padding: 12px;
              }

              .redeem-sent-btn {
                border-radius: 7px;
                padding: 3px 0;
                font-weight: 600;
                font-size: 11px;
                display: flex;
                flex-direction: column;

                .cancel-view-redeem-container {
                  display: flex;
                  justify-content: space-between;
                }

                .btn-body {
                  //  border-left: 2px solid !important;
                  margin-left: 30px;
                  float: right;
                }

                .btn-body-alt {
                  margin-left: 0px;
                }

                .view-detail-font-style {
                  text-decoration: underline;
                  font-size: 11px;
                  margin: 0px 0px 0px 4px;
                }

                .no-offer {
                  font-style: italic;
                  font-size: 14px;
                  display: flex;
                  align-items: center;
                }

                .cancel-request-btn {
                  text-decoration: underline;
                }
              }
            }
          }
        }
      }
    }
  }

  &.package-detail-page {
    .customer-body-section {
      .package-detail-container {
        .package-selection-container {
          width: 94%;
          margin: 0 auto;

          .package-detail-selection-card {
            background-color: #29385b;
            background-repeat: no-repeat;
            background-size: cover;
            color: white;
            border-radius: 25px;
            padding: 12px 20px 18px;
            text-align: center;
            margin: 4px 8px 13px 7px;
            display: flex;
            flex-direction: column;
            gap: 10px;

            .package-title {
              margin-top: 10px;
              font-size: 15px;
              font-weight: 600;
              text-transform: capitalize;
            }

            .package-rewards {
              font-size: 24px;
              font-weight: bold;
            }

            .package-desc {
              font-size: 11px;
              text-align: center;
              margin-top: 5px;
            }
          }

          .package-detail-container {
            background: white;
            border-radius: 25px;
            padding: 12px 20px 18px;
            text-align: center;
            margin: 4px 8px 13px 7px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

            .overall-allowed-night-detail {
              //  padding: 12px 0px;
              font-weight: 600;
              text-align: left;
              font-size: 13px;
            }

            .overall-allowed-nights-balance-container {
              display: flex;
              align-items: center;
              gap: 10px;
              color: white;

              .overall-allowed-night-balance-item {
                width: 70%;
                background: #29385B;
                text-align: center;
                border-radius: 20px;
                padding: 10px 5px 13px 4px;

                .balance-count {
                  font-size: 26px;
                  font-weight: bold;
                  font-family: "Oswald", sans-serif;
                }

                .balance-title {
                  font-size: 9px;
                  font-weight: 400;
                }
              }
            }

            .horizontal-separator-line {
              margin: 20px 0px 10px;
              opacity: 1.5;
              border: none;
              border-top: 1px solid #ccc;
            }

            .package-card-body {
              .package-details {
                .package-detail-item {
                  display: flex;
                  justify-content: space-between;
                  padding: 8px 0px;

                  .detail-label {
                    font-size: 13px;
                    font-weight: 400;
                  }

                  .detail-value {
                    font-size: 14px;
                    font-weight: 600;
                  }
                }

                .additional-info {
                  background: #C6C6C629;
                  border-radius: 25px;
                  padding: 10px 11px 14px 15px;
                  text-align: center;
                  margin: 7px 0px 0px 0px;
                  display: flex;
                  gap: 5px;

                  .offer-image {
                    width: 36px;
                  }

                  .info-text {
                    color: black;
                    font-size: 11px;
                    font-weight: 100;
                    line-height: 20px;
                    text-align: center;

                    strong {
                      font-weight: bold;
                    }
                  }
                }
              }

            }

          }

        }
      }
    }
  }

  &.booking-page {
    .hotel-search-container {

      .no-membership-package-container {
        border-radius: 14px;
        margin-bottom: 10px;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .membership-booking-package-container {
        background: linear-gradient(210deg, var(--package-color) 16%, #374156 71%, #2e384d);
        border-radius: 14px;
        padding: 12px;
        margin-bottom: 10px;
        color: white;
        cursor: pointer;
        border: 4px solid #fff;
        display: flex;
        align-items: center;
        justify-content: center;

        .booking-package-text {
          font-size: 15px;
          color: white;
          font-family: "Oswald", sans-serif;
        }
      }

      .hotel-custom-input-row {
        padding: 15px 8px 15px 8px;
        background-color: #f4f4f4;
        border-radius: 6px;
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-bottom: 10px;
        min-height: 68px;

        &.shadow-for-rating-section {
          box-shadow: 0 3px 2px rgba(0, 0, 0, 0.05);
        }

        .hotel-custom-input-icon {
          width: 39px;
        }

        .hotel-custom-input-text {
          .destination-city {
            color: #000;
            font-size: 15px;
            font-weight: 500;

            .destination-date {
              .destination-date-year {
                font-size: 12px;
                padding-left: 5px;
                font-weight: 400;
              }
            }

            .destination-night-counts {
              margin: 0px 15px;
              background-color: #fff;
              padding: 5px 7px;
              font-size: 12px;
              border-radius: 10px;

              span {
                display: inline-block;
                vertical-align: middle;
              }

              .night-text {
                font-size: 10px;
                margin-left: 3px;
              }
            }
          }

          .destination-country {
            font-size: 13px;
            color: #444;
            margin-top: 2px;
          }
        }
      }

      .preference-container {
        display: flex;
        overflow-x: auto;
        white-space: nowrap;
        gap: 8px;
      }

      .hotel-horizontal-separator-line {
        margin: 20px 0px 10px;
        opacity: 1.5;
        border: none;
        border-top: 1px solid #ccc
      }

      .price-range-section {
        //  padding: 16px;
        padding: 5px 10px 0px 15px;

        .section-title {
          font-weight: 600;
          font-size: 16px;
          margin-bottom: 12px;
        }

        .range-container {
          //  background: #f1f1f1;
          background: white;
          border-radius: 20px;
          padding: 20px;
          position: relative;

          .range-labels {
            display: flex;
            justify-content: space-between;
          }

          .range-text {
            text-align: center;
            background-color: #e7eefb;
            border: 2px solid #3880ff;
            border-radius: 70px;
            padding: 10px;
            font-size: 14px;
            font-weight: bold;
            color: black;
          }
        }
      }

      // Customize ion-range
      ion-range.custom-price-range {
        --bar-background: #d4d4d4;
        --bar-background-active: #3880ff;
        --knob-size: 35px;
        --pin-background: #3880ff;
        --pin-color: #fff;
        margin: 0px 4px 0px 4px;
        --bar-height: 4px;
        //  --knob-background: #ffffff; 

        &::part(pin) {
          width: 60px;
          height: 30px;
          font-size: 14px;
          border-radius: 15px;
          background: #3880ff;
          color: white;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  &.booking-hotel-listing-page {
    .card-container-hotel {
      //  margin-top: calc(100% - 250px);
      padding: 0px 5px 0px 5px;
    }

    .loading-card-container {
      padding: 0px 10px 12px 10px;
    }

    .hotel-card {
      border-radius: 20px;
      margin-bottom: 15px;
      background-color: #ffffff;
      box-shadow: rgba(0, 0, 0, 0.12) 0px 4px 16px;
      padding-bottom: 10px;
      position: relative;
      /* Ensure relative positioning for the ribbon container */

      &:last-child {
        margin-bottom: 0px;
      }

      .hotel-card-header {
        position: relative;

        .swiper-pagination {
          position: absolute;
          justify-content: center;
          bottom: 10px;
          display: flex;
          gap: 0px;
          z-index: 10;
          width: 114px !important;
        }

        .swiper-pagination-bullet {
          background-color: #f1f3f9;
          width: 10px;
          height: 10px;
        }

        .swiper-pagination-bullet-active {
          background-color: #fff;
        }

        .hotel-image-slide {
          img {
            width: 100%;
            height: 200px;
            border-radius: 20px 20px 0 0;
            margin-bottom: -4px;
            object-fit: cover;
          }
        }

        .rating-card {
          position: absolute;
          top: 10px;
          right: 10px;
          background-color: #29385b;
          border-radius: 5px;
          padding: 4px;
          display: flex;
          flex-direction: column;
          align-items: center;
          z-index: 9999;
        }
      }

      .hotel-card-grid {
        padding-bottom: 0px;
      }

      .hotel-card-body {
        padding: 5px 12px 12px 0px;

        .hotel-name {
          font-size: 13px;
          font-weight: 600;
          text-transform: capitalize;
        }

        .hotel-address {
          font-size: 9px;
          margin-top: 5px;
        }

        .hotel-hot-amenities {
          margin-top: 8px;
          display: flex;
          align-items: center;
          margin-left: -2px;

          .amenities-item {
            background-color: #fff;
            border: 1px solid #eee;
            padding: 5px 10px;
            border-radius: 15px;
            display: block;
            margin-right: 3px;
            padding-bottom: 6px;

            &:last-child {
              margin-right: 0px;
            }
          }
        }
      }

      .hotel-amenities {
        font-weight: bold;
        font-size: 11px;
        color: #2c3c64;
        margin-bottom: 5px;
      }

      .amenities {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
      }

      .amenities span {
        background-color: #f0f0f0;
        padding: 4px 8px;
        border-radius: 5px;
        font-size: 10px;
        color: #555;
      }

      .discounted-price-label {
        font-size: 8px;
        color: #555;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: right;
      }

      .discounted-price {
        font-size: 16px;
        font-weight: bold;
        color: #000;
        text-align: right;
      }

      .image-info {
        margin-top: 10px;
        text-align: left;
      }

      .image-text {
        margin: 2px 0;
        color: #2c3c64;

        &.main-heading {
          font-size: 17px;
          font-weight: bold;
        }

        &.inner-heading {
          font-size: 12px;
          font-weight: 600;
        }

        &.desc-heading {
          font-size: 12px;
        }
      }

      .price {
        float: right;
        margin-right: 5px;
      }

      .separator {
        width: 100%;
        height: 1px;
        background-color: var(--ion-color-toolbar);
        margin: 10px 0;
      }

      .amenities-heading {
        margin-top: 20px;
        color: #2c3c64;
        font-size: 14px;
        font-weight: 600;
      }

      .amenities-container {
        display: flex;
        align-items: flex-start;
        gap: 5px;
        margin-bottom: 15px;
      }

      .amenity-icon {
        //  width: 25px;
        margin-right: 5px;
      }

      .amenity-list {
        display: grid;
        grid-template-columns: repeat(2, auto);
        gap: 5px 10px;
        margin-top: 5px;
      }

      .amenity-item {
        font-size: 13px;
      }

      .buttons-container {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
      }

      .book-now,
      .view-details {
        flex: 1;
        margin-right: 10px;
        font-size: 12px;
      }

      .view-details {
        margin-right: 0;
        --background: #f1f3f9;
        --background-activated: #f1f3f9;
        --color: #29385b;
        --border-color: #29385b;
        border: 2px solid var(--ion-color-primary);
        border-radius: 15px;
        padding: 6px;
        --box-shadow: none;
      }
    }

    .ribbon-tag-container {
      position: absolute;
      top: 10px;
      z-index: 10;
    }

    .ribbon {
      --r: 0.8em;
      font-size: 14px;
      font-weight: 700;
      color: #fff;
      text-align: center;
      border-block: 0.2em solid #0000;
      padding-inline: 1em calc(var(--r) + 1em);
      line-height: 1.8;
      clip-path: polygon(100% 0,
          0 0,
          0 100%,
          100% 100%,
          100% calc(100% - 0.25em),
          calc(100% - var(--r)) 50%,
          100% 0.25em);
      //  background: linear-gradient(to right, #ff8c00, #ffc107);
      background: linear-gradient(to right, #228B22, #32CD32);
      width: fit-content;
    }
  }



  &.booking-hotel-detail-page {
    .hotel-detail-card-container {
      padding: 0px 16px;
      margin: 5px;



      .hotel-detail-card {
        background-color: #ffffff;
        border-radius: 20px; // Rounded corners
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        padding: 10px; // Internal spacing
        // max-width: 300px; // Limit the card width
        width: 100%;
        font-size: 14px;
        font-weight: bold;
        text-align: left;

        &.hotel-detail-card-ion-modal {
          background-color: unset;
          border-radius: 0px;
          box-shadow: unset;
          padding: 0px;
          margin-top: 20px;
        }

        .hotel-name {
          font-size: 15px;
          font-weight: 600;
          text-transform: capitalize;
        }

        .hotel-address {
          font-size: 12px;
          margin-top: 5px;
          font-weight: bold;
          display: inline;
        }

        .hotel-hot-amenities {
          margin-top: 8px;
          display: flex;
          align-items: center;
          margin-left: -2px;

          .amenities-item {
            background-color: #fff;
            border: 1px solid #eee;
            padding: 5px 10px;
            border-radius: 15px;
            display: block;
            margin-right: 3px;
            padding-bottom: 6px;

            &:last-child {
              margin-right: 0px;
            }
          }
        }

        .star-rating-outer {
          display: flex;
          align-items: center;
          height: 100%;
        }

        .star-rating {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #f4f5f9;
          padding: 10px 15px;
          border-radius: 8px;
          width: fit-content;
        }

        .star-rating-content {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .star-icon {
          color: gold;
        }

        .rating-text {
          font-size: 13px;
          color: #333;
          margin-top: 5px;
          font-weight: bold;
        }

        .hotel-desc {
          font-size: 12px;
          color: gray;
          line-height: 14px;
        }

        .hotel-amenities {
          font-weight: bold;
          font-size: 18px;
          color: #2c3c64;
          margin-bottom: 5px;
        }

        .amenities {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;

          .show-more {
            color: blue;
            cursor: pointer;
            text-decoration: underline;
            background: transparent;
          }
        }

        .free-txt-bg {
          background: #ebedf5;
          padding: 8px 20px;
          border-radius: 20px;
          font-size: 15px;
        }

        .free-text-btn {
          --border-radius: 11px;
          width: 75% !important;
          height: 38px;
          --background: linear-gradient(to right, #228B22, #32CD32);

          .free-text-btn-container {
            display: flex;
            align-items: center;
            gap: 5px;
          }

          .free-text {
            font-size: 15px;
            text-transform: uppercase;
          }
        }

        .amenities span {
          background-color: #f0f0f0;
          padding: 4px 8px;
          border-radius: 5px;
          font-size: 10px;
          color: #555;
        }

        .discounted-price-label {
          font-size: 8px;
          color: #555;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .discounted-price {
          font-size: 20px;
          font-weight: bold;
          color: #000;
          display: flex;
          flex-direction: column;

          .discounted-heading {
            font-size: 13px;
            font-weight: 600;
            color: #000;
          }
        }

        .discount-price-flex {
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .button-container {
          display: flex;
          justify-content: end;
          height: 100%;
        }

        .custom-button {
          --background: #2c3c64;
          --color: white;
          --border-radius: 4px;
          --box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
          font-size: 16px;
          --border-radius: 10px;
          width: 101px;
          height: 40px;
          font-size: 12px !important;
          text-transform: uppercase;
        }

        .custom-button-outlined {
          --background: #ffffff;
          --color: #2c3c64;
          --box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
          font-size: 16px;
          --border-radius: 12px;
          --border-color: #2c3c64;
          border: 1px solid;
          border-radius: 12px;
          // width: 101px;
          height: 40px;
          font-size: 12px !important;
          --padding-start: 15px;
          --padding-end: 14px;
        }
      }
    }

    .overlay-discount-body {
      background: #cdd7e9;
      position: fixed;
      bottom: 0px;
      z-index: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 0px 29px 0 28px;
      height: 90px;
      justify-content: center;

      .discounted-price-label {
        font-size: 8px;
        color: #555;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .discounted-price {
        font-size: 20px;
        font-weight: bold;
        color: #000;
        display: flex;
        flex-direction: column;

        .discounted-heading {
          font-size: 13px;
          font-weight: 600;
          color: #000;
        }
      }

      .discount-price-flex {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .button-container {
        display: flex;
        justify-content: end;
        height: 100%;
      }

      .custom-button {
        font-size: 12px;
        height: 45px;
        --background: #2c3c64;
        --color: white;
        --box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
        --border-radius: 10px;
        width: 120px;
      }
    }

    .hotel-rooms-outer {
      display: flex;
      flex-direction: column;
      position: relative;
      top: -14px;
      z-index: 1;
      margin-bottom: 65px;
    }

    .room-card-container {
      // display: flex;
      justify-content: center;
      align-items: center;
      padding: 6px 16px;

      .room-card {
        background-color: #ffffff;
        border-radius: 8px; // Rounded corners
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); // Subtle shadow for depth
        padding: 16px; // Internal spacing
        // max-width: 300px; // Limit the card width
        width: 100%;
        text-align: center;

        h2 {
          font-size: 18px;
          font-weight: bold;
          color: #29385b;
          margin-bottom: 8px;
        }

        p {
          font-size: 14px;
          color: #555555;
          margin: 0;
        }
      }
    }

    .hotel-card {
      border-radius: 20px;
      margin-bottom: 15px;
      background-color: #ffffff;
      box-shadow: rgba(0, 0, 0, 0.12) 0px 4px 16px;

      &:last-child {
        margin-bottom: 0px;
      }

      .hotel-card-header {
        position: relative;

        .swiper-pagination {
          position: absolute;
          justify-content: center;
          bottom: 20px;
          margin-left: 10px;
          display: flex;
          gap: 0px;
          z-index: 10;
          width: 114px !important;
        }

        .swiper-pagination-bullet {
          background-color: #f1f3f9;
          width: 10px;
          height: 10px;
        }

        .swiper-pagination-bullet-active {
          background-color: #fff;
        }

        .hotel-image-slide {
          img {
            width: 100%;
            height: 200px;
            margin-bottom: -4px;
            object-fit: cover;
          }
        }

        .rating-card {
          position: absolute;
          top: 10px;
          right: 10px;
          background-color: #29385b;
          border-radius: 5px;
          padding: 4px;
          display: flex;
          flex-direction: column;
          align-items: center;
          z-index: 9999;
        }
      }

      .hotel-card-grid {
        padding-bottom: 0px;
      }

      .hotel-card-body {
        padding: 5px 12px 12px 0px;

        .hotel-name {
          font-size: 13px;
          font-weight: 600;
          text-transform: capitalize;
        }

        .hotel-address {
          font-size: 9px;
          margin-top: 5px;
        }

        .hotel-hot-amenities {
          margin-top: 8px;
          display: flex;
          align-items: center;
          margin-left: -2px;

          .amenities-item {
            background-color: #fff;
            border: 1px solid #eee;
            padding: 5px 10px;
            border-radius: 15px;
            display: block;
            margin-right: 3px;
            padding-bottom: 6px;

            &:last-child {
              margin-right: 0px;
            }
          }
        }
      }

      .hotel-amenities {
        font-weight: bold;
        font-size: 11px;
        color: #2c3c64;
        margin-bottom: 5px;
        font-family: "Montserrat, Medium", sans-serif;
      }

      .amenities {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
      }

      .amenities span {
        background-color: #f0f0f0;
        padding: 4px 8px;
        border-radius: 5px;
        font-size: 10px;
        color: #555;
      }

      .discounted-price-label {
        font-size: 8px;
        color: #555;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: right;
      }

      .discounted-price {
        font-size: 16px;
        font-weight: bold;
        color: #000;
        text-align: right;
      }

      .image-info {
        margin-top: 10px;
        text-align: left;
      }

      .image-text {
        margin: 2px 0;
        color: #2c3c64;

        &.main-heading {
          font-size: 17px;
          font-weight: bold;
        }

        &.inner-heading {
          font-size: 12px;
          font-weight: 600;
        }

        &.desc-heading {
          font-size: 12px;
        }
      }

      .price {
        float: right;
        margin-right: 5px;
      }

      .separator {
        width: 100%;
        height: 1px;
        background-color: var(--ion-color-toolbar);
        margin: 10px 0;
      }

      .amenities-heading {
        margin-top: 20px;
        color: #2c3c64;
        font-size: 14px;
        font-weight: 600;
      }

      .amenities-container {
        display: flex;
        align-items: flex-start;
        gap: 5px;
        margin-bottom: 15px;
      }

      .amenity-icon {
        //  width: 25px;
        margin-right: 5px;
      }

      .amenity-list {
        display: grid;
        grid-template-columns: repeat(2, auto);
        gap: 5px 10px;
        margin-top: 5px;
      }

      .amenity-item {
        font-size: 13px;
      }

      .buttons-container {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
      }

      .book-now,
      .view-details {
        flex: 1;
        margin-right: 10px;
        font-size: 12px;
      }

      .view-details {
        margin-right: 0;
        --background: #f1f3f9;
        --background-activated: #f1f3f9;
        --color: #29385b;
        --border-color: #29385b;
        border: 2px solid var(--ion-color-primary);
        border-radius: 15px;
        padding: 6px;
        --box-shadow: none;
      }
    }
  }


  &.compaigns {
    .card-container-compaigns {
      margin-top: calc(100% - 350px);
      padding: 0px 5px 0px 5px;
    }

    .compaign-card {
      border-radius: 5px;
      margin-bottom: 15px;
      background-color: #ffffff;
      box-shadow: rgba(0, 0, 0, 0.12) 0px 4px 16px;
      padding-bottom: 10px;
      position: relative;

      &:last-child {
        margin-bottom: 0px;
      }

      .compaign-card-header {
        position: relative;

        .compaign-image-slide {
          text-align: center;

          img {
            width: 100%;
            height: 206px;
            margin-bottom: -4px;
            object-fit: cover;
          }
        }
      }

      .background-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 40vh;
        /* Adjust as needed */
        background-size: cover;
        background-position: center;
      }

      .background-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }



      .compaign-list-container {
        // display: flex;
        flex-direction: column;
        align-items: start;
        // margin-bottom: 20px;
        padding: 0 20px;

        .compaign-list-title {
          font-size: 16px;
          color: #29385B;
          font-weight: bold;
          text-transform: uppercase;
          text-align: center;
        }

        .campagin-type {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          font-size: 8px;
          font-weight: 600;
        }

        .fee-amount {
          text-align: right;
          font-size: 7px !important;
          color: #29385B;
          display: flex;
          justify-content: space-between;
          align-items: center;

          &.free-trip-amount-font {
            font-size: 16px !important;
            font-weight: 600 !important;
          }
        }

        .campaign-title {
          font-size: 13px;
          font-weight: 600;
          display: -webkit-box;
          -webkit-line-clamp: 16;
          -webkit-box-orient: vertical;
          overflow: auto;
          text-overflow: ellipsis;
          word-break: break-word;
        }

        .image-reward-type {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }


        .compaign-list-rewards {
          font-size: 15px;
          margin: 4px;
          text-align: center;
          color: #000000;
          font-weight: bold;
        }

        .text {
          max-height: 110px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          /* Show only 2 lines initially */
          -webkit-box-orient: vertical;
          transition: max-height 0.3s ease-in-out;
        }

        .text-container {
          max-width: 400px;
        }

        .text-container:has(.see-more.clicked) .text {
          max-height: none;
          /* Expand when clicked */
          -webkit-line-clamp: unset;
        }

        .see-more {
          color: blue;
          cursor: pointer;
          font-size: 13px;
        }

        .expanded {
          max-height: none;
          -webkit-line-clamp: unset;
        }

        .compaign-list-desc {
          font-size: 12px;
          text-align: center;
          margin: 5px 32px;
        }

        .campaign-description-reward {
          font-size: 10px;
          // margin: 5px 24px;
          font-weight: 300;
          // padding-left: 24px;
        }

        .separator-line-campgaigns {
          border: none;
          border-top: 1px solid #f5f6f9;
        }

        .description {
          text-align: start;
          display: flex;
          justify-content: flex-start;
        }
      }

    }



    .no-record {
      width: 100%;
      text-align: center;
      padding: 15px;
      font-size: 14px;
      font-weight: 500;
      background-color: #f8f8f8;
      border: 1px solid #ddd;
      min-height: 150px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
    }
  }



  &.compaign-detail {
    .compaign-card {
      border-radius: 5px;
      // margin-bottom: 15px;
      padding-bottom: 10px;
      position: relative;

      .compaign-card-header {
        position: relative;

        .compaign-image-slide {
          img {
            width: 100%;
            height: 180px;
            margin-bottom: -4px;
            object-fit: cover;
          }
        }
      }

      .compaign-detail-container {
        // display: flex;
        // flex-direction: column;
        align-items: center;

        .compaign-detail-title {
          font-size: 16px;
          color: #29385B;
          font-weight: bold;
          font-weight: 600;
        }

        .compaign-detail-rewards {
          font-size: 15px;
          margin: 4px;
          text-align: center;
          color: #000000;
          font-weight: bold;
        }

        .compaign-detail-desc {
          font-size: 10px;
          text-align: justify;
          font-weight: 400;
          line-height: 1.4;
          // text-align: center;
          // margin: 12px 32px;
        }

        .text {
          max-height: 110px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 4;
          /* Show only 2 lines initially */
          -webkit-box-orient: vertical;
          transition: max-height 0.3s ease-in-out;
          text-align: justify;
        }

        .text-container {
          max-width: 400px;
        }

        .image-reward-type {
          display: flex;
          // justify-content: space-between;
          font-size: 8px;
          font-weight: 600;
          align-items: center;
        }

        .padding-section {
          padding: 15px 20px 0px 20px;
        }

        .text-container:has(.see-more.clicked) .text {
          max-height: none;
          /* Expand when clicked */
          -webkit-line-clamp: unset;
        }

        .see-more {
          color: blue;
          cursor: pointer;
          font-size: 10px;
        }

        .expanded {
          max-height: none;
          -webkit-line-clamp: unset;
        }

        // .compaign-detail-desc span {
        //   display: inline; /* Keep text and button inline */
        // }
        .compaign-card-container {
          //  margin: 10px;
          padding: 0px 20px;
          display: flex;
          justify-content: space-between;
          flex-wrap: nowrap;
          width: 100%;
          border-top: 1px solid #f9f9fd;

          .compaign-card {
            border-radius: 0px !important;
            border-right: 1px solid #e7e7eb;
            padding: 13px 2px;
            display: flex;
            flex-direction: column;
            white-space: nowrap;
            align-items: center;
            text-align: center;
            flex: 1;

            &.start-date-padding {
              padding: 13px 4px;
            }
          }

          .compaign-max-participant {
            font-size: 8px;
            font-weight: 600;
          }

          .compaign-max-participant-container {
            display: flex;
            align-items: center;
            gap: 5px;

            .max-participant-icon {
              font-size: 16px;
            }

            .max-participant-text {
              font-size: 12px;
              font-weight: bold;
            }
          }



          .compaign-card:last-child {
            border-right: none;
            /* Removes the right border from the last item */
          }

          .user-limit-text {
            font-size: 9px;
            font-weight: 600;
          }

          .start-date-text {
            font-size: 12px;
            font-weight: bold;

            &.end-date {
              font-size: 12px;
            }
          }

          .day-time-text {
            font-size: 9px;
            font-weight: 450;
          }

        }

        .container-congratulations-section {
          padding: 15px;

          .congratulations-section {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
          }
        }


        .congratulations-container {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .congratulations-section {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 15px;

          .rupee-note-text {
            text-align: center;
            padding: 20px;
            font-size: 30px;
            font-weight: 550;
            color: #fff;

            .fee-info {
              font-size: 9px !important;
              font-weight: 100 !important;
            }
          }
        }

        .padding-congratulations-container {
          padding: 15px 20px !important;
        }

        .congratulations-content {
          position: absolute;
          display: flex;
          align-items: center;
          gap: 10px;
          /* Space between elements */
        }

        .congratulations-content img {
          position: absolute;
          left: 11px;
          top: 50%;
          transform: translateY(-50%);
          width: 40px;
          height: 41px;
        }

        //   .congratulations-content span {
        //     margin-left: 60px; /* Pushes text away from the image */
        //     font-size: 9px;
        //     line-height: 1.5;
        // }
        .text-content {
          line-height: 1.4;
          color: #333;
          white-space: wrap;
          flex: 1;
          /* Ensures text expands without pushing the trophy */
          margin: 0px 20px 0px 69px;
          /* Pushes text away from the image */
          font-size: 10px;
          text-align: justify;
        }



        .background-svg {
          width: 100%;
          display: block;
        }

        .overlay-svg {
          width: 40px;
          height: auto;
          flex-shrink: 0;
          /* Prevents trophy from resizing */
        }

        .eligibility {
          font-size: 11px;
          font-weight: 600;

          .no-font-weight {
            font-weight: 400 !important;
            font-size: 10px;
            text-align: justify;
          }
        }

        .eligibility-card {
          position: relative;
          padding: 0px 35px 0px 35px;
          overflow: hidden;

          .left-half-circle {
            position: absolute;
            z-index: 2;
            /* top: 0; */
            bottom: 0px;
            left: -24px;
          }

          .right-half-circle {
            position: absolute;
            z-index: 2;
            /* top: 0; */
            bottom: 0px;
            right: -24px;
          }

          .card-half-circle {
            height: 26px;
            width: 36px;
            display: block;
            background: #e1e7f3;
            border-radius: 30px;
          }
        }

      }

      .separator-line-campgaigns {
        border: none;
        border-top: 1px solid #f5f6f9;
      }
    }

    .grab-deal-button {
      --border-radius: 11px;
      height: 55px;
      margin: 2px 16px 14px;
      width: 92% !important;

      .grab-deal-container {
        display: flex;
        align-items: center;
        gap: 5px;

        .grab-deal-text {
          font-size: 12px;
          text-transform: uppercase;
        }
      }
    }

    .header-compaign-container {
      position: fixed;
      width: 100%;
      margin-top: 24px;
      padding-left: 9px;

      .customer-header-section {
        border-bottom: none;
        box-shadow: unset !important;

        .customer-header-container {
          background: unset !important;

          .dashboard-heading {
            .dashboard-subheading-text {
              display: none !important;
            }
          }

          .dashboard-icon-container {
            display: none;
          }

          .back-icon-container {
            .back-icon {
              stroke: #fff;
            }

          }
        }
      }
    }

    .font-cashback {
      font-size: 12px;
      color: #29385B;
      text-align: center;
    }

    .compaign-offer-end-conatiner {
      display: flex;
      justify-content: center;

      .offer-end-text {
        font-size: 10px;
      }
    }

    .compaign-wrapper {
      position: relative;
      // min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      // padding-bottom: 26px;
    }

    .compaign-card-header-new {
      position: fixed;
      // top: 0;
      left: 0;
      width: 100%;
      height: 40vh;
      /* Adjust height as needed */
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      // z-index: -1;

      /* Keeps it behind the card */
      .compaign-image-slide {
        text-align: center;

        img {
          width: 100%;
          height: 310px;
          margin-bottom: -4px;
          object-fit: cover;
        }
      }
    }

    .scrollable-card {
      position: relative;
      margin-top: 278px;
      /* Removes extra white space */
      width: 90%;
      max-width: 400px;
      background: white;
      // border-radius: 12px;
      // box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
      // padding: 20px;
      z-index: 1;
      transition: transform 0.3s ease-in-out;
      /* Smooth scrolling */
      // min-height: calc(100vh - 175px);
    }
  }

  .card-bottom-show-detail {
    width: 80%;
    margin: 0 auto;
    text-align: center;
    // background-color: #e9eefb;
    background: linear-gradient(210deg, var(--package-color) 16%, #374156 71%, #2e384d);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1); // Subtle shadow for depth
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    color: #f0f0f0;
    font-size: 11px;
  }

  .card-bottom-cancellation-policy {
    width: 80%;
    margin: 0 auto;
    text-align: center;
    background: #F1F3F7;
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1); // Subtle shadow for depth
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
    color: #2C3C64;
    font-size: 11px;
  }

  .package-design {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis !important;
    padding: 10px;
  }

  &.booking-hotel-add-guest-detail-page {
    .add-guest-card-container {
      // display: flex;
      justify-content: center;
      align-items: center;
      padding: 0px 16px;
      margin: 5px;

      .guest-type-section-container {
        //  padding: 5px;
        display: flex;
        justify-content: center;

        .guest-type-section-item {
          //  display: inline-block;
          display: flex;
          align-items: center;
          min-width: 140px;
          vertical-align: middle;
          border: 1px solid #2c3c64 !important;
          margin-right: 20px;
          background: white;
          border-radius: 10px;
          padding: 7px 7px 11px 7px;

          &.self-padding {
            padding: 7px 17px;
          }

          &:last-child {
            margin-right: 0px;
          }

          &.selected {
            background: aliceblue !important;
          }

          &.disabled {
            pointer-events: none;
            opacity: 0.5;
            /* Make it look disabled */
          }

          ion-checkbox,
          span {
            display: inline-block;
            vertical-align: middle;

            padding: 4px;
          }

          span {
            padding-left: 4px;
            font-size: 13px;
            font-weight: 500;
            //  margin-top: -3px;
            margin-top: 2px;
          }
        }
      }

      .star-rating-content {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .rating-text {
        font-size: 13px;
        color: #333;
        margin-top: 5px;
        font-weight: bold;
      }

      .room-card {
        margin-bottom: 10px;
      }

      .add-guest-card {
        background-color: #ffffff;
        border-radius: 20px; // Rounded corners
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); // Subtle shadow for depth
        padding: 3px 10px 10px 10px; // Internal spacing
        // max-width: 300px; // Limit the card width
        width: 100%;
        font-size: 14px;
        font-weight: bold;
        text-align: left;

        .ios & {
          margin-bottom: 20px;
        }

        .hotel-name {
          font-size: 14px;
          font-weight: 600;
          text-transform: capitalize;

          &.my-bookings-hotel-name {
            font-size: 16px;
            font-weight: bold;
          }
        }

        .hotel-address {
          font-size: 10px;
          margin-top: 5px;
        }

        .hotel-hot-amenities {
          margin-top: 8px;
          display: flex;
          align-items: center;
          margin-left: -2px;

          .amenities-item {
            background-color: #fff;
            border: 1px solid #eee;
            padding: 5px 10px;
            border-radius: 15px;
            display: block;
            margin-right: 3px;
            padding-bottom: 6px;

            &:last-child {
              margin-right: 0px;
            }
          }
        }

        .check-in-con {
          .label {
            font-size: 11px;
            color: #2c3c64;
            opacity: 50%;
          }

          .date {
            font-size: 16px;
          }

          .time {
            font-size: 11px;
            color: #2c3c64;
            opacity: 50%;
          }
        }

        .check-in-out-con {
          margin: 10px 0 0px 0px;

          &.my-bookings-check {
            align-items: center;
            margin: 0px;

            .my-booking-code {
              text-align: left;
            }

            .my-booking-text {
              font-size: 12px;
            }

            .my-booking-status {
              text-align: right;
            }
          }
        }

        .check-out-con {
          float: right;

          .label {
            font-size: 11px;
            color: #2c3c64;
            opacity: 50%;
          }

          .date {
            font-size: 16px;
          }

          .time {
            font-size: 11px;
            color: #2c3c64;
            opacity: 50%;
          }
        }

        .guest-and-rooms-con {
          margin: 10px 0 10px 0px;

          .label {
            font-size: 11px;
            color: #2c3c64;
            opacity: 50%;
          }

          .rooms {
            font-size: 16px;
          }
        }

        .time-duration-con {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .time-duration {
          opacity: 50%;
          background-color: #f0f0f0;
          padding: 4px 8px;
          border-radius: 10px;
          font-size: 10px;
          color: #555;
          width: 70px;
          margin-top: 5px;
          text-align: center;
        }

        .room-detail-con {
          margin: 10px 0 10px 0px;

          .title {
            font-size: 16px;
          }

          .size {
            font-size: 11px;
            color: #2c3c64;
            opacity: 50%;
          }
        }

        .room-facility-con {
          .title {
            font-size: 15px;
          }

          .desc {
            font-size: 11px;
            color: #2c3c64;
            opacity: 50%;
            margin: 5px 0px 5px 13px;
          }
        }

        .payment-con {
          .room-row {
            margin: 10px 0 10px 0px;
          }

          .detail {
            font-size: 13px;
            font-weight: 500;
          }

          .amount {
            font-size: 13px;
            color: #2c3c64;
            font-weight: bold;
            float: right;
          }

          .total-discount-con {
            .detail {
              font-size: 13px;
              font-weight: 500;
              color: gray;
            }

            .amount {
              font-size: 13px;
              color: gray;
              font-weight: bold;
              float: right;
            }
          }

          .total-amount-paid-con {
            margin-top: 18px;

            .detail {
              font-size: 12px;
              font-weight: 500;
              color: #29385b;
            }

            .amount {
              font-size: 13px;
              color: #29385b;
              font-weight: bold;
              float: right;
            }
          }
        }

        .custom-form {
          margin: 7px;

          ion-select::part(icon) {
            display: none;
          }

          .address-fields-container {
            display: flex;
            justify-content: space-between;
            gap: 10px;
          }

          .field-container {
            flex: 1;
            min-width: 0;
            margin-top: 10px;
          }

          .small-field {
            flex: 1;
            max-width: 100px;
          }

          .large-field {
            flex: 2;
          }

          .select-icon {
            // position: absolute;
            // right: 18px;
            // top: 48%;
            // transform: translateY(-50%);
            // z-index: 1;
            // height: 18px;
            // width: 18px;

            position: relative;
            margin-top: 15px;
            z-index: 1;
            width: 18px;
          }

          ion-select {
            padding-right: 40px;

            &.no-padding {
              padding-right: 0;
            }
          }
        }

        .hotel-desc {
          font-size: 10px;
          color: gray;
          line-height: 14px;
        }

        .hotel-amenities {
          font-weight: bold;
          font-size: 18px;
          color: #2c3c64;
          margin-bottom: 5px;
        }

        .amenities {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;

          .show-more {
            color: blue;
            cursor: pointer;
            text-decoration: underline;
            background: transparent;
          }
        }

        .amenities span {
          background-color: #f0f0f0;
          padding: 4px 8px;
          border-radius: 5px;
          font-size: 10px;
          color: #555;
        }

        .discounted-price-label {
          font-size: 8px;
          color: #555;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .discounted-heading {
          font-size: 13px;
          font-weight: 600;
          color: #000;
        }

        .discounted-price {
          font-size: 20px;
          font-weight: bold;
          color: #000;
        }

        .discount-price-flex {
          display: flex;
          flex-direction: column;
          justify-content: center;
        }

        .button-container {
          display: flex;
          justify-content: end;
          height: 100%;
        }

        .custom-button {
          --background: #2c3c64;
          --color: white;
          --border-radius: 4px;
          --box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
          font-size: 16px;
          --border-radius: 10px;
          width: 120px;
          height: 45px;
          font-size: 12px !important;
          text-transform: uppercase;
        }

        .custom-button-outlined {
          --background: #ffffff;
          --color: #2c3c64;
          --box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
          font-size: 16px;
          --border-radius: 12px;
          --border-color: #2c3c64;
          border: 1px solid;
          border-radius: 8px;
          height: 40px;
          font-size: 12px !important;
        }
      }
    }

    .overlay-discount-body {
      background: #cdd7e9;
      position: fixed;
      bottom: 0px;
      z-index: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 0px 4px 0px 28px;
      height: 90px;
      justify-content: center;
      left: 0;

      .ios & {
        //  padding: 0px 4px 25px 28px;
        //  padding: 0px 4px 10px 28px;
        height: 120px;
      }

      .discounted-price-label {
        font-size: 8px;
        color: #555;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .discounted-price {
        font-size: 20px;
        font-weight: bold;
        color: #000;
      }

      .discount-price-flex {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .button-container {
        display: flex;
        justify-content: end;
        height: 100%;
      }

      .custom-button {
        font-size: 12px;
        height: 45px;
        --background: #2c3c64;
        --color: white;
        --box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
        --border-radius: 10px;
        width: 120px;
      }
    }

    .hotel-rooms-outer {
      display: flex;
      flex-direction: column;
      position: relative;
      top: -65px;
      z-index: 1;
      margin-bottom: 22px;

      .ios & {
        margin-bottom: 5px;
      }

      &.my-bookings-outer {
        top: 0px;
        margin-bottom: 100px;
      }
    }

    ion-button {
      width: max-content;
      float: right;
      margin-right: 20px;

      &.my-bookings-ion-button {
        width: unset;
        float: unset;
        margin-right: 0px;
      }
    }

    .room-card-container {
      justify-content: center;
      align-items: center;
      // height: 100vh;
      padding: 6px 16px;

      .room-card {
        background-color: #ffffff; // White background for the card
        border-radius: 8px; // Rounded corners
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); // Subtle shadow for depth
        padding: 16px; // Internal spacing
        // max-width: 300px; // Limit the card width
        width: 100%; // Make it responsive
        text-align: center; // Center-align text

        h2 {
          font-size: 18px;
          font-weight: bold;
          color: #29385b; // Dark text color
          margin-bottom: 8px;
        }

        p {
          font-size: 14px;
          color: #555555; // Slightly lighter text for secondary info
          margin: 0;
        }
      }
    }

    .hotel-card {
      border-radius: 20px;
      margin-bottom: 15px;
      background-color: #ffffff;
      box-shadow: rgba(0, 0, 0, 0.12) 0px 4px 16px;

      &:last-child {
        margin-bottom: 0px;
      }

      .hotel-card-header {
        position: relative;

        .swiper-pagination-bullet {
          background-color: #f1f3f9;
          width: 10px;
          height: 10px;
        }

        .swiper-pagination-bullet-active {
          background-color: #fff;
        }

        .hotel-image-slide {
          img {
            width: 100%;
            height: 146px;
            margin-bottom: -4px;
            object-fit: cover;
          }
        }

        .rating-card {
          position: absolute;
          top: 10px;
          right: 10px;
          background-color: #29385b;
          border-radius: 5px;
          padding: 4px;
          display: flex;
          flex-direction: column;
          align-items: center;
          z-index: 9999;
        }
      }

      .hotel-card-grid {
        padding-bottom: 0px;
      }

      .hotel-card-body {
        padding: 5px 12px 12px 0px;

        .hotel-name {
          font-size: 13px;
          font-weight: 600;
          text-transform: capitalize;
        }

        .hotel-address {
          font-size: 9px;
          margin-top: 5px;
        }

        .hotel-hot-amenities {
          margin-top: 8px;
          display: flex;
          align-items: center;
          margin-left: -2px;

          .amenities-item {
            background-color: #fff;
            border: 1px solid #eee;
            padding: 5px 10px;
            border-radius: 15px;
            display: block;
            margin-right: 3px;
            padding-bottom: 6px;

            &:last-child {
              margin-right: 0px;
            }
          }
        }
      }

      .hotel-amenities {
        font-weight: bold;
        font-size: 11px;
        color: #2c3c64;
        margin-bottom: 5px;
        font-family: "Montserrat, Medium", sans-serif;
      }

      .amenities {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
      }

      .amenities span {
        background-color: #f0f0f0;
        padding: 4px 8px;
        border-radius: 5px;
        font-size: 10px;
        color: #555;
      }

      .discounted-price-label {
        font-size: 8px;
        color: #555;
        font-weight: bold;
        margin-bottom: 5px;
        text-align: right;
      }

      .discounted-price {
        font-size: 16px;
        font-weight: bold;
        color: #000;
        text-align: right;
      }

      .image-info {
        margin-top: 10px;
        text-align: left;
      }

      .image-text {
        margin: 2px 0;
        color: #2c3c64;

        &.main-heading {
          font-size: 17px;
          font-weight: bold;
        }

        &.inner-heading {
          font-size: 12px;
          font-weight: 600;
        }

        &.desc-heading {
          font-size: 12px;
        }
      }

      .price {
        float: right;
        margin-right: 5px;
      }

      .separator {
        width: 100%;
        height: 1px;
        background-color: var(--ion-color-toolbar);
        margin: 10px 0;
      }

      .amenities-heading {
        margin-top: 20px;
        color: #2c3c64;
        font-size: 14px;
        font-weight: 600;
      }

      .amenities-container {
        display: flex;
        align-items: flex-start;
        gap: 5px;
        margin-bottom: 15px;
      }

      .amenity-icon {
        //  width: 25px;
        margin-right: 5px;
      }

      .amenity-list {
        display: grid;
        grid-template-columns: repeat(2, auto);
        gap: 5px 10px;
        margin-top: 5px;
      }

      .amenity-item {
        font-size: 13px;
      }

      .buttons-container {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
      }

      .book-now,
      .view-details {
        flex: 1;
        margin-right: 10px;
        font-size: 12px;
      }

      .view-details {
        margin-right: 0;
        --background: #f1f3f9;
        --background-activated: #f1f3f9;
        --color: #29385b;
        --border-color: #29385b;
        border: 2px solid var(--ion-color-primary);
        border-radius: 15px;
        padding: 6px;
        --box-shadow: none;
      }
    }
  }

  &.my-booking-page {
    .my-booking-card-container {
      padding: 16px;

      .ios & {
        margin-bottom: 20px;
      }

      .my-booking-card {
        background-color: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

        &.no-box-shadow {
          box-shadow: none !important;
        }

        &.highlighted-payment {
          background-color: #f0f8ff !important; // Ensure it overrides default styles
          border: 2px solid #4caf50 !important; // Highlighting styles
        }

        &:last-child {
          margin-bottom: 0px;
        }

        .booking-information {
          margin-bottom: 5px;

          .booking-code {
            font-size: 13px;
            font-weight: 500;
          }

          .booking-status {
            font-size: 13px;
            font-weight: 500;
            padding: 5px 0px;
            font-size: 11px;
            text-align: center;
            border-radius: 8px;

            &.upcoming {
              color: #055160;
              background-color: #cff4fc;
              border-color: #b6effb;
            }

            &.completed {
              color: #0f5132;
              background-color: #d1e7dd;
              border-color: #badbcc;
            }

            &.cancalled {
              color: #842029;
              background-color: #f8d7da;
              border-color: #f5c2c7;
            }
          }
        }

        .hotel-name {
          font-size: 16px;
          font-weight: 500;
          text-transform: capitalize;
        }

        .hotel-address {
          font-size: 12px;
          margin-top: 5px;
          line-height: 19px;
        }

        .hotel-contact-number {
          font-size: 12px;
          margin-top: 5px;
          line-height: 19px;

          img,
          a {
            display: inline-block;
            vertical-align: middle;
          }

          img {
            margin-right: 5px;
          }
        }

        .booking-dates {
          margin-top: 7px;

          .check-in-date,
          .check-out-date {
            .label {
              font-size: 11px;
              color: #2c3c64;
              opacity: 50%;
              margin-bottom: 3px;
            }

            .date {
              font-size: 14px;
              font-weight: 500;
              color: #2c3c64;
            }
          }

          .check-out-date {
            text-align: right;
          }

          .time-duration-con {
            display: flex;
            flex-direction: column;
            align-items: center;

            .time-duration {
              background-color: #f0f0f0;
              padding: 4px 8px;
              border-radius: 10px;
              font-size: 10px;
              color: #2e384d;
              width: 70px;
              margin-top: 5px;
              text-align: center;
            }
          }
        }

        .room-and-guest-info {
          margin-top: 5px;

          .label {
            font-size: 12px;
            color: #2c3c64;
            opacity: 50%;
          }

          .rooms {
            font-size: 14px;
            font-weight: 500;
            margin-top: 3px;
          }
        }
      }
    }

    .overlay-discount-body {
      background: #cdd7e9;
      position: fixed;
      bottom: 0px;
      z-index: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 0px 32px 0 28px;
      height: 90px;
      justify-content: center;
      left: 0;

      .discounted-price-label {
        font-size: 8px;
        color: #555;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .discounted-price {
        font-size: 20px;
        font-weight: bold;
        color: #000;
      }

      .discount-price-flex {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .button-container {
        display: flex;
        justify-content: end;
        height: 100%;
      }

      .custom-button {
        font-size: 12px;
        height: 45px;
        --background: #2c3c64;
        --color: white;
        --box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
        --border-radius: 10px;
        width: 120px;
      }
    }

    .room-card-container {
      // display: flex;
      justify-content: center;
      align-items: center;
      // height: 100vh;
      padding: 6px 16px;

      .room-card {
        background-color: #ffffff; // White background for the card
        border-radius: 8px; // Rounded corners
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); // Subtle shadow for depth
        padding: 16px; // Internal spacing
        // max-width: 300px; // Limit the card width
        width: 100%; // Make it responsive
        text-align: center; // Center-align text

        h2 {
          font-size: 18px;
          font-weight: bold;
          color: #29385b; // Dark text color
          margin-bottom: 8px;
        }

        p {
          font-size: 14px;
          color: #555555; // Slightly lighter text for secondary info
          margin: 0;
        }
      }
    }
  }

  &.my-basic-detail-page {
    .customer-body-section {
      .profile-details-container {
        //  background: linear-gradient(to bottom, #ffffff 40%, #f1f3f9 70%);
        padding: 8px 20px 30px;
        text-align: left;
        display: flex;
        flex-direction: column;

        .profile-picture-wrapper {
          position: relative;
          max-width: 120px;
          margin: 15px auto;
        }

        .profile-picture-container {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 120px;
          height: 120px;
          border-radius: 50%;
          overflow: hidden;
          background-color: #f0f0f0;
          padding: 8px;
        }

        .profile-picture-container img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 50%;
        }

        .basic-detail-heading {
          font-size: 17px;
          font-weight: bold;
          margin-bottom: 12px;
          margin-left: 5px;
          color: gray;

          &.other-detail-heading {
            margin-top: 12px;
          }
        }

        .member-heading {
          font-weight: bold;
          font-size: 16px;
          margin-bottom: 10px;
        }

        .profile-card {
          background-color: #fff;
          border-radius: 8px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          padding: 10px 10px 1px;

          &.no-emergency-card {
            padding: 14px 14px 10px;
          }

          .profile-fields {
            &.co-applicant-container {
              display: flex;
              justify-content: space-between;
              align-items: center;
            }
          }

          .form-field {
            margin-bottom: 12px;
            //  border-bottom: 1px solid #f1f1f1;
            padding-bottom: 5px;

            &.family-form-field {
              margin-bottom: 6px;
              border-bottom: 0px;
              padding-bottom: 0px;

              &.gender-relation-dob-container {
                display: flex;
                justify-content: space-between;
                margin-right: 3px;
              }
            }
          }

          .gender-relation-container {
            display: flex;
            justify-content: flex-start;
            margin-right: 10px;
          }

          .form-field label {
            display: block;
            font-size: 14px;
            color: #888;
            margin-bottom: 5px;
          }

          .form-field .field-value {
            font-size: 16px;
            color: #333;

            &.bold-value {
              font-weight: bold;
            }

            &.small-field-value {
              font-size: 13px;
            }
          }
        }

        .document-card {
          background-color: #fff;
          border-radius: 8px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          padding: 2px 14px 15px;
          //  margin: 20px 0;
          text-align: center;

          &.no-document {
            padding: 1px 14px 5px;
          }

          .card-title {
            font-size: 16px;
            text-align: start;
            color: #333;
            font-weight: bold;

            &.no-document-title {
              font-weight: unset;
              font-size: 13px;
            }
          }

          .other-document-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            padding: 0px;

            .other-document-image-container {
              display: flex;
              align-items: center;
            }
          }

          .image-container {
            display: flex;
            justify-content: flex-start;
            gap: 10px;
            margin-top: 15px;
          }

          .card-image {
            width: 100px;
            height: 70px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }
        }

      }
    }
  }

  &.guest-coupons {
    .my-coupon-container {

      .my-coupon-card {
        background: white;
        border-radius: 14px;
        padding: 4px 10px;
        position: relative;
        margin-bottom: 10px;
        color: black;
        cursor: pointer;
        border: 4px solid #fff;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        width: 100%;
        min-height: 66px !important;

        .coupon-container {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .coupon-text-container {
            display: flex;
            flex-direction: column;
            width: 72%;
            overflow: hidden;
            gap: 6px;
            // margin-right: 10px;
          }

          .coupon-text {
            margin-top: 3px;
            color: black;
            font-weight: bold;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-transform: uppercase;
          }

          .coupon-amount {
            font-size: 11px;
            font-weight: 400;
            color: black;
            white-space: normal;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            margin-top: 3px;
          }

          .apply-coupon-container {
            width: 22%;
            text-align: center;
            margin-bottom: 6px;

            .balance-icon {
              font-size: 30px;
              margin-top: 5px;
            }

            .apply-text-font {
              color: #C29133;
              font-weight: 700;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

.location-popup {
  ion-header {
    ion-toolbar {
      --background: #fff;

      ion-title {
        font-size: 15px !important;
        font-weight: 500 !important;
        --color: #252729;
        opacity: 1;
      }

      i-feather {
        --color: #252729;
        color: #252729;
      }
    }
  }

  ion-content {
    min-height: 93vh !important;
  }

  .google-places-container {
    margin-top: 15px;

    .google-places {
      .google-place {
        --padding-start: 16px;
        --padding-end: 16px;
        --padding-bottom: 0px;
        --padding-top: 0px;
        --inner-padding-start: 0px;
        --inner-padding-end: 0px;
        --inner-padding-top: 0px;
        --inner-padding-bottom: 0px;

        ion-label {
          padding-top: 15px;
          padding-bottom: 15px;
          margin: 0px;

          .city-name {
            font-size: 16px;
            font-weight: 500;
          }

          .country-name {
            font-size: 12px;
          }
        }
      }
    }
  }
}

.star-rating-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;

  &.star-rating-row-padding-for-modal {
    padding: 15px 20px 2px 20px;
  }

  .star-btn {
    flex: 1;
    text-align: center;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 10px;
    padding: 15px;
    font-size: 14px;
    font-weight: 500;
    color: #222;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    white-space: nowrap;

    &.star-padding-not-for-modal {
      padding: 12px 20px 10px 15px;
    }

    &.selected-star {
      background-color: #f8e061 !important;
      color: #0054e9 !important;
      font-weight: bold;
    }
  }
}

.adults-selection-popup {
  .adults-selection-rows {
    .adults-selection-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0px;
      }

      .adults-selection-text {
        font-size: 17px;
        font-weight: 500;

        .help-text {
          font-size: 12px;
          color: #888;
        }
      }

      .adults-selection-inner-row {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .adults-selection-select {
        &.width-105 {
          width: 105px;
        }

        &.width-76 {
          width: 76px;
        }

        ion-select {
          border: 1px solid #000;
          padding: 0px 16px;
          border-radius: 8px;
          min-height: 32px !important;
          min-width: 55px !important;
        }
      }
    }
  }

  .best-text {
    font-size: 12px;
    margin-bottom: 10px;
  }

  .age-of-child-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  .room-card {
    background-color: #f8f8f8;

    .room-heading {
      font-size: 16px;
      font-weight: 500;
      background-color: #ddd;
      padding: 10px 16px;
    }

    .room-body {
      padding: 16px;
    }
  }
}

.customer-dashboard-container {
  height: calc(100vh - 80px);

  .ios & {
    height: calc(100vh - 140px);
  }

  &.account-page-height {
    overflow: auto;
  }

  /* Profile Section */
  .profile-section {
    text-align: center;
    margin-bottom: 20px;

    .profile-picture-wrapper {
      position: relative;
      max-width: 120px;
      margin: 15px auto;
    }

    .profile-picture-container {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120px;
      height: 120px;
      border-radius: 50%;
      overflow: hidden;
      background-color: #f0f0f0;
      padding: 8px;
    }

    .profile-picture-container img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
    }

    .user-name {
      font-size: 24px;
      font-weight: bold;
      margin-top: 10px;
    }

    .phone-number {
      font-size: 14px;
      color: #29385b;
      margin-top: 5px;
    }
  }

  ion-card {
    margin: 12px;
    border-radius: 22px;
    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 2px solid #e1e4ee;
  }

  .separator-line {
    border: none;
    height: 1px;
    background-color: #e1e4ee;
    margin: 10px 0;
  }

  ion-list {
    padding: 0;
  }

  ion-item {
    margin: 7px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  ion-icon {
    height: 20px;
  }

  .my-campaign-icon {
    height: 25px;
  }

  .arrow-right {
    color: #000;
    font-size: 20px;
  }

  .profile-actions-card {
    background: #e1e4ee;
  }

  .other-options-card {
    background: #e1e4ee;
  }

  .privacy-container {
    margin-top: 20px;
    margin-left: 15px;
    margin-right: 15px;
    text-align: center;

    ion-button {
      --background: #29385b;
      --box-shadow: 0px 3px 6px var(--ion-color-box-shadow);
      --border-radius: 12px;
      min-height: 54px;
      margin-bottom: 16px;
    }
  }
}

.register-action-button-container {
  position: absolute;
  width: 100%;
  bottom: 0px;
  left: 0px;
  text-align: right;
  padding: 20px;

  &.text-capitalize {
    text-transform: uppercase;
  }

  .dont-have-text {
    color: #29385b;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
  }

  .register-contiune-button {
    --border-radius: 8px;
  }
}

.resend-otp-text {
  color: #29385b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.booking-page {
  z-index: 999999999;

  &.ios {
    // --padding-top: var(--ion-safe-area-top);
    min-height: calc(100vh - (70px + var(--ion-safe-area-top))) !important;
  }

  .custom-form {
    height: 90%;

    &.height-for-ion-scroll {
      height: calc(100% - 8px);
      overflow-y: auto;
    }

    &.height-for-my-booking-detail {
      height: calc(100% - 10px);
      overflow-y: auto;
    }

    &.isTab {
      height: calc(100vh + 90px);
      overflow: scroll;
    }

    &.ios {
      // --padding-top: var(--ion-safe-area-top);
      min-height: calc(100vh - (70px + var(--ion-safe-area-top))) !important;
    }
  }

  ion-textarea {
    height: 150px;

    textarea {
      height: 150px;
    }
  }



  .booking-page-container {
    z-index: 999999999;
    padding: 8px 20px 30px;
    text-align: left;
    display: flex;
    flex-direction: column;
    // background-color: white;
    min-height: 100%;

    &.info-card {
      margin-top: 2px;
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }

    .info-label {
      font-size: 15px;
      color: #2e384d;
      font-weight: 400;
    }

    .info-value {
      font-size: 16px;
      color: #333;
      font-weight: 600;
      text-transform: capitalize;

      &.info-room-normal {
        margin-left: auto;
        font-size: 15px;
        font-weight: 500;
        margin-right: 4px;
        text-align: right;
      }
    }

    &.hotel-listing {
      padding: 18px 20px 30px;
      //   margin-bottom: 50px;
    }

    &.ios {
      // --padding-top: var(--ion-safe-area-top);
      min-height: calc(100vh - (70px + var(--ion-safe-area-top))) !important;
    }

    &.max-height {
      max-height: 100% !important;
    }

    &.profile-detail-container {
      background: linear-gradient(to bottom, #ffffff 40%, #f1f3f9 70%);
      box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12) !important;
    }

    &.supportList-padding {
      box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12) !important;
    }



    .form-container {
      min-height: 250px;

      //start guest form //
      .room-heading-guest {
        font-weight: bold;
        margin-top: 20px;
        font-size: 16px;
      }

      .guest-heading {
        font-weight: 600;
      }

      //end guest form //

      //start guest preview details//
      .booking-info-container {
        margin-top: 20px;

        &.no-margin {
          margin-top: 0px;
        }

        .booking-reference {
          font-size: 16px;
          font-weight: bold;
          color: #29385b;
        }

        .dates-container {
          display: flex;
          justify-content: space-between;
          margin-top: 10px;
        }

        .check-in {
          font-size: 15px;
          color: #2e384d;

          &.check-new-font {
            font-size: 13px;
          }
        }

        .check-out {
          //  margin-left: 20px;
          font-size: 14px;
          margin-right: 8px;
        }

        .check-date {
          font-size: 14px;
          font-weight: 500;
        }

        .separator-line {
          margin: 11px 0;
          border: none;
          border-top: 1px solid #ccc;
        }

        .room-and-guest-info {
          margin-top: 5px;

          .label {
            font-size: 12px;
            color: #2c3c64;
            opacity: 50%;
          }

          .rooms {
            font-size: 14px;
            font-weight: 500;
            margin-top: 3px;
          }
        }

        .booking-date-status {
          display: flex;
          flex-direction: column;
          margin-top: 10px;

          .hotel-address {
            font-size: 13px;
            margin-bottom: 5px;
          }

          &.room-main-guest {
            font-weight: bold;
            font-size: 16px;
          }
        }

        .date-status-item {
          display: flex;
          justify-content: space-between;
          width: 100%;
          margin-top: 7px;
        }

        .date-status-item strong {
          margin-right: 10px;
          font-size: 15px;
          color: #2e384d;
          font-weight: 400;

          &.subject-heading {
            font-size: 16px;
            font-weight: 600;
            margin-right: 0px;
          }

          &.subject-desc {
            font-size: 10px;
            font-weight: 500;
            margin-right: 0px;
          }

          &.subject-category {
            font-size: 12px;
            font-weight: 500;
            margin-right: 0px;
            text-align: left;
            border: 1px solid #f1aeb5;
            border-radius: 18px;
            padding: 10px 15px;
            background: #f8d7da;
            color: #58151c;
          }
        }

        .date-status-item .booking-date,
        .date-status-item .status {
          // margin-left: auto;
          font-size: 16px;
          font-weight: 500;
          margin-right: 10px;
          text-align: right;

          &.set-column-right {
            flex: 3;
          }

          &.confirm-booking-status {
            font-weight: bold;
          }

          &.address {
            font-size: 13px;
            text-align: left;
            line-height: 21px;
          }
        }

        .hotel-description-card {
          background-color: white;
          border-radius: 25px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          padding: 12px 22px 10px 22px;
          margin-top: 15px;

          .hotel-description-title {
            font-size: 15px;
            margin-bottom: 10px;
            color: #333;
          }

          .hotel-description-content {
            font-size: 12px;
            color: #2c3c64;
            margin-top: 15px;
            margin-bottom: 10px;
          }
        }

        .info-card {
          margin-top: 10px;

          .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
          }

          .info-label {
            font-size: 15px;
            color: #2e384d;
            font-weight: 400;
          }

          .info-value {
            font-size: 16px;
            color: #333;
            font-weight: 600;
            text-transform: capitalize;

            &.info-room-normal {
              margin-left: auto;
              font-size: 15px;
              font-weight: 500;
              margin-right: 4px;
              text-align: right;
            }
          }
        }

        .room-details-guest {
          margin-top: 15px;
          padding: 17px;
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

          .room-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
          }

          .arrow-icon {
            margin-left: auto;
            background: #dfe3ef;
            padding: 7px;
            border-radius: 6px;
          }

          .room-icon {
            display: flex;
          }

          .room-main-section {
            padding: 5px 15px;
            background: #dfe3ef;
            border-radius: 20px;
            font-size: 16px;
          }

          .guest-details {
            margin-top: 5px;

            .guest-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 14px;
              font-weight: 400;
              margin-top: 15px;

              strong {
                margin-right: auto;
              }

              .title {
                margin-left: auto;
                font-weight: 500;
              }
            }
          }
        }
      }

      //end guest preview details//

      .text-capitalize {
        text-transform: uppercase;
      }

      .privacy-container {
        text-align: center;

        ion-button {
          --background: #29385b;
          --box-shadow: 0px 3px 6px var(--ion-color-box-shadow);
          --border-radius: 12px;
          min-height: 54px;
          margin-bottom: 16px;
          width: 100%;

          &.primary-button {
            margin-bottom: 0;
          }

          &.text-capital {
            text-transform: uppercase;
          }

          &.grey-background {
            --background: #999 !important;
            margin-top: 15px;
          }
        }

        &.booking-pay-now {
          display: flex;
          flex-direction: column;
          align-items: center;

          .booking-text {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
          }

          .convenience-text {
            font-size: 11px;
            color: #7f8186;
          }

          .booking-text span {
            margin-right: 10px;
            font-size: 14px;
            color: #2e384d;
            font-weight: 400;
          }

          .booking-text strong {
            margin-left: auto;
            font-size: 15px;
            font-weight: bold;
            margin-right: 10px;
            text-align: right;
          }
        }
      }

      .back-search-container {
        margin-top: 15px;
        text-align: center;

        a {
          font-weight: 500;
          font-size: 16px;
          text-decoration: underline;
        }
      }

      .book-now-button-container {
        text-align: left;
      }

      .book-now-button-container ion-button {
        --background: #29385b;
        --box-shadow: 0px 3px 6px var(--ion-color-box-shadow);
        height: 45px;
        //   --border-radius: 12px;

        &.primary-button {
          margin-bottom: 0;
        }
      }

      &.text-left {
        text-align: left !important;
      }

      .page-heading {
        margin-top: 5px;
        font-size: 24px;
        font-weight: bold;
        //  font-family: "PlusJakartaBold-Sans", "sans-serif";

        &.form-page-heading {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .close-icon {
            font-size: 24px;
            cursor: pointer;
            color: #000;
          }
        }

        &.package-found {
          font-size: 20px;
          font-weight: 600;
          color: #29385b;
        }
      }

      .page-sub-heading {
        color: #485358;
        font-size: 12px;
        font-weight: 400;
        margin-top: 5px;
        margin-bottom: 5px;

        &.margin-left-heading {
          font-size: 13px;
          margin-top: 50px;
          margin-left: 15px;
          color: black;
          display: flex;
          text-align: center;

          .strong-code {
            font-weight: bold;
            margin-left: 5px;
            margin-right: 5px;
          }
        }

        &.download-scan-heading {
          font-size: 12px;
          margin-left: 25px;
          color: black;
          display: flex;
          text-align: center;

          .download-strong-code {
            font-size: 12px;
            font-weight: bold;
            margin-right: 5px;
          }
        }

        &.setup-two-factor {
          font-size: 26px;
          //   margin-left: 25px;
          color: black;
          display: block;
          text-align: center;
        }

        &.booking-confirm {
          font-size: 24px;
          font-weight: bold;
        }

        &.variation-heading {
          font-size: 14px;
          margin-top: 15px;
          color: black;
          display: flex;
          text-align: center;
        }

        &.booking-variation-heading {
          font-size: 12px;
        }

        &.generated-text {
          font-size: 16px;
          font-weight: 600;
          color: black;
          margin-top: 25px;
          text-align: center;
        }

        &.customer-otp-text {
          margin-top: 35px;
          color: black;
          font-size: 16px;
          text-align: center;
        }

        &:last-child {
          margin-bottom: 0px;
        }
      }

      .success-container {
        text-align: center;
        font-size: 18px;
        font-weight: 500;
        color: #333;
        display: flex;
        flex-direction: column;
      }

      .earned-text {
        font-size: 17px;
      }

      .earned-my-cash {
        //  color: #28a745;
        color: #cda60ef2;
        font-weight: bold;
        font-size: 19px;
      }

      .earned-promo-cash {
        color: #007bff;
        font-weight: bold;
        font-size: 15px;
      }

      .toggle-container {
        display: flex;
        align-items: center;
        margin-top: 20px;
        justify-content: center;
      }

      .toggle-container span {
        margin-right: 10px;
        font-size: 16px;
      }

      .toggle-container ion-toggle {
        width: 40px;
        height: 24px;
      }

      ion-toggle::part(track) {
        /* overflow: visible; */
        height: 22px;
        width: 43px;
        --track-background: #00000029;
        --track-background-checked: #00000029;
      }

      .update-password-container {
        text-align: right;
        margin-top: 15px;

        &.request-file {
          text-align: center;
        }

        a {
          color: #29385b;
          font-size: 14px;
          font-weight: bold;
          text-decoration: underline;
        }
      }

      .header-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        &.request-form-container {
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
          }

          .page-heading {
            font-size: 19px;
            font-weight: bold;
            margin-top: 10px;
          }

          .close-button {
            margin: 5px;
            font-size: 24px;
          }
        }
      }

      .settings-heading-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .settings-heading {
        font-size: 21px;
        font-weight: 500;
        color: #000000;
        text-align: left;
        margin-left: 6px;
        margin-bottom: 7px;
        flex: 1;

        &.booking-heading {
          font-size: 19px;
          text-align: center;
          margin-left: 0;
        }
      }

      .skip-text {
        font-size: 17px;
        color: black;
        cursor: pointer;
        margin-right: 7px;
        font-weight: 600;
      }

      /* Hide the default select icon */
      ion-select::part(icon) {
        display: none;
      }

      .address-fields-container {
        display: flex;
        justify-content: space-between;
        gap: 10px;
      }

      .field-container {
        flex: 1;
        min-width: 0;
        margin-top: 10px;

        &.add-request {
          margin-top: 0px;
        }
      }

      .small-field {
        flex: 1;
        max-width: 100px;
      }

      .large-field {
        flex: 2;
      }

      .select-icon {
        // position: absolute;
        // right: 18px;
        // top: 48%;
        // transform: translateY(-50%);
        // z-index: 1;
        // height: 18px;
        // width: 18px;

        position: relative;
        margin-top: 15px;
        z-index: 1;
        width: 18px;
      }

      ion-select {
        padding-right: 40px;

        &.no-padding {
          padding-right: 0;
        }
      }
    }

    ion-select.no-click {
      pointer-events: none;
    }

    .toggle-password-icon {
      //  font-size: 1.8rem;
      position: absolute;
      right: 15px;
      top: 13px;
      z-index: 9999;
    }

    .document-items {
      position: relative;
      margin-top: 20px;
      display: flex;
      flex-direction: column;

      &.attachment-con {
        margin-top: 0px;
      }

      &.generate-key {
        margin-top: 5px;
      }

      .document-upload-container {
        border: 2px dotted #4c64a0;
        border-radius: 8px;
        padding: 22px;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        cursor: pointer;
        background-color: #f3f5f9;
        transition: background-color 0.3s ease;
        margin-left: 5px;
        margin-right: 5px;
        margin-top: 10px;
        position: relative;
        height: 150px;
        // overflow: hidden;

        &.generate-key-container {
          height: auto;
          background-color: #f6f3ee;
          border: 2px dotted #b48429;
          padding: 18px;
        }
      }

      .code-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }

      .code-text {
        font-size: 16px;
        color: #b48429;
        font-weight: 600;
        margin: 0;
        padding: 0;
        white-space: pre-wrap;
        word-wrap: break-word;
        text-align: left;
        overflow-wrap: break-word;
        max-width: 100%;
      }

      .copy-clipboard {
        cursor: pointer;
        font-size: 16px;
        color: black;
        font-weight: bold;
        margin: 5px;
        padding: 0;
      }

      .copy-clipboard-clicked {
        color: #4caf50;
      }

      .upload-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 10px;
      }

      .uploaded-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
        position: absolute;
        top: 0;
        left: 0;
      }

      .upload-text {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 1;
      }

      .upload-adhar {
        display: block;
        font-size: 16px;
        color: black;
        margin-bottom: 5px;
        font-weight: bold;
      }

      .delete-icon {
        position: absolute;
        top: 0px;
        right: -5px;
        background-color: #29385b;
        color: #fff;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 2;
        font-size: 16px;
      }

      .delete-icon img {
        width: 16px;
        height: 16px;
      }

      .document-upload-container.missing-image {
        border: 2px solid var(--ion-color-warning-dark-red);
        background-color: #f3f5f9;
      }

    }

    .image-container {
      display: flex;
      justify-content: center;
      margin-top: 30px;

      &.scan-image-container {
        margin-top: 5px;
      }

      .custom-image {
        width: 170px;
        height: 170px;
      }

      .otp-image {
        width: 160px;
        height: 160px;
      }

      .two-factor-image {
        width: 130px;
        height: 130px;
        margin-bottom: 20px;
      }
    }
  }
}

/* Add this to your global styles or component styles */
.cropper-modal {
  --width: 100%;
  --height: 100%;
  --max-width: 100%;
  --max-height: 100%;
  --padding: 0;
}

.compaign-card-bg {
  // position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url(/assets/images/svg/group_booking.svg);
  background-repeat: no-repeat;
  background-position: center center;
  min-height: 100px;
  background-size: contain;
  padding: 0 15px;
  margin: 0 15px;
  padding: 15px 20px !important;

  &.random-campaign-card-bg {
    padding: 0 15px !important;
  }

  .congratulations-content-money {
    display: flex;
    align-items: center;
    width: 88%;

    .text-content-money {
      line-height: 1.4;
      color: #333;
      width: calc(100% - 46px);
      font-size: 10px;
      margin-left: 5px;

      &.random-campaign-text {
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
}

.compaign-card-bg-trophy {
  // position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: url(/assets/images/svg/group.svg);
  background-repeat: no-repeat;
  background-position: center center;
  min-height: 100px;
  background-size: contain;
  padding: 0 15px;
  padding: 15px 20px !important;

  .congratulations-content-trophy {
    display: flex;
    align-items: center;
    width: 100%;
    height: 53px !important;

    .text-content-trophy {
      line-height: 1.4;
      color: #333;
      width: calc(100% - 46px);
      font-size: 10px;
      margin-left: 5px;
    }

  }

}