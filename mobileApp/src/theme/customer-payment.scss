/* Container for the customer dashboard */
.customer-payment-page {
  --background: linear-gradient(to bottom, #ffffff 40%, #f5f6f9 70%);
  height: 100%;

  .convenience-text {
    font-size: 11px;
    color: #07090e;
  }

  .reward-card-container {
    border-radius: 14px;
    width: 100%;
    padding: 0px 4px;
    position: relative;
    color: white;
    cursor: pointer;
  }

  .reward-card {
    gap: 5px;
    align-items: center;
  }

  .reward-text-container {
    display: flex;
    align-items: center;
  }

  .reward-text {
    font-size: 10px;
    color: #07090e;
    font-weight: 600;
    gap: 6px;

    @media (min-width:400px) {
      font-size: 11px;
    }

    @media (min-width:430px) {
      font-size: 12px;
    }
  }

  .reward-icon {
    height: 24px;
  }

  .coupon-banner {
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
    border: 1px solid #eee;
    width: 100%;
    height: auto;
    border-radius: 10px;
    align-items: center;
    padding: 13px 18px 12px 10px;

    &.selected-coupon-container {
      display: block;
    }

    .coupon-banner-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .coupon-icon {
      height: 25px;
      margin-right: 6px;

      &.coupon-svg-icon {
        font-size: 22px;
      }
    }

    .coupon-text {
      display: flex;
      flex-direction: column;
      flex: 1;
    }

    .unlock-coupon-text {
      font-size: 12px;
      color: #07090e;
      font-weight: bold;
    }

    .new-coupons {
      color: purple;
    }

    .explore-now-text {
      font-size: 12px;
      font-weight: 500;

      &.coupon-rewards-text {
        white-space: normal;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }

    .applied-text {
      font-size: 13px;
      margin-top: 3px;
      color: red;
      font-weight: 600;
    }

    .right-arrow-icon {
      font-size: 20px;
    }

    .remove-coupon-container {
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      border: 2px solid #eee;
      height: auto;
      border-radius: 10px;
      padding: 8px 5px 8px 5px;
      margin-left: 10px;
      margin-bottom: 5px;

      .remove-text {
        font-size: 15px;
        font-weight: 600;
      }
    }

    .view-all-coupons-container {
      display: flex;
      justify-content: center;
      align-items: center;

      .view-all-text {
        font-size: 15px;
        font-weight: 600;
      }
    }
  }

  .payment-status-items {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    background-color: transparent;
    position: relative;

    .payment-status-item {
      flex: 1;
      text-align: center;
      padding: 15px 0;
      font-size: 14px;
      font-weight: 500;
      color: #999;
      cursor: pointer;
      position: relative;
      transition: color 0.3s;
      background-color: #f8f8f8;
      border-bottom: 3px solid #ddd;
      text-transform: uppercase;

      &.active {
        color: #29385b;
        border-bottom: 3px solid #29385b;
      }
    }
  }

  .my-wallet-tabs-items-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 2px;
    background: linear-gradient(to bottom, #ffffff 40%, #E0E3E9 70%);

    .my-wallet-tabs-items {
      display: flex;
      background: white;
      border-radius: 23px;
      border: 3px solid white;

      .my-wallet-tab-item {
        width: 140px;
        text-align: center;
        padding: 12px 0;
        font-size: 13px;
        font-weight: 600;
        color: #29385b;
        cursor: pointer;
        position: relative;
        transition: color 0.3s;
        background-color: white;
        border-top: 3px solid white;
        border-bottom: 3px solid white;
        border-radius: 23px;
        text-transform: uppercase;

        &.active {
          color: white;
          border-top: 3px solid #29385b;
          border-bottom: 3px solid #29385b;
          background: #29385b;
        }
      }
    }
  }

  .customer-body-section {
    .payment-items {
      text-align: center;
      padding: 2px 14px;
      margin-bottom: 80px;

      &.no-margin-bottom {
        margin-bottom: 0px;
      }

      &.no-padding {
        padding: 0px;
      }

      .payment-item {
        width: 100%;
        min-height: 70px;
        background: #ffffff;
        box-shadow: 0px 0px 10px #46464647;
        border-left: 5px solid #fff;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        text-align: left;

        &.box-shadow {
          box-shadow: unset;
          //  border: 1px solid #eee;
        }

        &.suport-detail-no-border {
          border-left: 0px !important;
        }

        &.highlighted-payment {
          background-color: #f0f8ff !important; // Ensure it overrides default styles
          border: 2px solid #4caf50 !important; // Highlighting styles
        }

        .no-record {
          margin-left: 10px;
        }

        .payment-item-container {
          width: 100%;
          padding: 15px;
          border-radius: 5px;

          .payment-case {
            font-size: 10px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 8px;

            i-feather {
              display: inline-block;
              vertical-align: middle;
              height: 27px;
              width: 20px;
            }

            .payment-text {
              font-size: 11px;
              font-weight: 600;
              text-transform: capitalize;

              &.amount-heading {
                font-size: 17px;
              }

              &.payment-type {
                font-size: 13px;
                font-weight: 500;
                background: #c5d2f58c;
                border-radius: 5px;
                padding: 7px 12px;
                color: #5122e3;
              }
            }

            .status-report {
              color: #b50000;
              margin-left: 5px;
            }
          }
        }

        &:last-child {
          margin-bottom: 0px;
        }

        &.warring {
          border-left: 5px solid #29385b;
        }

        &.in-progress {
          border-left: 5px solid #b48429;
        }

        &.success {
          border-left: 5px solid #59aa46;
        }

        &.rejected {
          border-left: 5px solid red;
        }

        .edit-icon {
          cursor: pointer;
        }
      }
    }

    .chat-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      position: relative;
      padding: 10px;
    }

    .chat-messages {
      flex: 1;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      justify-content: right;
      margin-right: 15px;
      //  background-color: #f0f0f0;
    }

    .chat-messages-receiver {
      flex: 1;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      justify-content: left;
      margin-right: 15px;
    }

    .message {
      max-width: 70%;
      margin-bottom: 10px;
      //  padding: 10px;
      border-radius: 10px;
      font-size: 14px;
      word-wrap: break-word;
    }

    .sender {
      color: #fff;
      align-self: flex-end;
    }

    .sender-text {
      background-color: #0084ff;
      color: #fff;
      align-self: flex-end;
    }

    .receiver {
      color: #000;
      align-self: flex-start;
      border: 1px solid #ddd;
    }

    .receiver-text {
      background-color: #ffffff;
      color: #000;
      align-self: flex-start;
      border: 1px solid #ddd;
    }

    .chat-input-container {
      display: flex;
      align-items: center;
      padding: 6px;
      border-top: 1px solid #ccc;
      background-color: #fff;
      position: fixed;
      width: 100%;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .upload-icon,
    .send-icon {
      font-size: 24px;
      color: #888;
      cursor: pointer;
    }

    .chat-input {
      flex: 1;
      padding: 10px;
      margin: 0 10px;
      border: 1px solid #ccc;
      border-radius: 20px;
      font-size: 14px;
    }

    .image-grid {
      display: grid;
      //  grid-template-columns: repeat(3, 1fr);
      /* 3 images per row */
      gap: 10px;
      /* Space between images */
      //  padding: 10px;
    }

    .image-item {
      display: flex;
      height: 100%;
      align-items: center;
    }

    .image-item img {
      width: 75px;
      height: 75px;
      border-radius: 8px;
      // box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
  }

  .separator-line {
    border: 0;
    border-top: 1px solid #ddd;
    margin: 12px 0px;
    opacity: 0.5;
  }

  &.my-wallet-page {
    --background: linear-gradient(to bottom, #ffffff 40%, #E0E3E9 70%);

    .my-wallet-container {
      &.see-more-trans-container {
        padding: 0px 0px 15px;
      }

      .compaign-list-container {
        //  overflow-x: auto;
        //  white-space: nowrap;
      }

      // .compaign-list {
      //   display: flex;
      //   gap: 13px;
      //   flex-wrap: nowrap; // Ensure all items are in a single row
      // }

      .campaign-slider-container {
        width: 100%;
        overflow: hidden;
        position: relative;
      }

      swiper {
        width: 100%;
      }

      .swiper-wrapper {
        display: flex;
        flex-direction: row;
        //  gap: 10px;
      }

      .swiper-slide {
        display: flex;
        justify-content: center;
        flex: 0 0 auto;
        width: auto;
      }

      .swiper-pagination {
        position: absolute;
        justify-content: center;
        bottom: 20px;
        left: 46%;
        transform: translateX(-50%);
        display: flex;
        gap: 10px;
        z-index: 10;
        width: 114px !important;
      }

      .swiper-pagination-bullet {
        width: 12px;
        height: 12px;
        background-color: #ccc;
        border-radius: 50%;
        transition: background-color 0.3s;
      }

      .swiper-pagination-bullet-active {
        background-color: #29385B;
      }

      .swiper-pagination-bullet:hover {
        background-color: #B0B8C1;
      }

      // .swiper-slide-active {
      //   border: 2px solid #29385B; 
      //   box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2); 
      // }

      .my-wallet-card {
        background: #2e384d;
        border-radius: 14px;
        padding: 20px 20px;
        position: relative;
        margin-bottom: 6px;
        color: white;
        cursor: pointer;
        border: 4px solid #fff;

        &.gift-voucher-container {
          background: white;
          min-width: 320px;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          width: 100%;

          &.slider-wallet-campaign {
            min-height: 145px;
          }
        }

        &.less-padding {
          padding: 14px 15px;
        }

        &.booking-card-height {
          min-height: 120px !important;
        }

        .swiper-slide {
          border: 2px solid #29385B;
          // box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2); 
        }

        &.see-more-card {
          background: white;
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          cursor: pointer;
          border-radius: 14px;
          text-align: center;
          border: 4px solid #fff;
          margin: 10px auto;

          .see-more-campaigns {
            font-size: 16px;
            font-weight: bold;
            color: #29385B;
            text-transform: uppercase;
          }

          .right-arrow {
            font-size: 18px;
          }
        }

        .gift-voucher-bal-container {
          display: flex;
          flex-direction: column;
        }

        &.selected-slide {
          border: 2px solid #575655; // Highlight color for selected slide
        }

        .balance-container {
          display: flex;
          justify-content: space-between;
          //  align-items: center;

          .balance-text-container {
            display: flex;
            flex-direction: column;
            width: 100%;
            overflow: hidden;
            margin-right: 10px;
          }

          .balance-text {
            font-size: 13px;

            &.voucher-text {
              color: black;
              font-weight: bold;
              font-size: 14px;
              display: block;
              width: 100%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              text-transform: uppercase;
            }
          }

          .balance-amount {
            font-size: 27px;
            font-weight: 500;
            margin-top: 6px;

            &.voucher-desc {
              font-size: 11px;
              font-weight: 400;
              color: black;
              white-space: normal;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }

          .balance-icon-container {
            .balance-icon {
              font-size: 60px;
            }

            .balance-image {
              width: 80px;
              height: 65px;
              display: flex;
              align-items: center;

              .image {
                height: 60px;
              }
            }
          }
        }

        .additional-info {
          &.voucher-info {
            display: flex;
            justify-content: space-between;
            color: #29385B;
          }

          .compaign-container {
            display: flex;
            align-items: center;
            gap: 5px;
          }

          .offer-text {
            font-size: 11px;
            text-align: justify;
            font-weight: bold;

            &.voucher-offer-text {
              font-size: 12px;
            }
          }

          strong {
            font-weight: 100;
          }

          .buy-voucher-container {
            background: #29385B;
            border-radius: 23px;
            //  padding: 1px 1px 1px 1px;
            display: flex;

            .voucher-icon {
              font-size: 18px;
              color: white;
            }
          }

          .see-more-compaigns {
            font-size: 13px;
            font-weight: bold;
            //  text-decoration: underline;
          }
        }
      }

      .no-recent-trans-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 10px;

        .no-recent-trans {
          display: flex;
          align-items: center;
          flex-direction: column;

          .no-recent-trans-image {
            height: 70px;
          }

          .no-recent-trans-text {
            font-size: 13px;
            opacity: 0.5;
          }
        }

        .queries-container {
          display: flex;
          gap: 37px;

          .how-it-works-text {
            color: #29385B;
            font-size: 15px;
            font-weight: bold;
          }

          .vertical-separator {
            border: none;
            border-left: 2px solid #29385B !important;
            height: 35px;
            // margin: 0px;
            margin: -7px;
          }
        }
      }

      .transactions-container {
        display: flex;
        flex-direction: column;
        background: white;
        padding: 15px;
        border-radius: 23px;

        &.no-background {
          background: unset;
          padding: 15px 20px 0px 20px;
        }

        .transactions-text {
          font-size: 18px;
          font-weight: bold;
          margin: 10px;
        }

        .promo-cash-container {
          display: flex;
          flex-direction: column;
          background: white;
          padding: 15px 10px;
          border-top: 1px solid #ddd;

          &.see-more-transactions-container {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-top: unset;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border-radius: 23px;
            padding: 24px 22px;
          }

          &:nth-child(1) {
            border-top: unset;
            /* Removes the border for the first child */
          }

          .promo-applied {
            display: flex;
            gap: 5px;
            align-items: center;
            justify-content: space-between;

            .promo-id {
              font-size: 13px;
              font-weight: bold;
            }

            .promo-action-container {
              display: flex;
              gap: 5px;
              align-items: center;
            }

            .promo-icon {
              font-size: 15px;
            }

            .promo-text {
              color: #BC8421;
              font-size: 10px;
              font-weight: 600;
            }
          }

          .promo-id-amount-container {
            display: flex;
            justify-content: space-between;
            margin-top: 3px;
            align-items: center;

            .promo-date {
              font-size: 11px;
            }

            .promo-amount {
              font-size: 16px;
              font-weight: bold;
              //  color: #29385B;

              &.cancelled-amount {
                text-decoration: line-through;
                text-decoration-color: black;
                position: relative;

                &::before {
                  content: "";
                  position: absolute;
                  top: 50%;
                  left: -2px;
                  right: -2px;
                  height: 2px;
                  background-color: black;
                  transform: translateY(-50%);
                  z-index: 1;
                }

              }
            }
          }

          .promo-note {
            font-size: 11px;
            text-align: justify;
          }
        }

        .see-more-text {
          text-align: center;
          font-size: 14px;
          font-weight: bold;
          text-decoration: underline;
          color: #5353f8;
        }
      }
    }
  }

  &.select-payment-method-page {
    --background: linear-gradient(to bottom, #ffffff 40%, #E0E3E9 70%);

    .select-payment-method-container {
      .select-payment-method {

        .wallet-detail-container {
          display: flex;
          flex-direction: column;

          .total-amount-container {
            display: flex;
            justify-content: space-between;
            padding: 11px;

            .total-amount-card {
              display: flex;
              align-items: center;
            }

            .total-amount-heading {
              font-size: 15px;
            }

            .chevron-icon-up-down {
              font-size: 15px;
            }

            .amount-container {
              display: flex;
              flex-direction: column;

              .total-amount-text {
                font-size: 16px;
                font-weight: bold;
              }

              .gst-text {
                font-size: 12px;
              }
            }
          }

          .horizontal-separator-line {
            margin: 0px 8px 16px;
            opacity: 0.5;
            border: none;
            border-top: 1px solid #ccc;
          }

          .detail-container {
            display: flex;
            flex-direction: column;
            padding: 0px 16px 15px 16px;

            .detail-card {
              display: flex;
              justify-content: space-between;

              .status-heading {
                font-size: 15px;
              }

              .status-detail {
                font-size: 15px;
                font-weight: 600;
              }
            }
          }
        }

        .wallet-cash-payment {
          height: 85px;
          background: white;
          border-radius: 15px;
          padding: 10px;
          position: relative;
          margin-bottom: 10px;
          color: black;
          cursor: pointer;
          border: 2px solid #f3efef;

          &.active {
            border: 2px solid #707481;
            //  border: 2px solid #f3efef;
          }

          &.disabled {
            pointer-events: none;
            opacity: 0.5;
            /* Make it look disabled */
          }

          .wallet-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0px 0px 0px 8px;

            .checkbox-text-container {
              display: flex;
              flex-direction: column;
              gap: 5px;

              .checkbox-container {
                display: flex;
                gap: 7px;
                align-items: center;

                .cash-balance-container {
                  display: flex;
                  flex-direction: column;

                  .cash-text {
                    font-size: 17px;
                    font-weight: bold;
                  }

                  .promo-cash-bal-container {
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    gap: 5px;
                  }

                  .bal-heading {
                    font-size: 11px;
                    font-weight: 500;
                  }

                  .cash-balance {
                    font-size: 13px;
                    font-weight: 600;
                  }
                }
              }

              .additional-text-container {
                text-align: left;
                margin-left: 20px;

                .additional-text {
                  font-size: 11px;
                }
              }

              .promo-expire-container {
                display: flex;
                align-items: center;
                flex-direction: row;

                .promo-expire-text {
                  font-size: 10px;
                  font-weight: 500;
                  color: red;
                }
              }
            }

            .icon-container {
              display: flex;
              align-items: center;

              .icon-size {
                font-size: 55px;
              }
            }
          }
        }
      }
    }
  }

  &.how-it-works-page {
    .how-it-works-container {
      padding: 16px;

      .how-it-works-card {
        background: white;
        font-size: 13px;
        border-radius: 14px;
        padding: 5px 20px;
        border: 2px solid #F1F3F7;

        .how-it-works-info {
          .view-text {
            font-size: 12px;
          }

          .terms-text {
            font-weight: bold;
            text-decoration: underline;
          }
        }
      }
    }
  }

  &.faq-page {
    .faq-container {
      padding: 16px;

      .faq-card {
        background: white;
        font-size: 13px;
        border-radius: 14px;
        padding: 20px 20px;
        border: 2px solid #F1F3F7;

        .faq-question {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .question-text {
            font-size: 13px;
            font-weight: bold;
          }

          .chevron-icon {
            font-size: 18px;
          }
        }

        .faq-answer {
          font-size: 11px;
        }
      }
    }
  }

  ion-fab {
    margin-bottom: 80px;
    margin-right: 5px;

    &.less-margin {
      margin-bottom: 20px;
    }
  }

  .fab-filter-icon {
    width: 39px;
    height: 39px;
    margin-top: 5px;
  }
}

/* Hide the default select icon */
ion-select::part(icon) {
  display: none;
}

.select-icon {
  // position: absolute;
  // right: 18px;
  // top: 48%;
  // transform: translateY(-50%);
  // z-index: 1;
  // height: 18px;
  // width: 18px;

  position: relative;
  margin-top: 15px;
  z-index: 1;
  width: 18px;
}

.payment-type-section-container {
  padding: 10px 0px;

  .payment-type-section-item {
    display: inline-block;
    vertical-align: middle;
    margin-right: 15px;

    &:last-child {
      margin-right: 0px;
    }

    ion-checkbox,
    span {
      display: inline-block;
      vertical-align: middle;
    }

    span {
      padding-left: 4px;
      font-size: 13px;
      font-weight: 500;
      margin-top: -3px;
    }
  }
}

// Support Card Container //
.status-class {
  background-color: #fff7f7;
  border-color: #fff7f7 !important;

  &.no-bg-color {
    background-color: unset;
    border-color: unset;
  }

  &.status-success {
    background-color: #d8fdec !important;
    border-color: #d8fdec !important;
  }

  &.status-warring {
    background-color: #dde6fc;
    border-color: #dde6fc !important;
  }

  &.status-reject {
    background-color: #ecb7b7;
    border-color: #ecb7b7 !important;
  }

  &.status-in-progress {
    background-color: #fdf2d0 !important;
    border-color: #fdf2d0 !important;
  }

  &.disabled-state {
    pointer-events: none;
    opacity: 0.6; // Optional: show visually it's disabled
  }

  .ticket-status {
    border-left: 2px solid #e7051d;
    padding-left: 10px;
    padding-right: 10px;

    &.font-progress {
      font-size: 14px;
      font-weight: 600;
    }

    &.date {
      border-left: 0px solid #e7051d;
      padding-left: 0px;
      padding-right: 10px;
      font-size: 14px;
    }

    &.ticket-warring {
      color: #29385b;
      border-left: 2px solid #29385b;
      padding-right: 10px;
    }

    &.ticket-reject {
      color: red;
      border-left: 2px solid red;
      padding-right: 10px;
      margin-left: 10px;
    }

    &.ticket-in-progress {
      color: #b48429;
      border-left: 2px solid #b48429;

      &.progress-margin-left {
        margin-left: 10px;
      }
    }

    &.ticket-success {
      color: #59aa46;
      border-left: 2px solid #59aa46;

      &.success-margin-left {
        margin-left: 10px;
      }
    }

    &.ticket-category {
      color: #29385b;
      border-left: 2px solid #29385b;
    }
  }
}

.support-card-container {
  width: 100%;
  display: inline-block;

  .support-status {
    font-size: 12px;
    margin-bottom: 10px;

    .ticket-id {
      color: #666;
      min-width: 40px;
      display: inline-block;
      font-weight: 900;
    }

    .ticket-status {
      font-weight: bold;
    }
  }

  .support-subject-title {
    span {
      color: #000;
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 5px;
      display: block;
    }

    p {
      margin: 0px;
      font-size: 13px;
      line-height: 19px;
      font-weight: 400;
    }

    img {
      height: 70px;
      margin: 10px 0 0 0;
    }
  }

  .support-bottom-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    /* border-top: 1px solid #e7001854; */
    padding-top: 5px;

    .support-category {
      span {
        width: 100%;
        display: inline-block;
        font-size: 12px;
        color: #666;
        font-weight: 500;

        &:last-child {
          font-size: 13px;
          color: #000;
          font-weight: 600;
        }
      }
    }
  }

  .resolution-con {
    .title {
      font-size: 18px;
      font-weight: 600;
      margin: 5px 0;
      font-family: "Oswald", sans-serif;
    }

    .desc {
      font-size: 12px;
      margin-bottom: 20px;
    }

    .image-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      /* 3 images per row */
      gap: 10px;
      /* Space between images */
      padding: 10px;
    }

    .image-item {
      display: flex;
      height: 100%;
      align-items: center;
    }

    .image-item img {
      width: 100%;
      height: 90px;
      border-radius: 8px;
      /* Optional: Add rounded corners */
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      /* Optional: Add a shadow effect */
    }

  }
}

// start css for my payments and support ticket //
.payment-card-container {
  &.payment-card-ios {
    .ios & {
      margin-bottom: 20px;
    }
  }

  .payment-item-card {
    background-color: #fff;
    box-shadow:
      rgba(0, 0, 0, 0.1) 0px 0px 5px 0px,
      rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
    border: 1px solid #eee;
    width: 100%;
    border-radius: 10px;
    border-bottom-right-radius: 25px;
    border-bottom-left-radius: 25px;
    // margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0px;
    }

    &.highlighted-payment {
      background-color: #f0f8ff !important; // Ensure it overrides default styles
      border: 2px solid #4caf50 !important; // Highlighting styles
    }

    &.ion-modal-payment-item-card {
      background-color: #fff;
      box-shadow: unset;
      border: unset;
      border-radius: unset;
      border-bottom-right-radius: unset;
      border-bottom-left-radius: unset;
      display: block;
      margin-bottom: 0px;
    }

    .payment-item-container {
      padding: 15px;
      position: relative;

      &.ion-modal-payment-item-container {
        padding: 5px;
      }

      // new design start //
      .new-design-container {
        display: flex;
        align-items: center;
        gap: 8px;

        .icon-style {
          width: 42px;
          height: 42px;
          color: #333;
        }

        .icon-background {
          background-color: #f5f6f9;
          width: 45px;
          height: 45px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon-style {
            font-size: 24px;
            color: #333;
          }
        }

        .new-design-id {
          font-size: 14px;
          font-weight: 700;
          color: #333;
        }

        .new-design-date {
          font-size: 11px;
          color: #333;
          margin-bottom: 5px;

          &.expired-date {
            font-weight: bold;
            color: red;
          }
        }
      }


      .new-design-payment-container {
        margin-top: 5px;
        display: flex;
        //  justify-content: flex-start;
        justify-content: space-between;
        align-items: center;
        font-size: 15px;
        color: #333;
        // flex-direction: column;

        .amount-icon-container {
          display: flex;
          flex-direction: column;
          margin-top: 19px;
        }

        .new-design-amount-type {
          display: flex;
          justify-content: flex-start;
          align-items: center;

          .new-design-amount {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 18px;
            font-weight: 600;
            color: #333;
          }

          .toggle-icon {
            font-size: 20px;
            cursor: pointer;
          }

          .new-design-type {
            font-size: 14px;
            font-weight: 400;
            color: #777;
          }
        }

        .new-design-date {
          font-size: 13px;
          font-weight: 500;
          margin-top: 3px;
          color: #333;

          &.expired-date {
            font-weight: bold;
            color: red;
          }
        }

        .status-icon-container {
          display: flex;
          flex-direction: column;

          .status-info {
            margin-left: 5px;
            text-align: end;
          }

          .status-icon {
            width: 43px;
            height: 43px;
          }

          .status-label {
            font-weight: 600;
            font-size: 12px;
            text-align: center;
          }
        }

        .new-design-payment-by-card {
          font-size: 12px;
          color: #333;
        }
      }

      .payment-case {
        margin-bottom: 3px;
        font-size: 12px;
        color: #666;

        &.payment-date {
          margin-bottom: 8px;
        }

        &.ion-modal-payment-case {
          margin-bottom: 8px;
          display: flex;
          width: 100%;
          justify-content: space-between;
          font-size: 12px;
          font-weight: 500;
        }

        &:last-child {
          margin-bottom: 0;
        }

        span {
          font-size: 13px !important;
          font-weight: 600;
          color: #29385b !important;
        }



        .payment-text {
          &.due-date-expired {
            color: red !important;
            font-weight: bold;
          }

          &.payment-type {
            //   background-color: rgb(243 243 243);
            padding: 5px 3px;
            border-radius: 4px;
            border: 1px solid #29385b;
            color: #29385b;
            background-color: #2c3c641a;
            display: inline-block;
            min-width: 150px;
            text-align: center;

            &.no-border {
              border: none;
            }
          }
        }
      }

      .refund-text {
        font-size: 15px;
        text-align: center;
        padding-top: 5px;
      }

      .payment-status-container {
        position: absolute;
        right: 11px;
        text-align: center;
        /* top: 50%; */
        margin-top: 13px;

        &.no-top-margin {
          top: unset;
        }

        .status-info {
          display: contents;

          .status-icon {
            display: block;
            width: 100%;
            text-align: center;
            //  margin-bottom: 10px;

            .status-ion-icon {
              width: 48px;
              height: 48px;
            }
          }

          .status-label {
            //    color: #20BF30;
            font-weight: 600;
            font-size: 12px;
          }
        }
      }
    }

    .pay-now-button {
      min-height: 44px;
      --background: #ebedf5;
      --background-focused: #ebedf5;
      --background-activated: #ebedf5;
      margin-top: 2px;
      margin-left: 8px;
      margin-right: 8px;
      margin-bottom: 8px;

      div {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #2c3c64;
        font-weight: 500;
      }

      .pay-now-text {
        text-transform: uppercase;

        .ios & {
          font-size: 14px;
        }
      }
    }
  }
}

.partially-completed {
  color: grey;
  /* Green */
}

.status-complete {
  color: #20bf30;
  /* Green */
}

.status-close {
  color: #dc3545;
  /* Red */
}

.status-due {
  color: #d6991d;
  /* Yellow */
}

.status-overDue {
  color: red;
  /* red */
}

.status-default {
  color: #6c757d;
  /* Grey */
}

.status-refund {
  color: #29385b;
}

// css for my payment pay now ion-modal //

.choose-option-container {
  background-color: #f5f6f9;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #ddd;
  border-radius: 8px;

  .total-amount {
    font-size: 15px;
    font-weight: 600;
    color: #333;
  }

  .full-payment-container {
    display: flex;
    align-items: center;
    gap: 10px;

    .full-payment-text {
      font-size: 15px;
      font-weight: bold;
      color: #333;
    }

    .right-arrow-icon {
      font-size: 18px;
      color: #333;
    }
  }
}

.payment-body-section {
  padding: 16px;
  height: calc(100% - 51px);

  &.package-tab-section {
    padding: 16px 16px 0px 16px;
  }

  &.less-height {
    height: calc(100% - 0px);
  }

  &.less-height-add-guest {
    height: calc(100% - 15px);
  }

  &.less-height-hotel-detail {
    height: calc(100% - 18px);

    .ios & {
      height: calc(100% - 0px);
    }
  }

  .payment-body-ion-content {
    min-height: auto !important;
    --background: transparent !important;
  }

  // ion-content {
  //   &.ios {
  //     --overflow: auto !important;
  //   }
  // }
}

table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;

  .emi-status {
    font-weight: 600;
  }

  tbody td:first-child {
    font-weight: 600;
  }

  tbody td:nth-child(2) {
    font-weight: 500;
  }

  tbody td:nth-child(3) {
    font-weight: 500;
  }

  .table-container {
    // padding: 16px;
    overflow-x: auto;
  }


  th,
  td {
    text-align: center;
    padding: 12px;
    border-bottom: 1px solid #ddd;
    font-size: 14px;
  }

  /* Alternating row colors */
  tbody tr:nth-child(odd) {
    background-color: #f9f9f9;
    /* Light gray */
  }

  tbody tr:nth-child(even) {
    background-color: #ffffff;
    /* White */
  }
}