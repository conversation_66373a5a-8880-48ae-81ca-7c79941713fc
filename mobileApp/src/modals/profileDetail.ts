// Define the FileDetail class
export class FileDetail {
  fileName: string | null = null;
  mimeType: string | null = null;
  size: number | null = null;
  path: string | null = null;
  originalName: string | null = null;
}

export class DocumentDetail {
  documentName: string | null = null;
  type: string | null = null;
  subType: string | null = null;
  documentExpiry: string | null = null;
  fileDetail: FileDetail = new FileDetail();
}

// Define the BankDetail class
export class BankDetail {
  // bankName: string | null = null;
  // accountNumber: string | null = null;
  // ifscCode: string | null = null;
  // branch: string | null = null;
  // umrnNo: string | null = null;
}

// Define the FamilyDetail class
export class FamilyDetail {
  firstName: string | null = null;
  lastName: string | null = null;
  gender: string | null = null;
  email: string | null = null;
  phoneNumber: string | null = null;
  relation: string | null = null;
  dob: string | null = null;
  isEmergencyContact: string | null = null;
}

// Define the AddressDetail class
export class AddressDetail {
  addressLine1: string | null = null;
  addressLine2: string | null = null;
  addressLine3: string | null = null;
  city: string | null = null;
  state: string | null = null;
  country: string | null = null;
  pinCode: string | null = null;
  landMark: string | null = null;
  type: string | null = null;

}

export class CoApplicantDetail{
  firstName: string | null = null;
  lastName: string | null = null;
  relation: string | null = null;
  salutation: string | null = null;
}

// Define the UserProfileDetail class
export class UserProfileDetail {
  dob: string | null = null;
  aadhaarNumber: string | null = null;
  panCard: string | null = null;
  maritalStatus: string | null = null;
  weddingAnniversary: string | null = null;
  specialInterests: string | null = null;
  passportNumber: string | null = null;
  passportCountry: string | null = null;
  passportIssueDate: string | null = null;
  passportExpiryDate: string | null = null;
  emergencyContactNumber: string | null = null;
  emergencyContactName: string | null = null;
  emergencyEmail: string | null = null;
  familyDetail: FamilyDetail[] | null = null;
  documentDetail: DocumentDetail[] | null = null;
  bankDetail: BankDetail[] | null = null;
  coApplicantDetail: CoApplicantDetail = new CoApplicantDetail();
  addressDetail: Array<AddressDetail> = new Array<AddressDetail>();
  referredBy: string | null = null;
}

export class ProfileDetail {
  email: string | null = null;
  password: string | null = null;
  firstName: string | null = null;
  lastName: string | null = null;
  salutation: string | null = null;
  phoneNumber: string = "";
  profileImageUrl: string | null = null;
  countryCode: string | null = '+91';
  referralCode: string | null = null;
  isOpenProfileChangeRequest: boolean = false;
  userProfileDetail: UserProfileDetail = new UserProfileDetail();
  packageDetail!: PackageDetail;
  paymentDetail!: PaymentDetail;
  isOnBoardingComplete :boolean = false;

  isValidBasicRequest(form: any) {
    if (!this.firstName || this.firstName.trim() === '') {
      form.controls.firstName.setErrors({ invalid: true });
      return false;
    }
    if (!this.lastName || this.lastName.trim() === '') {
      form.controls.lastName.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

  isValidEmailRequest(form: any) {
    if (!this.email || this.email.trim() === '') {
      form.controls.firstName.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

  static fromResponse(data: any): ProfileDetail {
    const record = new ProfileDetail();
    record.email = data.email;
    record.password = data.password;
    record.firstName = data.firstName;
    record.lastName = data.lastName;
    record.salutation = data.salutation;
    record.phoneNumber = data.phoneNumber;
    record.profileImageUrl = data.profileImageUrl;
    record.countryCode = data.countryCode;
    record.userProfileDetail = data.userProfileDetail;
    record.packageDetail = data.packageDetail;
    record.paymentDetail = data.paymentDetail;
    record.referralCode = data.referralCode;
    return record;
  }
}

export class PackageDetail {
  id!: string;
  package: string | null = null;
  offerId: string | null = null;
  packagePrice: number | null = null;
  payableAmount: number | null = null;
  baseAmount: number | null = null;
  gstAmount: number | null = null;
  minDownPayment: number | null = null;
  downPayment: number | null = null;
  offerAmount: number | null = null;
  packageDetails: any;
  fullyPaidHolidayDetail!: FullyPaidHolidayDetail;
  packageId: string | null = null;
  packageName: string | null = null;
  totalCount: number | null = null;
  offerDetails: any;
  isSelected: boolean = false;
  subTotal: number | null = null;
  offerGstAmount: number | null = null;
  packageGstAmount: number | null = null;
  fphId: string | null = null;
  fphGstAmount: number | null = null;
  convenienceFee: number =0;
  convenienceFeeGst : number | null =null;
}

export class PaymentDetail {
  paymentType: string | null = null;
  modeOfPayment: string | null = null;
  downpayment: string | null = null; // Add this line
  emiTenure: string | null = null; // Add this line
}


export class FullyPaidHolidayDetail {
  id!: string;
  slug!: string;
  updatedOn!: string;
  isActive: boolean = false;
  title: string | null = null;
  description: string | null = null;
  rating: string | null = null;
  type: string | null = null;
  location: string | null = null;
  forbcorpPrice: number | null = null;
  basePrice: number | null = null;
  gstRate: number | null = null;
  isGstIncluded: boolean = false;
  status: string | null = null;
  actualPrice: number | null = null;
  gstAmount: number | null = null;
  actualAmount: number | null = null;
  totalAmount: number | null = null;
  totalSoldFPHCount: number | null = null;
  totalRedeemedFPHCount: number | null = null;
  numberOfAdults: number | null = null;
  numberOfChildren: number | null = null;
}
