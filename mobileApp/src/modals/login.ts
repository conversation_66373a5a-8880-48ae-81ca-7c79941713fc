
export class Login {
  name?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  userName!: string;
  password?: string;
  newPassword?: string;
  confirmPassword?: string;
  oldPassword?: string;
  uniqueCode?: string;
  countryCode?:string;

  isValidLoginRequest(form: any) {
    if (!this.userName || this.userName.trim() === '') {
      form.controls.username.setErrors({ invalid: true });
      return false;
    }
    // if (!this.password || this.password.trim() === '') {
    //   form.controls.password.setErrors({ invalid: true });
    //   return false;
    // }
    return true;
  }

  isValidLoginRequestForMobile(form: any) {
    if (!this.userName || this.userName.trim() === '') {
      form.controls.username.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

  forRequest() {
    this.email = this.email?.trim();
    this.password = this.password?.trim();
    return this;
  }

  // forForgotPasswordRequest() {
  //   this.email = this.email?.trim();
  //   return this;
  // }

  forForgotPasswordRequest(view: string): any {
    const request: any = {};

    if (view === 'FORGOT_PASSWORD_VIA_EMAIL') {
      request.email = this.email;
    }

    if (view === 'FORGOT_PASSWORD_VIA_MOBILE') {
      request.userName = this.userName;
    }
    return request;
  }

  isValidForgotPasswordRequest(form: any, view: string) {
    if (view === 'FORGOT_PASSWORD_VIA_EMAIL') {
      const emailCtrl = form.controls?.userEmail;
      if (!this.email || this.email.trim() === '') {
        emailCtrl?.setErrors({ invalid: true });
        return false;
      }
    }
    if (view === 'FORGOT_PASSWORD_VIA_MOBILE') {
      const usernameCtrl = form.controls?.username;
      if (!this.userName || this.userName.trim() === '') {
        usernameCtrl?.setErrors({ invalid: true });
        return false;
      }
    }
    return true;
  }

  isValidChangePasswordRequest(form: any) {
    if (!this.oldPassword || this.oldPassword.trim() === '') {
      form.controls.currentPassword.setErrors({ invalid: true });
      return false;
    }
    if (!this.password || this.password.trim() === '') {
      form.controls.userPassword.setErrors({ invalid: true });
      return false;
    }
    if (!this.confirmPassword || this.confirmPassword.trim() === '') {
      form.controls.userCofirmPassword.setErrors({ invalid: true });
      return false;
    }
    if (this.password && this.confirmPassword && this.confirmPassword !== this.password) {
      form.controls.userCofirmPassword.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

  isValidResetPasswordRequest(form: any) {
    if (!this.password || this.password.trim() === '') {
      form.controls.userPassword.setErrors({ invalid: true });
      return false;
    }
    if (!this.confirmPassword || this.confirmPassword.trim() === '') {
      form.controls.userCofirmPassword.setErrors({ invalid: true });
      return false;
    }
    if (this.password && this.confirmPassword && this.confirmPassword !== this.password) {
      form.controls.userCofirmPassword.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

  isValidRegisterRequest(form: any) {
    if (!this.name || this.name.trim() === '') {
      form.controls.userName.setErrors({ required: true });
      return false;
    }
    if (!this.email || this.email.trim() === '') {
      form.controls.userEmail.setErrors({ required: true });
      return false;
    }
    if (!this.password || this.password.trim() === '') {
      form.controls.userPassword.setErrors({ required: true });
      return false;
    }
    if (!this.confirmPassword || this.confirmPassword.trim() === '') {
      form.controls.userConfirmPassword.setErrors({ required: true });
      return false;
    }
    if (this.password && this.confirmPassword && this.password !== this.confirmPassword) {
      form.controls.userConfirmPassword.setErrors({ required: true });
      return false;
    }
    return true;
  }
}
