// Define the UserDetail class
export class UserDetail {
  id: string | null = null;
  email: string | null = null;
  firstName: string | null = null;
  lastName: string | null = null;
  phoneNumber: string | null = null;
  fullName: string | null = null;
  totalCount: number | null = null;
  userProfileDetail: UserProfileDetail = new UserProfileDetail();

}

export class OffersDetail {
  id: string | null = null;
  actualAmount: number | null = null;
  baseAmount: number | null = null;
  discountAmount: number | null = null;
  discountPer: number | null = null;
  forbcorpPrice: number | null = null;
  gstAmount: number | null = null;
  gstRate: number | null = null;
  isActive: boolean | null = null;
  isDeleted: boolean | null = null;
  isGstIncluded: boolean | null = null;
  isTenureExtendable: boolean | null = null;
  maxUsageLimit: number | null = null;
  noOfTimeUsed: number | null = null;
  offer: string | null = null;
  status: string | null = null;
  title: string | null = null;
  totalAmount: number | null = null;
  totalCount: number | null = null;
  updatedOn: string | null = null;
  supportTicketId: string | null = null;
  supportTicketStatus: string | null = null;
}


export class UserProfileDetail {
  user: string | null = null;
  passportNumber: string | null = null;
  passportCountry: string | null = null;
  passportIssueDate: string | null = null;
  passportExpiryDate: string | null = null;
  customerId: string | null = null;
  totalCount: number = 0;
}


export class SoldFullyPaidHolidayOfferDetail {
  id: string | null = null;
  title: string | null = null;
  locationTitle: string | null = null;
  description: string | null = null;
  location: string | null = null;
  supportTicketId: string | null = null;
  supportTicketStatus: string | null = null;
  isAmountAdded: boolean | null = null;
}

// Define the PackageDetail class
export class PackageDetail {
  id: string | null = null;
  slug: string | null = null;
  updatedOn: string | null = null;
  isActive: boolean | null = null;
  package: string | null = null;
  title: string | null = null;
  description: string | null = null;
  price: number | null = null;
  totalNights: number | null = null;
  tenure: number | null = null;
  roomCapNightsPerYear: number | null = null;
  firstAsfCharges: number | null = null;
  packageCode: string | null = null;
  nightsPerYear: number | null = null;
  indianNights: number | null = null;
  asianNights: number | null = null;
  internationalNights: number | null = null;
  isGstInclude: boolean | null = null;
  gstRate: number | null = null;
  color: string | null = null;
  totalCount: number | null = null;
}

// Define the PackageData class
export class PackageData {
  id: string | null = null;
  packageDetail: PackageDetail = new PackageDetail();
  userDetail: UserDetail = new UserDetail();
  offersDetail: OffersDetail = new OffersDetail();
  soldFullyPaidHolidayOfferDetail: SoldFullyPaidHolidayOfferDetail = new SoldFullyPaidHolidayOfferDetail();
  status: string | null = null;
  purchasedDate: string | null = null;
  enrolmentDate: string | null = null;
  expiryDate: string | null = null;
  downpayment: number | null = null;
  carryForwardAvailable: boolean | null = null;
  usedNights: number | null = null;
  currentYearDetail: string | null = null;
  isBookingAllowed: boolean | null = null;
  bookingNotAllowedNote: string | null = null;
  subTotal: number | null = null;
  gstAmount: number | null = null;
  discountAmount: number | null = null;
  totalAmount: number | null = null;
  isOfferAvailable: boolean | null = null;
  isRedeemProcessStart: boolean | null = null;
  offerClaimed: boolean | null = null;
  offerType: string | null = null;
  locationTitle: string | null = null;
  soldBy: string | null = null;
  reviewBy: string | null = null;
  isEmiAvailed: boolean | null = null;
  balanceAmount: number | null = null;
  balanceNights: number | null = null;
  totalCount: number | null = null;
  currentYearDetails: any;
  supportTicketId: string | null = null;
  isSupportTicketAvailable: boolean | null = null;
  packageCount: number | null = null;
}
