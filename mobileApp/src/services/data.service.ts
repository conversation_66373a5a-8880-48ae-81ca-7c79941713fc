import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { RestResponse } from 'src/shared/auth.model';

@Injectable({
  providedIn: 'root'
})
export class DataService {

  constructor(private readonly http: HttpClient) {
  }

  getRecords(path: string): Observable<any> {
    return this.http
      .get(environment.BaseApiUrl + path, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  getRecordsById(path: string, payload: any): Observable<any> {
    return this.http
      .get(environment.BaseApiUrl + path + payload, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  saveRecord(path: string, resource: any): Observable<any> {
    return this.http
      .post(environment.BaseApiUrl + path, resource, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  updateRecord(path: string, resource: any): Observable<any> {
    return this.http
      .put(environment.BaseApiUrl + path, resource, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  removeRecord(path: string): Observable<any> {
    return this.http
      .delete(environment.BaseApiUrl + path, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  postUrl(path: string): Observable<RestResponse> {
    return this.http
      .post<RestResponse>(environment.BaseApiUrl + path, null, { headers: environment.AppHeaders })
      .pipe(
        catchError((error) => this.handleError(error))
      );
  }

  protected handleError(error: HttpErrorResponse): Promise<any> {
    if (error.status === 404) {
      return Promise.reject({ "message": error.message ? error.message : 'Something went wrong while processing your request' });
    }
    if (error.status === 401) {
      return Promise.reject({ "message": error.message ? error.message : 'Something went wrong while processing your request' });
    }
    const internalError: any = JSON.parse(JSON.stringify(error));
    if (!internalError.message) {
      internalError.message = 'Something went wrong while processing your request';
    }
    return Promise.reject(internalError);
  }

  login(data: any): Observable<RestResponse> {
    return this.saveRecord('/app/v1/account/login', data);
  }

  loginWithOtp(data: any): Observable<RestResponse> {
    console.log("data",data)
    return this.saveRecord('/app/v1/account/login/with/otp', data);
  }

  forgotPassword(data: any): Observable<RestResponse> {
    return this.saveRecord('/app/v1/account/forgot/password', data);
  }

  forgotPasswordMobile(data: any): Observable<RestResponse> {
    return this.saveRecord('/app/v1/account/forgot/password/mobile', data);
  }

  verifyResetPasswordOtp(data: any): Observable<RestResponse> {
    return this.saveRecord('/app/v1/account/verify/otp', data);
  }

  resetPassword(data: any): Observable<RestResponse> {
    return this.saveRecord('/app/v1/account/reset/password/otp', data);
  }

  generateQRCode(token: string): Observable<RestResponse> {
    return this.postUrl(`/api/v1/account/mfa/setup`);
  }

  initiateMFA(): Observable<RestResponse> {
    return this.getRecords('/api/v1/account/mfa/verification/init');
  }

  verifyOtp(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/account/mfa/setup/verify', data);
  }

  verifyOtpForProfileTwoFactor(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/account/mfa/verification/verify', data);
  }

  changePassword(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/app/account/change/password', data);
  }

  customerRegister(data: any): Observable<RestResponse> {
    return this.saveRecord(`/app/v1/account/register`, data);
  }

  createAccount(data: any): Observable<RestResponse> {
    return this.saveRecord(`/app/v1/account/registration`, data);
  }

  verifyMobile(data: any): Observable<RestResponse> {
    return this.updateRecord('/app/v1/account/verify/phonenumber', data);
  }

  verifyMobileForLoginWithOtp(data: any): Observable<RestResponse> {
    return this.updateRecord('/app/v1/account/verify/login/otp', data);
  }

  customerOnboarding(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/v1/app/account/update', data);
  }

  resendMobileVerificationCode(data: any): Observable<RestResponse> {
    return this.saveRecord(`/app/v1/account/resend/otp/${data.id}`, data);
  }

  mobileAvailable(data: any): Observable<RestResponse> {
    return this.saveRecord(`/app/v1/account/phonenumber/validate`, data);
  }

  emailAvailable(data: any): Observable<RestResponse> {
    return this.saveRecord(`/app/v1/account/email/validate`, data);
  }

  fetchAvailablePackageWithOffers(): Observable<RestResponse> {
    return this.getRecords('/app/v1/account/available/packages/with/offers');
  }

  fetchAvailableMonthsLocation(payload: any): Observable<RestResponse> {
    return this.getRecordsById('/api/available/months/location/', payload);
  }

  getProfileDetails(): Observable<RestResponse> {
    const url = `${environment.BaseApiUrl}/api/customer/my/profile`;
    return this.http.get<RestResponse>(url);
    //return this.getRecords('/api/customer/my/profile');
  }

  getFamilyDetails(data: any): Observable<any> {
    return this.http.post<RestResponse>(`${environment.BaseApiUrl}/api/userfamilies`, data);
  }

  showPackageDetails(data: any): Observable<any> {
    return this.http.post<RestResponse>(`${environment.BaseApiUrl}/api/customerpackages`, data);
  }

  fullPayment(data: any, token: string): Observable<RestResponse> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
    return this.http.put<RestResponse>(`${environment.BaseApiUrl}/api/customer/package/payment`, data, { headers });
  }

  calculateEmi(data: any): Observable<RestResponse> {
    return this.http.post<RestResponse>(`${environment.BaseApiUrl}/app/v1/account/emi/calculator`, data);
  }

  calculateEmiSummary(data: any): Observable<RestResponse> {
    return this.saveRecord(`/app/v1/account/emi/summary/calculate`, data);
  }

  uploadFile(data: FormData): Observable<RestResponse> {
    return this.http.post<RestResponse>(`${environment.BaseApiUrl}/api/v1/file/group/items/upload`, data);
  }

  availableCities(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/available/cities', data);
  }

  searchHotelsByDestination(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/hotels/search/by/destination', data);
  }

  fetchHotelDetailById(data: any): Observable<RestResponse> {
    return this.getRecordsById('/api/hotel/details/', data.hotelCode);
    // const url = `/api/hotel/${hotelCode}/details`;
    // return this.getRecords(url);
  }

  preBooking(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/pre/booking', data);
  }

  bookingAllowed(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/booking/allowed', data);
  }

  booking(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/verify/booking/payment', data);
  }

  bookingWithoutPayment(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/booking', data);
  }

  bookingDetails(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/hotel/booking/details', data);
  }

  bookingCancel(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/cancel/my/booking', data);
  }

  myBookings(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/my/bookings', data);
  }

  bookingCancellationPolicy(bookingId: string): Observable<RestResponse> {
    return this.getRecords(`/api/fetch/booking/${bookingId}/cancellation/policy`);
  }

  hotelCancellationPolicy(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/hotel/fetch/cancellation/policy', data);
  }

  myNotifications(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/my/notifications', data);
  }

  readSingleNotification(notificationId: string): Observable<RestResponse> {
    const path = `/api/v1/read/notification/${notificationId}`;
    return this.updateRecord(path, null); // No body is needed for this PUT request
  }

  readAllNotifications(): Observable<RestResponse> {
    const path = '/api/read/all/notifications';
    return this.updateRecord(path, null); // Pass 'null' as there is no body
  }

  carryForwardHolidays(packageId: string): Observable<RestResponse> {
    const path = `/api/package/${packageId}/carry/forward`;
    return this.updateRecord(path, null); // Pass 'null' as there is no body
  }

  lapseHolidays(packageId: string): Observable<RestResponse> {
    const path = `/api/package/${packageId}/nights/elapsed`;
    return this.updateRecord(path, null); // Pass 'null' as there is no body
  }

  myRedeemedRequest(): Observable<RestResponse> {
    return this.getRecords(`/api/v1/my/redeemed/request`);
  }

  myEMIDetail(): Observable<RestResponse> {
    return this.getRecords(`/api/v1/my/installmentdetails`);
  }

  getUnreadNotificationCount(): Observable<RestResponse> {
    return this.getRecords(`/api/v1/my/notifications/count`);
  }

  myPayments(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/my/payments', data);
  }

  recurringPayments(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/verify/recurring/payment', data);
  }

  fphPayments(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/verify/additional/fph/payment', data);
  }

  otherPayments(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/verify/other/payment', data);
  }

  supportRequestList(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/my/support/requests', data);
  }

  createSupportRequest(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/support/request?returnDetailedResponse=true', data);
  }

  fphRedeemRequest(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/fph/redeem/request', data);
  }

  offerRedeemRequest(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/offer/redeem/request', data);
  }

  updatePanCard(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/v1/app/account/change/pancard', data);
  }

  cancelRedeemRequest(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/v1/cancel/redeem/request', data);
  }

  resumeBooking(bookingId: string): Observable<RestResponse> {
    const url = `/api/resume/booking/${bookingId}`;
    return this.saveRecord(url, null); // Pass null since no payload is required
  }

  createChangeProfileRequest(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/profile/change/request?returnDetailedResponse=true', data);
  }

  myProfileChangeRequests(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/my/profile/change/requests', data);
  }

  getProfileChangeRequestDetail(data: any): Observable<RestResponse> {
    return this.getRecordsById('/api/v1/profile/change/request/', data);
  }

  invoiceDownloadForBooking(id: string): Observable<RestResponse> {
    const path = `/api/v1/payment/${id}/booking/invoice/download`;
    return this.getRecordsById(path, '');
  }

  invoiceDownloadForDownOrFullPayment(id: string): Observable<RestResponse> {
    const path = `/api/v1/payment/${id}/package/invoice/download`;
    return this.getRecordsById(path, '');
  }

  invoiceDownloadForOthers(id: string): Observable<RestResponse> {
    const path = `/api/v1/payment/${id}/invoice/download`;
    return this.getRecordsById(path, '');
  }

  invoiceDownloadForMyBookings(id: string): Observable<RestResponse> {
    const path = `/api/v1/booking/${id}/voucher/download`;
    return this.getRecordsById(path, '');
  }

  sendComment(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/support/request/comment', data);
  }

  mySupportComments(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/v1/support/request/comments', data);
  }

  getSupportRequestById(id: string): Observable<RestResponse> {
    const path = `/api/support/request/${id}`;
    return this.getRecordsById(path, '');
  }

  getDashboardDetails(): Observable<RestResponse> {
    return this.saveRecord('/api/v1/app/account/dashboard/data', null);
  }

  // this below api's not in use for now //
  resendOtp(data: any): Observable<RestResponse> {
    return this.saveRecord('/app/v1/forgot/otp/resend', data);
  }

  getDetails(): Observable<RestResponse> {
    return this.getRecords('/api/v1/account/details');
  }

  getHotelDetailsById(): Observable<RestResponse> {
    return this.getRecords('/api/hotel/details');
  }

  saveToken(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/v1/token/registration`, input);
  }

  createOrder(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/v1/create/order`, input);
  }

  walletRecharge(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/v1/verify/wallet/recharge`, input);
  }

  freeTrip(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/v1/verify/campaign/payment`, input);
  }

  activeCampaign(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/campaign/active`, input);
  }

  getCountryCode(longitude: number, latitude: number): Observable<any> {
    const data = `https://nominatim.openstreetmap.org/reverse?lat=${latitude}&lon=${longitude}&format=json`;
    return this.http.get(data);
  }

  getWalletDetails(): Observable<RestResponse> {
    return this.getRecords(`/api/v1/my/wallet`);
  }

  getPromoCashDetails(): Observable<RestResponse> {
    return this.getRecords(`/api/v1/my/promocash`);
  }

  getWalletCashTransactions(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/v1/my/wallet/transactions`, input);
  }

  availableCashDetails(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/v1/my/available/cash`, input);
  }

  purchasePackage(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/v1/verify/package/payment`, input);
  }

  fetchMyActiveSubscription(): Observable<RestResponse> {
    return this.getRecords(`/api/my/subscription`);
  }

  fetchRandomCampaigns(): Observable<RestResponse> {
    return this.getRecords(`/api/campaign/random/select`);
  }

  myPackages(): Observable<RestResponse> {
    return this.getRecords(`/api/v1/my/packages`);
  }

  setAsDefaultPackage(id: string): Observable<RestResponse> {
    const path = `/api/v1/customers/packages/${id}/set-default`;
    return this.updateRecord(path, null); // No body is needed for this PUT request
  }

  myCompaigns(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/campaign/mycampaign', data);
  }

  campaignDetailId(id: string): Observable<RestResponse> {
    const path = `/api/campaign/${id}`;
    return this.getRecordsById(path, '');
  }

  logout(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/v1/app/account/logout`, input);
  }

  pushNotificationDetailById(id: string): Observable<RestResponse> {
    const path = `/api/custompushnotification/${id}`;
    return this.getRecordsById(path, '');
  }

  packageCustomPushNotification(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/packages/with/offer/detail`, input);
  }

  campaignBookingCashback(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/campaign/booking/cashback`, input);
  }

  fetchBookingDetail(bookingId: string): Observable<RestResponse> {
    return this.getRecords(`/api/my/booking/${bookingId}`);
  }
}


