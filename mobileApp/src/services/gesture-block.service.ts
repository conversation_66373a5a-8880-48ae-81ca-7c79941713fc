import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class GestureBlockService {
  private gestureListeners: (() => void)[] = [];

  constructor(private platform: Platform) {}

  public blockAllSwipeGestures(): void {
    console.log('Blocking all swipe gestures globally');
    
    // Block at document level
    this.blockDocumentGestures();
    
    // Block iOS specific gestures
    if (this.platform.is('ios')) {
      this.blockIOSGestures();
    }
    
    // Block Android specific gestures
    if (this.platform.is('android')) {
      this.blockAndroidGestures();
    }
  }

  private blockDocumentGestures(): void {
    const preventSwipe = (event: TouchEvent): boolean | void => {
      if (event.touches.length !== 1) return;

      const touch = event.touches[0];
      const startX = touch.clientX;
      const screenWidth = window.innerWidth;

      // Block swipes from edges (common navigation trigger areas)
      if (startX < 50 || startX > screenWidth - 50) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
        return false;
      }
    };

    const preventHorizontalSwipe = (event: TouchEvent): boolean | void => {
      if (event.touches.length !== 1) return;

      const touch = event.touches[0];
      const target = event.target as HTMLElement;

      // Store initial touch position
      if (!target.dataset['touchStartX']) {
        target.dataset['touchStartX'] = touch.clientX.toString();
        target.dataset['touchStartY'] = touch.clientY.toString();
        return;
      }

      const startX = parseFloat(target.dataset['touchStartX'] || '0');
      const startY = parseFloat(target.dataset['touchStartY'] || '0');
      const deltaX = Math.abs(touch.clientX - startX);
      const deltaY = Math.abs(touch.clientY - startY);

      // If horizontal movement is greater than vertical, block it
      if (deltaX > deltaY && deltaX > 20) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
        return false;
      }
    };

    const clearTouchData = (event: TouchEvent) => {
      const target = event.target as HTMLElement;
      delete target.dataset['touchStartX'];
      delete target.dataset['touchStartY'];
    };

    // Add event listeners
    const addListener = (event: string, handler: (e: any) => void) => {
      document.addEventListener(event, handler, { passive: false, capture: true });
      this.gestureListeners.push(() => {
        document.removeEventListener(event, handler, { capture: true } as any);
      });
    };

    addListener('touchstart', preventSwipe);
    addListener('touchmove', preventHorizontalSwipe);
    addListener('touchend', clearTouchData);
    addListener('touchcancel', clearTouchData);
  }

  private blockIOSGestures(): void {
    // Block iOS specific gesture events
    const blockGesture = (event: Event) => {
      event.preventDefault();
      event.stopPropagation();
      event.stopImmediatePropagation();
      return false;
    };

    const gestureEvents = ['gesturestart', 'gesturechange', 'gestureend'];
    
    gestureEvents.forEach(eventName => {
      document.addEventListener(eventName, blockGesture, { passive: false, capture: true });
      this.gestureListeners.push(() => {
        document.removeEventListener(eventName, blockGesture, { capture: true } as any);
      });
    });

    // Block WebKit specific navigation gestures
    const style = document.createElement('style');
    style.textContent = `
      * {
        -webkit-touch-callout: none !important;
        -webkit-user-select: none !important;
        -webkit-tap-highlight-color: transparent !important;
        -webkit-user-drag: none !important;
      }
      
      body, html {
        -webkit-overflow-scrolling: touch !important;
        overflow-x: hidden !important;
        touch-action: pan-y !important;
      }
    `;
    document.head.appendChild(style);
    
    this.gestureListeners.push(() => {
      if (style.parentNode) {
        style.parentNode.removeChild(style);
      }
    });
  }

  private blockAndroidGestures(): void {
    // Block Android specific swipe gestures
    const preventAndroidSwipe = (event: TouchEvent): boolean | void => {
      const touch = event.touches[0];
      if (!touch) return;

      const startX = touch.clientX;
      const screenWidth = window.innerWidth;

      // Block edge swipes on Android
      if (startX < 30 || startX > screenWidth - 30) {
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
    };

    document.addEventListener('touchstart', preventAndroidSwipe, { passive: false, capture: true });
    this.gestureListeners.push(() => {
      document.removeEventListener('touchstart', preventAndroidSwipe, { capture: true } as any);
    });
  }

  public unblockAllGestures(): void {
    console.log('Unblocking all gesture listeners');
    this.gestureListeners.forEach(removeListener => removeListener());
    this.gestureListeners = [];
  }
}
