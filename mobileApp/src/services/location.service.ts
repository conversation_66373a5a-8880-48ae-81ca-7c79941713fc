import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

interface State {
    name: string;
    districts: string[];
}

interface StatesData {
    states: State[];
}

@Injectable({
    providedIn: 'root'
})
export class LocationService {
    private apiUrl = '/assets/location/location.json'; // Path to your JSON file

    constructor(private http: HttpClient) { }

    // Fetch the state data from the JSON file
    private getStateData(): Observable<StatesData> {
        return this.http.get<StatesData>(this.apiUrl).pipe(
            catchError(this.handleError('getStateData', { states: [] }))
        );
    }

    // Extract all states and sort them alphabetically
    getAllStates(): Observable<string[]> {
        return this.getStateData().pipe(
            map(data => data.states.map(state => state.name).sort()), // Sort states alphabetically
            catchError(() => of([])) // Return an empty array if there is an error
        );
    }

    // Error handling
    private handleError<T>(operation = 'operation', result?: T) {
        return (error: any): Observable<T> => {
            console.error(`${operation} failed: ${error.message}`);
            return of(result as T);
        };
    }
}
