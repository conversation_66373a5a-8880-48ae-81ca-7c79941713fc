import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class NavigationDataService {

  private data: any;
  private returnUrl: string | null = null;

  setData(data: any) {
    this.data = data;
  }

  getData() {
    return this.data;
  }

  setReturnUrl(url: string) {
    this.returnUrl = url;
  }

  getReturnUrl(): string | null {
    return this.returnUrl;
  }

  clearReturnUrl() {
    this.returnUrl = null;
  }
}
