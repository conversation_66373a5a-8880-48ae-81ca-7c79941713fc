import { Injectable } from "@angular/core";
import { Animation<PERSON><PERSON>roller, ModalController } from "@ionic/angular";
import { MaskitoElementPredicate, MaskitoOptions } from "@maskito/core";
import { Subject } from "rxjs";
import { LoginComponent } from "src/app/authentication/login/login.component";
import { CommaSeparatorPipe } from 'src/services/comma.separator.service';
import { FullImageComponent } from "src/shared/full-image/full-image.component";

@Injectable({
  providedIn: 'root'
})

export class CommonService {

  isLocationSelectionPopupOpen: boolean = false;
  isCitySelectionPopupOpen: boolean = false;
  isNoPackageAvailablePopupOpen: boolean = false;
  loginModalShown = false;
  isLoginModalOpenOrNot = false;

  private randomCampaignFetched = false;

  constructor(private modalCtrl: ModalController,
    private animationCtrl: AnimationController,
  ) {
  }

  // readonly phoneMasks: MaskitoOptions = {
  //   mask: [
  //     /\d/, /\d/, /\d/, /\d/, /\d/, ' ',
  //     /\d/, /\d/, /\d/, /\d/, /\d/
  //   ]
  // };

  readonly phoneMask: MaskitoOptions = {
    mask: [
      '+', '9', '1', ' ', // Country code +91 and space
      /\d/, /\d/, /\d/, /\d/, /\d/, // First segment (5 digits)
      ' ', // Space
      /\d/, /\d/, /\d/, /\d/, /\d/  // Second segment (5 digits)
    ]
  };

  readonly adhaarMask: MaskitoOptions = {
    mask: [
      /\d/, /\d/, /\d/, /\d/, ' ', // First 4 digits
      /\d/, /\d/, /\d/, /\d/, ' ',// Next 4 digits
      /\d/, /\d/, /\d/, /\d/  // Last 4 digits
    ] // Enforces exactly 12 digits
  };

  readonly pinCodeMask: MaskitoOptions = {
    mask: [
      /\d/, /\d/, /\d/, /\d/, /\d/, /\d/
    ] // Enforces exactly 6 digits
  };

  readonly panCardMask: MaskitoOptions = {
    mask: [
      /[A-Za-z]/, /[A-Za-z]/, /[A-Za-z]/, /[A-Za-z]/, /[A-Za-z]/, // Five letters (any case)
      /\d/, /\d/, /\d/, /\d/, // Four digits
      /[A-Za-z]/ // One letter (any case)
    ],
  };

  readonly passportMask: MaskitoOptions = {
    mask: [
      /[A-Za-z]/, // One letters (any case)
      /\d/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/, // Seven digits
    ],
  };

  readonly maskPredicate: MaskitoElementPredicate = async (el) => (el as HTMLIonInputElement).getInputElement();


  static isNullOrUndefined(value: any): boolean {
    return value === null || value === undefined;
  }

  convertToUpperCase(event: any) {
    const inputElement = event.target as HTMLInputElement;
    inputElement.value = inputElement.value.toUpperCase();
    return inputElement.value;
  }

  isValidEmail(email: string): boolean {
    const emailPattern = /^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$/;
    return emailPattern.test(email);
  }

  isValidPhoneNumber(phone: string): boolean {
    const phonePattern = /^\+91\s?\d{5}\s?\d{5}$/; // Adjust this regex as per your format requirements
    return phonePattern.test(phone);
  }

  changeInputFocus(event: any) {
    const pattern = /\d/;
    if (!pattern.test(event.detail.value)) {
      event.target.value = "";
      event.preventDefault();
      return;
    }
    const targetValueLength = event.detail.value.length;
    // Handle other key presses
    if (targetValueLength === 1) {
      const nextElement = event.target.closest('ion-input').nextElementSibling?.querySelector('input');
      if (nextElement) {
        nextElement.focus();
      }
    }
  }

  handleBackspace(event: any) {
    const keyCode = event.keyCode || event.which;

    // Handle backspace key press
    if (keyCode === 8 && event.target.value.length === 0) {
      const previousElement = event.target.closest('ion-input').previousElementSibling?.querySelector('input');
      if (previousElement) {
        previousElement.focus();
        previousElement.value = ''; // Clear the previous input value
      }
    }
  }

  getOTPFromInputs(otpInput1: any, otpInput2: any, otpInput3: any, otpInput4: any, otpInput5?: any, otpInput6?: any) {
    const input1 = otpInput1 != null ? otpInput1.toString().charAt(0) : '';
    const input2 = otpInput2 != null ? otpInput2.toString().charAt(0) : '';
    const input3 = otpInput3 != null ? otpInput3.toString().charAt(0) : '';
    const input4 = otpInput4 != null ? otpInput4.toString().charAt(0) : '';
    const input5 = otpInput5 != null ? otpInput5.toString().charAt(0) : '';
    const input6 = otpInput6 != null ? otpInput6.toString().charAt(0) : '';
    return `${input1 || ''}${input2 || ''}${input3 || ''}${input4 || ''}${input5 || ''}${input6 || ''}`;
  }

  formatDate(date: Date): string { // format :- Jan 1, 2026
    const options: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric', year: 'numeric' };
    return new Date(date).toLocaleDateString('en-US', options);
  }

  formatDateForHolidayExpire(date: Date): string {  // format :- 1 Jan 2026
    const options: Intl.DateTimeFormatOptions = { day: 'numeric', month: 'short', year: 'numeric' };
    return new Date(date).toLocaleDateString('en-GB', options).replace(',', '');
  }

  getTotalAdults(adults: string): number {
    if (!adults) return 0;
    return adults.split('|').map(Number).reduce((a, b) => a + b, 0);
  }

  getTotalChildren(children: string): number {
    if (!children) return 0;
    return children.split('|').map(Number).reduce((a, b) => a + b, 0);
  }

  getDisplayValue(input: number) {
    if (Number(input) <= 9) {
      return `0${input}`;
    }
    return input;
  }

  formatNumber(value: number | null, round: boolean = false): string {
    if (value == null) {
      return '';
    }
    // Round the value if needed
    const roundedValue = round ? Math.floor(value) : value;

    return new CommaSeparatorPipe().transform(roundedValue);
  }

  formatText(type: string | null): string {
    if (!type) return '';

    return type
      .toLowerCase()
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  formatTextAllCapital(type: string): string {
    if (!type) return '';

    return type
      .split('_') // Split the string by underscores
      .map(word => word.toUpperCase()) // Ensure all words are uppercase
      .join(' '); // Join the words with spaces
  }

  isObjectEmpty(obj: object): boolean {
    return Object.keys(obj).length === 0;
  }

  formatNotificationDate(dateString: string): { date: string; time: string } {
    const date = new Date(dateString);

    // Format options
    const dateOptions: Intl.DateTimeFormatOptions = { day: '2-digit', month: 'short', year: 'numeric' };
    const timeOptions: Intl.DateTimeFormatOptions = { hour: '2-digit', minute: '2-digit', hour12: false };

    const formattedDate = date.toLocaleDateString('en-US', dateOptions); // e.g., 17 Dec 2024
    const formattedTime = date.toLocaleTimeString('en-US', timeOptions); // e.g., 10:39

    return { date: formattedDate, time: formattedTime };
  }

  formatDateForCampaignEndDate(dateString: string): { date: string; time: string } {
    const utcDate = new Date(dateString);

    // Format date as "22 Nov, 2024" in UTC
    const dateOptions: Intl.DateTimeFormatOptions = {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      timeZone: 'UTC'
    };
    const formattedDate = utcDate.toLocaleDateString('en-US', dateOptions).replace(/(\d{2}) (\w{3}) (\d{4})/, '$1 $2, $3');

    // Get weekday in UTC
    const weekday = utcDate.toLocaleDateString('en-US', { weekday: 'short', timeZone: 'UTC' });

    // Check if time is exactly midnight (T00:00:00)
    const isMidnightUTC = dateString.includes('T00:00:00');

    let displayTime: string;

    if (isMidnightUTC) {
      displayTime = `${weekday}, 11:59 PM`;
    } else {
      // Extract hour and minute in UTC and format as 12-hour time
      const timeOptions: Intl.DateTimeFormatOptions = {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
        timeZone: 'UTC'
      };
      const formattedTime = utcDate.toLocaleTimeString('en-US', timeOptions);
      displayTime = `${weekday}, ${formattedTime}`;
    }

    return {
      date: formattedDate,
      time: displayTime,
    };
  }

  hasValue(value: string) {
    return value && value.trim() !== "";
  }

  formatAdhaarNumber(adhaarNumber: string): string {
    return adhaarNumber.replace(/(\d{4})(\d{4})(\d{4})/, '$1 $2 $3');
  }

  formatPhoneNumber(phoneNumber: string | null): string {
    if (!phoneNumber) {
      return '';
    }
    const countryCode = '+91 ';
    const formatted = phoneNumber.replace(/(\d{5})(\d{5})/, '$1 $2');
    return formatted;
  }

  formatPanCard(panCard: string): string {
    if (!panCard) {
      return '';
    }
    // Convert to uppercase
    const formattedPan = panCard.toUpperCase();
    return formattedPan;
  }

  // Prevent user from entering non-numeric characters
  restrictInput(event: KeyboardEvent) {
    const charCode = event.key.charCodeAt(0);  // convert pressed key to ASCII code.

    // ASCII values: 48 = '0', 57 = '9'
    // below code is restrict that user enter only 0-9 numbers.
    if (charCode < 48 || charCode > 57) {
      event.preventDefault();
    }
  }

  // Function to format the number with commas
  formatAmount(amount: number | string | null | undefined): string {
    if (!amount || isNaN(Number(amount))) {
      return "0"; // Return "0" instead of an empty string
    }
    return Number(amount).toLocaleString('en-IN');
  }

  getDescriptionUppercase(description: string): string {
    if (description) {
      return description.toUpperCase();
    }
    return '';
  }

  async viewImage(imageUrl: string) {
    const modal = await this.modalCtrl.create({
      component: FullImageComponent,
      componentProps: { imageUrl },
      cssClass: 'full-image-modal'
    });
    await modal.present();
  }

  expireHolidays(packageData: any, balanceNights: number): boolean {
    const carryForwardAvailable = packageData.currentYearDetails.carryForwardAvailable;
    const endDate = new Date(packageData.currentYearDetails.endDate).toISOString().split('T')[0];
    const today = new Date().toISOString().split('T')[0];

    // Calculate days remaining
    const endDateObj = new Date(endDate);
    const todayObj = new Date(today);
    const timeDiff = endDateObj.getTime() - todayObj.getTime();
    const daysRemaining = timeDiff / (1000 * 3600 * 24);

    // before 7 days package end date shown popup for carry forward or lapse holidays. 
    return daysRemaining <= 7 &&
      daysRemaining >= 0 &&
      balanceNights > 0 &&
      carryForwardAvailable === true;
  }

  openGoogleMaps(long: string, lat: string) {
    const latitude = lat; // Replace with your latitude
    const longitude = long; // Replace with your longitude
    const url = `https://www.google.com/maps?q=${latitude},${longitude}`;
    window.open(url, '_system'); // Opens in Google Maps App if installed
  }

  // async openLoginModals() {
  //   const modal = await this.modalCtrl.create({
  //     component: LoginComponent,
  //     cssClass: 'login-modal',
  //     animated: true,
  //     backdropDismiss: false,
  //     showBackdrop: true,
  //     // breakpoints: [0.5, 1],
  //     // initialBreakpoint: 0.5,
  //     handle: true
  //   });
  //   await modal.present();
  // }

  //   async closeLoginModals() {
  //   const topModal = await this.modalCtrl.getTop();
  //   if (topModal && topModal.component === LoginComponent) {
  //     await topModal.dismiss();
  //     this.closeLoginModalShown();
  //   }
  // }

  async openLoginModal() {
    await this.closeLoginModal();

    if (this.isLoginModalOpenOrNot) {
      console.log("Login modal is already open or just closed. Skipping open.");
      return;
    }

    this.isLoginModalOpenOrNot = true;

    const modal = await this.modalCtrl.create({
      component: LoginComponent,
      cssClass: 'login-modal',
      animated: true,
      backdropDismiss: false,
      showBackdrop: true
    });

    modal.onDidDismiss().then(() => {
      this.isLoginModalOpenOrNot = false;
    });
    await modal.present();
  }

  customEnterAnimation = (baseEl: any) => {
    const root = baseEl.shadowRoot;
    const modalWrapper = root.querySelector('.modal-wrapper');

    return this.animationCtrl.create()
      .addElement(modalWrapper)
      .duration(3000)
      .easing('cubic-bezier(0.36,0.66,0.04,1)')
      .fromTo('transform', 'translateY(100%)', 'translateY(0%)')
      .fromTo('opacity', '1', '1');
  }

  customLeaveAnimation = (baseEl: any) => {
    const root = baseEl.shadowRoot;
    const modalWrapper = root.querySelector('.modal-wrapper');

    return this.animationCtrl.create()
      .addElement(modalWrapper)
      .duration(1000)
      .easing('cubic-bezier(0.36,0.66,0.04,1)')
      .fromTo('transform', 'translateY(0%)', 'translateY(100%)')
      .fromTo('opacity', '1', '0');
  }

  hasLoginModalBeenShown(): boolean {
    return this.loginModalShown;
  }

  setLoginModalShown() {
    this.loginModalShown = true;
  }

  async closeLoginModal() {
    const topModal = await this.modalCtrl.getTop();
    if (topModal && topModal.component === LoginComponent) {
      await topModal.dismiss();
      this.isLoginModalOpenOrNot = false;
    }
  }

  closeLoginModalShown() {
    this.loginModalShown = false;
  }

  hasRandomCampaignBeenFetched(): boolean {
    return this.randomCampaignFetched;
  }

  setRandomCampaignFetched(): void {
    this.randomCampaignFetched = true;
  }

}
