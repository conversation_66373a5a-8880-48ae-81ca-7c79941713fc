<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <title>Ionic App</title>

  <base href="/" />

  <meta name="color-scheme" content="light" />
  <meta name="viewport" content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="format-detection" content="telephone=no" />
  <meta name="msapplication-tap-highlight" content="no" />

  <!-- Disable swipe gestures and navigation -->
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="apple-touch-fullscreen" content="yes" />
  <meta name="mobile-web-app-capable" content="yes" />

  <!-- Prevent gesture navigation -->
  <style>
    * {
      -webkit-touch-callout: none !important;
      -webkit-user-select: none !important;
      -webkit-tap-highlight-color: transparent !important;
      touch-action: pan-y !important;
    }

    body, html {
      overflow-x: hidden !important;
      touch-action: pan-y !important;
      -webkit-overflow-scrolling: touch !important;
    }
  </style>

  <link rel="icon" type="image/png" href="assets/images/icons/favicon.png" />
  <script src='https://maps.googleapis.com/maps/api/js?libraries=places&key=AIzaSyCnlSN1LUkE4I7vIRZQXbVma1-S3Bh3Zzk&language=en'></script>

  <!-- add to homescreen for ios -->
  <!-- <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black" /> -->
</head>

<body>
  <app-root></app-root>
</body>

</html>
