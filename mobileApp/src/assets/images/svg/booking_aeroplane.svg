<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="414" height="310" viewBox="0 0 414 310">
  <defs>
    <linearGradient id="linear-gradient" x1="0.962" x2="0.975" y2="1.203" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#a5b5db"/>
      <stop offset="1" stop-color="#080b12"/>
    </linearGradient>
    <clipPath id="clip-path">
      <rect id="Rectangle_8753" data-name="Rectangle 8753" width="132.233" height="101.353" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_87526" data-name="Path 87526" d="M54.016,601.229A16.46,16.46,0,0,1,75.192,610.9c3.179,8.519-5.386,32.648-5.386,32.648S47.528,630.919,44.349,622.4A16.461,16.461,0,0,1,54.016,601.229Zm8.95,23.98a9.515,9.515,0,1,0-12.24-5.59A9.514,9.514,0,0,0,62.966,625.209Z" transform="translate(-43.306 -600.186)" fill="#ecaf4c"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <path id="Path_87529" data-name="Path 87529" d="M158.376,362a16.461,16.461,0,0,1,21.176,9.667c3.179,8.519-5.387,32.648-5.387,32.648s-22.277-12.624-25.456-21.139A16.462,16.462,0,0,1,158.376,362Zm8.945,23.98a9.515,9.515,0,1,0-12.24-5.59A9.514,9.514,0,0,0,167.321,385.979Z" transform="translate(-147.666 -360.956)" fill="#ecaf4c"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <rect id="Rectangle_8771" data-name="Rectangle 8771" width="561.86" height="419.407" transform="translate(0 -85)" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <path id="Path_87535" data-name="Path 87535" d="M622.961,718.15h-99.79a20.408,20.408,0,0,0-11.322,3.428l-7.621,5.085a1.427,1.427,0,0,0,.591,2.6l93.367,13.3a9.183,9.183,0,0,0,7.135-2l19.829-16.3a3.45,3.45,0,0,0-2.189-6.116Z" transform="translate(-503.592 -718.15)" fill="#093f6b"/>
    </clipPath>
    <clipPath id="clip-path-6">
      <path id="Path_87540" data-name="Path 87540" d="M372.68,564.3,139.975,600.372a4.963,4.963,0,0,0-3.573,2.751h0c-2.154,4.176.486,9.781,4.621,9.822l198.954,2.068a22.437,22.437,0,0,0,17.588-8.172l8.027-9.266a17.362,17.362,0,0,0,3.735-13.268l-.255-2.23a5.36,5.36,0,0,1,2.137-5.154l4.343-2.78a5.36,5.36,0,0,0,2.137-5.154h0c-.336-2.948-2.606-5.073-5.009-4.691Z" transform="translate(-135.633 -564.253)" fill="#d4ebff"/>
    </clipPath>
    <clipPath id="clip-path-7">
      <path id="Path_87563" data-name="Path 87563" d="M490.082,556.416c-26.721-16.36-103.583-65.806-124.814-78.13s-63.038-18.237-73.648-.979c0,0-16.552,2.8-8.756,24.115S373.375,578.649,431,608.8c28.922,15.133,56.633,29.454,81.883,34.122,45.774,8.467,80.511,2.033,86.134-1.263,8.716-5.114-82.213-68.887-108.934-85.242Z" transform="translate(-280.776 -466.555)" fill="#fff"/>
    </clipPath>
    <clipPath id="clip-path-8">
      <path id="Path_87569" data-name="Path 87569" d="M542.562,577.475l310.906,20.576a7.107,7.107,0,0,1,5.021,2.513h0c3.307,4.025.458,9.8-4.992,10.112l-262.114,15.26a36.13,36.13,0,0,1-24.109-6.99l-11.623-8.722a15.557,15.557,0,0,1-6.423-13l.087-2.247a5.266,5.266,0,0,0-3.394-5l-6.04-2.484a5.264,5.264,0,0,1-3.394-5h0a5.5,5.5,0,0,1,6.081-5.015Z" transform="translate(-536.483 -577.455)" fill="#d4ebff"/>
    </clipPath>
    <clipPath id="clip-path-9">
      <path id="Path_87574" data-name="Path 87574" d="M763.679,718.15h99.79a20.408,20.408,0,0,1,11.322,3.428l7.621,5.085a1.427,1.427,0,0,1-.591,2.6l-93.367,13.3a9.183,9.183,0,0,1-7.135-2l-19.829-16.3a3.45,3.45,0,0,1,2.189-6.116Z" transform="translate(-760.223 -718.15)" fill="#d4ebff"/>
    </clipPath>
    <clipPath id="clip-path-10">
      <path id="Path_87513" data-name="Path 87513" d="M909.472,398.195c-.8-2.924.993-6.093,4.207-7.993a2.87,2.87,0,0,0,1.143-3.791c-4.9-9.377-18.5-13.83-32.384-10.029a38.117,38.117,0,0,0-5.556,1.991,3.849,3.849,0,0,1-4.247-.707,5.328,5.328,0,0,0-1.334-.938c-2.267-1.108-6.173,1.9-8.51,4.062a5.209,5.209,0,0,1-4.5,1.284,17.983,17.983,0,0,0-7.993.406c-7.4,2.026,6.218,9.322-3.962,12.251a4.544,4.544,0,0,0-2.974,2.648c-2.307,5.762,9.418,7.452,13.76,5.807a2.527,2.527,0,0,1,3.31,1.615l.03.09a4.639,4.639,0,0,0,5.9,2.964c.191-.06.381-.12.577-.171,5-1.369,9.879.552,10.907,4.293a5.7,5.7,0,0,1-.261,3.656,2.561,2.561,0,0,0,2.342,3.53c4.423,0,10.029-1.434,13.113-6.524a10.177,10.177,0,0,1,13.77-3.375c3.079,1.76,5.837,2.823,6.975,1.675a11.015,11.015,0,0,0,2.808-4.709,2.91,2.91,0,0,0-2.071-3.621,6.617,6.617,0,0,1-5.055-4.413Z" transform="translate(-843.067 -375.025)" fill="#addc72"/>
    </clipPath>
    <clipPath id="clip-path-11">
      <path id="Path_87523" data-name="Path 87523" d="M914.139,316.76a10.209,10.209,0,0,0-9.9,10.506c.166,5.636,10.726,17.5,10.726,17.5s9.849-12.456,9.683-18.093a10.209,10.209,0,0,0-10.506-9.9Zm.466,15.866a5.9,5.9,0,1,1,5.727-6.073A5.9,5.9,0,0,1,914.606,332.626Z" transform="translate(-904.231 -316.76)" fill="#ecaf4c"/>
    </clipPath>
    <clipPath id="clip-iPhone_XR_XS_Max_11_91">
      <rect width="414" height="310"/>
    </clipPath>
  </defs>
  <g id="iPhone_XR_XS_Max_11_91" data-name="iPhone XR, XS Max, 11 – 91" clip-path="url(#clip-iPhone_XR_XS_Max_11_91)">
    <rect width="414" height="310" fill="#eee"/>
    <g id="OS_HomeIndicator_OnLgiht" data-name="OS/HomeIndicator/OnLgiht" transform="translate(414 310) rotate(180)">
      <path id="_-boundary" data-name="-boundary" d="M0,0H414V311H0Z" fill="url(#linear-gradient)"/>
    </g>
    <g id="POST_1" data-name="POST 1" transform="translate(-150.265 -295.357)">
      <g id="Group_14300" data-name="Group 14300" transform="translate(30.65 316.76)">
        <g id="Group_14253" data-name="Group 14253" transform="translate(9.81 266.475)">
          <g id="Group_14237" data-name="Group 14237">
            <path id="Path_87474" data-name="Path 87474" d="M226.361,829.249C277.4,909.366,380.445,848.992,420.984,903.3c-32.912-70.909-136.808.614-186.914-83.788-45.05-75.883-147.487-31.47-186.48-10.789,50.211-21.063,137.23-44.686,178.771,20.524Z" transform="translate(-47.59 -776.891)" fill="#fff" opacity="0.5"/>
          </g>
        </g>
        <g id="Group_14297" data-name="Group 14297" transform="translate(4 -4)">
          <g id="Group_14266" data-name="Group 14266" transform="translate(295 21.597)" clip-path="url(#clip-path)">
            <g id="Group_14265" data-name="Group 14265" transform="translate(5.856 8.378)">
              <g id="Group_14262" data-name="Group 14262" transform="translate(0 110.705)">
                <path id="Path_87524" data-name="Path 87524" d="M54.016,601.229A16.46,16.46,0,0,1,75.192,610.9c3.179,8.519-5.386,32.648-5.386,32.648S47.528,630.919,44.349,622.4A16.461,16.461,0,0,1,54.016,601.229Zm8.95,23.98a9.515,9.515,0,1,0-12.24-5.59A9.514,9.514,0,0,0,62.966,625.209Z" transform="translate(-43.306 -600.186)" fill="#ecaf4c"/>
                <g id="Group_14261" data-name="Group 14261" clip-path="url(#clip-path-2)">
                  <path id="Path_87525" data-name="Path 87525" d="M61.567,594.977s-7.312-9.9-19.214-5.461-11.041,17.5-10.074,20.093h0l-8.057,1.522-3.383-26.9,42.134-7.867-1.407,18.621" transform="translate(-31.236 -587.385)" fill="#fff"/>
                  <circle id="Ellipse_162" data-name="Ellipse 162" cx="9.949" cy="9.949" r="9.949" transform="translate(2.851 11.626) rotate(-21.97)" fill="#e8d556"/>
                </g>
              </g>
              <g id="Group_14264" data-name="Group 14264" transform="translate(48.293)">
                <path id="Path_87527" data-name="Path 87527" d="M158.376,362a16.461,16.461,0,0,1,21.176,9.667c3.179,8.519-5.387,32.648-5.387,32.648s-22.277-12.624-25.456-21.139A16.462,16.462,0,0,1,158.376,362Zm8.945,23.98a9.515,9.515,0,1,0-12.24-5.59A9.514,9.514,0,0,0,167.321,385.979Z" transform="translate(-147.666 -360.956)" fill="#fff"/>
                <g id="Group_14263" data-name="Group 14263" clip-path="url(#clip-path-3)">
                  <path id="Path_87528" data-name="Path 87528" d="M165.917,355.747s-7.312-9.9-19.214-5.461-11.041,17.5-10.074,20.093h0l-8.057,1.522L125.19,345l42.134-7.867-1.407,18.621" transform="translate(-135.591 -348.155)" fill="#fff"/>
                  <circle id="Ellipse_163" data-name="Ellipse 163" cx="9.949" cy="9.949" r="9.949" transform="matrix(0.364, -0.931, 0.931, 0.364, 2.888, 22.782)" fill="#def0ff"/>
                </g>
              </g>
              <path id="Path_87530" data-name="Path 87530" d="M99.643,708.211a3.489,3.489,0,1,1,4.489,2.05A3.488,3.488,0,0,1,99.643,708.211Z" transform="translate(-73.453 -544.985)" fill="#fff"/>
              <path id="Path_87531" data-name="Path 87531" d="M205.313,472.481a3.489,3.489,0,1,1,4.489,2.05A3.488,3.488,0,0,1,205.313,472.481Z" transform="translate(-130.224 -418.341)" fill="#fff"/>
              <path id="Path_87532" data-name="Path 87532" d="M133.022,583.984a1.178,1.178,0,0,0-1.509-.708h0a1.183,1.183,0,0,0-.708,1.513h0a1.187,1.187,0,0,0,1.509.708h.009a1.182,1.182,0,0,0,.694-1.509Zm4.831-.6a1.173,1.173,0,0,0,.657-1.527h0a1.171,1.171,0,0,0-1.532-.652h0a1.179,1.179,0,0,0-.657,1.527h0a1.186,1.186,0,0,0,1.5.671h0a.072.072,0,0,0,.028-.014Zm-10.375,2.554a1.182,1.182,0,0,0-1.485-.75h0a1.179,1.179,0,0,0-.75,1.485h0a1.175,1.175,0,0,0,1.485.75h0a.444.444,0,0,0,.046-.018h0a1.184,1.184,0,0,0,.708-1.472Zm15.836-4.864a1.178,1.178,0,0,0,.592-1.56h0a1.167,1.167,0,0,0-1.55-.588h0a1.174,1.174,0,0,0-.6,1.555h0a1.163,1.163,0,0,0,1.481.62h0s.051-.018.069-.028Zm5.359-2.545a1.171,1.171,0,0,0,.523-1.578h0a1.173,1.173,0,0,0-1.578-.523h0a1.171,1.171,0,0,0-.523,1.578h0a1.169,1.169,0,0,0,1.458.578h0c.037-.014.079-.037.116-.055Zm5.238-2.809h0a1.175,1.175,0,0,0,.435-1.606h0a1.175,1.175,0,0,0-1.606-.435h0a1.173,1.173,0,0,0-.43,1.606h0a1.181,1.181,0,0,0,1.43.523h0a1.168,1.168,0,0,0,.171-.083Zm5.072-3.128a1.169,1.169,0,0,0,.329-1.629h0a1.165,1.165,0,0,0-1.629-.329h0a1.174,1.174,0,0,0-.329,1.629h0a1.18,1.18,0,0,0,1.388.453h0a1.01,1.01,0,0,0,.241-.13Zm4.84-3.512a1.172,1.172,0,0,0,.185-1.652h0a1.172,1.172,0,0,0-1.652-.185h0a1.183,1.183,0,0,0-.194,1.657h0a1.175,1.175,0,0,0,1.337.366h0a1.224,1.224,0,0,0,.319-.18Zm4.5-3.984a1.181,1.181,0,0,0,0-1.666h0a1.181,1.181,0,0,0-1.666,0h0a1.187,1.187,0,0,0,0,1.671h0a1.179,1.179,0,0,0,1.245.268h0A1.209,1.209,0,0,0,168.322,565.095Zm3.961-4.586h0a1.18,1.18,0,0,0-.259-1.643h0a1.172,1.172,0,0,0-1.643.255h0a1.181,1.181,0,0,0,.259,1.643h0a1.164,1.164,0,0,0,1.106.153h0A1.185,1.185,0,0,0,172.283,560.509Zm3.059-5.312h0a1.179,1.179,0,0,0-.62-1.546h0a1.171,1.171,0,0,0-1.541.625h0a1.174,1.174,0,0,0,.62,1.541h0a1.169,1.169,0,0,0,.875.018h0a1.18,1.18,0,0,0,.671-.639Zm1.527-6.034A1.169,1.169,0,0,0,175.8,547.9h0a1.174,1.174,0,0,0-1.268,1.078h0a1.174,1.174,0,0,0,1.078,1.268h0a1.238,1.238,0,0,0,.509-.069h0a1.174,1.174,0,0,0,.759-1.013Zm-.671-6.238h0a1.174,1.174,0,0,0-1.467-.782h0a1.176,1.176,0,0,0-.782,1.467h0a1.174,1.174,0,0,0,1.467.782h0a.545.545,0,0,0,.069-.018h0a1.188,1.188,0,0,0,.717-1.448Zm-3.063-3.938a1.174,1.174,0,0,0,.287-1.638h0a1.184,1.184,0,0,0-1.643-.287h0a1.187,1.187,0,0,0-.278,1.643h0a1.172,1.172,0,0,0,1.374.421h0a.961.961,0,0,0,.259-.143Zm-3.785-4.475a1.179,1.179,0,0,0-.176-1.652h0a1.178,1.178,0,0,0-1.657.185h0a1.177,1.177,0,0,0,.185,1.652h0a1.18,1.18,0,0,0,1.148.185h0a1.194,1.194,0,0,0,.5-.37Zm-4.831-3.332a1.178,1.178,0,0,0-.528-1.578h0a1.175,1.175,0,0,0-1.573.523h0a1.17,1.17,0,0,0,.528,1.578h0a1.158,1.158,0,0,0,.935.046h0a1.172,1.172,0,0,0,.639-.574Zm-5.373-2.392a1.175,1.175,0,0,0-.722-1.5h0a1.183,1.183,0,0,0-1.5.731h0a1.178,1.178,0,0,0,.727,1.495h0a1.147,1.147,0,0,0,.8-.009h0a1.16,1.16,0,0,0,.7-.713Zm-5.6-1.8a1.184,1.184,0,0,0-.819-1.453h0a1.176,1.176,0,0,0-1.448.824h0a1.177,1.177,0,0,0,.824,1.444h0a1.163,1.163,0,0,0,.722-.032h0a1.162,1.162,0,0,0,.722-.787Zm-5.687-1.523a1.174,1.174,0,0,0-.833-1.439h0a1.172,1.172,0,0,0-1.439.833h0a1.172,1.172,0,0,0,.833,1.439h0a1.215,1.215,0,0,0,.717-.032h0a1.173,1.173,0,0,0,.727-.8ZM142.18,523.9a1.181,1.181,0,0,0-.764-1.481h0a1.183,1.183,0,0,0-1.481.764h0a1.167,1.167,0,0,0,.764,1.472h0a1.163,1.163,0,0,0,.773-.018h0a1.163,1.163,0,0,0,.708-.74Zm-5.548-1.967a1.177,1.177,0,0,0-.551-1.564h0a1.173,1.173,0,0,0-1.569.555h0a1.178,1.178,0,0,0,.555,1.569h0a1.2,1.2,0,0,0,.921.042h0a1.145,1.145,0,0,0,.648-.6Zm-5.113-2.883a1.175,1.175,0,0,0-.1-1.666h0a1.172,1.172,0,0,0-1.657.1h0a1.168,1.168,0,0,0,.1,1.657h0a1.171,1.171,0,0,0,1.194.222h0a1.16,1.16,0,0,0,.467-.319Zm-3.864-4.378a1.177,1.177,0,0,0,.6-1.555h0a1.18,1.18,0,0,0-1.555-.6h0a1.186,1.186,0,0,0-.6,1.555h0a1.182,1.182,0,0,0,1.49.625h0a.4.4,0,0,0,.065-.032Zm-.565-6.816a1.171,1.171,0,0,0-1.212-1.134h0a1.168,1.168,0,0,0-1.138,1.208h0a1.176,1.176,0,0,0,1.212,1.143h0a1.131,1.131,0,0,0,.375-.079h0a1.188,1.188,0,0,0,.768-1.138Zm.907-5.28a1.171,1.171,0,0,0-.662-1.522h0a1.169,1.169,0,0,0-1.522.662h0a1.175,1.175,0,0,0,.666,1.527h0a1.163,1.163,0,0,0,.838.009h0a1.161,1.161,0,0,0,.685-.676Zm2.869-4.637a1.173,1.173,0,0,0-.12-1.657h0a1.177,1.177,0,0,0-1.657.116h0a1.179,1.179,0,0,0,.125,1.661h0a1.164,1.164,0,0,0,1.18.213h0a1.176,1.176,0,0,0,.477-.333Zm4.146-3.776a1.175,1.175,0,0,0,.245-1.643h0a1.177,1.177,0,0,0-1.643-.254h0a1.185,1.185,0,0,0-.245,1.647h0a1.172,1.172,0,0,0,1.356.4h0a1.244,1.244,0,0,0,.287-.157Zm22-10.306a1.171,1.171,0,0,0-1.388-.912h0a1.169,1.169,0,0,0-.916,1.384h0a1.176,1.176,0,0,0,1.384.921h0c.065-.018.12-.032.176-.051h0a1.177,1.177,0,0,0,.745-1.337Zm-17.15,7.275a1.17,1.17,0,0,0,.481-1.587h0a1.174,1.174,0,0,0-1.592-.486h0a1.179,1.179,0,0,0-.481,1.592h0a1.176,1.176,0,0,0,1.448.546h0C139.76,491.183,139.811,491.164,139.862,491.137Zm11.435-5.863a1.174,1.174,0,0,0-1.472-.768h0a1.17,1.17,0,0,0-.768,1.472h0a1.172,1.172,0,0,0,1.472.768h0a.547.547,0,0,0,.065-.019h0a1.184,1.184,0,0,0,.708-1.458Zm-6.215,3.429a1.181,1.181,0,0,0,.648-1.536h0a1.177,1.177,0,0,0-1.536-.643h0a1.183,1.183,0,0,0-.648,1.536h0a1.177,1.177,0,0,0,1.5.657h0l.032-.014Z" transform="translate(-87.055 -426.483)" fill="#fff"/>
            </g>
          </g>
          <g id="Group_14296" data-name="Group 14296" transform="translate(45.85 44.031)" clip-path="url(#clip-path-4)">
            <g id="Group_14295" data-name="Group 14295" transform="translate(14.949 42.72)">
              <g id="Group_14293" data-name="Group 14293">
                <g id="Group_14271" data-name="Group 14271" transform="translate(174.044 119.509)">
                  <rect id="Rectangle_8754" data-name="Rectangle 8754" width="4.019" height="35.066" transform="translate(29.119 15.309)" fill="#303645"/>
                  <path id="Path_87533" data-name="Path 87533" d="M498.133,701.017,439.195,690.7a3.667,3.667,0,0,1-3.035-3.614v-10.5a3.671,3.671,0,0,1,4.3-3.614L499.4,683.29a3.666,3.666,0,0,1,3.035,3.614v10.5A3.671,3.671,0,0,1,498.133,701.017Z" transform="translate(-436.16 -672.916)" fill="#303645"/>
                  <g id="Group_14270" data-name="Group 14270" transform="translate(5.484 44.341)">
                    <g id="Group_14267" data-name="Group 14267" transform="translate(29.078)">
                      <ellipse id="Ellipse_164" data-name="Ellipse 164" cx="8.067" cy="11.137" rx="8.067" ry="11.137" fill="#303645"/>
                      <rect id="Rectangle_8755" data-name="Rectangle 8755" width="7.494" height="22.268" transform="translate(8.067)" fill="#303645"/>
                      <ellipse id="Ellipse_165" data-name="Ellipse 165" cx="8.067" cy="11.137" rx="8.067" ry="11.137" transform="translate(7.494)" fill="#303645"/>
                      <ellipse id="Ellipse_166" data-name="Ellipse 166" cx="4.442" cy="6.127" rx="4.442" ry="6.127" transform="translate(11.119 5.009)" fill="#303645"/>
                      <ellipse id="Ellipse_167" data-name="Ellipse 167" cx="3.695" cy="5.096" rx="3.695" ry="5.096" transform="translate(11.119 6.04)" fill="#303645"/>
                    </g>
                    <g id="Group_14268" data-name="Group 14268">
                      <ellipse id="Ellipse_168" data-name="Ellipse 168" cx="8.067" cy="11.137" rx="8.067" ry="11.137" fill="#303645"/>
                      <rect id="Rectangle_8756" data-name="Rectangle 8756" width="7.494" height="22.268" transform="translate(8.067)" fill="#303645"/>
                      <ellipse id="Ellipse_169" data-name="Ellipse 169" cx="8.067" cy="11.137" rx="8.067" ry="11.137" transform="translate(7.494)" fill="#303645"/>
                      <ellipse id="Ellipse_170" data-name="Ellipse 170" cx="4.442" cy="6.127" rx="4.442" ry="6.127" transform="translate(11.119 5.009)" fill="#303645"/>
                      <ellipse id="Ellipse_171" data-name="Ellipse 171" cx="3.695" cy="5.096" rx="3.695" ry="5.096" transform="translate(11.119 6.04)" fill="#303645"/>
                    </g>
                    <g id="Group_14269" data-name="Group 14269" transform="translate(13.876)">
                      <ellipse id="Ellipse_172" data-name="Ellipse 172" cx="8.067" cy="11.137" rx="8.067" ry="11.137" fill="#303645"/>
                      <rect id="Rectangle_8757" data-name="Rectangle 8757" width="7.494" height="22.268" transform="translate(8.067)" fill="#303645"/>
                      <ellipse id="Ellipse_173" data-name="Ellipse 173" cx="8.067" cy="11.137" rx="8.067" ry="11.137" transform="translate(7.494)" fill="#303645"/>
                      <ellipse id="Ellipse_174" data-name="Ellipse 174" cx="4.442" cy="6.127" rx="4.442" ry="6.127" transform="translate(11.119 5.009)" fill="#303645"/>
                      <ellipse id="Ellipse_175" data-name="Ellipse 175" cx="3.695" cy="5.096" rx="3.695" ry="5.096" transform="translate(11.119 6.04)" fill="#303645"/>
                    </g>
                  </g>
                  <rect id="Rectangle_8758" data-name="Rectangle 8758" width="7.592" height="23.849" rx="3.796" transform="translate(27.329 15.309)" fill="#303645"/>
                  <rect id="Rectangle_8759" data-name="Rectangle 8759" width="4.019" height="20.594" transform="translate(44.823 19.988) rotate(45)" fill="#303645"/>
                </g>
                <g id="Group_14273" data-name="Group 14273" transform="translate(213.096 145.706)">
                  <path id="Path_87534" data-name="Path 87534" d="M622.961,718.15h-99.79a20.408,20.408,0,0,0-11.322,3.428l-7.621,5.085a1.427,1.427,0,0,0,.591,2.6l93.367,13.3a9.183,9.183,0,0,0,7.135-2l19.829-16.3a3.45,3.45,0,0,0-2.189-6.116Z" transform="translate(-503.592 -718.15)" fill="#303645"/>
                  <g id="Group_14272" data-name="Group 14272" clip-path="url(#clip-path-5)">
                    <rect id="Rectangle_8760" data-name="Rectangle 8760" width="131.775" height="10.795" transform="translate(131.774 3.869) rotate(180)" fill="#425a72"/>
                  </g>
                </g>
                <path id="Path_87536" data-name="Path 87536" d="M693.81,632.066l60.137,39.184,35.026-99.106a6.415,6.415,0,0,0-6.052-8.554h0a63.08,63.08,0,0,0-47.2,21.231L693.81,632.072Z" transform="translate(-370.553 -507.394)" fill="#fff"/>
                <g id="Group_14276" data-name="Group 14276" transform="translate(0 56.58)">
                  <path id="Path_87537" data-name="Path 87537" d="M150.385,659.836l220.874,2.9-.365-9.6-209.2-.075a18.1,18.1,0,0,0-8,1.876l-3.869,1.9c-1.274.625-.828,2.983.568,3Z" transform="translate(-141.288 -601.629)" fill="#fff"/>
                  <g id="Group_14275" data-name="Group 14275">
                    <path id="Path_87538" data-name="Path 87538" d="M372.68,564.3,139.975,600.372a4.963,4.963,0,0,0-3.573,2.751h0c-2.154,4.176.486,9.781,4.621,9.822l198.954,2.068a22.437,22.437,0,0,0,17.588-8.172l8.027-9.266a17.362,17.362,0,0,0,3.735-13.268l-.255-2.23a5.36,5.36,0,0,1,2.137-5.154l4.343-2.78a5.36,5.36,0,0,0,2.137-5.154h0c-.336-2.948-2.606-5.073-5.009-4.691Z" transform="translate(-135.633 -564.253)" fill="#e6edf4"/>
                    <g id="Group_14274" data-name="Group 14274" clip-path="url(#clip-path-6)">
                      <path id="Path_87539" data-name="Path 87539" d="M107.283,603.516,376.822,571.89l-1.963-17.16L105.32,586.356Z" transform="translate(-122.875 -560.245)" fill="#fff"/>
                    </g>
                  </g>
                  <path id="Path_87541" data-name="Path 87541" d="M363.432,624.332l20.785,25.551c.649.793-.069,2.033-.892,1.546l-15.619-9.266a9.456,9.456,0,0,1-4.257-6.9l-1.378-10.141c-.127-.938.8-1.477,1.355-.793Z" transform="translate(-230.929 -589.407)" fill="#2d3442"/>
                </g>
                <g id="Group_14280" data-name="Group 14280" transform="translate(84.057)">
                  <path id="Path_87542" data-name="Path 87542" d="M490.082,556.416c-26.721-16.36-103.583-65.806-124.814-78.13s-63.038-18.237-73.648-.979c0,0-16.552,2.8-8.756,24.115S373.375,578.649,431,608.8c28.922,15.133,56.633,29.454,81.883,34.122,45.774,8.467,80.511,2.033,86.134-1.263,8.716-5.114-82.213-68.887-108.934-85.242Z" transform="translate(-280.776 -466.555)" fill="#fff"/>
                  <g id="Group_14279" data-name="Group 14279" clip-path="url(#clip-path-7)">
                    <path id="Path_87543" data-name="Path 87543" d="M575.574,703.177c-126.5-42.161-243.981-114.726-357.294-201.352l13.882-5.594a63.687,63.687,0,0,1,30.682-4.251l72.779,7.87a68.2,68.2,0,0,1,26.785,8.751L601.7,646.759l-26.136,56.425Z" transform="translate(-254.473 -477.1)" fill="#d4ebff" opacity="0.5"/>
                    <path id="Path_87544" data-name="Path 87544" d="M575.574,703.177c-126.5-42.161-243.981-114.726-357.294-201.352l13.882-5.594a63.687,63.687,0,0,1,30.682-4.251l72.779,7.87a68.2,68.2,0,0,1,26.785,8.751L601.7,646.759l-26.136,56.425Z" transform="translate(-254.473 -477.1)" fill="#e4e6e9" opacity="0.5"/>
                    <path id="Path_87545" data-name="Path 87545" d="M515.463,738.011C376.385,688.588,245.618,608.35,118.69,513.865l14.774-5.085a70.252,70.252,0,0,1,33.034-2.89l79.231,11.594a83.151,83.151,0,0,1,29.443,10.28l265.9,153.516-25.6,56.731Z" transform="translate(-212.559 -482.781)" fill="#ecaf4c"/>
                    <path id="Path_87546" data-name="Path 87546" d="M515.463,738.011C376.385,688.588,245.618,608.35,118.69,513.865l14.774-5.085a70.252,70.252,0,0,1,33.034-2.89l79.231,11.594a83.151,83.151,0,0,1,29.443,10.28l265.9,153.516-25.6,56.731Z" transform="translate(-212.559 -482.781)" fill="#fff"/>
                    <path id="Path_87547" data-name="Path 87547" d="M503.323,758.371C364.245,708.948,233.478,628.71,106.55,534.225l14.774-5.085a70.252,70.252,0,0,1,33.034-2.89l79.231,11.594a83.152,83.152,0,0,1,29.443,10.28l265.9,153.516-25.6,56.732Z" transform="translate(-207.45 -491.349)" fill="#ebb34d"/>
                    <g id="Group_14277" data-name="Group 14277" transform="translate(10.838 5.262)">
                      <path id="Path_87548" data-name="Path 87548" d="M308.785,476.7c-3.811.683-7.482,2.079-9.3,4.876a55.736,55.736,0,0,1,5.982-.822l3.307-4.054Z" transform="translate(-299.49 -476.087)" fill="#303645"/>
                      <path id="Path_87549" data-name="Path 87549" d="M353.683,484.583l-2.722-6.967c-1.32-.3-2.774-.562-4.372-.776l-1.378,5.345a71.022,71.022,0,0,1,8.473,2.4Z" transform="translate(-318.732 -476.146)" fill="#303645"/>
                      <path id="Path_87550" data-name="Path 87550" d="M358.756,479.11l2.212,6.787s5.7,2.56,8.658,4.118c0,0,1.679-7.291-10.876-10.9Z" transform="translate(-324.431 -477.101)" fill="#303645"/>
                      <path id="Path_87551" data-name="Path 87551" d="M331.255,476.1c-1.558-.162-3.232-.284-5.044-.359a62.511,62.511,0,0,0-8.774.127l-4.036,4.286a69,69,0,0,1,16.916,1.2l.944-5.264Z" transform="translate(-305.344 -475.641)" fill="#303645"/>
                    </g>
                    <g id="Group_14278" data-name="Group 14278" transform="translate(71.484 19.415)">
                      <path id="Path_87552" data-name="Path 87552" d="M406.532,509.856h0a2.322,2.322,0,0,1-2.322-2.322V502.4a2.322,2.322,0,0,1,2.322-2.322h0a2.322,2.322,0,0,1,2.322,2.322v5.131A2.322,2.322,0,0,1,406.532,509.856Z" transform="translate(-404.21 -500.08)" fill="#303645"/>
                      <path id="Path_87553" data-name="Path 87553" d="M421.842,518.506h0a2.322,2.322,0,0,1-2.322-2.322v-5.131a2.322,2.322,0,0,1,2.322-2.322h0a2.322,2.322,0,0,1,2.322,2.322v5.131A2.322,2.322,0,0,1,421.842,518.506Z" transform="translate(-410.654 -503.721)" fill="#303645"/>
                      <path id="Path_87554" data-name="Path 87554" d="M437.142,527.156h0a2.322,2.322,0,0,1-2.322-2.322V519.7a2.322,2.322,0,0,1,2.322-2.322h0a2.322,2.322,0,0,1,2.322,2.322v5.131A2.322,2.322,0,0,1,437.142,527.156Z" transform="translate(-417.093 -507.361)" fill="#303645"/>
                      <path id="Path_87555" data-name="Path 87555" d="M452.452,535.816h0a2.322,2.322,0,0,1-2.322-2.322v-5.131a2.322,2.322,0,0,1,2.322-2.322h0a2.322,2.322,0,0,1,2.322,2.322v5.131A2.322,2.322,0,0,1,452.452,535.816Z" transform="translate(-423.536 -511.006)" fill="#303645"/>
                      <path id="Path_87556" data-name="Path 87556" d="M467.752,544.466h0a2.322,2.322,0,0,1-2.322-2.322v-5.131a2.322,2.322,0,0,1,2.322-2.322h0a2.322,2.322,0,0,1,2.322,2.322v5.131A2.322,2.322,0,0,1,467.752,544.466Z" transform="translate(-429.976 -514.646)" fill="#303645"/>
                      <path id="Path_87557" data-name="Path 87557" d="M483.062,553.116h0a2.322,2.322,0,0,1-2.322-2.322v-5.131a2.322,2.322,0,0,1,2.322-2.322h0a2.322,2.322,0,0,1,2.322,2.322v5.131A2.322,2.322,0,0,1,483.062,553.116Z" transform="translate(-436.419 -518.287)" fill="#303645"/>
                      <path id="Path_87558" data-name="Path 87558" d="M498.362,561.766h0a2.322,2.322,0,0,1-2.322-2.322v-5.131a2.322,2.322,0,0,1,2.322-2.322h0a2.322,2.322,0,0,1,2.322,2.322v5.131A2.322,2.322,0,0,1,498.362,561.766Z" transform="translate(-442.859 -521.927)" fill="#303645"/>
                      <rect id="Rectangle_8761" data-name="Rectangle 8761" width="4.639" height="9.77" rx="2.319" transform="translate(62.048 35.084)" fill="#303645"/>
                      <path id="Path_87559" data-name="Path 87559" d="M528.982,579.076h0a2.322,2.322,0,0,1-2.322-2.322v-5.131a2.322,2.322,0,0,1,2.322-2.322h0a2.322,2.322,0,0,1,2.322,2.322v5.131A2.322,2.322,0,0,1,528.982,579.076Z" transform="translate(-455.746 -529.213)" fill="#303645"/>
                      <path id="Path_87560" data-name="Path 87560" d="M544.282,587.726h0a2.322,2.322,0,0,1-2.322-2.322v-5.131a2.322,2.322,0,0,1,2.322-2.322h0a2.322,2.322,0,0,1,2.322,2.322V585.4A2.322,2.322,0,0,1,544.282,587.726Z" transform="translate(-462.185 -532.853)" fill="#303645"/>
                      <path id="Path_87561" data-name="Path 87561" d="M559.592,596.386h0a2.322,2.322,0,0,1-2.322-2.322v-5.131a2.322,2.322,0,0,1,2.322-2.322h0a2.322,2.322,0,0,1,2.322,2.322v5.131A2.322,2.322,0,0,1,559.592,596.386Z" transform="translate(-468.628 -536.498)" fill="#303645"/>
                      <path id="Path_87562" data-name="Path 87562" d="M574.892,605.036h0a2.322,2.322,0,0,1-2.322-2.322v-5.131a2.322,2.322,0,0,1,2.322-2.322h0a2.322,2.322,0,0,1,2.322,2.322v5.131A2.322,2.322,0,0,1,574.892,605.036Z" transform="translate(-475.068 -540.138)" fill="#303645"/>
                      <rect id="Rectangle_8762" data-name="Rectangle 8762" width="4.639" height="9.77" rx="2.319" transform="translate(106.369 60.143)" fill="#303645"/>
                    </g>
                  </g>
                </g>
                <g id="Group_14281" data-name="Group 14281" transform="translate(71.965 91.245)">
                  <path id="Path_87564" data-name="Path 87564" d="M352.429,690.317h0a14.406,14.406,0,0,0-3.828-19.042c-5.357-3.932-11.42-8.253-17.206-12.272-7.6-5.282-11.287.915-16.065,8.849h0a16.021,16.021,0,0,0,6.341,22.482c6.26,3.249,6.156,2.641,12.22,5.548a14.388,14.388,0,0,0,18.538-5.565Z" transform="translate(-282.259 -637.97)" fill="#303645"/>
                  <path id="Path_87565" data-name="Path 87565" d="M328.147,675.824h0a20.922,20.922,0,0,0-5.56-27.665c-6.521-4.784-14.878-11.281-17.432-13.274-.521-.405-1.048-.8-1.61-1.158a23.283,23.283,0,0,0-32.391,7.668h0a23.3,23.3,0,0,0,9.249,32.686c9.058,4.7,12,5.618,20.768,9.822a20.921,20.921,0,0,0,26.97-8.073Z" transform="translate(-263.23 -626.639)" fill="#ebb34d"/>
                  <ellipse id="Ellipse_176" data-name="Ellipse 176" cx="22.447" cy="23.356" rx="22.447" ry="23.356" transform="matrix(1, -0.011, 0.011, 1, 0, 0.509)" fill="#fff"/>
                  <path id="Path_87585" data-name="Path 87585" d="M18.909,0C29.351,0,37.817,8.808,37.817,19.673S29.351,39.346,18.909,39.346,0,30.538,0,19.673,8.466,0,18.909,0Z" transform="matrix(1, -0.011, 0.011, 1, 1.826, 4.178)" fill="#303745"/>
                  <path id="Path_87588" data-name="Path 87588" d="M7.2,0a7.353,7.353,0,0,1,7.2,7.494,7.353,7.353,0,0,1-7.2,7.494A7.353,7.353,0,0,1,0,7.494,7.353,7.353,0,0,1,7.2,0Z" transform="matrix(1, -0.011, 0.011, 1, 23.652, 20.888)" fill="#fff" opacity="0.44"/>
                  <path id="Path_87566" data-name="Path 87566" d="M313.138,668.035a5.269,5.269,0,1,1-5.328-5.415A5.375,5.375,0,0,1,313.138,668.035Z" transform="translate(-277.873 -640.318)" fill="#fff"/>
                </g>
                <g id="Group_14284" data-name="Group 14284" transform="translate(232.144 64.225)">
                  <path id="Path_87567" data-name="Path 87567" d="M933.623,647.066l-198.288,13.87-.7-9.6,188.1-10.471a15.406,15.406,0,0,1,7.4,1.477l3.683,1.708c1.21.562,1.066,2.936-.191,3.023Z" transform="translate(-619.882 -604.132)" fill="#fff"/>
                  <g id="Group_14283" data-name="Group 14283">
                    <path id="Path_87568" data-name="Path 87568" d="M542.562,577.475l310.906,20.576a7.107,7.107,0,0,1,5.021,2.513h0c3.307,4.025.458,9.8-4.992,10.112l-262.114,15.26a36.13,36.13,0,0,1-24.109-6.99l-11.623-8.722a15.557,15.557,0,0,1-6.423-13l.087-2.247a5.266,5.266,0,0,0-3.394-5l-6.04-2.484a5.264,5.264,0,0,1-3.394-5h0a5.5,5.5,0,0,1,6.081-5.015Z" transform="translate(-536.483 -577.455)" fill="#e6edf4"/>
                    <g id="Group_14282" data-name="Group 14282" clip-path="url(#clip-path-8)">
                      <rect id="Rectangle_8763" data-name="Rectangle 8763" width="359.234" height="17.275" transform="translate(-14.211 -4.564) rotate(2.18)" fill="#fff"/>
                    </g>
                  </g>
                  <path id="Path_87570" data-name="Path 87570" d="M788.232,625.247l22.412,28.678c.695.892-.475,2-1.459,1.378l-18.712-11.745a8.818,8.818,0,0,1-4.274-7.523l.1-10.239a1.081,1.081,0,0,1,1.928-.556Z" transform="translate(-641.581 -597.393)" fill="#272d3a"/>
                  <path id="Path_87571" data-name="Path 87571" d="M861.322,619.207l22.412,28.679c.695.892-.475,2-1.459,1.378l-18.712-11.745A8.818,8.818,0,0,1,859.29,630l.1-10.239a1.081,1.081,0,0,1,1.928-.556Z" transform="translate(-672.343 -594.851)" fill="#272d3a"/>
                  <path id="Path_87572" data-name="Path 87572" d="M936.032,616.047l22.412,28.679c.695.892-.469,2-1.459,1.378l-18.712-11.745A8.819,8.819,0,0,1,934,626.836l.1-10.239a1.081,1.081,0,0,1,1.929-.556Z" transform="translate(-703.786 -593.521)" fill="#272d3a"/>
                </g>
                <g id="Group_14286" data-name="Group 14286" transform="translate(361.719 145.706)">
                  <path id="Path_87573" data-name="Path 87573" d="M763.679,718.15h99.79a20.408,20.408,0,0,1,11.322,3.428l7.621,5.085a1.427,1.427,0,0,1-.591,2.6l-93.367,13.3a9.183,9.183,0,0,1-7.135-2l-19.829-16.3a3.45,3.45,0,0,1,2.189-6.116Z" transform="translate(-760.223 -718.15)" fill="#e6edf4"/>
                  <g id="Group_14285" data-name="Group 14285" clip-path="url(#clip-path-9)">
                    <rect id="Rectangle_8764" data-name="Rectangle 8764" width="131.775" height="10.795" transform="translate(-8.949 -6.926)" fill="#fff"/>
                  </g>
                </g>
                <g id="Group_14291" data-name="Group 14291" transform="translate(279.127 119.509)">
                  <path id="Path_87589" data-name="Path 87589" d="M0,0H4.019V35.066H0Z" transform="translate(37.157 50.381) rotate(180)" fill="#303645"/>
                  <path id="Path_87575" data-name="Path 87575" d="M621.907,701.017l58.938-10.32a3.666,3.666,0,0,0,3.035-3.614v-10.5a3.671,3.671,0,0,0-4.3-3.614l-58.938,10.32a3.667,3.667,0,0,0-3.035,3.614v10.5A3.671,3.671,0,0,0,621.907,701.017Z" transform="translate(-617.61 -672.916)" fill="#2b313f"/>
                  <g id="Group_14290" data-name="Group 14290" transform="translate(8.079 44.341)">
                    <g id="Group_14287" data-name="Group 14287" transform="translate(29.078)">
                      <ellipse id="Ellipse_179" data-name="Ellipse 179" cx="8.067" cy="11.137" rx="8.067" ry="11.137" fill="#2b313f"/>
                      <rect id="Rectangle_8766" data-name="Rectangle 8766" width="7.494" height="22.268" transform="translate(8.067)" fill="#2b313f"/>
                      <ellipse id="Ellipse_180" data-name="Ellipse 180" cx="8.067" cy="11.137" rx="8.067" ry="11.137" transform="translate(7.494)" fill="#2b313f"/>
                      <ellipse id="Ellipse_181" data-name="Ellipse 181" cx="4.442" cy="6.127" rx="4.442" ry="6.127" transform="translate(11.119 5.009)" fill="#2b313f"/>
                      <ellipse id="Ellipse_182" data-name="Ellipse 182" cx="3.695" cy="5.096" rx="3.695" ry="5.096" transform="translate(11.119 6.04)" fill="#2b313f"/>
                    </g>
                    <g id="Group_14288" data-name="Group 14288">
                      <ellipse id="Ellipse_183" data-name="Ellipse 183" cx="8.067" cy="11.137" rx="8.067" ry="11.137" fill="#2b313f"/>
                      <rect id="Rectangle_8767" data-name="Rectangle 8767" width="7.494" height="22.268" transform="translate(8.067)" fill="#2b313f"/>
                      <ellipse id="Ellipse_184" data-name="Ellipse 184" cx="8.067" cy="11.137" rx="8.067" ry="11.137" transform="translate(7.494)" fill="#2b313f"/>
                      <ellipse id="Ellipse_185" data-name="Ellipse 185" cx="4.442" cy="6.127" rx="4.442" ry="6.127" transform="translate(11.119 5.009)" fill="#2b313f"/>
                      <ellipse id="Ellipse_186" data-name="Ellipse 186" cx="3.695" cy="5.096" rx="3.695" ry="5.096" transform="translate(11.119 6.04)" fill="#2b313f"/>
                    </g>
                    <g id="Group_14289" data-name="Group 14289" transform="translate(13.876)">
                      <ellipse id="Ellipse_187" data-name="Ellipse 187" cx="8.067" cy="11.137" rx="8.067" ry="11.137" fill="#2b313f"/>
                      <rect id="Rectangle_8768" data-name="Rectangle 8768" width="7.494" height="22.268" transform="translate(8.067)" fill="#2b313f"/>
                      <ellipse id="Ellipse_188" data-name="Ellipse 188" cx="8.067" cy="11.137" rx="8.067" ry="11.137" transform="translate(7.494)" fill="#2b313f"/>
                      <ellipse id="Ellipse_189" data-name="Ellipse 189" cx="4.442" cy="6.127" rx="4.442" ry="6.127" transform="translate(11.119 5.009)" fill="#2b313f"/>
                      <ellipse id="Ellipse_190" data-name="Ellipse 190" cx="3.695" cy="5.096" rx="3.695" ry="5.096" transform="translate(11.119 6.04)" fill="#2b313f"/>
                    </g>
                  </g>
                  <rect id="Rectangle_8769" data-name="Rectangle 8769" width="7.592" height="23.849" rx="3.796" transform="translate(38.946 39.163) rotate(180)" fill="#2b313f"/>
                  <rect id="Rectangle_8770" data-name="Rectangle 8770" width="4.019" height="20.594" transform="translate(36.004 34.553) rotate(135)" fill="#2b313f"/>
                </g>
                <g id="Group_14292" data-name="Group 14292" transform="translate(276.519 75.508)">
                  <path id="Path_87576" data-name="Path 87576" d="M712.418,669.3h0a14.406,14.406,0,0,0-3.828-19.042c-5.357-3.932-11.42-8.253-17.206-12.272a16.023,16.023,0,0,0-22.864,4.894h0a16.021,16.021,0,0,0,6.341,22.482c6.26,3.249,12.955,6.6,19.019,9.5a14.388,14.388,0,0,0,18.538-5.565Z" transform="translate(-635.461 -613.009)" fill="#303645"/>
                  <path id="Path_87577" data-name="Path 87577" d="M688.148,652.591h0a20.922,20.922,0,0,0-5.56-27.665c-7.789-5.716-16.592-11.988-25-17.831a23.281,23.281,0,0,0-33.225,7.112h0a23.281,23.281,0,0,0,9.214,32.669c9.092,4.72,18.822,9.585,27.63,13.806a20.907,20.907,0,0,0,26.941-8.09Z" transform="translate(-616.438 -599.46)" fill="#ebb34d"/>
                  <ellipse id="Ellipse_191" data-name="Ellipse 191" cx="22.447" cy="23.356" rx="22.447" ry="23.356" transform="matrix(1, -0.011, 0.011, 1, 0, 0.509)" fill="#fff"/>
                  <path id="Path_87584" data-name="Path 87584" d="M18.909,0C29.351,0,37.817,8.808,37.817,19.673S29.351,39.346,18.909,39.346,0,30.538,0,19.673,8.466,0,18.909,0Z" transform="matrix(1, -0.011, 0.011, 1, 1.849, 4.166)" fill="#303645"/>
                  <ellipse id="Ellipse_193" data-name="Ellipse 193" cx="7.204" cy="7.494" rx="7.204" ry="7.494" transform="matrix(1, -0.011, 0.011, 1, 23.646, 20.888)" fill="#8f8f8f"/>
                  <path id="Path_87578" data-name="Path 87578" d="M666.338,640.845a5.269,5.269,0,1,1-5.328-5.415A5.375,5.375,0,0,1,666.338,640.845Z" transform="translate(-631.079 -613.137)" fill="#fff"/>
                </g>
              </g>
              <g id="Group_14294" data-name="Group 14294" transform="translate(124.347 -83.154)">
                <path id="Path_87579" data-name="Path 87579" d="M970.362,870.854a6.512,6.512,0,0,1-1.124-9.386,5.2,5.2,0,0,0,.825-4.07c-.488-4.062-8.755-6.4-18.456-5.232a39.907,39.907,0,0,0-6.415,1.3,2.1,2.1,0,0,0,.109-.947c-.295-2.45-5.089-3.9-10.859-3.367,1.263-.977,1.953-2.1,1.818-3.228-.219-1.8-2.492-3.11-5.665-3.54,2.189-1.532,3.4-3.275,3.195-4.983-.4-3.3-5.93-5.467-13.2-5.535,1.233-.922,1.9-1.953,1.78-2.967-.307-2.551-5.5-4.024-11.6-3.287-.383.046-.762.1-1.132.16a3.879,3.879,0,0,0,.564-2.786c-.85-4-9.289-5.6-18.848-3.573a40.759,40.759,0,0,0-5.072,1.414,2.031,2.031,0,0,0,.067-.808c-.24-2-6.339-4.941-11.545-5.185a29.152,29.152,0,0,1-10.261-2.285C851.778,807,835.01,800.32,835.01,800.32l157.344,86.747s-9.79-7.454-21.992-16.213Z" transform="translate(-620.601 -726.913)" fill="#fff" opacity="0.14"/>
                <path id="Path_87581" data-name="Path 87581" d="M661.625,825.879a4.3,4.3,0,0,1-.745-6.2,3.419,3.419,0,0,0,.547-2.69c-.324-2.685-5.783-4.23-12.2-3.46a26.379,26.379,0,0,0-4.238.859,1.392,1.392,0,0,0,.072-.627c-.194-1.616-3.363-2.58-7.176-2.227a2.486,2.486,0,0,0,1.2-2.134c-.143-1.187-1.646-2.054-3.746-2.34,1.448-1.014,2.248-2.163,2.109-3.3-.265-2.18-3.919-3.611-8.725-3.658a2.3,2.3,0,0,0,1.178-1.961c-.2-1.688-3.637-2.66-7.665-2.172-.253.029-.505.067-.749.105a2.568,2.568,0,0,0,.375-1.839c-.56-2.643-6.137-3.7-12.454-2.361a27.508,27.508,0,0,0-3.35.934,1.359,1.359,0,0,0,.046-.534c-.16-1.322-4.188-3.266-7.631-3.426a19.236,19.236,0,0,1-6.781-1.511c-8.435-3.666-19.517-8.081-19.517-8.081l103.974,57.322s-6.469-4.929-14.534-10.716Z" transform="translate(-557.396 -766.717)" fill="#fff" opacity="0.14"/>
                <path id="Path_87582" data-name="Path 87582" d="M611.787,876.642a.575.575,0,0,1-.316-.093L537.33,829.657a.592.592,0,1,1,.631-1L612.1,875.547a.594.594,0,0,1,.185.817.6.6,0,0,1-.5.278Z" transform="translate(-537.054 -795.27)" fill="#fff"/>
                <path id="Path_87583" data-name="Path 87583" d="M895.275,910.182a.575.575,0,0,1-.316-.093L852.28,883.1a.592.592,0,0,1,.631-1l42.679,26.992a.594.594,0,0,1,.185.817A.6.6,0,0,1,895.275,910.182Z" transform="translate(-785.232 -909.217)" fill="#fff"/>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_14301" data-name="Group 14301" transform="translate(283.257 53.403)">
      <g id="Group_14257" data-name="Group 14257" transform="translate(0 4.478)">
        <path id="Path_87504" data-name="Path 87504" d="M843.189,424.467l-23.153,14.8L800.55,368.153l23.152-14.8Z" transform="translate(-800.55 -339.48)" fill="#fff"/>
        <path id="Path_87505" data-name="Path 87505" d="M866.217,424.467l27.46.928L874.2,354.283l-27.465-.933Z" transform="translate(-823.572 -339.48)" fill="#d4ebff"/>
        <path id="Path_87506" data-name="Path 87506" d="M944.139,396.807l-23.158,14.8L901.5,340.493l23.153-14.8Z" transform="translate(-850.877 -325.69)" fill="#fff"/>
        <path id="Path_87507" data-name="Path 87507" d="M967.167,396.807l27.46.928-19.482-71.112-27.465-.933Z" transform="translate(-873.9 -325.69)" fill="#d4ebff"/>
        <path id="Path_87508" data-name="Path 87508" d="M944.136,397.362l-23.313,14.227L902.76,345.676l23.318-14.226Z" transform="translate(-851.506 -328.562)" fill="#93a1c3"/>
        <path id="Path_87509" data-name="Path 87509" d="M967.318,397.362l24.572.015L973.9,331.731l-24.642-.281Z" transform="translate(-874.688 -328.562)" fill="#5c6a8a"/>
        <path id="Path_87510" data-name="Path 87510" d="M866.358,425.027l27.315.356L875.61,359.471l-27.31-.351Z" transform="translate(-824.355 -342.356)" fill="#5c6a8a"/>
        <path id="Path_87511" data-name="Path 87511" d="M845.858,425.027l-20.62,13.489L807.18,372.6,827.8,359.12Z" transform="translate(-803.855 -342.356)" fill="#93a1c3"/>
        <g id="Group_14256" data-name="Group 14256" transform="translate(21.321 24.74)">
          <g id="Group_14255" data-name="Group 14255">
            <path id="Path_87512" data-name="Path 87512" d="M909.472,398.195c-.8-2.924.993-6.093,4.207-7.993a2.87,2.87,0,0,0,1.143-3.791c-4.9-9.377-18.5-13.83-32.384-10.029a38.117,38.117,0,0,0-5.556,1.991,3.849,3.849,0,0,1-4.247-.707,5.328,5.328,0,0,0-1.334-.938c-2.267-1.108-6.173,1.9-8.51,4.062a5.209,5.209,0,0,1-4.5,1.284,17.983,17.983,0,0,0-7.993.406c-7.4,2.026,6.218,9.322-3.962,12.251a4.544,4.544,0,0,0-2.974,2.648c-2.307,5.762,9.418,7.452,13.76,5.807a2.527,2.527,0,0,1,3.31,1.615l.03.09a4.639,4.639,0,0,0,5.9,2.964c.191-.06.381-.12.577-.171,5-1.369,9.879.552,10.907,4.293a5.7,5.7,0,0,1-.261,3.656,2.561,2.561,0,0,0,2.342,3.53c4.423,0,10.029-1.434,13.113-6.524a10.177,10.177,0,0,1,13.77-3.375c3.079,1.76,5.837,2.823,6.975,1.675a11.015,11.015,0,0,0,2.808-4.709,2.91,2.91,0,0,0-2.071-3.621,6.617,6.617,0,0,1-5.055-4.413Z" transform="translate(-843.067 -375.025)" fill="#addc72"/>
            <g id="Group_14254" data-name="Group 14254" clip-path="url(#clip-path-10)">
              <rect id="Rectangle_8751" data-name="Rectangle 8751" width="26.242" height="48.14" transform="translate(6.497 6.171) rotate(-15.32)" fill="#85b66a" opacity="0.63"/>
              <rect id="Rectangle_8752" data-name="Rectangle 8752" width="10.977" height="44.71" transform="translate(58.314 -3.327) rotate(-15.32)" fill="#85b66a" opacity="0.63"/>
            </g>
          </g>
          <path id="Path_87514" data-name="Path 87514" d="M982.355,418.49a2.71,2.71,0,1,0,1.986-2.813A2.459,2.459,0,0,0,982.355,418.49Z" transform="translate(-912.474 -395.234)" fill="#addc72"/>
          <path id="Path_87515" data-name="Path 87515" d="M871.295,380.17a2.71,2.71,0,1,0,1.986-2.813A2.459,2.459,0,0,0,871.295,380.17Z" transform="translate(-857.107 -376.131)" fill="#addc72"/>
          <path id="Path_87516" data-name="Path 87516" d="M952.262,393.979c-.441-1.61-3.5-2.176-6.82-1.269s-5.666,2.959-5.225,4.568c.231.847,1.188,1.4,2.522,1.61a2.066,2.066,0,0,1,1.8,1.715.858.858,0,0,0,.03.155c.3,1.073,2.332,1.454,4.553.847s3.781-1.976,3.485-3.049a1.5,1.5,0,0,0-1.073-.933.761.761,0,0,1-.291-1.269A2.531,2.531,0,0,0,952.262,393.979Z" transform="translate(-891.473 -383.626)" fill="#fff"/>
          <path id="Path_87517" data-name="Path 87517" d="M901.667,409.312c-2.472,2.116-4.047,6.354-6,6.083s-3.721-8.67-9.377-8.194-9.152.486-11.328-2.547-9.964-4.939-4.8-9.182,8.58-11.9,14.227-8.475,17.847-3.129,20-1.64-4.709,6.87-3.636,9.8,11.6,3.55,11.243,5.667,6.569,1.875,5.21,4.784-11.829.532-15.535,3.7Z" transform="translate(-855.707 -380.064)" fill="#85b66a" opacity="0.58"/>
          <path id="Path_87518" data-name="Path 87518" d="M903.254,394.765c-6.033,1.926-10.41-1.459-12.958.552s-8.64,7.206-7.186,9.247,8.149,1.294,12.913,2.407c2.728.637,4.368,8.359,7.873,6.73s11.85-6.79,8.339-8.64-8.1-1.379-8.008-2.743,3.094-8.846-.968-7.547Z" transform="translate(-862.919 -384.793)" fill="#85b66a"/>
          <path id="Path_87519" data-name="Path 87519" d="M947.531,383.318c-1.845-1.359.567-4.649,4.428-4.107,1.675.236,7,2.693,5.16,4.674S949.186,384.536,947.531,383.318Z" transform="translate(-894.831 -377.082)" fill="#85b66a" opacity="0.58"/>
        </g>
      </g>
      <g id="Group_14259" data-name="Group 14259" transform="translate(51.992)">
        <path id="Path_87520" data-name="Path 87520" d="M914.139,316.76a10.209,10.209,0,0,0-9.9,10.506c.166,5.636,10.726,17.5,10.726,17.5s9.849-12.456,9.683-18.093a10.209,10.209,0,0,0-10.506-9.9Zm.466,15.866a5.9,5.9,0,1,1,5.727-6.073A5.9,5.9,0,0,1,914.606,332.626Z" transform="translate(-904.231 -316.76)" fill="#ebb34d"/>
        <g id="Group_14258" data-name="Group 14258" clip-path="url(#clip-path-11)">
          <path id="Path_87521" data-name="Path 87521" d="M899.44,317.8s1.885-7.4,9.763-7.627,10.43,7.472,10.481,9.187h0l4.985-1.008L920.321,302.1l-26.041,5.326,5.16,10.365" transform="translate(-899.27 -309.451)" fill="#fff"/>
          <path id="Path_87522" data-name="Path 87522" d="M925.723,331.255a6.171,6.171,0,1,1-6.349-5.987A6.173,6.173,0,0,1,925.723,331.255Z" transform="translate(-908.794 -321)" fill="#e8d556"/>
        </g>
      </g>
    </g>
  </g>
</svg>
