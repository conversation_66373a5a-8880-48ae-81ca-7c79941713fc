<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60.518" height="73.062" viewBox="0 0 60.518 73.062">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" y1="0.039" x2="0.5" y2="2.22" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#3f3ced" stop-opacity="0"/>
      <stop offset="0.28" stop-color="#3c3de4" stop-opacity="0.278"/>
      <stop offset="0.73" stop-color="#3541cc" stop-opacity="0.729"/>
      <stop offset="1" stop-color="#2f43bb"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#1b2641"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="0.505" y1="0.338" x2="0.433" y2="2.582" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#005a01" stop-opacity="0"/>
      <stop offset="1" stop-color="#005a01"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="0.501" y1="0.502" x2="0.821" y2="1.724" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-6" x1="0.489" y1="0.642" x2="0.372" y2="2.146" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-7" x1="0.635" y1="0.849" x2="0.437" y2="-0.065" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0"/>
      <stop offset="1" stop-color="#1b2641"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="0.5" y1="0.172" x2="0.5" y2="1.155" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffc738"/>
      <stop offset="0.188" stop-color="#fdbf3c"/>
      <stop offset="0.484" stop-color="#f7a848"/>
      <stop offset="0.849" stop-color="#ed835b"/>
      <stop offset="1" stop-color="#e97264"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="0.5" y1="0.021" x2="0.5" y2="1.317" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffd945"/>
      <stop offset="0.304" stop-color="#ffcd3e"/>
      <stop offset="0.856" stop-color="#ffad2b"/>
      <stop offset="1" stop-color="#ffa325"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="0.5" y1="0.039" x2="0.5" y2="2.22" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e87264" stop-opacity="0"/>
      <stop offset="1" stop-color="#ff7044"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="0.484" y1="0.304" x2="0.37" y2="-1.119" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffd945" stop-opacity="0"/>
      <stop offset="1" stop-color="#fbed21"/>
    </linearGradient>
    <linearGradient id="linear-gradient-23" y1="0.021" y2="1.317" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-30" x1="0.484" y1="0.304" x2="0.37" y2="-1.119" xlink:href="#linear-gradient-14"/>
    <linearGradient id="linear-gradient-43" x1="0.334" y1="0.268" x2="0.864" y2="0.97" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-44" x1="0.329" y1="0.26" x2="0.859" y2="0.961" xlink:href="#linear-gradient-11"/>
    <linearGradient id="linear-gradient-45" x1="0.33" y1="0.252" x2="0.86" y2="0.953" xlink:href="#linear-gradient-11"/>
    <clipPath id="clip-path">
      <path id="Path_80822" data-name="Path 80822" d="M15.927,6.044a9.882,9.882,0,1,0,9.882,9.882A9.887,9.887,0,0,0,15.927,6.044Zm0,1.62a8.262,8.262,0,1,1-8.262,8.262A8.266,8.266,0,0,1,15.927,7.664Z" transform="translate(-6.044 -6.044)" clip-rule="evenodd"/>
    </clipPath>
    <filter id="Union_4" x="19.556" y="31.548" width="27.212" height="27.676" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-color="#b05d01"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_61222" x="24.225" y="31.548" width="22.543" height="20.493" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-color="#b05d01"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="money-bag" transform="translate(-5.197 7.998)">
    <path id="Path_86741" data-name="Path 86741" d="M403.523,392.676h-5.281a.373.373,0,0,1-.372-.372v-3.969a.373.373,0,0,1,.372-.373h5.281a.373.373,0,0,1,.373.373V392.3A.373.373,0,0,1,403.523,392.676Z" transform="translate(-341.257 -338.877)" fill="url(#linear-gradient)"/>
    <path id="Path_86745" data-name="Path 86745" d="M403.523,356.676h-5.281a.373.373,0,0,1-.372-.372v-3.969a.373.373,0,0,1,.372-.373h5.281a.373.373,0,0,1,.373.373V356.3A.373.373,0,0,1,403.523,356.676Z" transform="translate(-341.257 -307.591)" fill="url(#linear-gradient)"/>
    <path id="Path_86771" data-name="Path 86771" d="M105.75,117.407l-1.889-9.906A15.669,15.669,0,0,0,88.47,94.767H81.383A15.669,15.669,0,0,0,65.992,107.5l-1.071,5.616a59.726,59.726,0,0,1-5.135,15.59,9.8,9.8,0,0,0-1.026,4.805,10.086,10.086,0,0,0,9.628,9.42l.11,0a8.585,8.585,0,0,1,6.06,2.528,9.83,9.83,0,0,0,15.057-1.251,3.04,3.04,0,0,1,2.508-1.277h.017a8.575,8.575,0,0,0,1.26-.093,3.037,3.037,0,0,1,2.2.536,9.834,9.834,0,0,0,14.54-12.28A55.629,55.629,0,0,1,105.75,117.407Z" transform="translate(-46.542 -84.072)" fill="url(#linear-gradient-3)"/>
    <circle id="Ellipse_120" data-name="Ellipse 120" cx="9.833" cy="9.833" r="9.833" transform="translate(12.209 39.195)" fill="url(#linear-gradient-4)"/>
    <circle id="Ellipse_121" data-name="Ellipse 121" cx="9.833" cy="9.833" r="9.833" transform="translate(25.189 44.659)" fill="url(#linear-gradient-5)"/>
    <circle id="Ellipse_122" data-name="Ellipse 122" cx="9.833" cy="9.833" r="9.833" transform="translate(44.893 41.438)" fill="url(#linear-gradient-6)"/>
    <path id="Path_86772" data-name="Path 86772" d="M124.736,149.689a9.812,9.812,0,0,0-.94-4.934,55.627,55.627,0,0,1-4.388-13.69l-1.889-9.906a15.621,15.621,0,0,0-3.341-7.076,15.638,15.638,0,0,0-9.993-3.6H97.1a15.669,15.669,0,0,0-15.391,12.734l-1.071,5.616a59.723,59.723,0,0,1-5.135,15.59,9.8,9.8,0,0,0-1.026,4.805,9.6,9.6,0,0,0,1.977,5.374,9.648,9.648,0,0,0,5.593,1.988l.11,0a8.585,8.585,0,0,1,6.06,2.528,9.83,9.83,0,0,0,15.057-1.251,3.041,3.041,0,0,1,2.508-1.277h.017a8.575,8.575,0,0,0,1.26-.093,3.037,3.037,0,0,1,2.2.536,9.833,9.833,0,0,0,15.48-7.346Z" transform="translate(-60.2 -97.731)" opacity="0.4" fill="url(#linear-gradient-7)"/>
    <path id="Path_86775" data-name="Path 86775" d="M200.685,12.408h-9.659l-4.395-8.425A2.084,2.084,0,0,1,188.1.97L190.98.442a27.031,27.031,0,0,1,9.749,0l2.878.528a2.084,2.084,0,0,1,1.472,3.013Z" transform="translate(-157.471 -1.713)" fill="url(#linear-gradient-3)"/>
    <path id="Path_86776" data-name="Path 86776" d="M239.376,12.408h-3.213L234.7,3.983c-.216-1.244.031-2.76.489-3.013l.957-.528a3.187,3.187,0,0,1,3.243,0l.957.528c.459.253.705,1.769.489,3.013Z" transform="translate(-199.386 -1.713)" fill="url(#linear-gradient-3)"/>
    <path id="Path_86777" data-name="Path 86777" d="M213.42,1.821a.539.539,0,0,1-.2.789,4.81,4.81,0,0,1-4.362,0l-1.212-.615a4.81,4.81,0,0,0-4.362,0l-1.21.615a4.811,4.811,0,0,1-4.362,0l-1.267-.645a.529.529,0,0,1,.128-.989l.012,0,2.878-.529a27.07,27.07,0,0,1,9.749,0l2.878.529a2.061,2.061,0,0,1,1.327.847Z" transform="translate(-165.956 -1.716)" fill="#9ba0b1"/>
    <path id="Path_86779" data-name="Path 86779" d="M215.483,86.953a38.622,38.622,0,0,1-5.7-.385.944.944,0,1,1,.28-1.868,40.437,40.437,0,0,0,10.839,0,.944.944,0,1,1,.28,1.868A38.622,38.622,0,0,1,215.483,86.953Z" transform="translate(-177.099 -75.314)" fill="url(#linear-gradient-10)"/>
    <path id="Path_86781" data-name="Path 86781" d="M26.463,478.714H5.908A.711.711,0,0,1,5.2,478v-3.291A.711.711,0,0,1,5.908,474H26.463a.711.711,0,0,1,.711.711V478A.711.711,0,0,1,26.463,478.714Z" transform="translate(0 -413.65)" fill="url(#linear-gradient-11)"/>
    <path id="Path_86782" data-name="Path 86782" d="M41.793,478.714H26.457a.618.618,0,0,1-.618-.618v-3.478a.618.618,0,0,1,.618-.618H41.793a.618.618,0,0,1,.618.618V478.1A.618.618,0,0,1,41.793,478.714Z" transform="translate(-17.939 -413.65)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86783" data-name="Path 86783" d="M71.761,478.714H66.481a.373.373,0,0,1-.373-.373v-3.969a.373.373,0,0,1,.373-.373h5.281a.373.373,0,0,1,.373.373v3.969A.373.373,0,0,1,71.761,478.714Z" transform="translate(-52.935 -413.65)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86784" data-name="Path 86784" d="M10.645,478.38a.711.711,0,0,0-.711.711v3.291a.711.711,0,0,0,.013.133.734.734,0,0,0,.078,0H30.579a.711.711,0,0,0,.711-.711v-3.291a.711.711,0,0,0-.013-.133.734.734,0,0,0-.078,0Z" transform="translate(-4.117 -417.456)" fill="url(#linear-gradient-14)"/>
    <path id="Path_86785" data-name="Path 86785" d="M26.463,442.714H5.908A.711.711,0,0,1,5.2,442v-3.291A.711.711,0,0,1,5.908,438H26.463a.711.711,0,0,1,.711.711V442A.711.711,0,0,1,26.463,442.714Z" transform="translate(0 -382.363)" fill="url(#linear-gradient-11)"/>
    <path id="Path_86786" data-name="Path 86786" d="M41.793,442.713H26.457a.618.618,0,0,1-.618-.618v-3.478a.618.618,0,0,1,.618-.618H41.793a.618.618,0,0,1,.618.618V442.1A.618.618,0,0,1,41.793,442.713Z" transform="translate(-17.939 -382.362)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86787" data-name="Path 86787" d="M71.761,442.714H66.481a.373.373,0,0,1-.373-.372v-3.969a.373.373,0,0,1,.373-.372h5.281a.373.373,0,0,1,.373.372v3.969A.373.373,0,0,1,71.761,442.714Z" transform="translate(-52.935 -382.363)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86788" data-name="Path 86788" d="M10.645,442.38a.711.711,0,0,0-.711.711v3.291a.711.711,0,0,0,.013.133.7.7,0,0,0,.078,0H30.579a.711.711,0,0,0,.711-.711v-3.291a.711.711,0,0,0-.013-.133.7.7,0,0,0-.078,0Z" transform="translate(-4.117 -386.17)" fill="url(#linear-gradient-14)"/>
    <path id="Path_86789" data-name="Path 86789" d="M26.463,406.714H5.908A.711.711,0,0,1,5.2,406v-3.291A.711.711,0,0,1,5.908,402H26.463a.711.711,0,0,1,.711.711V406A.711.711,0,0,1,26.463,406.714Z" transform="translate(0 -351.077)" fill="url(#linear-gradient-11)"/>
    <path id="Path_86790" data-name="Path 86790" d="M41.793,406.714H26.457a.618.618,0,0,1-.618-.618v-3.478a.618.618,0,0,1,.618-.618H41.793a.618.618,0,0,1,.618.618V406.1A.618.618,0,0,1,41.793,406.714Z" transform="translate(-17.939 -351.077)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86791" data-name="Path 86791" d="M71.761,406.714H66.481a.373.373,0,0,1-.373-.372v-3.969a.373.373,0,0,1,.373-.372h5.281a.373.373,0,0,1,.373.372v3.969A.372.372,0,0,1,71.761,406.714Z" transform="translate(-52.935 -351.077)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86792" data-name="Path 86792" d="M10.645,406.38a.711.711,0,0,0-.711.711v3.291a.711.711,0,0,0,.013.133.742.742,0,0,0,.078,0H30.579a.711.711,0,0,0,.711-.711v-3.291a.712.712,0,0,0-.013-.133.734.734,0,0,0-.078,0Z" transform="translate(-4.117 -354.884)" fill="url(#linear-gradient-14)"/>
    <path id="Path_86793" data-name="Path 86793" d="M26.463,370.714H5.908A.711.711,0,0,1,5.2,370v-3.291A.711.711,0,0,1,5.908,366H26.463a.711.711,0,0,1,.711.711V370A.711.711,0,0,1,26.463,370.714Z" transform="translate(0 -319.791)" fill="url(#linear-gradient-23)"/>
    <path id="Path_86794" data-name="Path 86794" d="M41.793,370.713H26.457a.618.618,0,0,1-.618-.618v-3.478a.618.618,0,0,1,.618-.618H41.793a.618.618,0,0,1,.618.618V370.1A.618.618,0,0,1,41.793,370.713Z" transform="translate(-17.939 -319.79)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86795" data-name="Path 86795" d="M71.761,370.714H66.481a.373.373,0,0,1-.373-.372v-3.969a.373.373,0,0,1,.373-.372h5.281a.373.373,0,0,1,.373.372v3.969A.372.372,0,0,1,71.761,370.714Z" transform="translate(-52.935 -319.791)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86796" data-name="Path 86796" d="M10.645,370.38a.711.711,0,0,0-.711.711v3.291a.711.711,0,0,0,.013.133.7.7,0,0,0,.078,0H30.579a.711.711,0,0,0,.711-.711v-3.291a.712.712,0,0,0-.013-.133.7.7,0,0,0-.078,0Z" transform="translate(-4.117 -323.597)" fill="url(#linear-gradient-14)"/>
    <path id="Path_86797" data-name="Path 86797" d="M26.463,334.715H5.908A.711.711,0,0,1,5.2,334v-3.291A.711.711,0,0,1,5.908,330H26.463a.711.711,0,0,1,.711.711V334A.711.711,0,0,1,26.463,334.715Z" transform="translate(0 -288.505)" fill="url(#linear-gradient-11)"/>
    <path id="Path_86798" data-name="Path 86798" d="M41.793,334.715H26.457a.618.618,0,0,1-.618-.618v-3.478a.618.618,0,0,1,.618-.618H41.793a.618.618,0,0,1,.618.618V334.1A.618.618,0,0,1,41.793,334.715Z" transform="translate(-17.939 -288.505)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86799" data-name="Path 86799" d="M71.761,334.715H66.481a.373.373,0,0,1-.373-.373v-3.969a.373.373,0,0,1,.373-.372h5.281a.373.373,0,0,1,.373.372v3.969A.373.373,0,0,1,71.761,334.715Z" transform="translate(-52.935 -288.505)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86800" data-name="Path 86800" d="M10.645,334.38a.711.711,0,0,0-.711.711v3.291a.711.711,0,0,0,.013.133.734.734,0,0,0,.078,0H30.579a.711.711,0,0,0,.711-.711v-3.291a.712.712,0,0,0-.013-.133.726.726,0,0,0-.078,0Z" transform="translate(-4.117 -292.311)" fill="url(#linear-gradient-30)"/>
    <path id="Path_86801" data-name="Path 86801" d="M26.463,298.715H5.908A.711.711,0,0,1,5.2,298v-3.291A.711.711,0,0,1,5.908,294H26.463a.711.711,0,0,1,.711.711V298A.711.711,0,0,1,26.463,298.715Z" transform="translate(0 -257.219)" fill="url(#linear-gradient-11)"/>
    <path id="Path_86802" data-name="Path 86802" d="M41.793,298.714H26.457a.618.618,0,0,1-.618-.618v-3.478a.618.618,0,0,1,.618-.618H41.793a.618.618,0,0,1,.618.618V298.1A.618.618,0,0,1,41.793,298.714Z" transform="translate(-17.939 -257.218)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86803" data-name="Path 86803" d="M71.761,298.715H66.481a.373.373,0,0,1-.373-.372v-3.969a.373.373,0,0,1,.373-.372h5.281a.373.373,0,0,1,.373.372v3.969A.373.373,0,0,1,71.761,298.715Z" transform="translate(-52.935 -257.219)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86804" data-name="Path 86804" d="M10.645,298.381a.711.711,0,0,0-.711.711v3.291a.711.711,0,0,0,.013.133.734.734,0,0,0,.078,0H30.579a.711.711,0,0,0,.711-.711v-3.291a.711.711,0,0,0-.013-.133.726.726,0,0,0-.078,0Z" transform="translate(-4.117 -261.026)" fill="url(#linear-gradient-14)"/>
    <path id="Path_86805" data-name="Path 86805" d="M320.808,478.714H300.253a.711.711,0,0,1-.711-.711v-3.291a.711.711,0,0,1,.711-.711h20.554a.711.711,0,0,1,.711.711V478A.711.711,0,0,1,320.808,478.714Z" transform="translate(-255.804 -413.65)" fill="url(#linear-gradient-11)"/>
    <path id="Path_86806" data-name="Path 86806" d="M336.138,478.714H320.8a.618.618,0,0,1-.618-.618v-3.478A.618.618,0,0,1,320.8,474h15.336a.618.618,0,0,1,.618.618V478.1A.618.618,0,0,1,336.138,478.714Z" transform="translate(-273.743 -413.65)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86807" data-name="Path 86807" d="M366.106,478.714h-5.281a.373.373,0,0,1-.372-.373v-3.969a.373.373,0,0,1,.372-.373h5.281a.373.373,0,0,1,.373.373v3.969A.373.373,0,0,1,366.106,478.714Z" transform="translate(-308.74 -413.65)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86808" data-name="Path 86808" d="M304.99,478.38a.711.711,0,0,0-.711.711v3.291a.714.714,0,0,0,.013.133.734.734,0,0,0,.078,0h20.554a.711.711,0,0,0,.711-.711v-3.291a.711.711,0,0,0-.013-.133.734.734,0,0,0-.078,0Z" transform="translate(-259.921 -417.456)" fill="url(#linear-gradient-14)"/>
    <path id="Path_86809" data-name="Path 86809" d="M320.808,442.714H300.253a.711.711,0,0,1-.711-.711v-3.291a.711.711,0,0,1,.711-.711h20.554a.711.711,0,0,1,.711.711V442A.711.711,0,0,1,320.808,442.714Z" transform="translate(-255.804 -382.363)" fill="url(#linear-gradient-11)"/>
    <path id="Path_86810" data-name="Path 86810" d="M336.138,442.713H320.8a.618.618,0,0,1-.618-.618v-3.478A.618.618,0,0,1,320.8,438h15.336a.618.618,0,0,1,.618.618V442.1A.618.618,0,0,1,336.138,442.713Z" transform="translate(-273.743 -382.362)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86811" data-name="Path 86811" d="M366.106,442.714h-5.281a.373.373,0,0,1-.372-.372v-3.969a.373.373,0,0,1,.372-.372h5.281a.373.373,0,0,1,.373.372v3.969A.373.373,0,0,1,366.106,442.714Z" transform="translate(-308.74 -382.363)" fill="url(#linear-gradient-12)"/>
    <path id="Path_86812" data-name="Path 86812" d="M304.99,442.38a.711.711,0,0,0-.711.711v3.291a.715.715,0,0,0,.013.133.7.7,0,0,0,.078,0h20.554a.711.711,0,0,0,.711-.711v-3.291a.711.711,0,0,0-.013-.133.7.7,0,0,0-.078,0Z" transform="translate(-259.921 -386.17)" fill="url(#linear-gradient-14)"/>
    <path id="Path_86813" data-name="Path 86813" d="M16.146,165.583a14.49,14.49,0,0,0-4.27,4.3.637.637,0,0,1-1.068,0,14.492,14.492,0,0,0-4.255-4.311.637.637,0,0,1,0-1.061,14.49,14.49,0,0,0,4.27-4.3.637.637,0,0,1,1.068,0,14.491,14.491,0,0,0,4.255,4.311.637.637,0,0,1,0,1.061Z" transform="translate(1.771 -154.079)" fill="url(#linear-gradient-43)"/>
    <path id="Path_86814" data-name="Path 86814" d="M98.471,42.307a10.247,10.247,0,0,0-3.02,3.038.451.451,0,0,1-.755,0A10.248,10.248,0,0,0,91.688,42.3a.451.451,0,0,1,0-.75,10.247,10.247,0,0,0,3.02-3.038.451.451,0,0,1,.755,0,10.248,10.248,0,0,0,3.009,3.049A.451.451,0,0,1,98.471,42.307Z" transform="translate(-73.75 -46.301)" fill="url(#linear-gradient-44)"/>
    <path id="Path_86815" data-name="Path 86815" d="M386.841,66.932a10.246,10.246,0,0,0-3.02,3.038.451.451,0,0,1-.755,0,10.247,10.247,0,0,0-3.009-3.049.451.451,0,0,1,0-.75,10.247,10.247,0,0,0,3.02-3.038.451.451,0,0,1,.755,0,10.249,10.249,0,0,0,3.009,3.049A.451.451,0,0,1,386.841,66.932Z" transform="translate(-329.304 -68.123)" fill="url(#linear-gradient-45)"/>
    <g id="coin" transform="translate(28.082 23.654)">
      <g id="Group_11178" data-name="Group 11178" transform="translate(2.014 2.307)">
        <ellipse id="Ellipse_100" data-name="Ellipse 100" cx="7.871" cy="7.871" rx="7.871" ry="7.871" transform="translate(0 0)" fill="#f39e09"/>
      </g>
      <path id="Path_80816" data-name="Path 80816" d="M718.8,1040.626a7.813,7.813,0,0,1,.432,1.478l-9.594,9.11a7.826,7.826,0,0,1-1.454-.509Zm-3.027-3.669a7.919,7.919,0,0,1,2.481,2.571L707.12,1050.1a7.92,7.92,0,0,1-2.44-2.609Zm-2.464-1.054a7.828,7.828,0,0,1,1.461.5l-10.593,10.057a7.826,7.826,0,0,1-.425-1.486Z" transform="translate(-701.616 -1033.387)" fill="#faa50f" fill-rule="evenodd"/>
      <g id="Group_11179" data-name="Group 11179" transform="translate(0.81 0.81)">
        <path id="Path_80817" data-name="Path 80817" d="M29.568,16.464a9.074,9.074,0,1,1-9.074,9.074A9.079,9.079,0,0,1,29.568,16.464Zm0,1.5A7.871,7.871,0,1,1,21.7,25.831,7.875,7.875,0,0,1,29.568,17.96Z" transform="translate(-20.493 -16.464)" fill="#b05d01" fill-rule="evenodd"/>
      </g>
      <g id="Group_11184" data-name="Group 11184" transform="translate(0 0)">
        <path id="Path_80818" data-name="Path 80818" d="M15.927,6.044a9.882,9.882,0,1,0,9.882,9.882A9.887,9.887,0,0,0,15.927,6.044Zm0,1.62a8.262,8.262,0,1,1-8.262,8.262A8.266,8.266,0,0,1,15.927,7.664Z" transform="translate(-6.044 -6.044)" fill="#ffd400" fill-rule="evenodd"/>
        <g id="Group_11183" data-name="Group 11183" transform="translate(0 0)" clip-path="url(#clip-path)">
          <g id="Group_11180" data-name="Group 11180" transform="matrix(0.689, 0.725, -0.725, 0.689, 17.681, 0.224)">
            <path id="Path_80819" data-name="Path 80819" d="M0,0H3.572V26H0Z" transform="translate(0 0)" fill="#ffe66d" fill-rule="evenodd"/>
          </g>
          <g id="Group_11181" data-name="Group 11181" transform="matrix(0.688, 0.725, -0.725, 0.689, 15.957, -1.531)">
            <path id="Path_80820" data-name="Path 80820" d="M0,0H1.37V26H0Z" transform="translate(0 0)" fill="#ffe66d" fill-rule="evenodd"/>
          </g>
          <g id="Group_11182" data-name="Group 11182" transform="matrix(0.688, 0.725, -0.725, 0.689, 20.965, 3.648)">
            <path id="Path_80821" data-name="Path 80821" d="M0,0H1.37V26H0Z" transform="translate(0 0)" fill="#ffe66d" fill-rule="evenodd"/>
          </g>
        </g>
      </g>
      <g id="Group_11188" data-name="Group 11188" transform="translate(0.007 3.09)">
        <g id="Group_11185" data-name="Group 11185" transform="translate(0.331 9.363)">
          <path id="Path_80823" data-name="Path 80823" d="M33.711,161.259a9.887,9.887,0,0,1-19.094,0,9.954,9.954,0,0,0,19.094,0Z" transform="translate(-14.618 -161.259)" fill="#fff5b3" fill-rule="evenodd"/>
        </g>
        <g id="Group_11186" data-name="Group 11186" transform="translate(1.441 0)">
          <path id="Path_80824" data-name="Path 80824" d="M32.161,44.816a8.264,8.264,0,1,0,9.406,0,8.436,8.436,0,1,1-9.406,0Z" transform="translate(-28.427 -44.816)" fill="#fff5b3" fill-rule="evenodd"/>
        </g>
        <g id="Group_11187" data-name="Group 11187" transform="translate(0 7.347)">
          <path id="Path_80825" data-name="Path 80825" d="M30.252,136.186q.009.2.008.4a9.878,9.878,0,0,1-19.755,0q0-.2.008-.4a9.885,9.885,0,0,0,19.738,0Z" transform="translate(-10.505 -136.186)" fill="#b05d01" fill-rule="evenodd"/>
        </g>
      </g>
    </g>
    <g id="Group_263" data-name="Group 263" transform="translate(34.307 29.584)">
      <path id="Union_4-2" data-name="Union 4" d="M.693,9.676V5.218H0L4.13.108V3.034H6.087V5.218H3.1V9.676ZM4.669,2.492V0H9.212L6.462,2.492Z" transform="translate(0 0)" fill="#b05d01"/>
      <path id="Path_61222-2" data-name="Path 61222" d="M453.1,0h4.543l-2.75,2.493H453.1Z" transform="translate(-448.432 0)" fill="#b05d01"/>
    </g>
    <g id="Group_263-2" data-name="Group 263" transform="translate(33.753 29.55)">
      <g transform="matrix(1, 0, 0, 1, -28.56, -37.55)" filter="url(#Union_4)">
        <path id="Union_4-3" data-name="Union 4" d="M.693,9.676V5.218H0L4.13.108V3.034H6.087V5.218H3.1V9.676ZM4.669,2.492V0H9.212L6.462,2.492Z" transform="translate(28.56 37.55)" fill="#ffd400"/>
      </g>
      <g transform="matrix(1, 0, 0, 1, -28.56, -37.55)" filter="url(#Path_61222)">
        <path id="Path_61222-3" data-name="Path 61222" d="M453.1,0h4.543l-2.75,2.493H453.1Z" transform="translate(-419.88 37.55)" fill="#fdd835"/>
      </g>
    </g>
  </g>
</svg>
