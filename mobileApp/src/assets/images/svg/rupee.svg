<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="353.895" height="149.588" viewBox="0 0 353.895 149.588">
  <defs>
    <linearGradient id="linear-gradient" x1="0.496" y1="1.564" x2="0.722" y2="-0.022" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#a5b5db"/>
      <stop offset="1" stop-color="#1e232e"/>
    </linearGradient>
    <filter id="Rectangle_8692" x="0" y="0" width="353.895" height="149.588" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="_Fees_to_participate_in_the_offer" x="99.001" y="80.124" width="156" height="31" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_14841" data-name="Group 14841" transform="translate(-32 -577)">
    <g id="Group_14071" data-name="Group 14071" transform="translate(26.447 398.738)">
      <g transform="matrix(1, 0, 0, 1, 5.55, 178.26)" filter="url(#Rectangle_8692)">
        <rect id="Rectangle_8692-2" data-name="Rectangle 8692" width="335.895" height="131.588" transform="translate(344.89 137.59) rotate(180)" fill="url(#linear-gradient)"/>
      </g>
      <g transform="matrix(1, 0, 0, 1, 5.55, 178.26)" filter="url(#_Fees_to_participate_in_the_offer)">
        <text id="_Fees_to_participate_in_the_offer-2" data-name="*Fees to participate in the offer" transform="translate(109 96.12)" fill="#fff" stroke="rgba(0,0,0,0)" stroke-width="1" font-size="9" font-family="Montserrat-Regular, Montserrat" letter-spacing="-0.01em"><tspan x="0" y="0">*Fees to participate in the offer</tspan></text>
      </g>
    </g>
    <g id="Group_1258" data-name="Group 1258" transform="translate(44.886 588.756)" opacity="0.25">
      <g id="Group_1257" data-name="Group 1257" transform="translate(3.114 1.147)">
        <g id="Group_1256" data-name="Group 1256" transform="translate(0 0)">
          <g id="Group_1255" data-name="Group 1255">
            <circle id="Ellipse_20" data-name="Ellipse 20" cx="20.5" cy="20.5" r="20.5" transform="translate(32 38.097)" fill="#fff"/>
            <ellipse id="Ellipse_21" data-name="Ellipse 21" cx="20" cy="20.5" rx="20" ry="20.5" transform="translate(250 38.097)" fill="#fff"/>
            <g id="Group_1254" data-name="Group 1254" transform="translate(0 0.097)">
              <g id="Group_1253" data-name="Group 1253">
                <g id="Group_1252" data-name="Group 1252">
                  <g id="Group_1251" data-name="Group 1251">
                    <g id="Group_1250" data-name="Group 1250">
                      <g id="Group_1249" data-name="Group 1249">
                        <g id="Group_1248" data-name="Group 1248">
                          <g id="Group_1246" data-name="Group 1246" transform="translate(0 0.097)">
                            <g id="Group_1245" data-name="Group 1245">
                              <g id="Group_1244" data-name="Group 1244">
                                <g id="Group_1243" data-name="Group 1243">
                                  <g id="Group_1242" data-name="Group 1242">
                                    <circle id="Ellipse_12" data-name="Ellipse 12" cx="10" cy="10" r="10" transform="translate(0 0)" fill="#fff"/>
                                    <circle id="Ellipse_19" data-name="Ellipse 19" cx="10" cy="10" r="10" transform="translate(302 0)" fill="#fff"/>
                                    <circle id="Ellipse_17" data-name="Ellipse 17" cx="10" cy="10" r="10" transform="translate(0 98)" fill="#fff"/>
                                    <circle id="Ellipse_18" data-name="Ellipse 18" cx="10" cy="10" r="10" transform="translate(302 98)" fill="#fff"/>
                                  </g>
                                </g>
                              </g>
                            </g>
                          </g>
                          <path id="Rectangle_177" data-name="Rectangle 177" d="M4.025,4.037V114.059h313.95V4.037H4.025M0,0H322V118.1H0Z" fill="#fff"/>
                        </g>
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
