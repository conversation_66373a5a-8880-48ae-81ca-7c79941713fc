<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="36.24" height="36.24" viewBox="0 0 36.24 36.24">
  <defs>
    <clipPath id="clip-path">
      <path id="path4032" d="M0-682.665H36.24v36.24H0Z" transform="translate(0 682.665)" fill="#2c3c64"/>
    </clipPath>
    <filter id="path4042" x="10.751" y="8.939" width="25.989" height="21.711" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="path4046" x="12.156" y="0.281" width="23.179" height="26.657" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="path4050" x="-4.572" y="8.939" width="25.99" height="21.711" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-3"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-3"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="path4054" x="-3.166" y="0.281" width="23.179" height="26.657" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-4"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-4"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="path4062" x="-7.691" y="17.922" width="39.992" height="19.5" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-5"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-5"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="path4074" x="2.947" y="21.623" width="18.012" height="19.5" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-6"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-6"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Package" transform="translate(0 682.665)">
    <g id="g4028" transform="translate(0 -682.665)">
      <g id="g4030" clip-path="url(#clip-path)">
        <g id="g4036" transform="translate(1.309 13.613)">
          <path id="path4038" d="M-527.1,0a1.33,1.33,0,0,1-1.325,1.326h-26.9A1.33,1.33,0,0,1-556.648,0" transform="translate(556.648)" fill="none" stroke="#2e384d" stroke-width="1.5"/>
        </g>
        <g id="g4040" transform="translate(20.501 14.939)">
          <g transform="matrix(1, 0, 0, 1, -20.5, -14.94)" filter="url(#path4042)">
            <path id="path4042-2" data-name="path4042" d="M-115.754,0V2.961h-6.489V0" transform="translate(142.74 14.94)" fill="none" stroke="#2e384d" stroke-width="1.5"/>
          </g>
        </g>
        <g id="g4044" transform="translate(21.906 6.281)">
          <g transform="matrix(1, 0, 0, 1, -21.91, -6.28)" filter="url(#path4046)">
            <path id="path4046-2" data-name="path4046" d="M0-154.426v-8.657m3.679,0v8.657" transform="translate(21.91 169.37)" fill="none" stroke="#2e384d" stroke-width="1.5"/>
          </g>
        </g>
        <g id="g4048" transform="translate(5.179 14.939)">
          <g transform="matrix(1, 0, 0, 1, -5.18, -14.94)" filter="url(#path4050)">
            <path id="path4050-2" data-name="path4050" d="M-115.753,0V2.961h-6.489V0" transform="translate(127.42 14.94)" fill="none" stroke="#2e384d" stroke-width="1.5"/>
          </g>
        </g>
        <g id="g4052" transform="translate(6.584 6.281)">
          <g transform="matrix(1, 0, 0, 1, -6.58, -6.28)" filter="url(#path4054)">
            <path id="path4054-2" data-name="path4054" d="M0-154.426v-8.657m3.679,0v8.657" transform="translate(6.58 169.37)" fill="none" stroke="#2e384d" stroke-width="1.5"/>
          </g>
        </g>
        <g id="g4056" transform="translate(7.153 0.708)">
          <path id="path4058" d="M-311.36-99.419v-2.347a3.229,3.229,0,0,0-3.226-3.226h-11.006a3.228,3.228,0,0,0-3.224,3.226v2.347m2.386,0v-2.347a.84.84,0,0,1,.838-.839h11.006a.84.84,0,0,1,.838.839v2.347" transform="translate(328.815 104.993)" fill="none" stroke="#2e384d" stroke-width="1.5"/>
        </g>
        <g id="g4060" transform="translate(1.309 24.672)">
          <g transform="matrix(1, 0, 0, 1, -1.31, -24.67)" filter="url(#path4062)">
            <path id="path4062-2" data-name="path4062" d="M0,0H21.992" transform="translate(1.31 24.67)" fill="none" stroke="#2e384d" stroke-width="1.5"/>
          </g>
        </g>
        <g id="g4064" transform="translate(1.309 6.281)">
          <path id="path4066" d="M-136.459-394.063h-6.325a1.331,1.331,0,0,1-1.325-1.326v-19.44a1.328,1.328,0,0,1,1.325-1.326h26.9c.73,0,1.325,1.326,1.325,1.326v13.148m-10.862,7.618h-5.228" transform="translate(144.109 416.155)" fill="none" stroke="#2e384d" stroke-width="1.5"/>
        </g>
        <g id="g4068" transform="translate(19.106 19.613)">
          <path id="path4070" d="M-272.237-9.779a2.239,2.239,0,0,1-.276,3.23l-2.7,2.763,1.884,5.927-1.8,1.776-3.005-5.16L-280.753,1l.964,3.2-1.427,1.392L-282.8,2.771l-.09.078c-.467.462-1.361.841-1.947.317a.9.9,0,0,1-.1-.1c-.034-.03-.068-.062-.1-.1-.529-.58-.16-1.479.3-1.95l.078-.092L-287.5-.63l1.375-1.443,3.212.929,2.209-2.644-5.188-2.953,1.756-1.816,5.942,1.822,2.731-2.733A2.236,2.236,0,0,1-272.237-9.779Z" transform="translate(287.499 10.327)" fill="none" stroke="#2e384d" stroke-width="1.5"/>
        </g>
        <g id="g4072" transform="translate(11.947 28.373)">
          <g transform="matrix(1, 0, 0, 1, -11.95, -28.37)" filter="url(#path4074)">
            <path id="path4074-2" data-name="path4074" d="M0,0H.012" transform="translate(11.95 28.37)" fill="none" stroke="#2e384d" stroke-width="1.5"/>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
