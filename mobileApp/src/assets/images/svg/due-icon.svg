<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60.927" height="60.927" viewBox="0 0 60.927 60.927">
  <defs>
    <filter id="Path_80752" x="6.5" y="8.648" width="47.582" height="47.582" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_11094" data-name="Group 11094" transform="translate(-291.5 -301.213)">
    <g id="checklist" transform="translate(292 301.713)">
      <path id="Path_80749" data-name="Path 80749" d="M63.927,33.963c0,2.557-3.141,4.664-3.77,7.021-.649,2.437.994,5.833-.24,7.965-1.253,2.167-5.024,2.427-6.782,4.185s-2.018,5.528-4.185,6.782c-2.132,1.233-5.528-.409-7.965.24-2.357.629-4.465,3.77-7.021,3.77s-4.664-3.141-7.021-3.77c-2.437-.649-5.833.994-7.965-.24-2.167-1.253-2.427-5.024-4.185-6.782S9.264,51.117,8.01,48.95c-1.233-2.132.409-5.528-.24-7.965C7.141,38.628,4,36.52,4,33.963S7.141,29.3,7.77,26.942c.649-2.437-.994-5.833.24-7.965,1.253-2.167,5.024-2.427,6.782-4.185s2.018-5.528,4.185-6.782c2.132-1.233,5.528.409,7.965-.24C29.3,7.141,31.406,4,33.963,4s4.664,3.141,7.021,3.77c2.437.649,5.833-.994,7.965.24,2.167,1.253,2.427,5.024,4.185,6.782s5.528,2.018,6.782,4.185c1.233,2.132-.409,5.528.24,7.965.629,2.357,3.77,4.465,3.77,7.021Z" transform="translate(-4 -4)" fill="#d6991d" stroke="#d6991d" stroke-width="1"/>
    </g>
    <g id="overdue_2_" data-name="overdue (2)" transform="translate(307 315.861)">
      <path id="Path_80750" data-name="Path 80750" d="M362,437h1.733v1.733H362Z" transform="translate(-341.085 -411.751)" fill="#fff"/>
      <path id="Path_80751" data-name="Path 80751" d="M362,347h1.733v3.467H362Z" transform="translate(-341.085 -326.951)" fill="#fff"/>
      <g transform="matrix(1, 0, 0, 1, -15.5, -14.65)" filter="url(#Path_80752)">
        <path id="Path_80752-2" data-name="Path 80752" d="M29.213,25.646l-5.2-8.667a2.616,2.616,0,0,0-1.531-1.166c.034-.359.052-.72.052-1.08a11.249,11.249,0,0,0-4.116-8.7L19.93,3.871l-1.42-.994L17,5.037A11.187,11.187,0,0,0,12.133,3.5V1.733h1.733V0h-5.2V1.733H10.4V3.5A11.187,11.187,0,0,0,5.535,5.037L4.023,2.877,2.6,3.871,4.116,6.033a11.265,11.265,0,0,0,10.29,19.521l-.055.092a2.6,2.6,0,0,0,2.23,3.936h10.4a2.6,2.6,0,0,0,2.23-3.936Zm-13.383-2.54a9.535,9.535,0,1,1,4.97-8.373,9.665,9.665,0,0,1-.076,1.211,2.609,2.609,0,0,0-1.172,1.035l-3.7,6.167Zm11.153,4.743h-10.4a.867.867,0,0,1-.744-1.311l5.2-8.667a.867.867,0,0,1,1.488,0l5.2,8.667a.867.867,0,0,1-.744,1.311Z" transform="translate(15.5 14.65)" fill="#fff"/>
      </g>
      <path id="Path_80753" data-name="Path 80753" d="M181.683,149.994H179.95l0,6.934h6.936v-1.733h-5.2Z" transform="translate(-169.55 -141.327)" fill="#fff"/>
    </g>
  </g>
</svg>
