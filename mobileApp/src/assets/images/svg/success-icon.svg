<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="308.654" height="276.296" viewBox="0 0 308.654 276.296">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffd400"/>
      <stop offset="1" stop-color="#f39e09"/>
    </linearGradient>
    <clipPath id="clip-path">
      <path id="Path_80822" data-name="Path 80822" d="M105.18,6.044a99.136,99.136,0,1,0,99.136,99.136A99.185,99.185,0,0,0,105.18,6.044Zm0,16.252A82.884,82.884,0,1,1,22.3,105.18,82.923,82.923,0,0,1,105.18,22.3Z" transform="translate(-6.044 -6.044)" clip-rule="evenodd"/>
    </clipPath>
    <filter id="Path_87846">
      <feOffset dy="16" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161" result="color"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur"/>
      <feComposite operator="in" in="color"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_14835" data-name="Group 14835" transform="translate(-17562.674 -2083.019)">
    <g id="Ñëîé_1" data-name="Ñëîé 1" transform="translate(17025.828 3177.079)">
      <g id="Group_14834" data-name="Group 14834" transform="translate(536.845 -1094.06)">
        <g id="Group_14831" data-name="Group 14831" transform="translate(0 0)">
          <g id="Group_14824" data-name="Group 14824" transform="translate(0 1.444)">
            <g id="Group_14811" data-name="Group 14811" transform="translate(26.906 65.702)">
              <path id="Path_88136" data-name="Path 88136" d="M617.582,630.561c.356.112.288.2.771.493a4.962,4.962,0,0,1,1.577,1.823c.981,1.683,1.1,3.751,1.433,5.625.321,1.793.946,3.7,2.6,4.665a3.428,3.428,0,0,0,1.308.378,4.621,4.621,0,0,0,.8-.311.754.754,0,0,1-.36-.093c-1.713-1-2.1-3.639-2.386-5.393-.517-3.135-1.294-6.8-5.036-7.226-.183-.021-.368-.043-.555-.062-.052.032-.1.066-.155.1Z" transform="translate(-582.936 -608.268)" fill="url(#linear-gradient)"/>
              <path id="Path_88137" data-name="Path 88137" d="M734.255,691.594a7.529,7.529,0,0,1,1.591,1.209,44.08,44.08,0,0,0,10.216,7.155,41.763,41.763,0,0,0,18.586,3.8c-.484.006-1.408-.216-2.048-.209a33.562,33.562,0,0,1-14.863-3.615,44.219,44.219,0,0,1-10.143-7.275,6.129,6.129,0,0,0-3.249-1.477,2.2,2.2,0,0,0-.635.213c.139.054.318.119.547.2Z" transform="translate(-680.716 -659.393)" fill="url(#linear-gradient)"/>
              <path id="Path_88138" data-name="Path 88138" d="M672.583,691.588c-1,.452-2,.915-3,1.372a7.084,7.084,0,0,1-2.511,1.005,4.626,4.626,0,0,1-.8.311,17.844,17.844,0,0,0,2.007.065c1.67.01,3.462-1.2,4.932-1.874.981-.449,1.955-.918,2.969-1.291l.735-.182.014,0a2.2,2.2,0,0,1,.635-.213A9.163,9.163,0,0,0,672.583,691.588Z" transform="translate(-623.932 -658.997)" fill="url(#linear-gradient)"/>
              <path id="Path_88139" data-name="Path 88139" d="M560.317,629.971c-2.329.593-4.344,1.988-6.744,2.342a1.188,1.188,0,0,1-.623.107,14.238,14.238,0,0,0,2.644.106c2.073-.131,3.955-1.394,5.866-2.084a5.807,5.807,0,0,1,1.694-.435s.006,0,.011,0c.052-.035.1-.071.155-.1A8.508,8.508,0,0,0,560.317,629.971Z" transform="translate(-528.519 -607.718)" fill="url(#linear-gradient)"/>
              <path id="Path_88140" data-name="Path 88140" d="M398.34,490.506c.452-.07,1.425.3,2.048.209,2.6-.4,5.128-.849,7.629.22a12,12,0,0,1,4.379,3.53c2.547,3.08,3.867,7.068,4.922,10.857.811,2.907.809,8.674,4.688,9.293.254.041.51.076.766.107a1.187,1.187,0,0,0,.623-.107.166.166,0,0,0-.027,0c-.634.092-1.555-1.172-1.847-1.605a7.542,7.542,0,0,1-.9-2.405c-.564-2.239-.978-4.494-1.643-6.711a25.453,25.453,0,0,0-5.33-10.239c-3.949-4.24-10.047-3.961-15.306-3.154Z" transform="translate(-398.34 -490.021)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14812" data-name="Group 14812" transform="translate(201.852 22.833)">
              <path id="Path_88141" data-name="Path 88141" d="M1683.343,368.654a12.379,12.379,0,0,1-1.748,3.615,5.311,5.311,0,0,1-1.168,1.215c-.024.051-.046.1-.068.149.435-.321.858-.653,1.271-.986.1-.082.2-.164.3-.247a10.158,10.158,0,0,0,3.677-7.06,4.176,4.176,0,0,1-1.888.484A11.841,11.841,0,0,1,1683.343,368.654Z" transform="translate(-1652.726 -342.173)" fill="url(#linear-gradient)"/>
              <path id="Path_88142" data-name="Path 88142" d="M1569.623,423.4a7.52,7.52,0,0,1,3.284.164,10.35,10.35,0,0,0,6.667-.932,15.625,15.625,0,0,0,7.427-7,8.414,8.414,0,0,0,.664-4.047c-.634.512-1.291,1.027-1.977,1.489a10.464,10.464,0,0,1-.081,1.97,8.749,8.749,0,0,1-4.661,6.458c-2.658,1.271-4.949.722-7.706.4a6.359,6.359,0,0,0-4.142,1.214,5.444,5.444,0,0,1-.337.6,3.594,3.594,0,0,1,.861-.314Z" transform="translate(-1558.761 -381.106)" fill="url(#linear-gradient)"/>
              <path id="Path_88143" data-name="Path 88143" d="M1512.219,488.147a37.162,37.162,0,0,0-3.111,6.708q-1.683,4.046-3.369,8.094c-.087.207-.172.416-.259.623.247-.594,1.7-.747,1.977-1.413,2.057-4.943,3.781-10.277,6.434-14.934a12.284,12.284,0,0,1,1.293-1.8l.182-.19a1.084,1.084,0,0,1,.115-.07,5.429,5.429,0,0,0,.337-.6,13.6,13.6,0,0,0-3.6,3.587Z" transform="translate(-1505.48 -442.554)" fill="url(#linear-gradient)"/>
              <path id="Path_88144" data-name="Path 88144" d="M1633.871,378.562c-1.384,1.038-1.4,2.427-.657,3.93a7.256,7.256,0,0,0,2.339,2.479c1.074.782,1.515.9,2.642.332a13.212,13.212,0,0,0,1.351-.8c.239-.161.474-.33.706-.5.022-.047.046-.1.068-.149a2.3,2.3,0,0,1-.795.376,1.538,1.538,0,0,1-.951-.077c-2.008-.837-3.908-2.9-3.916-5.158a1.219,1.219,0,0,1,.077-.431,2.01,2.01,0,0,1,.365-.91c-.446.289-.86.637-1.228.913Z" transform="translate(-1612.617 -352.538)" fill="url(#linear-gradient)"/>
              <path id="Path_88145" data-name="Path 88145" d="M1645.449,375.483c.016-.036.029-.058.033-.068a4.09,4.09,0,0,1,1.032.177,4.516,4.516,0,0,1,2.222,1.6,7.016,7.016,0,0,1,1.5,3.957,3.775,3.775,0,0,0,1.963-1.591c-.3-2.672-1.774-5.186-4.571-5.546a2.755,2.755,0,0,0-1.816.559,2.016,2.016,0,0,0-.365.91Z" transform="translate(-1623.332 -349.463)" fill="url(#linear-gradient)"/>
              <path id="Path_88146" data-name="Path 88146" d="M1687.616,319.593c0-.03.01-.055.011-.068l0,0c.006-.025,0-.019,0,0a6.846,6.846,0,0,1,1.378,2.487,9.175,9.175,0,0,1,.563,1.948,3.481,3.481,0,0,0,1.6.465c.175.008.335.011.485.008a11.333,11.333,0,0,0-.491-3.34c-.327-1.076-1.327-3.971-2.837-3.04-.281.174-.566.352-.842.542.035.333.115.679.134,1Z" transform="translate(-1658.721 -302.204)" fill="url(#linear-gradient)"/>
              <path id="Path_88147" data-name="Path 88147" d="M1675.107,324.325c-1.128,2.716,1.274,5.256,3.881,5.369h.071a4.155,4.155,0,0,0,1.888-.484,8,8,0,0,0,1.024-.612c.246-.169.482-.352.713-.537-.1-.009-.2-.024-.3-.04q-.119-.019-.232-.043a2.083,2.083,0,0,1-1.163.291c-.148,0-.31,0-.485-.008a3.469,3.469,0,0,1-1.6-.465,4.385,4.385,0,0,1-2.042-3.538,4.683,4.683,0,0,1,.09-.828c-.019-.322-.1-.668-.134-1a4.45,4.45,0,0,0-1.716,1.893Z" transform="translate(-1648.062 -306.044)" fill="url(#linear-gradient)"/>
              <path id="Path_88148" data-name="Path 88148" d="M1724.978,220.141c-.463,5.292-.528,10.816-1.653,16.017a14.362,14.362,0,0,1-1.179,3.3,5.339,5.339,0,0,1-.357.627,2.005,2.005,0,0,1-.559.583q.114.024.232.043c.1.016.2.028.3.039a16.57,16.57,0,0,0,1.356-1.229c2.163-2.182,2.506-5.981,2.792-8.86.4-3.973.7-7.955,1.046-11.93-.041.465-1.926.836-1.977,1.413Z" transform="translate(-1687.138 -218.73)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14813" data-name="Group 14813" transform="translate(158.535)">
              <path id="Path_88149" data-name="Path 88149" d="M1273.427,77.781c.852-.618,2.745-.137,3.676-.087a6.146,6.146,0,0,0,4.545-1.191c.308-.259-.784-2.1-.574-2.272-1.792,1.507-4.071,1.234-6.259,1.081-1.007-.071-2.272-.321-2.389.988a2.64,2.64,0,0,0,.15.935c.294.194.6.378.866.586C1273.438,77.806,1273.428,77.795,1273.427,77.781Z" transform="translate(-1265.93 -74.23)" fill="url(#linear-gradient)"/>
              <path id="Path_88150" data-name="Path 88150" d="M1279.406,96.244c-.594-.891-1.967-1.172-2.922-1.436a12.893,12.893,0,0,1-1.406-.472c-.134-.054-.732-.205-.812-.509-.269-.21-.57-.392-.866-.586.068.226.141.446.191.642a3.7,3.7,0,0,0,2.525,2.914c.913.367,1.885.529,2.8.915.823.349.823.472.909.575v.022a2.558,2.558,0,0,1,.533.491,7.109,7.109,0,0,0-.948-2.555Z" transform="translate(-1266.755 -90.236)" fill="url(#linear-gradient)"/>
              <path id="Path_88151" data-name="Path 88151" d="M1257.2,131.32l-.017-.008a6.566,6.566,0,0,1,4.407-2.825c1.67-.344,4.808.837,4.5-1.972-.025-.234-.063-.477-.109-.724a2.55,2.55,0,0,0-.532-.491c0,.153-.112.37-.431.681-.673.649-2.166.179-2.977.147a6.571,6.571,0,0,0-2.96.725c-1.6.736-3.078,2.25-2.686,4.071.017.079.033.158.049.235a2.988,2.988,0,0,0,.76.163Z" transform="translate(-1252.38 -117.23)" fill="url(#linear-gradient)"/>
              <path id="Path_88152" data-name="Path 88152" d="M1258.536,164.888c1.726.784,4.445.8,5.926,2.151,0,.006-.016.058-.016.058l.019.006a1.161,1.161,0,0,1,.653.417,6.962,6.962,0,0,0-.24-1,7.558,7.558,0,0,0-.687-1.539c-.581-.937-2.346-1.187-3.291-1.471a15.44,15.44,0,0,1-3.108-.969,2.976,2.976,0,0,1-.76-.164c.224,1.09.378,2,1.5,2.506Z" transform="translate(-1252.972 -148.451)" fill="url(#linear-gradient)"/>
              <path id="Path_88153" data-name="Path 88153" d="M1231.929,202.533c-.242-6.4,10.361-3.206,11.594-8.833a2.922,2.922,0,0,0-.027-1.111,1.152,1.152,0,0,0-.653-.417.09.09,0,0,1-.019-.006s.008-.019.006-.009c-.066.491-.657.951-.752,1.071a5.121,5.121,0,0,1-1.558,1.16,15.815,15.815,0,0,1-4.284,1.263c-2.378.547-4.989,1.915-4.883,4.7.027.7.558,1.673.578,2.184Z" transform="translate(-1231.35 -173.519)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14814" data-name="Group 14814" transform="translate(77.945 25.706)">
              <path id="Path_88154" data-name="Path 88154" d="M723,242.943c-.093-.749.861-1.773,1.215-2.34a4.409,4.409,0,0,0,.822-3.268c-.057-.283-1.588-.232-1.626-.425.329,1.647-.631,2.985-1.482,4.306-.392.608-.986,1.318-.205,1.842a1.908,1.908,0,0,0,.64.229c.224-.117.443-.245.668-.341-.009,0-.021,0-.03,0Z" transform="translate(-721.342 -236.91)" fill="url(#linear-gradient)"/>
              <path id="Path_88155" data-name="Path 88155" d="M731.755,259.9c-.766.066-1.416.831-1.912,1.34a9.143,9.143,0,0,1-.784.721c-.081.065-.382.389-.6.335-.224.1-.444.223-.668.341.164.036.329.065.469.1a2.656,2.656,0,0,0,2.7-.58c.547-.447.983-1,1.542-1.443.5-.4.582-.354.676-.373a.063.063,0,0,1,.014.008,1.826,1.826,0,0,1,.493-.164,5.063,5.063,0,0,0-1.933-.286Z" transform="translate(-726.771 -256.255)" fill="url(#linear-gradient)"/>
              <path id="Path_88156" data-name="Path 88156" d="M756.342,269.2l-.011.008a4.706,4.706,0,0,1-.253-3.745c.36-1.169,2.187-2.732.314-3.508-.156-.065-.322-.125-.493-.182a1.88,1.88,0,0,0-.493.164c.1.054.194.2.278.507.175.646-.637,1.424-.935,1.922a4.713,4.713,0,0,0-.567,2.111c-.089,1.258.351,2.712,1.629,3.094l.164.051a2.173,2.173,0,0,0,.365-.422Z" transform="translate(-748.983 -257.842)" fill="url(#linear-gradient)"/>
              <path id="Path_88157" data-name="Path 88157" d="M767.7,292.166c1.089-.814,2.04-2.517,3.4-2.982,0,0,.032.03.032.03l.011-.009a.821.821,0,0,1,.488-.265,4.872,4.872,0,0,0-.709-.193,5.418,5.418,0,0,0-1.206-.1c-.79.043-1.557,1.063-2.061,1.56a11.088,11.088,0,0,1-1.683,1.618,2.176,2.176,0,0,1-.365.422c.762.235,1.384.452,2.1-.079Z" transform="translate(-758.615 -280.468)" fill="url(#linear-gradient)"/>
              <path id="Path_88158" data-name="Path 88158" d="M801.337,301.224c-4.108-2.061,1.564-7.62-1.547-10.337a2.13,2.13,0,0,0-.708-.367.821.821,0,0,0-.488.265l-.009.009s-.009-.011,0-.008c.286.212.371.741.414.842a3.643,3.643,0,0,1,.19,1.379,11.314,11.314,0,0,1-.687,3.129c-.479,1.683-.52,3.8,1.267,4.693.446.223,1.245.228,1.572.392Z" transform="translate(-786.067 -282.049)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14815" data-name="Group 14815" transform="translate(251.375 216.439)">
              <path id="Path_88159" data-name="Path 88159" d="M1911.979,1492.439c.275.63-.3,1.773-.458,2.361a4.012,4.012,0,0,0,.126,3.07c.122.232,1.449-.207,1.533-.049-.713-1.356-.219-2.773.185-4.15.186-.634.523-1.406-.3-1.666a1.727,1.727,0,0,0-.619-.035c-.166.161-.324.329-.5.471C1911.961,1492.442,1911.969,1492.438,1911.979,1492.439Z" transform="translate(-1896.678 -1484.374)" fill="url(#linear-gradient)"/>
              <path id="Path_88160" data-name="Path 88160" d="M1891.8,1495.438c.653-.256,1.024-1.094,1.326-1.666a8.562,8.562,0,0,1,.5-.833c.054-.077.234-.438.438-.449.172-.142.33-.31.5-.471-.153.011-.3.028-.436.032a2.422,2.422,0,0,0-2.215,1.206c-.362.532-.6,1.131-.976,1.659-.338.477-.417.46-.495.5,0,0-.01,0-.014,0a1.643,1.643,0,0,1-.389.272,4.635,4.635,0,0,0,1.765-.248Z" transform="translate(-1878.787 -1484.424)" fill="url(#linear-gradient)"/>
              <path id="Path_88161" data-name="Path 88161" d="M1875.46,1472.363l.008-.01a4.291,4.291,0,0,1,1.188,3.211c-.014,1.116-1.207,2.955.63,3.149.153.016.314.027.477.031a1.705,1.705,0,0,0,.389-.272c-.1-.022-.221-.123-.375-.371-.321-.52.19-1.41.322-1.923a4.313,4.313,0,0,0-.049-1.992c-.246-1.124-1.008-2.282-2.225-2.287h-.156A1.954,1.954,0,0,0,1875.46,1472.363Z" transform="translate(-1866.519 -1467.483)" fill="url(#linear-gradient)"/>
              <path id="Path_88162" data-name="Path 88162" d="M1851.453,1472.486c-.741.994-1.135,2.727-2.2,3.486a.228.228,0,0,1-.035-.019l-.006.011a.759.759,0,0,1-.359.359,4.554,4.554,0,0,0,.67-.014,4.862,4.862,0,0,0,1.079-.224c.679-.24,1.087-1.332,1.4-1.9a10.09,10.09,0,0,1,1.054-1.85,1.933,1.933,0,0,1,.21-.463c-.727-.009-1.327-.038-1.812.612Z" transform="translate(-1844.114 -1467.458)" fill="url(#linear-gradient)"/>
              <path id="Path_88163" data-name="Path 88163" d="M1818.88,1444.033c4.126.741.6,7.068,4.023,8.643a1.916,1.916,0,0,0,.714.138.759.759,0,0,0,.359-.359l.006-.011s.011.008,0,0c-.305-.111-.515-.553-.58-.63a3.343,3.343,0,0,1-.523-1.158,10.349,10.349,0,0,1-.207-2.914c-.016-1.6-.526-3.456-2.321-3.778-.447-.081-1.147.123-1.476.063Z" transform="translate(-1818.88 -1443.949)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14816" data-name="Group 14816" transform="translate(59.974 129.375)">
              <path id="Path_88164" data-name="Path 88164" d="M610.228,898.851c-.449-.6-.13-1.955-.1-2.617a4.378,4.378,0,0,0-.9-3.224c-.188-.216-1.484.583-1.613.435,1.1,1.259.926,2.887.842,4.447-.039.719-.2,1.623.732,1.691a1.835,1.835,0,0,0,.665-.119c.136-.212.262-.43.408-.623A.085.085,0,0,1,610.228,898.851Z" transform="translate(-607.61 -892.973)" fill="url(#linear-gradient)"/>
              <path id="Path_88165" data-name="Path 88165" d="M623.85,902.978a3.583,3.583,0,0,0-.989,2.1,9.332,9.332,0,0,1-.321,1.008c-.036.1-.138.523-.352.585-.147.194-.273.411-.408.623.16-.051.316-.106.455-.144a2.637,2.637,0,0,0,2.046-1.833c.251-.656.356-1.349.619-2,.239-.591.327-.591.4-.654h.016a1.886,1.886,0,0,1,.345-.386,5.046,5.046,0,0,0-1.809.7Z" transform="translate(-619.541 -900.801)" fill="url(#linear-gradient)"/>
              <path id="Path_88166" data-name="Path 88166" d="M648.527,907.913a.087.087,0,0,1-.006.013,4.682,4.682,0,0,1-2.064-3.108c-.264-1.187.542-3.435-1.457-3.182-.166.021-.34.051-.515.087a1.883,1.883,0,0,0-.344.386c.109,0,.264.076.49.3.469.472.152,1.542.139,2.119a4.67,4.67,0,0,0,.55,2.1c.544,1.13,1.639,2.166,2.931,1.868l.167-.038a2.148,2.148,0,0,0,.107-.544Z" transform="translate(-638.368 -900.25)" fill="url(#linear-gradient)"/>
              <path id="Path_88167" data-name="Path 88167" d="M673.007,912.8c.539-1.239.521-3.176,1.465-4.247,0,0,.041.011.041.011a.048.048,0,0,1,0-.014.82.82,0,0,1,.291-.469,4.864,4.864,0,0,0-.708.183,5.419,5.419,0,0,0-1.089.507c-.661.425-.818,1.684-1.01,2.362a11,11,0,0,1-.654,2.225,2.081,2.081,0,0,1-.107.544c.774-.172,1.417-.292,1.768-1.1Z" transform="translate(-661.185 -905.693)" fill="url(#linear-gradient)"/>
              <path id="Path_88168" data-name="Path 88168" d="M699.416,915.893c-4.56.246-2.4-7.345-6.427-8.158a2.1,2.1,0,0,0-.792.032.833.833,0,0,0-.291.469s0,.009,0,.014-.014,0-.006,0c.351.041.686.457.773.523a3.625,3.625,0,0,1,.844,1.1,11.271,11.271,0,0,1,.948,3.039c.417,1.688,1.422,3.532,3.405,3.426C698.362,916.3,699.052,915.912,699.416,915.893Z" transform="translate(-678.576 -905.381)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14817" data-name="Group 14817" transform="translate(227.541 235.241)">
              <path id="Path_88169" data-name="Path 88169" d="M1752.486,1695.757c-.047,1.051-1.6,2.234-2.22,2.931a6.141,6.141,0,0,0-1.9,4.3c.011.4,2.129.691,2.136.965-.065-2.34,1.568-3.955,3.05-5.572.682-.744,1.666-1.578.714-2.485a2.6,2.6,0,0,0-.826-.465c-.335.109-.667.232-1,.311a.391.391,0,0,1,.039.014Z" transform="translate(-1735.676 -1674.494)" fill="url(#linear-gradient)"/>
              <path id="Path_88170" data-name="Path 88170" d="M1728.159,1694.475c1.068.09,2.143-.811,2.942-1.392a13.081,13.081,0,0,1,1.245-.807c.125-.071.616-.444.9-.319.332-.081.662-.2,1-.311-.218-.087-.436-.166-.621-.25a3.7,3.7,0,0,0-3.852.163c-.856.487-1.586,1.147-2.457,1.62-.785.427-.882.351-1.016.354a.191.191,0,0,1-.017-.014,2.567,2.567,0,0,1-.716.111A7.064,7.064,0,0,0,1728.159,1694.475Z" transform="translate(-1716.48 -1670.708)" fill="url(#linear-gradient)"/>
              <path id="Path_88171" data-name="Path 88171" d="M1718.355,1640.095l.017-.008a6.565,6.565,0,0,1-.533,5.208c-.771,1.522-3.649,3.243-1.256,4.747.2.125.412.247.635.365a2.57,2.57,0,0,0,.716-.111c-.12-.095-.22-.318-.264-.761-.089-.931,1.209-1.808,1.738-2.422a6.569,6.569,0,0,0,1.275-2.769c.417-1.708.153-3.81-1.514-4.636l-.215-.107A2.962,2.962,0,0,0,1718.355,1640.095Z" transform="translate(-1708.129 -1627.487)" fill="url(#linear-gradient)"/>
              <path id="Path_88172" data-name="Path 88172" d="M1685.417,1635.767c-1.688.863-3.4,2.98-5.371,3.3,0,0-.035-.049-.035-.049l-.017.011a1.158,1.158,0,0,1-.733.251,7.048,7.048,0,0,0,.929.433,7.611,7.611,0,0,0,1.632.42c1.095.128,2.389-1.1,3.2-1.661a15.563,15.563,0,0,1,2.693-1.83,2.98,2.98,0,0,1,.6-.493c-.992-.5-1.8-.946-2.9-.382Z" transform="translate(-1677.489 -1624.038)" fill="url(#linear-gradient)"/>
              <path id="Path_88173" data-name="Path 88173" d="M1669.235,1562.942c5.162,3.8-3.938,10.105-.3,14.572a2.931,2.931,0,0,0,.886.67,1.155,1.155,0,0,0,.733-.251l.016-.011s.011.017,0,.011a2.393,2.393,0,0,1-.37-1.256,5.086,5.086,0,0,1,.063-1.942,15.844,15.844,0,0,1,1.678-4.138c1.052-2.2,1.605-5.1-.64-6.749-.559-.412-1.658-.6-2.068-.907Z" transform="translate(-1668.049 -1562.94)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14818" data-name="Group 14818" transform="translate(262.887 159.525)">
              <path id="Path_88174" data-name="Path 88174" d="M2089.484,1129.749a6.127,6.127,0,0,0,.727-1.749c-1.678.95-3.283,2.43-3.271,4.2l.042-.033c.035-.068.073-.136.119-.207-.046.071-.082.141-.119.207.272-.194.539-.392.8-.6A7.76,7.76,0,0,0,2089.484,1129.749Z" transform="translate(-2056.094 -1121.011)" fill="none"/>
              <path id="Path_88175" data-name="Path 88175" d="M2132.987,1134.845a12.459,12.459,0,0,0-1.7-8.4c-.033-.052-.071-.106-.108-.16a2.212,2.212,0,0,0-1.074-.534,1.316,1.316,0,0,1,.208.311,10.91,10.91,0,0,1,.5,1.726,12.649,12.649,0,0,1,.253,5.038c-.082.641,1.986,1.507,1.921,2.019Z" transform="translate(-2092.434 -1119.117)" fill="url(#linear-gradient)"/>
              <path id="Path_88176" data-name="Path 88176" d="M1892.313,1084.428a25.624,25.624,0,0,0,11.706,7.563,23.293,23.293,0,0,0,15.95-1.131c1.15-.517,2.2-1.223,3.34-1.759a3.281,3.281,0,0,1,.324-.131,8.636,8.636,0,0,1,1.841-1.046,4.011,4.011,0,0,0-2.152-1.236c-1.887-.332-4.167,1.653-5.79,2.381-7.58,3.407-18.776,2.086-24.633-4.18-.588-.627-1.928-1.895-.588-.463Z" transform="translate(-1891.736 -1083.771)" fill="url(#linear-gradient)"/>
              <path id="Path_88177" data-name="Path 88177" d="M2105.577,1112.687h.009Q2105.1,1111.613,2105.577,1112.687Z" transform="translate(-2071.587 -1107.666)" fill="url(#linear-gradient)"/>
              <path id="Path_88178" data-name="Path 88178" d="M2077.589,1118.3c-.646-.608-1.209-1.176-.692-2.168-.014.011-.058.041,0,0-.012-1.765,1.55-3.212,3.228-4.164a11.4,11.4,0,0,1,1.387-.667c.25-.1.493-.188.719-.256a1.965,1.965,0,0,1,1.441.567,2.2,2.2,0,0,1,1.074.534c-.877-1.3-2.384-2.935-3.928-3.17a2.205,2.205,0,0,0-1.073.156,8.633,8.633,0,0,0-1.841,1.046c-.269.186-.514.365-.722.5a4.56,4.56,0,0,0-2.223,3.886c0,1.4,1.473,2.827,2.628,3.731Z" transform="translate(-2046.007 -1104.976)" fill="url(#linear-gradient)"/>
              <path id="Path_88179" data-name="Path 88179" d="M2086.815,1130.769c.156.122.27.254.411.356.014.008.028.036.081.006,2.746-1.863,4.911-4.532,3.432-7.36a11.417,11.417,0,0,0-1.387.667,6.141,6.141,0,0,1-.727,1.749,7.732,7.732,0,0,1-1.7,1.811c-.259.21-.526.409-.8.6-.016.029-.763,1.086.692,2.168Z" transform="translate(-2055.233 -1117.45)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14819" data-name="Group 14819" transform="translate(46.517 211.473)">
              <path id="Path_88180" data-name="Path 88180" d="M581.8,1535.188a6.141,6.141,0,0,0,1.015,1.6c.186-1.92-.1-4.085-1.557-5.088,0,.017,0,.036,0,.054.035.068.07.137.1.215-.033-.079-.068-.149-.1-.215,0,.333.011.667.035,1a7.755,7.755,0,0,0,.5,2.433Z" transform="translate(-571.967 -1512.869)" fill="none"/>
              <path id="Path_88181" data-name="Path 88181" d="M522.45,1562.745a12.454,12.454,0,0,0,7.857,3.43l.193,0a2.205,2.205,0,0,0,1.054-.573,1.251,1.251,0,0,1-.375-.01,10.9,10.9,0,0,1-1.7-.578,12.664,12.664,0,0,1-4.27-2.686c-.479-.436-2.375.76-2.757.412Z" transform="translate(-522.45 -1538.579)" fill="url(#linear-gradient)"/>
              <path id="Path_88182" data-name="Path 88182" d="M629.079,1412.627a25.639,25.639,0,0,0-12.915,5.238,23.287,23.287,0,0,0-8.234,13.706c-.237,1.239-.262,2.5-.48,3.745a3.444,3.444,0,0,1-.079.341,8.629,8.629,0,0,1-.2,2.108,4.01,4.01,0,0,0,2.247-1.052c1.356-1.354,1.04-4.361,1.375-6.107,1.566-8.162,9.077-16.568,17.57-17.764.852-.12,2.658-.49.717-.216Z" transform="translate(-593.783 -1412.527)" fill="url(#linear-gradient)"/>
              <path id="Path_88183" data-name="Path 88183" d="M601.726,1570.39l-.006.006Q602.88,1570.613,601.726,1570.39Z" transform="translate(-589.194 -1545.445)" fill="url(#linear-gradient)"/>
              <path id="Path_88184" data-name="Path 88184" d="M567.522,1525.208c.869-.18,1.656-.314,2.173.678h0c1.452,1,1.74,3.115,1.553,5.033a11.4,11.4,0,0,1-.251,1.519c-.063.262-.13.512-.2.736a1.958,1.958,0,0,1-1.293.853,2.2,2.2,0,0,1-1.054.574c1.568.028,3.772-.267,4.851-1.4a2.214,2.214,0,0,0,.488-.967,8.63,8.63,0,0,0,.2-2.108c0-.327,0-.63,0-.88a4.555,4.555,0,0,0-1.9-4.052c-1.149-.8-3.16-.419-4.564.008Z" transform="translate(-560.398 -1507.001)" fill="url(#linear-gradient)"/>
              <path id="Path_88185" data-name="Path 88185" d="M564.438,1526.95c-.19.057-.363.076-.528.131-.016.008-.046,0-.051.063-.052,3.318.888,6.624,4.053,7.037a11.4,11.4,0,0,0,.251-1.519,6.147,6.147,0,0,1-1.015-1.6,7.713,7.713,0,0,1-.5-2.434c-.024-.333-.032-.665-.035-1-.016-.028-.45-1.248-2.173-.678Z" transform="translate(-557.314 -1508.743)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14820" data-name="Group 14820" transform="translate(135.79 196.524)">
              <path id="Path_88186" data-name="Path 88186" d="M1090.795,1657.048a16.472,16.472,0,0,0-1.244,9.416,15.373,15.373,0,0,0,4.826,8.587c-.115-.106.942-1.577.748-1.756a15.738,15.738,0,0,1-4.9-9,15,15,0,0,1,.25-5.853,8.733,8.733,0,0,1,.754-2.048c0-.112,0-.223,0-.335q-.223.491-.434.986Z" transform="translate(-1089.023 -1602.628)" fill="url(#linear-gradient)"/>
              <path id="Path_88187" data-name="Path 88187" d="M1087.428,1520.9c.168,1.566,1.651,2.773,2.874,3.559,1.544.992,3.206,1.659,4.428,3.084a4.317,4.317,0,0,1,.373,5.183c-1.1,1.52-2.568,2.74-3.707,4.233a15.956,15.956,0,0,0-1.779,3.1c0,.112,0,.223,0,.335a18.616,18.616,0,0,1,3.222-4.18c1.653-1.745,3.532-4.221,3.694-6.75.273-4.27-3.6-5.267-6.39-7.4a4.417,4.417,0,0,1-1.969-3.777c-.1.079-.2.152-.291.223a4.755,4.755,0,0,0-.455,2.386Z" transform="translate(-1087.409 -1486.628)" fill="url(#linear-gradient)"/>
              <path id="Path_88188" data-name="Path 88188" d="M1101.215,1463.058a15.775,15.775,0,0,1-2.626,1.874c-2.754,1.654-6.452,1.975-7.877,5.223-.1.237-.206.469-.3.7.09-.071.186-.142.291-.223v-.021a4.83,4.83,0,0,1,.675-.717,6.048,6.048,0,0,1,1.1-.762,19.1,19.1,0,0,1,2.852-1.225c2.923-1.147,5.746-2.908,6.976-5.888.046-.112.09-.221.131-.33a2.677,2.677,0,0,1-.311.175,3.047,3.047,0,0,1-.909,1.195Z" transform="translate(-1089.936 -1438.972)" fill="url(#linear-gradient)"/>
              <path id="Path_88189" data-name="Path 88189" d="M1140.553,1326.772c-.935,3.48-.09,6.461,1.756,9.435a10.5,10.5,0,0,1,1.694,3.81,1.439,1.439,0,0,1,.017.795,2.555,2.555,0,0,0,.311-.176,4.936,4.936,0,0,0,.368-2.7,12.1,12.1,0,0,0-2.032-4.116c-2.506-4.059-2.049-8.249.144-12.42.315-.6.648-1.188.977-1.781.278-.5.491-1.323.7-1.7a39.735,39.735,0,0,0-3.935,8.851Z" transform="translate(-1131.832 -1317.92)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14821" data-name="Group 14821" transform="translate(251.771 61.268)">
              <path id="Path_88190" data-name="Path 88190" d="M2110.351,477.112a16.458,16.458,0,0,0,7.6-5.7,15.382,15.382,0,0,0,2.762-9.456c0,.156-1.786.428-1.778.694a15.745,15.745,0,0,1-3.007,9.794,14.968,14.968,0,0,1-4.36,3.914,8.736,8.736,0,0,1-1.991.893c-.079.079-.158.158-.239.235.338-.122.675-.25,1.008-.379Z" transform="translate(-2063.839 -461.96)" fill="url(#linear-gradient)"/>
              <path id="Path_88191" data-name="Path 88191" d="M2019.636,573.961c1-1.215.828-3.119.534-4.543-.37-1.8-1.056-3.451-.891-5.322a4.314,4.314,0,0,1,3.443-3.89c1.855-.278,3.754-.081,5.619-.311a16.005,16.005,0,0,0,3.464-.9c.081-.077.16-.156.239-.235a18.568,18.568,0,0,1-5.241.619c-2.4.039-5.487.427-7.41,2.081-3.242,2.791-1.25,6.254-.82,9.742a4.422,4.422,0,0,1-1.323,4.048c.13.019.248.038.362.052a4.766,4.766,0,0,0,2.024-1.343Z" transform="translate(-1986.301 -543.464)" fill="url(#linear-gradient)"/>
              <path id="Path_88192" data-name="Path 88192" d="M1926.7,645.63a15.831,15.831,0,0,1,3.176.567c3.11.811,5.924,3.231,9.242,1.978.242-.092.479-.18.713-.275-.114-.014-.232-.033-.362-.052l-.016.014,0,0s0,0,0,0a4.883,4.883,0,0,1-.984.019,6.1,6.1,0,0,1-1.313-.251,19.222,19.222,0,0,1-2.871-1.182c-2.865-1.288-6.1-2.073-9.088-.869-.112.046-.221.09-.327.138a2.621,2.621,0,0,1,.343.1,3.056,3.056,0,0,1,1.489-.186Z" transform="translate(-1908.518 -616.062)" fill="url(#linear-gradient)"/>
              <path id="Path_88193" data-name="Path 88193" d="M1830.471,662.026c3.141-1.765,4.682-4.454,5.516-7.853a10.48,10.48,0,0,1,1.537-3.875,1.445,1.445,0,0,1,.556-.569,2.488,2.488,0,0,0-.343-.1,4.941,4.941,0,0,0-2.187,1.624,12.1,12.1,0,0,0-1.522,4.331c-1.149,4.63-4.462,7.234-8.977,8.583-.648.194-1.3.368-1.956.547-.552.152-1.289.574-1.707.687a39.728,39.728,0,0,0,9.078-3.378Z" transform="translate(-1821.39 -619.975)" fill="url(#linear-gradient)"/>
            </g>
            <path id="Path_88194" data-name="Path 88194" d="M464.5,1118.469a20.748,20.748,0,0,1,6.833-12.016,10.433,10.433,0,0,1,6.348-2.9,23.7,23.7,0,0,1,6.063.556c2.772.608,5.453,1.58,8.288,1.872a9.149,9.149,0,0,0,4.421-.822c1.229-.534,2.236-1.471,3.415-2.1a9.885,9.885,0,0,1,5.349-.7,21.93,21.93,0,0,1,6.49,1.76c-.422-.166-1.059.136-1.511-.041-2.219-.875-4.78-2-7.2-1.545-2.365.441-4.036,2.451-6.332,3.14-3.465,1.038-7.5.207-10.881-.689-2.522-.667-5.245-1.659-7.876-1.236-2.351.379-4.388,2.046-6.054,3.649a20.478,20.478,0,0,0-5.842,11.109,8.592,8.592,0,0,1-1.511-.041Z" transform="translate(-427.14 -939.845)" fill="url(#linear-gradient)"/>
            <g id="Group_14822" data-name="Group 14822" transform="translate(229.51 120.112)">
              <path id="Path_88195" data-name="Path 88195" d="M1700.407,915.789c-6.488-3-13.973.6-19.9,3.506.924-.453,2.974-.77,4.206-1.375,2.764-1.357,5.343-2.171,8.427-1.7,2.324.357,4.4,1.85,6.678,2.441,3.1.8,6.292.028,9.242-.935.1-.035.2-.074.307-.109.2-.231.4-.463.6-.694-2.765,1.155-5.862.575-9.566-1.136Z" transform="translate(-1680.51 -901.969)" fill="url(#linear-gradient)"/>
              <path id="Path_88196" data-name="Path 88196" d="M1872.061,835.823a18.781,18.781,0,0,1-5.118,11.216,9.466,9.466,0,0,1-3.19,2.269c-.2.231-.4.463-.6.694,7.384-2.508,12.366-7.553,13.256-15.652-.057.512-4.263.711-4.347,1.474Z" transform="translate(-1834.29 -834.35)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14823" data-name="Group 14823" transform="translate(0 133.935)">
              <path id="Path_88197" data-name="Path 88197" d="M328.065,926.455c4.088,4.428,11.077,4.042,16.631,3.683-.866.055-2.565-.36-3.721-.284a11.46,11.46,0,0,1-7.112-1.4c-1.7-1.027-2.828-2.86-4.415-4.055-2.154-1.621-4.892-2.051-7.5-2.25-.092-.006-.182-.008-.273-.014l-.692.346C323.507,922.478,325.732,923.929,328.065,926.455Z" transform="translate(-306.299 -922.091)" fill="url(#linear-gradient)"/>
              <path id="Path_88198" data-name="Path 88198" d="M231.93,930.049a15.828,15.828,0,0,1,7.6-7.087,7.979,7.979,0,0,1,3.217-.74l.692-.346c-6.561-.428-12.069,1.9-15.373,7.92.209-.381,3.551.82,3.862.253Z" transform="translate(-228.07 -921.827)" fill="url(#linear-gradient)"/>
            </g>
          </g>
          <g id="Group_14830" data-name="Group 14830" transform="translate(32.191)">
            <path id="Path_88199" data-name="Path 88199" d="M1002.645,423.048s-2.478-.251-4.475-.544l1.041-2.895s3.508,1.386,4.2.984a5.642,5.642,0,0,0-.77,2.456Z" transform="translate(-908.672 -363.59)" fill="url(#linear-gradient)"/>
            <path id="Path_88200" data-name="Path 88200" d="M436.265,1294.669s-2.478-.251-4.475-.544l1.041-2.895s3.508,1.386,4.2.984a5.643,5.643,0,0,0-.77,2.456Z" transform="translate(-431.79 -1097.479)" fill="url(#linear-gradient)"/>
            <path id="Path_88201" data-name="Path 88201" d="M1312.978,1174.638s-1.677-.171-3.028-.368l.7-1.959s2.374.937,2.843.667a3.813,3.813,0,0,0-.52,1.661Z" transform="translate(-1171.186 -997.351)" fill="url(#linear-gradient)"/>
            <path id="Path_88202" data-name="Path 88202" d="M689.635,1004.48s-2.478-.251-4.475-.544l1.041-2.9s3.508,1.386,4.2.984A5.642,5.642,0,0,0,689.635,1004.48Z" transform="translate(-645.123 -853.144)" fill="url(#linear-gradient)"/>
            <path id="Path_88203" data-name="Path 88203" d="M1498.785,630.7s-.318-2.255-.495-4.091l2.8.292s-.465,3.416.043,3.947a5.155,5.155,0,0,0-2.348-.147Z" transform="translate(-1329.765 -537.88)" fill="url(#linear-gradient)"/>
            <path id="Path_88204" data-name="Path 88204" d="M1077.125,609.7s-.318-2.255-.495-4.091l2.8.292s-.465,3.416.043,3.947a5.156,5.156,0,0,0-2.348-.147Z" transform="translate(-974.734 -520.199)" fill="url(#linear-gradient)"/>
            <path id="Path_88205" data-name="Path 88205" d="M695.042,1306.378s-.567-4.028-.882-7.308l5,.521s-.829,6.1.078,7.049a9.211,9.211,0,0,0-4.192-.264Z" transform="translate(-652.701 -1104.08)" fill="url(#linear-gradient)"/>
            <path id="Path_88206" data-name="Path 88206" d="M1754.295,1065.771s-.318-2.255-.495-4.091l2.8.292s-.465,3.416.044,3.947a5.154,5.154,0,0,0-2.348-.147Z" transform="translate(-1544.9 -904.202)" fill="url(#linear-gradient)"/>
            <path id="Path_88207" data-name="Path 88207" d="M1126.89,995.625s-.16-1.139-.25-2.065l1.413.147s-.235,1.725.022,1.993A2.607,2.607,0,0,0,1126.89,995.625Z" transform="translate(-1016.842 -846.846)" fill="url(#linear-gradient)"/>
            <path id="Path_88208" data-name="Path 88208" d="M1176.958,1477.464s-.237-1.683-.368-3.054l2.089.218s-.348,2.55.033,2.945a3.845,3.845,0,0,0-1.752-.111Z" transform="translate(-1058.899 -1251.714)" fill="url(#linear-gradient)"/>
            <path id="Path_88209" data-name="Path 88209" d="M1487.135,1402.3s-.318-2.255-.495-4.091l2.8.292s-.465,3.416.043,3.947a5.156,5.156,0,0,0-2.348-.147Z" transform="translate(-1319.956 -1187.554)" fill="url(#linear-gradient)"/>
            <path id="Path_88210" data-name="Path 88210" d="M1569.2,960.43s-2.269.2-4.112.278l.439-2.778s3.388.643,3.944.164a5.166,5.166,0,0,0-.27,2.337Z" transform="translate(-1386.009 -816.846)" fill="url(#linear-gradient)"/>
            <path id="Path_88211" data-name="Path 88211" d="M529.337,714.62s5.611,1.068,10.113,2.133l-2.953,6.408s-7.738-3.864-9.407-3.086A12.927,12.927,0,0,0,529.337,714.62Z" transform="translate(-512.031 -611.983)" fill="url(#linear-gradient)"/>
            <path id="Path_88212" data-name="Path 88212" d="M1013.86,1026.75s1.64-3.5,3.092-6.27l3.7,3.009s-3.829,4.423-3.6,5.648A8.734,8.734,0,0,0,1013.86,1026.75Z" transform="translate(-921.883 -869.512)" fill="url(#linear-gradient)"/>
            <path id="Path_88213" data-name="Path 88213" d="M1305.645,791.058s-2.245-.228-4.055-.493l.943-2.625s3.179,1.256,3.81.893a5.1,5.1,0,0,0-.7,2.225Z" transform="translate(-1164.147 -673.717)" fill="url(#linear-gradient)"/>
            <path id="Path_88214" data-name="Path 88214" d="M1124.263,907.482s2.66,1.117,4.771,2.114l-2.132,2.854s-3.416-2.721-4.322-2.511a6.521,6.521,0,0,0,1.681-2.459Z" transform="translate(-1013.423 -774.368)" fill="url(#linear-gradient)"/>
            <path id="Path_88215" data-name="Path 88215" d="M1794.6,1141.9s1.809-3.86,3.413-6.92l4.088,3.322s-4.225,4.881-3.971,6.232a9.66,9.66,0,0,0-3.529-2.633Z" transform="translate(-1579.253 -965.919)" fill="url(#linear-gradient)"/>
            <path id="Path_88216" data-name="Path 88216" d="M849.63,1272.54s1.809-3.86,3.413-6.92l4.088,3.322s-4.225,4.881-3.971,6.232a9.658,9.658,0,0,0-3.529-2.633Z" transform="translate(-783.604 -1075.916)" fill="url(#linear-gradient)"/>
            <path id="Path_88217" data-name="Path 88217" d="M1052.67,1724.834s1.745-.871,3.187-1.514l.523,2.35s-2.9.552-3.192,1.108a4.409,4.409,0,0,0-.518-1.945Z" transform="translate(-954.56 -1461.292)" fill="url(#linear-gradient)"/>
            <path id="Path_88218" data-name="Path 88218" d="M1424.15,298.654s1.745-.871,3.187-1.514l.525,2.35s-2.9.551-3.192,1.108a4.409,4.409,0,0,0-.518-1.945Z" transform="translate(-1267.34 -260.472)" fill="url(#linear-gradient)"/>
            <path id="Path_88219" data-name="Path 88219" d="M1799.17,1395.474s1.745-.871,3.187-1.514l.523,2.35s-2.9.552-3.192,1.108a4.407,4.407,0,0,0-.518-1.945Z" transform="translate(-1583.101 -1183.976)" fill="url(#linear-gradient)"/>
            <path id="Path_88220" data-name="Path 88220" d="M520.97,1256.824s1.745-.871,3.187-1.514l.523,2.35s-2.9.552-3.192,1.108a4.408,4.408,0,0,0-.518-1.945Z" transform="translate(-506.878 -1067.235)" fill="url(#linear-gradient)"/>
            <path id="Path_88221" data-name="Path 88221" d="M543.96,492.544s1.745-.871,3.187-1.514l.523,2.35s-2.9.551-3.192,1.108a4.408,4.408,0,0,0-.518-1.945Z" transform="translate(-526.235 -423.724)" fill="url(#linear-gradient)"/>
            <path id="Path_88222" data-name="Path 88222" d="M1029.6,716.224s1.745-.871,3.187-1.514l.523,2.35s-2.9.551-3.192,1.108a4.409,4.409,0,0,0-.518-1.945Z" transform="translate(-935.136 -612.059)" fill="url(#linear-gradient)"/>
            <path id="Path_88223" data-name="Path 88223" d="M1245.46,1642.634s1.745-.871,3.187-1.514l.525,2.35s-2.9.552-3.192,1.108a4.407,4.407,0,0,0-.518-1.945Z" transform="translate(-1116.886 -1392.081)" fill="url(#linear-gradient)"/>
            <path id="Path_88224" data-name="Path 88224" d="M1730.49,320.384s1.745-.871,3.187-1.514l.525,2.35s-2.9.551-3.192,1.108a4.407,4.407,0,0,0-.518-1.945Z" transform="translate(-1525.273 -278.768)" fill="url(#linear-gradient)"/>
            <path id="Path_88225" data-name="Path 88225" d="M1436.47,1464.5s1.744-.871,3.187-1.514l.525,2.35s-2.9.552-3.192,1.108a4.406,4.406,0,0,0-.518-1.945Z" transform="translate(-1277.714 -1242.098)" fill="url(#linear-gradient)"/>
            <path id="Path_88226" data-name="Path 88226" d="M1537.43,637.634s1.745-.871,3.187-1.514l.525,2.35s-2.9.551-3.192,1.108a4.41,4.41,0,0,0-.518-1.945Z" transform="translate(-1362.72 -545.888)" fill="url(#linear-gradient)"/>
            <path id="Path_88227" data-name="Path 88227" d="M1332.71,874.271s1.225-2.61,2.309-4.68l2.765,2.247s-2.859,3.3-2.686,4.216a6.517,6.517,0,0,0-2.388-1.781Z" transform="translate(-1190.349 -742.465)" fill="url(#linear-gradient)"/>
            <path id="Path_88228" data-name="Path 88228" d="M1413.67,1541.063s.836-1.858,1.579-3.333l1.978,1.557s-1.982,2.364-1.85,3.007A4.594,4.594,0,0,0,1413.67,1541.063Z" transform="translate(-1258.516 -1305.028)" fill="url(#linear-gradient)"/>
            <path id="Path_88229" data-name="Path 88229" d="M1795.95,710.8s.836-1.858,1.579-3.333l1.978,1.556s-1.982,2.364-1.85,3.007A4.593,4.593,0,0,0,1795.95,710.8Z" transform="translate(-1580.39 -605.963)" fill="url(#linear-gradient)"/>
            <path id="Path_88230" data-name="Path 88230" d="M922.11,1561.563s.836-1.858,1.579-3.333l1.978,1.556s-1.982,2.364-1.85,3.007A4.593,4.593,0,0,0,922.11,1561.563Z" transform="translate(-844.631 -1322.289)" fill="url(#linear-gradient)"/>
            <path id="Path_88231" data-name="Path 88231" d="M1098.01,521.753s.836-1.858,1.579-3.333l1.978,1.556s-1.982,2.364-1.85,3.007A4.593,4.593,0,0,0,1098.01,521.753Z" transform="translate(-992.736 -446.786)" fill="url(#linear-gradient)"/>
            <path id="Path_88232" data-name="Path 88232" d="M1105.727,1123.89s.653,1.4,1.131,2.547l-1.868.367s-.378-2.3-.811-2.544A3.5,3.5,0,0,0,1105.727,1123.89Z" transform="translate(-997.931 -956.582)" fill="url(#linear-gradient)"/>
            <path id="Path_88233" data-name="Path 88233" d="M1701.667,1651.34s.653,1.4,1.131,2.547l-1.868.367s-.378-2.3-.811-2.544A3.5,3.5,0,0,0,1701.667,1651.34Z" transform="translate(-1499.703 -1400.686)" fill="url(#linear-gradient)"/>
            <path id="Path_88234" data-name="Path 88234" d="M1905.847,1162.2s.653,1.4,1.131,2.547l-1.868.367s-.378-2.3-.811-2.544A3.494,3.494,0,0,0,1905.847,1162.2Z" transform="translate(-1671.619 -988.838)" fill="url(#linear-gradient)"/>
            <path id="Path_88235" data-name="Path 88235" d="M758.842,555.63s3.272-.651,5.945-1.06l-.2,4.118s-5.038-.4-5.771.39a7.559,7.559,0,0,0,.022-3.446Z" transform="translate(-707.144 -477.224)" fill="url(#linear-gradient)"/>
            <path id="Path_88236" data-name="Path 88236" d="M514.512,400.06s3.273-.651,5.945-1.06l-.2,4.118s-5.038-.4-5.771.39a7.558,7.558,0,0,0,.022-3.446Z" transform="translate(-501.422 -346.237)" fill="url(#linear-gradient)"/>
            <path id="Path_88237" data-name="Path 88237" d="M1803.322,956.7s3.273-.651,5.945-1.06l-.2,4.118s-5.038-.4-5.771.39a7.557,7.557,0,0,0,.022-3.446Z" transform="translate(-1586.578 -814.918)" fill="url(#linear-gradient)"/>
            <path id="Path_88238" data-name="Path 88238" d="M714.657,1519.9s2.443-.485,4.437-.792l-.147,3.073s-3.761-.3-4.308.291a5.627,5.627,0,0,0,.016-2.573Z" transform="translate(-669.945 -1289.35)" fill="url(#linear-gradient)"/>
            <path id="Path_88239" data-name="Path 88239" d="M1562.51,560.413s.836-1.858,1.579-3.333l1.978,1.557s-1.981,2.364-1.85,3.007A4.591,4.591,0,0,0,1562.51,560.413Z" transform="translate(-1383.837 -479.337)" fill="url(#linear-gradient)"/>
            <g id="Group_14825" data-name="Group 14825" transform="translate(181.054 113.118)">
              <path id="Path_88240" data-name="Path 88240" d="M1578.17,801.1a4.414,4.414,0,0,0-.553,2.647c.256,1.726,1.683,3.02,1.95,4.769-.071-.471.85-1.136.77-1.667a9.117,9.117,0,0,0-1.27-3.037,8.123,8.123,0,0,1-.591-1.373,3.982,3.982,0,0,1-.142-.719c0-.1-.008-.2-.011-.3A1.326,1.326,0,0,1,1578.17,801.1Z" transform="translate(-1577.579 -797.916)" fill="url(#linear-gradient)"/>
              <path id="Path_88241" data-name="Path 88241" d="M1587.8,784.2c-.4-1.539-1.063-3.345-2.968-3.241-1.277.07-2.751,1.647-3.279,2.723-.076.155-.15.307-.221.457a1.386,1.386,0,0,0,.152.315c0-.019,0-.036,0-.055a1.03,1.03,0,0,1,.36-.457c.989-.792,2.125-1.621,3.378-1.119,1.157.465,1.528,1.978,1.808,3.043-.111-.419.9-1.18.77-1.667Z" transform="translate(-1580.737 -780.953)" fill="url(#linear-gradient)"/>
            </g>
            <path id="Path_88242" data-name="Path 88242" d="M1577.659,331.907c-1.936,2.138-1.33,5.145-.738,7.681a21.046,21.046,0,0,1,1.6-1.43c.176-.169,1.549-1.27,1.484-1.553a17.612,17.612,0,0,1-.591-4.2,5.532,5.532,0,0,1,.283-1.607c.063-.009.123-.025.187-.032.5-.085,1.263-1.1,1.6-1.43.248-.24,1.16-1.5,1.484-1.553-2.179.371-3.9,2.577-5.311,4.129Z" transform="translate(-1395.482 -286.271)" fill="url(#linear-gradient)"/>
            <path id="Path_88243" data-name="Path 88243" d="M1490.268,1104.006a16.57,16.57,0,0,0,5.467.386c-2.231.095-4.946-5.652-7.531-5.543a13.864,13.864,0,0,1-10.571-3.677c-.907-.038-1.812-.1-2.713-.221A41.524,41.524,0,0,0,1490.268,1104.006Z" transform="translate(-1310.088 -932.215)" fill="url(#linear-gradient)"/>
            <g id="Group_14826" data-name="Group 14826" transform="translate(199.991 208.194)">
              <path id="Path_88244" data-name="Path 88244" d="M1709.164,1389.715a5.629,5.629,0,0,0-1.184-3.238c-1.439-1.7-3.854-2.135-5.32-3.848.394.461-.156,1.8.289,2.321a11.606,11.606,0,0,0,3.42,2.432,10.1,10.1,0,0,1,1.563,1.089,4.942,4.942,0,0,1,.638.683l.218.322a1.756,1.756,0,0,1,.378.239Z" transform="translate(-1701.832 -1382.63)" fill="url(#linear-gradient)"/>
              <path id="Path_88245" data-name="Path 88245" d="M1697.709,1430.5c1.474,1.394,3.4,2.9,5.395,1.509,1.335-.935,1.869-3.636,1.716-5.155-.022-.218-.044-.431-.068-.642a1.756,1.756,0,0,0-.378-.239l.04.058a1.3,1.3,0,0,1-.084.738c-.537,1.523-1.209,3.186-2.9,3.486-1.563.277-2.987-1.112-4.006-2.076.4.379-.177,1.88.289,2.321Z" transform="translate(-1697.42 -1419.121)" fill="url(#linear-gradient)"/>
            </g>
            <path id="Path_88246" data-name="Path 88246" d="M872.577,869.587c1.944-2.448.935-5.379-.771-7.618a18.172,18.172,0,0,0-7.786-5.779c.741.324,1,1.91,1.812,2.268,1.075.469,2.132.958,3.165,1.514a13.6,13.6,0,0,1,1.692,1.116l.379.319q.112.244.408.82a5.092,5.092,0,0,1-.714,5.094c-.186.235,1.893,2.166,1.813,2.268Z" transform="translate(-795.72 -731.183)" fill="url(#linear-gradient)"/>
            <path id="Path_88247" data-name="Path 88247" d="M1478.036,281.448c-4.188-2.318-8.359-.057-11.311,3.083-3.671,3.906-5.456,8.019-6.765,13.213.3-1.2,2.634-1.988,2.966-3.309.439-1.741.908-3.462,1.489-5.161a21.037,21.037,0,0,1,1.258-2.838l.387-.654q.341-.232,1.136-.823a7.8,7.8,0,0,1,7.874-.2c.4.223,2.794-3.405,2.966-3.309Z" transform="translate(-1297.492 -246.459)" fill="url(#linear-gradient)"/>
            <path id="Path_88248" data-name="Path 88248" d="M1209.526,1371.1c.882-1.738,2.964-2.02,4.56-2.889a9.6,9.6,0,0,0,4.088-4.777c.68-1.656-1.664-3.478-2.98-4.1a2.142,2.142,0,0,1,.563.414c1.168,1.835-1.482,4.459-2.71,5.545-1.722,1.523-4.685,1.606-5.768,3.737C1207.114,1369.36,1209.609,1370.94,1209.526,1371.1Z" transform="translate(-1084.732 -1154.827)" fill="url(#linear-gradient)"/>
            <path id="Path_88249" data-name="Path 88249" d="M1640.04,543.969c-1.909-.4-2.726-2.332-3.984-3.644a9.6,9.6,0,0,0-5.682-2.693c-1.776-.221-2.92,2.517-3.175,3.95a2.08,2.08,0,0,1,.253-.651c1.465-1.609,4.691.262,6.061,1.163,1.922,1.263,2.778,4.1,5.118,4.587.356.074,1.226-2.748,1.408-2.712Z" transform="translate(-1438.305 -462.952)" fill="url(#linear-gradient)"/>
            <path id="Path_88250" data-name="Path 88250" d="M939.32,685.269c-1.909-.4-2.726-2.332-3.984-3.644a9.6,9.6,0,0,0-5.682-2.693c-1.776-.221-2.92,2.517-3.175,3.95a2.083,2.083,0,0,1,.253-.651c1.465-1.609,4.691.262,6.061,1.163,1.922,1.263,2.778,4.1,5.118,4.587.356.074,1.228-2.748,1.408-2.712Z" transform="translate(-848.311 -581.925)" fill="url(#linear-gradient)"/>
            <path id="Path_88251" data-name="Path 88251" d="M1274.1,970.984a5.67,5.67,0,0,0-.362-1.844c.231.73-1.429,2.143-1.161,2.99a4.751,4.751,0,0,1-.272,3.829c.068.3.128.608.169.918a14.218,14.218,0,0,0,1.624-5.891Z" transform="translate(-1139.485 -826.285)" fill="url(#linear-gradient)"/>
            <path id="Path_88252" data-name="Path 88252" d="M983.69,1275.43c-2.944,3.37-1.92,8.018-.931,11.937a33.1,33.1,0,0,1,2.446-2.263c.267-.267,2.369-2.013,2.258-2.451a27.365,27.365,0,0,1-1.035-6.5,8.555,8.555,0,0,1,.393-2.5c.1-.017.191-.044.289-.055.777-.147,1.928-1.745,2.446-2.263.379-.381,1.757-2.358,2.257-2.451-3.369.638-5.986,4.105-8.122,6.553Z" transform="translate(-894.847 -1078.661)" fill="url(#linear-gradient)"/>
            <path id="Path_88253" data-name="Path 88253" d="M922.81,326.616c1.056-2-.049-3.943-1.556-5.306a13.113,13.113,0,0,0-6.294-3.07c.569.131.961,1.225,1.588,1.368.825.191,1.64.4,2.448.656a9.793,9.793,0,0,1,1.349.567c.1.058.352.2.311.177.074.106.207.281.4.528a3.677,3.677,0,0,1,.167,3.712C921.121,325.439,922.853,326.534,922.81,326.616Z" transform="translate(-838.611 -278.238)" fill="url(#linear-gradient)"/>
            <path id="Path_88254" data-name="Path 88254" d="M1514.55,1686.786c1.056-2-.049-3.943-1.556-5.306a13.112,13.112,0,0,0-6.294-3.07c.569.131.961,1.225,1.588,1.368.825.191,1.64.4,2.448.656a9.793,9.793,0,0,1,1.349.567c.1.058.352.2.311.177.074.106.207.281.4.528a3.677,3.677,0,0,1,.167,3.712C1512.861,1685.609,1514.593,1686.7,1514.55,1686.786Z" transform="translate(-1336.846 -1423.478)" fill="url(#linear-gradient)"/>
            <path id="Path_88255" data-name="Path 88255" d="M999.82,73.466c1.056-2-.049-3.943-1.557-5.306a13.112,13.112,0,0,0-6.294-3.07c.569.131.961,1.225,1.588,1.368.825.191,1.64.4,2.448.656a9.8,9.8,0,0,1,1.349.567c.1.058.352.2.311.177.074.106.207.281.4.528a3.677,3.677,0,0,1,.167,3.712C998.131,72.289,999.863,73.384,999.82,73.466Z" transform="translate(-903.452 -65.09)" fill="url(#linear-gradient)"/>
            <path id="Path_88256" data-name="Path 88256" d="M1842.684,1333.576c1.285-3.061-.586-5.769-2.974-7.571a19.285,19.285,0,0,0-9.62-3.676c.85.119,1.568,1.667,2.5,1.8,1.234.172,2.456.367,3.671.64a14.4,14.4,0,0,1,2.051.656l.48.218c.123.145.34.385.653.722a5.41,5.41,0,0,1,.73,5.415c-.123.294,2.557,1.672,2.5,1.8Z" transform="translate(-1609.135 -1123.665)" fill="url(#linear-gradient)"/>
            <path id="Path_88257" data-name="Path 88257" d="M1117.17,1163.66c1.411.382,2.483-.593,3.129-1.738a8.49,8.49,0,0,0,1.043-4.412c0,.378-.637.781-.638,1.2,0,.548-.017,1.092-.065,1.639a6.237,6.237,0,0,1-.166.932c-.022.074-.076.251-.066.223-.057.062-.149.171-.277.327a2.375,2.375,0,0,1-2.32.634c-.136-.036-.58,1.214-.638,1.2Z" transform="translate(-1008.868 -984.889)" fill="url(#linear-gradient)"/>
            <path id="Path_88258" data-name="Path 88258" d="M1255.75,1480.982c2.086.564,3.671-.875,4.627-2.571a12.558,12.558,0,0,0,1.542-6.521c0,.558-.94,1.155-.943,1.77,0,.811-.025,1.615-.1,2.422a9.489,9.489,0,0,1-.245,1.379l-.1.329q-.126.138-.409.484a3.52,3.52,0,0,1-3.429.938c-.2-.053-.858,1.794-.943,1.77Z" transform="translate(-1125.55 -1249.592)" fill="url(#linear-gradient)"/>
            <path id="Path_88259" data-name="Path 88259" d="M755.222,1177.865c.659-2.113-.755-3.792-2.454-4.835a12.873,12.873,0,0,0-6.618-1.83c.572.025,1.146,1.008,1.776,1.035.83.036,1.653.088,2.478.193a9.53,9.53,0,0,1,1.4.3l.332.114c.092.089.25.234.479.438a3.607,3.607,0,0,1,.828,3.547c-.063.2,1.8.948,1.776,1.035Z" transform="translate(-696.476 -996.416)" fill="url(#linear-gradient)"/>
            <path id="Path_88260" data-name="Path 88260" d="M1330.185,1009.728s-2.245-.228-4.055-.493l.943-2.625s3.179,1.256,3.81.893a5.1,5.1,0,0,0-.7,2.225Z" transform="translate(-1184.809 -857.834)" fill="url(#linear-gradient)"/>
            <path id="Path_88261" data-name="Path 88261" d="M1429.675,1217.458s-3.32-.337-6-.728l1.394-3.879s4.7,1.857,5.632,1.319a7.554,7.554,0,0,0-1.032,3.288Z" transform="translate(-1266.945 -1031.484)" fill="url(#linear-gradient)"/>
            <path id="Path_88262" data-name="Path 88262" d="M1491.326,968.07s-2.551-.259-4.606-.559l1.071-2.98s3.611,1.427,4.327,1.013A5.812,5.812,0,0,0,1491.326,968.07Z" transform="translate(-1320.023 -822.403)" fill="url(#linear-gradient)"/>
            <path id="Path_88263" data-name="Path 88263" d="M1379.218,398.334s-.237-1.683-.368-3.054l2.089.218s-.348,2.55.033,2.945a3.847,3.847,0,0,0-1.752-.111Z" transform="translate(-1229.199 -343.104)" fill="url(#linear-gradient)"/>
            <path id="Path_88264" data-name="Path 88264" d="M1358.238,586.424s-.237-1.683-.368-3.054l2.089.218s-.348,2.55.033,2.945a3.847,3.847,0,0,0-1.752-.111Z" transform="translate(-1211.534 -501.473)" fill="url(#linear-gradient)"/>
            <path id="Path_88265" data-name="Path 88265" d="M1386.74,722.1c1.746.507,3.1-.681,3.93-2.092a10.554,10.554,0,0,0,1.4-5.463c-.011.469-.809.958-.82,1.474-.016.681-.046,1.359-.119,2.037a8.045,8.045,0,0,1-.228,1.157c-.03.092-.1.31-.088.275-.071.076-.188.209-.353.4a2.957,2.957,0,0,1-2.9.736c-.167-.049-.749,1.5-.82,1.474Z" transform="translate(-1235.842 -611.924)" fill="url(#linear-gradient)"/>
            <path id="Path_88266" data-name="Path 88266" d="M1092.661,374.915c-2.268-3.857-7-4.357-11.031-4.655.137.011,1.266,2.819,1.375,3.036.169.338,1.161,2.882,1.612,2.917a27.314,27.314,0,0,1,6.5,1.073,8.63,8.63,0,0,1,2.249,1.163c-.014.1-.019.194-.04.291-.106.784,1.046,2.38,1.375,3.036.24.48,1.681,2.413,1.612,2.917.46-3.4-2-6.976-3.65-9.776Z" transform="translate(-978.944 -322.038)" fill="url(#linear-gradient)"/>
            <path id="Path_88267" data-name="Path 88267" d="M870.09,489.072c2.086.564,3.671-.875,4.625-2.571a12.559,12.559,0,0,0,1.542-6.521c0,.558-.94,1.155-.943,1.77,0,.811-.025,1.615-.1,2.422a9.484,9.484,0,0,1-.245,1.379l-.1.329c-.084.092-.22.253-.409.484a3.52,3.52,0,0,1-3.429.939c-.2-.054-.858,1.793-.943,1.77Z" transform="translate(-800.831 -414.42)" fill="url(#linear-gradient)"/>
            <g id="Group_14827" data-name="Group 14827" transform="translate(119.9 123.636)">
              <path id="Path_88268" data-name="Path 88268" d="M1218.53,863.129a4.323,4.323,0,0,0,2.609-.477c1.473-.872,2.119-2.647,3.607-3.535-.4.239-1.346-.352-1.8-.082a8.908,8.908,0,0,0-2.286,2.279,8,8,0,0,1-1.027,1.044,3.779,3.779,0,0,1-.6.4l-.273.123a1.393,1.393,0,0,1-.229.254Z" transform="translate(-1214.112 -857.152)" fill="url(#linear-gradient)"/>
              <path id="Path_88269" data-name="Path 88269" d="M1192.651,847.591c-1.248.937-2.644,2.2-1.844,3.9.536,1.135,2.514,1.888,3.686,1.969l.5.032a1.3,1.3,0,0,0,.229-.254l-.049.022a1.007,1.007,0,0,1-.548-.158c-1.086-.605-2.258-1.329-2.266-2.655-.006-1.221,1.231-2.121,2.094-2.768C1194.109,847.928,1193.046,847.3,1192.651,847.591Z" transform="translate(-1190.572 -847.511)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14828" data-name="Group 14828" transform="translate(217.863 154.248)">
              <path id="Path_88270" data-name="Path 88270" d="M1838.48,1056.859a4.324,4.324,0,0,0,2.609-.477c1.473-.872,2.119-2.647,3.607-3.535-.4.239-1.346-.352-1.8-.082a8.928,8.928,0,0,0-2.286,2.279,8.04,8.04,0,0,1-1.027,1.045,3.8,3.8,0,0,1-.6.4l-.273.123a1.387,1.387,0,0,1-.229.254Z" transform="translate(-1834.062 -1050.882)" fill="url(#linear-gradient)"/>
              <path id="Path_88271" data-name="Path 88271" d="M1812.6,1041.321c-1.248.937-2.644,2.2-1.844,3.9.536,1.135,2.514,1.888,3.687,1.969l.5.032a1.287,1.287,0,0,0,.229-.255l-.049.022a1.007,1.007,0,0,1-.548-.158c-1.086-.605-2.258-1.329-2.266-2.655-.006-1.222,1.231-2.121,2.094-2.769C1814.059,1041.657,1813,1041.025,1812.6,1041.321Z" transform="translate(-1810.522 -1041.24)" fill="url(#linear-gradient)"/>
            </g>
            <g id="Group_14829" data-name="Group 14829" transform="translate(118.91 59.835)">
              <path id="Path_88272" data-name="Path 88272" d="M1212.26,459.369a4.325,4.325,0,0,0,2.609-.477c1.473-.872,2.119-2.647,3.607-3.535-.4.239-1.346-.352-1.8-.082a8.906,8.906,0,0,0-2.286,2.279,8.012,8.012,0,0,1-1.027,1.044,3.78,3.78,0,0,1-.6.4l-.273.123a1.4,1.4,0,0,1-.229.254Z" transform="translate(-1207.842 -453.393)" fill="url(#linear-gradient)"/>
              <path id="Path_88273" data-name="Path 88273" d="M1186.381,443.831c-1.248.937-2.644,2.2-1.844,3.9.536,1.135,2.514,1.888,3.686,1.969l.5.032a1.294,1.294,0,0,0,.229-.254l-.049.022a1.007,1.007,0,0,1-.548-.158c-1.086-.605-2.258-1.329-2.266-2.655-.006-1.221,1.231-2.121,2.094-2.768C1187.839,444.168,1186.776,443.536,1186.381,443.831Z" transform="translate(-1184.302 -443.751)" fill="url(#linear-gradient)"/>
            </g>
            <path id="Path_88274" data-name="Path 88274" d="M1096,805.411c.284,1.544,1.727,2.132,3.137,2.226a9.123,9.123,0,0,0,4.76-1.037c-.367.174-1.052-.254-1.457-.063-.533.251-1.067.49-1.618.7a6.835,6.835,0,0,1-.981.272l-.247.038c-.087-.025-.234-.065-.446-.117a2.556,2.556,0,0,1-1.691-1.953C1097.43,805.326,1096.013,805.474,1096,805.411Z" transform="translate(-991.044 -688.423)" fill="url(#linear-gradient)"/>
            <path id="Path_88275" data-name="Path 88275" d="M1223.607,294.382s3.32.343,5.994.74L1228.2,299s-4.7-1.866-5.629-1.331a7.544,7.544,0,0,0,1.037-3.287Z" transform="translate(-1097.613 -258.148)" fill="url(#linear-gradient)"/>
            <path id="Path_88276" data-name="Path 88276" d="M1504.016,1455.15s4.5.466,8.124,1l-1.9,5.254s-6.365-2.528-7.631-1.8A10.256,10.256,0,0,0,1504.016,1455.15Z" transform="translate(-1333.402 -1235.497)" fill="url(#linear-gradient)"/>
            <path id="Path_88277" data-name="Path 88277" d="M1650.064,681.5s2.478.256,4.474.553l-1.046,2.893s-3.5-1.392-4.2-.992A5.632,5.632,0,0,0,1650.064,681.5Z" transform="translate(-1456.904 -584.097)" fill="url(#linear-gradient)"/>
            <path id="Path_88278" data-name="Path 88278" d="M867.714,924.6s2.478.256,4.474.553l-1.046,2.893s-3.5-1.392-4.2-.992A5.632,5.632,0,0,0,867.714,924.6Z" transform="translate(-798.179 -788.783)" fill="url(#linear-gradient)"/>
            <path id="Path_88279" data-name="Path 88279" d="M1634.334,1283.02s2.478.256,4.474.553l-1.046,2.893s-3.5-1.392-4.2-.992a5.632,5.632,0,0,0,.774-2.454Z" transform="translate(-1443.66 -1090.567)" fill="url(#linear-gradient)"/>
            <path id="Path_88280" data-name="Path 88280" d="M1862.174,1769.51s2.478.256,4.474.553l-1.046,2.893s-3.5-1.392-4.2-.992A5.633,5.633,0,0,0,1862.174,1769.51Z" transform="translate(-1635.498 -1500.183)" fill="url(#linear-gradient)"/>
            <path id="Path_88281" data-name="Path 88281" d="M1569.447,930.9s-1.817,3.857-3.426,6.913l-4.082-3.328s4.235-4.873,3.982-6.224a9.658,9.658,0,0,0,3.524,2.641Z" transform="translate(-1383.357 -791.865)" fill="url(#linear-gradient)"/>
            <path id="Path_88282" data-name="Path 88282" d="M1258.221,1675.66c-4.466.194-6.687,4.031-7.507,7.972-1.019,4.9-.438,9.05,1.054,13.826-.345-1.1,1.13-2.868.751-4.083-.5-1.6-.964-3.2-1.331-4.838a19.323,19.323,0,0,1-.381-2.874c0-.237-.013-.8-.011-.709.106-.234.273-.635.5-1.209a7.279,7.279,0,0,1,6.174-4c.428-.019.566-4.075.751-4.083Z" transform="translate(-1120.879 -1421.163)" fill="url(#linear-gradient)"/>
            <path id="Path_88283" data-name="Path 88283" d="M1437.029,1343.511c3.222-.8,4.271-3.922,4.285-6.913a19.3,19.3,0,0,0-2.811-9.908c.414.752-.4,2.255.059,3.083.6,1.092,1.176,2.187,1.684,3.325a14.6,14.6,0,0,1,.7,2.035c.038.172.128.581.112.515-.043.186-.1.5-.188.954a5.41,5.41,0,0,1-3.9,3.825c-.31.078.191,3.05.058,3.083Z" transform="translate(-1278.049 -1127.336)" fill="url(#linear-gradient)"/>
            <path id="Path_88284" data-name="Path 88284" d="M1270.671,541.219c-1.642-1.637-4.08-1.266-6.141-.888.071-.013,1,1.248,1.087,1.342.13.147.961,1.293,1.191,1.251a14.135,14.135,0,0,1,3.4-.313,4.431,4.431,0,0,1,1.28.289c0,.051.016.1.019.152.049.406.836,1.056,1.087,1.342.183.209,1.158.989,1.191,1.252-.215-1.764-1.92-3.235-3.111-4.423Z" transform="translate(-1132.943 -464.962)" fill="url(#linear-gradient)"/>
            <path id="Path_88285" data-name="Path 88285" d="M1163.086,1260.845s-3.043-.31-5.5-.668l1.278-3.557s4.309,1.7,5.164,1.209a6.928,6.928,0,0,0-.945,3.015Z" transform="translate(-1042.901 -1068.338)" fill="url(#linear-gradient)"/>
            <path id="Path_88286" data-name="Path 88286" d="M1338.046,734.64s2.367.706,4.259,1.362l-1.552,2.628s-3.16-2-3.912-1.741A5.58,5.58,0,0,0,1338.046,734.64Z" transform="translate(-1193.827 -628.84)" fill="url(#linear-gradient)"/>
            <path id="Path_88287" data-name="Path 88287" d="M1985.686,1034.89s2.367.706,4.259,1.362l-1.552,2.628s-3.16-2-3.912-1.741A5.579,5.579,0,0,0,1985.686,1034.89Z" transform="translate(-1739.129 -881.645)" fill="url(#linear-gradient)"/>
            <path id="Path_88288" data-name="Path 88288" d="M796.046,769.34s2.367.706,4.259,1.362l-1.552,2.628s-3.16-2-3.913-1.741A5.58,5.58,0,0,0,796.046,769.34Z" transform="translate(-737.472 -658.057)" fill="url(#linear-gradient)"/>
            <path id="Path_88289" data-name="Path 88289" d="M959.35,1647.681c2.783,1.809,5.782.45,7.978-1.579a19.279,19.279,0,0,0,5.33-8.813c-.269.815-1.92,1.247-2.215,2.144-.389,1.184-.8,2.351-1.283,3.5a14.39,14.39,0,0,1-1.01,1.9c-.1.144-.338.49-.3.433q-.247.142-.826.514a5.407,5.407,0,0,1-5.458-.245c-.267-.174-2.1,2.219-2.215,2.144Z" transform="translate(-875.987 -1388.856)" fill="url(#linear-gradient)"/>
          </g>
        </g>
      </g>
    </g>
    <g id="coin" transform="translate(17617.865 2117.255)">
      <g id="Group_11178" data-name="Group 11178" transform="translate(14.406 12.705)">
        <ellipse id="Ellipse_100" data-name="Ellipse 100" cx="85.46" cy="86.453" rx="85.46" ry="86.453" transform="translate(0 0)" fill="#f39e09"/>
      </g>
      <path id="Path_80816" data-name="Path 80816" d="M853.286,1082.832a77.532,77.532,0,0,1,4.3,14.691l-95.343,90.524a77.757,77.757,0,0,1-14.444-5.057Zm-30.077-36.459a78.72,78.72,0,0,1,24.65,25.548L737.183,1177a78.691,78.691,0,0,1-24.247-25.932ZM798.726,1035.9a77.776,77.776,0,0,1,14.518,4.988l-105.262,99.94a77.66,77.66,0,0,1-4.225-14.764Z" transform="translate(-670.71 -1019.931)" fill="#faa50f" fill-rule="evenodd"/>
      <g id="Group_11184" data-name="Group 11184" transform="translate(0)">
        <path id="Path_80818" data-name="Path 80818" d="M105.18,6.044a99.136,99.136,0,1,0,99.136,99.136A99.185,99.185,0,0,0,105.18,6.044Zm0,16.252A82.884,82.884,0,1,1,22.3,105.18,82.923,82.923,0,0,1,105.18,22.3Z" transform="translate(-6.044 -6.044)" fill="#ffd400" fill-rule="evenodd"/>
        <g id="Group_11183" data-name="Group 11183" transform="translate(0 0)" clip-path="url(#clip-path)">
          <g id="Group_11180" data-name="Group 11180" transform="matrix(0.689, 0.725, -0.725, 0.689, 177.368, 2.248)">
            <path id="Path_80819" data-name="Path 80819" d="M0,0H35.835V260.811H0Z" transform="translate(0 0)" fill="#ffe66d" fill-rule="evenodd"/>
          </g>
          <g id="Group_11181" data-name="Group 11181" transform="matrix(0.688, 0.725, -0.725, 0.689, 160.074, -15.356)">
            <path id="Path_80820" data-name="Path 80820" d="M0,0H13.74V260.811H0Z" transform="translate(0 0)" fill="#ffe66d" fill-rule="evenodd"/>
          </g>
          <g id="Group_11182" data-name="Group 11182" transform="matrix(0.688, 0.725, -0.725, 0.689, 210.315, 36.591)">
            <path id="Path_80821" data-name="Path 80821" d="M0,0H13.74V260.811H0Z" transform="translate(0 0)" fill="#ffe66d" fill-rule="evenodd"/>
          </g>
        </g>
      </g>
      <g id="Group_11188" data-name="Group 11188" transform="translate(0.072 30.999)">
        <g id="Group_11185" data-name="Group 11185" transform="translate(3.318 93.927)">
          <path id="Path_80823" data-name="Path 80823" d="M206.157,161.259a99.182,99.182,0,0,1-191.539,0,99.854,99.854,0,0,0,191.539,0Z" transform="translate(-14.618 -161.259)" fill="#fff5b3" fill-rule="evenodd"/>
        </g>
        <g id="Group_11186" data-name="Group 11186" transform="translate(14.457 0)">
          <path id="Path_80824" data-name="Path 80824" d="M65.879,44.816a82.9,82.9,0,1,0,94.358,0,84.63,84.63,0,1,1-94.358,0Z" transform="translate(-28.427 -44.816)" fill="#fff5b3" fill-rule="evenodd"/>
        </g>
        <g id="Group_11187" data-name="Group 11187" transform="translate(0 73.703)">
          <path id="Path_80825" data-name="Path 80825" d="M208.6,136.186q.086,2.007.084,4.034a99.087,99.087,0,0,1-198.175,0q0-2.027.084-4.034a99.158,99.158,0,0,0,198.006,0Z" transform="translate(-10.505 -136.186)" fill="#b05d01" fill-rule="evenodd"/>
        </g>
      </g>
    </g>
    <g id="trophy_861506" transform="translate(17653.422 2154.453)">
      <g id="Group_14404" data-name="Group 14404" transform="translate(0 0)">
        <g data-type="innerShadowGroup">
          <path id="Path_87846-2" data-name="Path 87846" d="M63.579,0A63.579,63.579,0,1,1,0,63.579,63.579,63.579,0,0,1,63.579,0Z" fill="#b05d01"/>
          <g transform="matrix(1, 0, 0, 1, -90.75, -71.43)" filter="url(#Path_87846)">
            <path id="Path_87846-3" data-name="Path 87846" d="M63.579,0A63.579,63.579,0,1,1,0,63.579,63.579,63.579,0,0,1,63.579,0Z" transform="translate(90.75 71.43)" fill="#fff"/>
          </g>
        </g>
      </g>
      <path id="Path_87750" data-name="Path 87750" d="M254.679,168a5.028,5.028,0,0,1-2.349-.623L239.6,160.688a3.372,3.372,0,0,0-2.392,0l-12.733,6.694a5.039,5.039,0,0,1-2.349.623,3.794,3.794,0,0,1-2.955-1.347,4.534,4.534,0,0,1-.87-3.762l2.432-14.178a3.358,3.358,0,0,0-.739-2.275l-10.3-10.041a4.077,4.077,0,0,1,2.359-7.259l14.235-2.068a3.357,3.357,0,0,0,1.935-1.406l6.366-12.9a4.077,4.077,0,0,1,7.632,0l6.367,12.9a3.354,3.354,0,0,0,1.935,1.406l14.236,2.068a4.077,4.077,0,0,1,2.359,7.258l-10.3,10.042a3.354,3.354,0,0,0-.74,2.275L258.5,162.9a4.536,4.536,0,0,1-.869,3.762A3.8,3.8,0,0,1,254.679,168Z" transform="translate(-174.243 -79.369)" fill="#ffd00d"/>
      <path id="Path_87751" data-name="Path 87751" d="M228.729,172.117l2.635-15.363a3.637,3.637,0,0,0-.8-2.466L219.4,143.407a4.69,4.69,0,0,1-1.514-4.536l-5.841.849a4.077,4.077,0,0,0-2.359,7.259l10.3,10.042a3.357,3.357,0,0,1,.739,2.275l-2.432,14.178a4.538,4.538,0,0,0,.87,3.762,3.8,3.8,0,0,0,2.955,1.347,5.039,5.039,0,0,0,2.349-.623l4.722-2.483A5.2,5.2,0,0,1,228.729,172.117Z" transform="translate(-174.242 -89.946)" fill="#ffaf10"/>
    </g>
  </g>
</svg>
