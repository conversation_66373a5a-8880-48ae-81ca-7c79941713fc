<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="39.584" height="36.866" viewBox="0 0 39.584 36.866">
  <defs>
    <filter id="Path_61156" x="6.959" y="0" width="25.665" height="25.665" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_61157" x="15.525" y="2.636" width="23.03" height="23.03" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_61158" x="0" y="8.565" width="39.584" height="28.301" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-3"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-3"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_61159" x="1.029" y="2.636" width="23.03" height="23.03" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-4"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-4"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="customer" transform="translate(9.25 6.25)">
    <g transform="matrix(1, 0, 0, 1, -9.25, -6.25)" filter="url(#Path_61156)">
      <path id="Path_61156-2" data-name="Path 61156" d="M172.583,40.165A3.583,3.583,0,1,0,169,36.583,3.587,3.587,0,0,0,172.583,40.165Zm0-5.93a2.347,2.347,0,1,1-2.347,2.347A2.35,2.35,0,0,1,172.583,34.235Z" transform="translate(-152.79 -26.75)" fill="#2c3c64" stroke="#2c3c64" stroke-width="0.5"/>
    </g>
    <g transform="matrix(1, 0, 0, 1, -9.25, -6.25)" filter="url(#Path_61157)">
      <path id="Path_61157-2" data-name="Path 61157" d="M379.265,101.53A2.265,2.265,0,1,0,377,99.265,2.267,2.267,0,0,0,379.265,101.53Zm0-3.294a1.03,1.03,0,1,1-1.029,1.03A1.031,1.031,0,0,1,379.265,98.235Z" transform="translate(-352.23 -88.11)" fill="#2c3c64" stroke="#2c3c64" stroke-width="0.5"/>
    </g>
    <g transform="matrix(1, 0, 0, 1, -9.25, -6.25)" filter="url(#Path_61158)">
      <path id="Path_61158-2" data-name="Path 61158" d="M18.288,241h-.955a2.8,2.8,0,0,0-2.541,1.621A4.972,4.972,0,0,0,11.118,241H9.967a4.972,4.972,0,0,0-3.674,1.621A2.8,2.8,0,0,0,3.752,241H2.8A2.791,2.791,0,0,0,0,243.779v4.5a1.21,1.21,0,0,0,1.211,1.207H4.989A1.444,1.444,0,0,0,6.425,250.8h8.234a1.444,1.444,0,0,0,1.436-1.318H19.83a1.253,1.253,0,0,0,1.254-1.249v-4.454A2.791,2.791,0,0,0,18.288,241ZM1.235,243.779A1.554,1.554,0,0,1,2.8,242.235h.955a1.554,1.554,0,0,1,1.561,1.544v.423c-.415,1.082-.329,1.73-.329,4.045H1.235Zm13.631,5.579a.207.207,0,0,1-.207.207H6.425a.207.207,0,0,1-.207-.207v-3.375a3.753,3.753,0,0,1,3.748-3.748h1.151a3.753,3.753,0,0,1,3.748,3.748Zm4.983-1.125c0,.021.23.014-3.747.014,0-2.333.085-2.966-.329-4.045v-.423a1.554,1.554,0,0,1,1.561-1.544h.955a1.554,1.554,0,0,1,1.561,1.544Z" transform="translate(9.25 -226.18)" fill="#2c3c64" stroke="#2c3c64" stroke-width="0.5"/>
    </g>
    <g transform="matrix(1, 0, 0, 1, -9.25, -6.25)" filter="url(#Path_61159)">
      <path id="Path_61159-2" data-name="Path 61159" d="M27.265,101.53A2.265,2.265,0,1,0,25,99.265,2.267,2.267,0,0,0,27.265,101.53Zm0-3.294a1.03,1.03,0,1,1-1.03,1.03A1.031,1.031,0,0,1,27.265,98.235Z" transform="translate(-14.72 -88.11)" fill="#2c3c64" stroke="#2c3c64" stroke-width="0.5"/>
    </g>
  </g>
</svg>
