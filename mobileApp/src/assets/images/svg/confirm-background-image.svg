<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="414" height="896" viewBox="0 0 414 896">
  <defs>
    <linearGradient id="linear-gradient" x1="0.541" y1="0.21" x2="0.5" y2="1.316" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#dfe3ef"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#2c3c64"/>
      <stop offset="1" stop-color="#eceef6"/>
    </linearGradient>
    <clipPath id="clip-iPhone_XR_XS_Max_11_53">
      <rect width="414" height="896"/>
    </clipPath>
  </defs>
  <g id="iPhone_XR_XS_Max_11_53" data-name="iPhone XR, XS Max, 11 – 53" clip-path="url(#clip-iPhone_XR_XS_Max_11_53)">
    <rect width="414" height="896" fill="#fff"/>
    <path id="Home_Indicator" data-name="Home Indicator" d="M252.5,21h-129a2.5,2.5,0,0,0,0,5h129a2.5,2.5,0,0,0,0-5Z" transform="translate(23 858)"/>
    <path id="Path_61225" data-name="Path 61225" d="M0,0H414V896H0Z" fill="url(#linear-gradient)"/>
    <circle id="Ellipse_1" data-name="Ellipse 1" cx="3" cy="3" r="3" transform="translate(194 954)" fill="#dfdfdf"/>
    <circle id="Ellipse_2" data-name="Ellipse 2" cx="3" cy="3" r="3" transform="translate(205 954)" fill="#dfdfdf"/>
    <rect id="Rectangle_4641" data-name="Rectangle 4641" width="20" height="6" rx="3" transform="translate(171 954)" fill="#dfdfdf"/>
    <circle id="Ellipse_3" data-name="Ellipse 3" cx="3" cy="3" r="3" transform="translate(215 954)" fill="#dfdfdf"/>
    <circle id="Ellipse_4" data-name="Ellipse 4" cx="3" cy="3" r="3" transform="translate(226 954)" fill="#dfdfdf"/>
    <circle id="Ellipse_5" data-name="Ellipse 5" cx="3" cy="3" r="3" transform="translate(237 954)" fill="#dfdfdf"/>
    <path id="angle-circle-right" d="M15.451,10.27l-4.742-4.4L9.289,7.193l4.739,4.4-4.7,4.36,1.422,1.319,4.7-4.36A1.772,1.772,0,0,0,15.451,10.27Z" transform="translate(333.714 797.617)" fill="#fff"/>
    <g id="Group_10335" data-name="Group 10335" transform="translate(584 -565.831)">
      <g id="Rectangle_713" data-name="Rectangle 713" transform="translate(0 0.242)" fill="#fff" stroke="rgba(230,230,230,0.69)" stroke-width="1">
        <rect width="342" height="56.589" rx="10" stroke="none"/>
        <rect x="0.5" y="0.5" width="341" height="55.589" rx="9.5" fill="none"/>
      </g>
      <g id="Group_3260" data-name="Group 3260" transform="translate(22 21.242)">
        <text id="Username" transform="translate(0 12)" fill="#71828a" font-size="12" font-family="Montserrat-Regular, Montserrat"><tspan x="0" y="0">Email*</tspan></text>
      </g>
    </g>
    <g id="Group_10336" data-name="Group 10336" transform="translate(584 -500.831)">
      <g id="Rectangle_713-2" data-name="Rectangle 713" transform="translate(0 0.242)" fill="#fff" stroke="rgba(230,230,230,0.69)" stroke-width="1">
        <rect width="342" height="56.589" rx="10" stroke="none"/>
        <rect x="0.5" y="0.5" width="341" height="55.589" rx="9.5" fill="none"/>
      </g>
      <g id="Group_3260-2" data-name="Group 3260" transform="translate(22 21.242)">
        <text id="Username-2" data-name="Username" transform="translate(0 12)" fill="#71828a" font-size="12" font-family="Montserrat-Regular, Montserrat"><tspan x="0" y="0">Date of birth*</tspan></text>
      </g>
    </g>
    <g id="Group_10337" data-name="Group 10337" transform="translate(584 -435.831)">
      <g id="Rectangle_713-3" data-name="Rectangle 713" transform="translate(0 0.242)" fill="#fff" stroke="rgba(230,230,230,0.69)" stroke-width="1">
        <rect width="342" height="56.589" rx="10" stroke="none"/>
        <rect x="0.5" y="0.5" width="341" height="55.589" rx="9.5" fill="none"/>
      </g>
      <g id="Group_3260-3" data-name="Group 3260" transform="translate(22 21.242)">
        <text id="Username-3" data-name="Username" transform="translate(0 12)" fill="#71828a" font-size="12" font-family="Montserrat-Regular, Montserrat"><tspan x="0" y="0">Anniversary Date*</tspan></text>
      </g>
    </g>
    <g id="calendar_1_" data-name="calendar (1)" transform="translate(880.108 -482.73)">
      <g id="Group_10303" data-name="Group 10303">
        <g id="Group_10302" data-name="Group 10302">
          <path id="Path_62683" data-name="Path 62683" d="M19.716,1.745H18.669V0H16.924V1.745H5.409V0H3.664V1.745H2.617A2.62,2.62,0,0,0,0,4.362V19.716a2.62,2.62,0,0,0,2.617,2.617h17.1a2.62,2.62,0,0,0,2.617-2.617V4.362A2.62,2.62,0,0,0,19.716,1.745Zm.872,17.971a.873.873,0,0,1-.872.872H2.617a.873.873,0,0,1-.872-.872V8.2H20.588Zm0-13.26H1.745V4.362a.873.873,0,0,1,.872-.872H3.664V5.234H5.409V3.49H16.924V5.234h1.745V3.49h1.047a.873.873,0,0,1,.872.872Z" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10305" data-name="Group 10305" transform="translate(3.436 9.878)">
        <g id="Group_10304" data-name="Group 10304" transform="translate(0 0)">
          <rect id="Rectangle_4865" data-name="Rectangle 4865" width="2" height="1" transform="translate(-0.436 0.455)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10307" data-name="Group 10307" transform="translate(6.872 9.878)">
        <g id="Group_10306" data-name="Group 10306" transform="translate(0 0)">
          <rect id="Rectangle_4866" data-name="Rectangle 4866" width="2" height="1" transform="translate(0.128 0.455)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10309" data-name="Group 10309" transform="translate(10.308 9.878)">
        <g id="Group_10308" data-name="Group 10308" transform="translate(0 0)">
          <rect id="Rectangle_4867" data-name="Rectangle 4867" width="2" height="1" transform="translate(-0.308 0.455)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10311" data-name="Group 10311" transform="translate(13.744 9.878)">
        <g id="Group_10310" data-name="Group 10310" transform="translate(0 0)">
          <rect id="Rectangle_4868" data-name="Rectangle 4868" width="1" height="1" transform="translate(0.256 0.455)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10313" data-name="Group 10313" transform="translate(17.179 9.878)">
        <g id="Group_10312" data-name="Group 10312" transform="translate(0 0)">
          <rect id="Rectangle_4869" data-name="Rectangle 4869" width="2" height="1" transform="translate(-0.179 0.455)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10315" data-name="Group 10315" transform="translate(3.436 13.314)">
        <g id="Group_10314" data-name="Group 10314" transform="translate(0 0)">
          <rect id="Rectangle_4870" data-name="Rectangle 4870" width="2" height="2" transform="translate(-0.436 0.019)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10317" data-name="Group 10317" transform="translate(6.872 13.314)">
        <g id="Group_10316" data-name="Group 10316" transform="translate(0 0)">
          <rect id="Rectangle_4871" data-name="Rectangle 4871" width="2" height="2" transform="translate(0.128 0.019)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10319" data-name="Group 10319" transform="translate(10.308 13.314)">
        <g id="Group_10318" data-name="Group 10318" transform="translate(0 0)">
          <rect id="Rectangle_4872" data-name="Rectangle 4872" width="2" height="2" transform="translate(-0.308 0.019)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10321" data-name="Group 10321" transform="translate(13.744 13.314)">
        <g id="Group_10320" data-name="Group 10320" transform="translate(0 0)">
          <rect id="Rectangle_4873" data-name="Rectangle 4873" width="1" height="2" transform="translate(0.256 0.019)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10323" data-name="Group 10323" transform="translate(3.436 17.179)">
        <g id="Group_10322" data-name="Group 10322" transform="translate(0 0)">
          <rect id="Rectangle_4874" data-name="Rectangle 4874" width="2" height="2" transform="translate(-0.436 0.154)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10325" data-name="Group 10325" transform="translate(6.872 17.179)">
        <g id="Group_10324" data-name="Group 10324" transform="translate(0 0)">
          <rect id="Rectangle_4875" data-name="Rectangle 4875" width="2" height="2" transform="translate(0.128 0.154)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10327" data-name="Group 10327" transform="translate(10.308 17.179)">
        <g id="Group_10326" data-name="Group 10326" transform="translate(0 0)">
          <rect id="Rectangle_4876" data-name="Rectangle 4876" width="2" height="2" transform="translate(-0.308 0.154)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10329" data-name="Group 10329" transform="translate(13.744 17.179)">
        <g id="Group_10328" data-name="Group 10328" transform="translate(0 0)">
          <rect id="Rectangle_4877" data-name="Rectangle 4877" width="1" height="2" transform="translate(0.256 0.154)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10331" data-name="Group 10331" transform="translate(17.179 13.314)">
        <g id="Group_10330" data-name="Group 10330" transform="translate(0 0)">
          <rect id="Rectangle_4878" data-name="Rectangle 4878" width="2" height="2" transform="translate(-0.179 0.019)" fill="#585858"/>
        </g>
      </g>
    </g>
    <g id="calendar_1_2" data-name="calendar (1)" transform="translate(880.108 -418.73)">
      <g id="Group_10303-2" data-name="Group 10303">
        <g id="Group_10302-2" data-name="Group 10302">
          <path id="Path_62683-2" data-name="Path 62683" d="M19.716,1.745H18.669V0H16.924V1.745H5.409V0H3.664V1.745H2.617A2.62,2.62,0,0,0,0,4.362V19.716a2.62,2.62,0,0,0,2.617,2.617h17.1a2.62,2.62,0,0,0,2.617-2.617V4.362A2.62,2.62,0,0,0,19.716,1.745Zm.872,17.971a.873.873,0,0,1-.872.872H2.617a.873.873,0,0,1-.872-.872V8.2H20.588Zm0-13.26H1.745V4.362a.873.873,0,0,1,.872-.872H3.664V5.234H5.409V3.49H16.924V5.234h1.745V3.49h1.047a.873.873,0,0,1,.872.872Z" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10305-2" data-name="Group 10305" transform="translate(3.436 9.878)">
        <g id="Group_10304-2" data-name="Group 10304" transform="translate(0 0)">
          <rect id="Rectangle_4865-2" data-name="Rectangle 4865" width="2" height="1" transform="translate(-0.436 0.455)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10307-2" data-name="Group 10307" transform="translate(6.872 9.878)">
        <g id="Group_10306-2" data-name="Group 10306" transform="translate(0 0)">
          <rect id="Rectangle_4866-2" data-name="Rectangle 4866" width="2" height="1" transform="translate(0.128 0.455)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10309-2" data-name="Group 10309" transform="translate(10.308 9.878)">
        <g id="Group_10308-2" data-name="Group 10308" transform="translate(0 0)">
          <rect id="Rectangle_4867-2" data-name="Rectangle 4867" width="2" height="1" transform="translate(-0.308 0.455)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10311-2" data-name="Group 10311" transform="translate(13.744 9.878)">
        <g id="Group_10310-2" data-name="Group 10310" transform="translate(0 0)">
          <rect id="Rectangle_4868-2" data-name="Rectangle 4868" width="1" height="1" transform="translate(0.256 0.455)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10313-2" data-name="Group 10313" transform="translate(17.179 9.878)">
        <g id="Group_10312-2" data-name="Group 10312" transform="translate(0 0)">
          <rect id="Rectangle_4869-2" data-name="Rectangle 4869" width="2" height="1" transform="translate(-0.179 0.455)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10315-2" data-name="Group 10315" transform="translate(3.436 13.314)">
        <g id="Group_10314-2" data-name="Group 10314" transform="translate(0 0)">
          <rect id="Rectangle_4870-2" data-name="Rectangle 4870" width="2" height="2" transform="translate(-0.436 0.019)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10317-2" data-name="Group 10317" transform="translate(6.872 13.314)">
        <g id="Group_10316-2" data-name="Group 10316" transform="translate(0 0)">
          <rect id="Rectangle_4871-2" data-name="Rectangle 4871" width="2" height="2" transform="translate(0.128 0.019)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10319-2" data-name="Group 10319" transform="translate(10.308 13.314)">
        <g id="Group_10318-2" data-name="Group 10318" transform="translate(0 0)">
          <rect id="Rectangle_4872-2" data-name="Rectangle 4872" width="2" height="2" transform="translate(-0.308 0.019)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10321-2" data-name="Group 10321" transform="translate(13.744 13.314)">
        <g id="Group_10320-2" data-name="Group 10320" transform="translate(0 0)">
          <rect id="Rectangle_4873-2" data-name="Rectangle 4873" width="1" height="2" transform="translate(0.256 0.019)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10323-2" data-name="Group 10323" transform="translate(3.436 17.179)">
        <g id="Group_10322-2" data-name="Group 10322" transform="translate(0 0)">
          <rect id="Rectangle_4874-2" data-name="Rectangle 4874" width="2" height="2" transform="translate(-0.436 0.154)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10325-2" data-name="Group 10325" transform="translate(6.872 17.179)">
        <g id="Group_10324-2" data-name="Group 10324" transform="translate(0 0)">
          <rect id="Rectangle_4875-2" data-name="Rectangle 4875" width="2" height="2" transform="translate(0.128 0.154)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10327-2" data-name="Group 10327" transform="translate(10.308 17.179)">
        <g id="Group_10326-2" data-name="Group 10326" transform="translate(0 0)">
          <rect id="Rectangle_4876-2" data-name="Rectangle 4876" width="2" height="2" transform="translate(-0.308 0.154)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10329-2" data-name="Group 10329" transform="translate(13.744 17.179)">
        <g id="Group_10328-2" data-name="Group 10328" transform="translate(0 0)">
          <rect id="Rectangle_4877-2" data-name="Rectangle 4877" width="1" height="2" transform="translate(0.256 0.154)" fill="#585858"/>
        </g>
      </g>
      <g id="Group_10331-2" data-name="Group 10331" transform="translate(17.179 13.314)">
        <g id="Group_10330-2" data-name="Group 10330" transform="translate(0 0)">
          <rect id="Rectangle_4878-2" data-name="Rectangle 4878" width="2" height="2" transform="translate(-0.179 0.019)" fill="#585858"/>
        </g>
      </g>
    </g>
    <g id="Group_10342" data-name="Group 10342" transform="translate(584 -253.831)">
      <g id="Rectangle_713-4" data-name="Rectangle 713" transform="translate(0 -0.758)" fill="#fff" stroke="rgba(230,230,230,0.69)" stroke-width="1">
        <rect width="342" height="56.589" rx="10" stroke="none"/>
        <rect x="0.5" y="0.5" width="341" height="55.589" rx="9.5" fill="none"/>
      </g>
      <g id="Group_3260-4" data-name="Group 3260" transform="translate(22 20.242)">
        <text id="Username-4" data-name="Username" transform="translate(0 12)" fill="#71828a" font-size="12" font-family="Montserrat-Regular, Montserrat"><tspan x="0" y="0">Country*</tspan></text>
      </g>
      <g id="Rectangle_4883" data-name="Rectangle 4883" transform="translate(0 65.242)" fill="#fff" stroke="rgba(230,230,230,0.69)" stroke-width="1">
        <rect width="342" height="56.589" rx="10" stroke="none"/>
        <rect x="0.5" y="0.5" width="341" height="55.589" rx="9.5" fill="none"/>
      </g>
      <g id="Group_10343" data-name="Group 10343" transform="translate(22 86.242)">
        <text id="Username-5" data-name="Username" transform="translate(0 12)" fill="#71828a" font-size="12" font-family="Montserrat-Regular, Montserrat"><tspan x="0" y="0">City*</tspan></text>
      </g>
      <g id="Rectangle_4886" data-name="Rectangle 4886" transform="translate(0 131.242)" fill="#fff" stroke="rgba(230,230,230,0.69)" stroke-width="1">
        <rect width="342" height="56.589" rx="10" stroke="none"/>
        <rect x="0.5" y="0.5" width="341" height="55.589" rx="9.5" fill="none"/>
      </g>
      <g id="Group_10345" data-name="Group 10345" transform="translate(22 152.242)">
        <text id="Username-6" data-name="Username" transform="translate(0 12)" fill="#71828a" font-size="12" font-family="Montserrat-Regular, Montserrat"><tspan x="0" y="0">Passport Number*</tspan></text>
      </g>
    </g>
    <g id="Layer_1" data-name="Layer 1" transform="translate(-132.917 54.672)">
      <path id="Path_79866" data-name="Path 79866" d="M308.36,291.9a28.228,28.228,0,0,1,.472-3.368,3.442,3.442,0,0,1,1.3-2.229l.563.147a2.882,2.882,0,0,1,.282,2.033c-.87,1.127-1.666,2.37-2.615,3.417Z" transform="translate(-95.399 -90.103)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79867" data-name="Path 79867" d="M322.1,313.465c-.024-.061-.074-.11-.074-.172-.055-.71.135-1.1-.251-1.764a5.487,5.487,0,0,1-1.764-1.311c-.3-.4-.129-.582-.043-1.029.116.049.227.1.343.153,2.131.913,6.626,1.255,7.919,2.995C327.141,313.563,323.7,313.287,322.1,313.465Z" transform="translate(-99.85 -98.975)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79868" data-name="Path 79868" d="M309.925,289.856a51.947,51.947,0,0,0,1.366,7.5c-.343.392-.263.465-.808.514a12.715,12.715,0,0,0-4.832,1.3,32.772,32.772,0,0,0,1.666-5.9c.955-1.047,1.745-2.29,2.615-3.417Z" transform="translate(-94.349 -91.479)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79869" data-name="Path 79869" d="M297.948,306.94l-1.433,2.952a6.245,6.245,0,0,1-.508,2.627c-2-.778-4.354-.606-6.449-1.127-.784-.2-1.409-.306-1.837-1,.919-.7,8.286-2.67,10.228-3.454Z" transform="translate(-87.4 -98.103)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79870" data-name="Path 79870" d="M580.308,248.35a3.967,3.967,0,0,1,2.756,1.029,2.533,2.533,0,0,1,.68,2.021c-.1,1.421-.931,2.186-1.911,3.1a8.436,8.436,0,0,1-1.709-.233,3.613,3.613,0,0,1-1.9-1.69,2.866,2.866,0,0,1-.092-2.248C578.482,249.281,579.413,248.858,580.308,248.35Z" transform="translate(-199.885 -75.395)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79871" data-name="Path 79871" d="M306.17,321.533c.606-.184.318-.2.759-.055,1.727.582,2.75.269,4.446.159a1.363,1.363,0,0,1,.343.067c-.735,2.431-.717,5.083-1.225,7.582-.557.508-.576.57-1.317.643-1.776-1.311-2.033-5.873-2.854-8.023-.049-.129-.1-.251-.147-.374Z" transform="translate(-94.55 -103.7)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79872" data-name="Path 79872" d="M333.982,165.5c.11.018.22.037.325.061a4.811,4.811,0,0,1,3.283,2.088c.055.092.1.184.153.276s.086.2.123.294a2.583,2.583,0,0,1,.147.612,2.691,2.691,0,0,1,.025.312c0,.1,0,.208-.006.318a2.727,2.727,0,0,1-.037.312,2.481,2.481,0,0,1-.074.306c-.374,1.3-1.292,1.825-2.388,2.462a5.6,5.6,0,0,1-2.689-.986,3.139,3.139,0,0,1-1.188-2.658C331.771,167.294,332.763,166.382,333.982,165.5Z" transform="translate(-104.424 -43.284)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79873" data-name="Path 79873" d="M442.646,294.39a5.5,5.5,0,0,1,3.632,1.715,2.932,2.932,0,0,1,.545,2.37c-.294,1.525-1.274,2.174-2.487,3.019a4.756,4.756,0,0,1-3.2-1.219,2.79,2.79,0,0,1-.815-2.119c.049-1.66,1.145-2.756,2.333-3.76Z" transform="translate(-146.543 -93.239)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79874" data-name="Path 79874" d="M651.467,321.856l.478.116a3.412,3.412,0,0,1,.122,1.935,28.951,28.951,0,0,0-1.2,7.11c-.049,3.809,1.6,6.167,4.109,8.807a24.648,24.648,0,0,1-2.946-1.764,8.54,8.54,0,0,1-.729-.612,8.665,8.665,0,0,1-.661-.68,8.44,8.44,0,0,1-.582-.747c-.184-.257-.349-.527-.508-.8a8.436,8.436,0,0,1-.416-.851c-.122-.288-.233-.588-.331-.888s-.172-.606-.239-.919-.11-.625-.141-.937a14.535,14.535,0,0,1,3.038-9.774Z" transform="translate(-227.183 -103.882)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79875" data-name="Path 79875" d="M240.55,447.426a14.964,14.964,0,0,1,2.027-.092,4.38,4.38,0,0,1,2.731,1.409,3.039,3.039,0,0,1,.8,2.315c-.129,1.519-1.023,2.376-2.131,3.307-.257.024-.521.043-.778.055a4.3,4.3,0,0,1-3.411-1.164,3.422,3.422,0,0,1-1.072-2.578,4.5,4.5,0,0,1,1.825-3.252Z" transform="translate(-68.407 -152.512)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79876" data-name="Path 79876" d="M945.549,392.42a3.327,3.327,0,0,1,.515.7c1.127,2.015,3.056,6.492,2.168,8.709-.453,1.121-1.709,1.439-2.738,1.794-.153-.037-.306-.067-.459-.1a3.38,3.38,0,0,1-2.431-1.549c-.637-2.94,1.764-6.853,2.946-9.542Z" transform="translate(-341.175 -131.233)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79877" data-name="Path 79877" d="M174.148,394.5a11.618,11.618,0,0,1,2.064.031,3.368,3.368,0,0,1,2.3,1.5,3.217,3.217,0,0,1,.41,2.4c-.38,1.752-1.439,2.542-2.878,3.46a5.121,5.121,0,0,1-3.766-1.513,3.254,3.254,0,0,1-.747-2.64c.257-1.58,1.353-2.419,2.609-3.246Z" transform="translate(-42.352 -132.027)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79878" data-name="Path 79878" d="M958.349,384.989a5.38,5.38,0,0,1,1.433-.061,2.96,2.96,0,0,1,1.684,1.623,4.048,4.048,0,0,1,.49,3.209,2.282,2.282,0,0,1-1.317,1.262c-2.015.778-4.146-.423-6-1.157l-2.486-.986.374-.5-.22-.147a4.73,4.73,0,0,1-1.225.221l.165-.459a26.04,26.04,0,0,1,7.11-3.007Z" transform="translate(-344.501 -128.316)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79879" data-name="Path 79879" d="M928.5,384.09c3.338.4,7.515,2.474,10.528,3.962l-3.92,1.139a26.493,26.493,0,0,1-4.33,1.415,3.248,3.248,0,0,1-2.737-.38,5.1,5.1,0,0,1-.839-3.65,3.229,3.229,0,0,1,1.292-2.48Z" transform="translate(-335.237 -128.004)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79880" data-name="Path 79880" d="M243.43,573.846a6.238,6.238,0,0,1,1.225.269,4.27,4.27,0,0,1,2.658,2.315q.073.184.129.367a3.8,3.8,0,0,1,.092.38,2.77,2.77,0,0,1,.049.386c.012.129.012.257.012.386a2.725,2.725,0,0,1-.031.386,3.763,3.763,0,0,1-.325,1.115,4.8,4.8,0,0,1-2.995,2.45c-1.286-.141-2.823-.41-3.656-1.525a2.968,2.968,0,0,1-.582-2.529c.374-1.978,1.825-2.995,3.417-4.005Z" transform="translate(-68.881 -201.547)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79881" data-name="Path 79881" d="M163.152,669.52a9.468,9.468,0,0,1,1.317.123,4.089,4.089,0,0,1,2.811,1.758,3.665,3.665,0,0,1,.447,2.872,5.335,5.335,0,0,1-2.695,3.258,4.714,4.714,0,0,1-1.941-.141,4.52,4.52,0,0,1-2.419-2.327,4.444,4.444,0,0,1-.276-.845,3.65,3.65,0,0,1-.067-.441q-.018-.22-.018-.441a3.556,3.556,0,0,1,.031-.441c.018-.147.043-.294.073-.435a3.788,3.788,0,0,1,.294-.839,3.909,3.909,0,0,1,2.45-2.1Z" transform="translate(-38.019 -238.63)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79882" data-name="Path 79882" d="M940.674,685.9c-.037-.135-.08-.27-.116-.4a4.216,4.216,0,0,1,.4-3.711,4.823,4.823,0,0,1,3.111-1.7c1.47.4,2.633.759,3.393,2.18a4.031,4.031,0,0,1,.159,3.332,4.477,4.477,0,0,1-2.3,2.536,3.3,3.3,0,0,1-.312.122,1.952,1.952,0,0,1-.325.086,2.972,2.972,0,0,1-.337.055,3.062,3.062,0,0,1-1-.055,2.841,2.841,0,0,1-.325-.092,3.351,3.351,0,0,1-.312-.129,4.5,4.5,0,0,1-1.948-2.07l-.092-.147Z" transform="translate(-340.33 -242.727)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79883" data-name="Path 79883" d="M940.674,685.9c-.037-.135-.08-.27-.116-.4a4.216,4.216,0,0,1,.4-3.711,4.823,4.823,0,0,1,3.111-1.7c.753,1.047,1.617,2,1.47,3.375a1.557,1.557,0,0,1-.723,1.145c-.968.827-2.946.931-4.146,1.292Z" transform="translate(-340.33 -242.727)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79884" data-name="Path 79884" d="M309.581,302.11a15.359,15.359,0,0,0,3.178,4.336c-.086.447-.257.631.043,1.029a5.67,5.67,0,0,0,1.764,1.311c.392.661.2,1.053.251,1.764,0,.061.049.116.073.171a9.7,9.7,0,0,0-5.089,3.515,1.362,1.362,0,0,0-.343-.067c-1.69.1-2.719.423-4.446-.159-.435-.147-.153-.129-.759.055a27.043,27.043,0,0,0-3.013-3.417,6.263,6.263,0,0,0,.508-2.627,10.156,10.156,0,0,0,1.274,2.315,4.97,4.97,0,0,0,3.423,2.186,4.235,4.235,0,0,0,3.283-1.194c1.6-1.415,1.592-3.191,1.782-5.169l-3.325-1.862c-1.488-.024-2.97.233-4.471.245l.22-.619a12.634,12.634,0,0,1,4.832-1.3c.545-.049.465-.123.808-.514Z" transform="translate(-92.64 -96.231)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79885" data-name="Path 79885" d="M943.919,369.018a9.713,9.713,0,0,1,5.493,1.292,50.65,50.65,0,0,1-2.995,9.2c-.159.5-.324.992-.453,1.5-.024.092-.043.184-.061.282l-.3.086c-.821-2.566-3.473-8.317-3.007-10.846.177-.943.557-1.01,1.317-1.519Z" transform="translate(-341.192 -122.155)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79886" data-name="Path 79886" d="M689.78,342.432a6.425,6.425,0,0,1,1.237.006,4.131,4.131,0,0,1,3.338,5.414c-.447,1.629-1.531,2.327-2.9,3.172a5.149,5.149,0,0,1-.906-.031,4.517,4.517,0,0,1-3.258-1.837,3.919,3.919,0,0,1-.5-2.995c.337-1.862,1.5-2.707,2.983-3.73Z" transform="translate(-242.034 -111.848)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79887" data-name="Path 79887" d="M759.539,557.951a6.753,6.753,0,0,1,2.523.263,4.359,4.359,0,0,1,2.4,5.72,4.976,4.976,0,0,1-3.007,2.787,13.57,13.57,0,0,1-1.868-.061,4.443,4.443,0,0,1-2.725-2.058,4.36,4.36,0,0,1-.429-3.644c.514-1.672,1.629-2.254,3.111-3.013Z" transform="translate(-268.978 -195.383)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79888" data-name="Path 79888" d="M75.592,711.549a7.325,7.325,0,0,1,2.309.178,5.164,5.164,0,0,1,3.111,2.621,4.375,4.375,0,0,1,.233,3.485,4.91,4.91,0,0,1-2.854,2.9,9.179,9.179,0,0,1-1.966-.006,4.661,4.661,0,0,1-3.056-1.856,4.569,4.569,0,0,1-.723-3.785c.423-1.794,1.445-2.6,2.946-3.534Z" transform="translate(-3.991 -254.913)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79889" data-name="Path 79889" d="M120.473,593.983a15.572,15.572,0,0,1,2.2-.184,4.5,4.5,0,0,1,2.976,1.678,4.283,4.283,0,0,1,.87,3.325c-.239,1.917-1.366,2.8-2.829,3.9a10.644,10.644,0,0,1-2.535,0,4.936,4.936,0,0,1-3.087-1.96,3.859,3.859,0,0,1-.6-2.97c.355-1.886,1.494-2.774,3.007-3.8Z" transform="translate(-21.387 -209.281)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79890" data-name="Path 79890" d="M106.435,791.019a7.128,7.128,0,0,1,1.139.073,5.786,5.786,0,0,1,4.122,2.229,4.2,4.2,0,0,1,.661,3.289c-.386,1.917-1.6,2.823-3.185,3.809a8.033,8.033,0,0,1-2.064,0,5.111,5.111,0,0,1-3.35-2.278,4.792,4.792,0,0,1-.5-3.607c.453-1.837,1.623-2.6,3.172-3.509Z" transform="translate(-15.849 -285.72)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79891" data-name="Path 79891" d="M424.943,75.971a8.367,8.367,0,0,1,5.224,1.188c.845,1.513,1.733,2.983,1.243,4.771-.478,1.745-1.984,2.67-3.442,3.534a4.384,4.384,0,0,1-1.519.129,5.546,5.546,0,0,1-3.889-2.3q-.138-.211-.257-.441a4.952,4.952,0,0,1-.367-.949,4.579,4.579,0,0,1-.11-.5c-.025-.171-.049-.337-.061-.508a3.6,3.6,0,0,1,0-.514,4.638,4.638,0,0,1,.049-.508,4.561,4.561,0,0,1,.1-.5C422.322,77.667,423.5,76.8,424.943,75.971Z" transform="translate(-139.348 -8.567)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79892" data-name="Path 79892" d="M427.26,78.75c.172.067.349.116.515.2a2.832,2.832,0,0,1,1.39,1.654,3.06,3.06,0,0,1-1.286,2.046,6.525,6.525,0,0,1-1.292-.092c-.527-.514-.778-.692-.7-1.5a3.713,3.713,0,0,1,1.378-2.3Z" transform="translate(-140.943 -9.662)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79893" data-name="Path 79893" d="M941.824,525.08a6.691,6.691,0,0,1,2.554.453,5.634,5.634,0,0,1,2.909,3.546,4.432,4.432,0,0,1-.441,3.466,4.99,4.99,0,0,1-3.46,2.217,8.272,8.272,0,0,1-2.836-.576,5.481,5.481,0,0,1-2.848-3.062,4.057,4.057,0,0,1,.245-3.35,5.9,5.9,0,0,1,3.877-2.689Z" transform="translate(-339.22 -182.648)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79894" data-name="Path 79894" d="M909.275,793.486a6.858,6.858,0,0,1,2.419.257,5.912,5.912,0,0,1,3.546,3.025,4.724,4.724,0,0,1,.177,3.607,5.721,5.721,0,0,1-3.411,3.234,11.992,11.992,0,0,1-2.437-.073,5.2,5.2,0,0,1-3.276-2.266,4.79,4.79,0,0,1-.5-3.846c.515-1.947,1.825-2.94,3.479-3.938Z" transform="translate(-326.875 -286.674)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79895" data-name="Path 79895" d="M649.42,217.955c.9-2.995,1.99-5.726,4.8-7.41,3.785-2.272,10.5-1.948,14.612-.968-1.758.723-4.789-.055-6.822.122-.239,1.684-1.347,4.679-1.158,6.081,1.635,1.764,5.414.257,7.374,2.125-4.924-.778-13.13-2.731-17.454.386l-1.347-.343Z" transform="translate(-227.586 -60.078)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79896" data-name="Path 79896" d="M504.824,321.73c4.887,2.186,9.536,4.948,14.478,6.994-.98,1.856-2.186,3.607-3.325,5.371-2.321-.3-11.354-5.579-14.116-6.957a53.224,53.224,0,0,1,2.964-5.4Z" transform="translate(-170.395 -103.835)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79897" data-name="Path 79897" d="M339.924,241.13c2.235.423,4.422.943,6.62,1.537q-2.416,7.3-4.4,14.735a62.821,62.821,0,0,0-6.437-.68q2.315-7.735,4.22-15.592Z" transform="translate(-105.999 -72.597)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79898" data-name="Path 79898" d="M861.141,669.571c.747-.049,1.494-.086,2.241-.122,1.66-.067,3.509-.018,4.722,1.335a3.7,3.7,0,0,1,.955,3c-.282,2.523-2.388,5.089-4.318,6.6a4.937,4.937,0,0,1-3.215.288,6.168,6.168,0,0,1-3.215-3.3,7.01,7.01,0,0,1-.318-5.475c.619-1.439,1.776-1.837,3.148-2.333Z" transform="translate(-308.259 -238.595)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79899" data-name="Path 79899" d="M398.212,270.476a7.934,7.934,0,0,1,3.693.325,6.035,6.035,0,0,1,3.27,3.252c.073.184.141.367.2.557a4.615,4.615,0,0,1,.135.576c.037.2.061.386.08.588a5.659,5.659,0,0,1-.025,1.176c-.025.2-.055.392-.1.582s-.1.38-.159.57a5.435,5.435,0,0,1-.214.551c-.876,1.978-2.346,2.682-4.275,3.423a11.206,11.206,0,0,1-2.523-.061,4.394,4.394,0,0,1-.563-.129c-.184-.055-.367-.116-.545-.184s-.355-.153-.527-.239-.337-.19-.5-.294a5.061,5.061,0,0,1-.465-.343c-.147-.123-.294-.251-.429-.386s-.263-.282-.386-.429a5,5,0,0,1-.337-.466,5.615,5.615,0,0,1-.594-4.575c.674-2.278,2.26-3.4,4.256-4.489Z" transform="translate(-128.48 -83.944)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79900" data-name="Path 79900" d="M795.366,165.493c4.5-1,8.978-2.364,13.431-3.577,5.083-1.384,10.24-2.652,15.255-4.256l1.923,3.5c-6.265,1.286-12.249,3.13-18.36,5.016-3.466,1.072-7.049,2.021-10.436,3.319-.655-1.311-1.231-2.658-1.819-4.005Z" transform="translate(-284.148 -40.246)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79901" data-name="Path 79901" d="M246.066,525.523a9.141,9.141,0,0,1,3.056.214,6.026,6.026,0,0,1,3.564,3.129,6.355,6.355,0,0,1,.11,4.912c-.833,2.125-2.333,3.123-4.373,3.962a8.714,8.714,0,0,1-2.9-.251,6.261,6.261,0,0,1-3.607-3.178c-.086-.19-.165-.38-.233-.576a5.054,5.054,0,0,1-.172-.594c-.049-.2-.086-.4-.116-.612a5.165,5.165,0,0,1-.055-.619c-.006-.208,0-.417.006-.619s.037-.41.067-.619a5.347,5.347,0,0,1,.129-.606c.055-.2.116-.4.19-.594.8-2.137,2.315-3.111,4.336-3.956Z" transform="translate(-69.422 -182.81)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79902" data-name="Path 79902" d="M247.5,531.71a5.151,5.151,0,0,1,1.531.374,2.39,2.39,0,0,1,.962,1.776,3.521,3.521,0,0,1-1.029,2.529,4.378,4.378,0,0,1-1-.325,2.523,2.523,0,0,1-1.335-1.733A3.44,3.44,0,0,1,247.5,531.71Z" transform="translate(-71.459 -185.218)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79903" data-name="Path 79903" d="M809.784,394.57a9.6,9.6,0,0,1,2.958.631c.2.08.386.171.576.269s.367.208.545.325a6.094,6.094,0,0,1,.508.38,6.209,6.209,0,0,1,.465.429q.221.23.423.478a4.939,4.939,0,0,1,.367.515,6.078,6.078,0,0,1,.318.551,4.928,4.928,0,0,1,.257.582,5.854,5.854,0,0,1,.312,1.121,4.571,4.571,0,0,1,.073.582c.012.2.018.392.018.582a4.6,4.6,0,0,1-.043.582c-.025.2-.055.386-.1.576s-.1.38-.159.563-.135.367-.214.545A6.6,6.6,0,0,1,812,406.806a8.1,8.1,0,0,1-4.8-.864,6.122,6.122,0,0,1-2.039-8.6c1.188-1.782,2.584-2.333,4.606-2.768Z" transform="translate(-287.573 -132.066)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79906" data-name="Path 79906" d="M569.608,296.616a8.723,8.723,0,0,1,2.774.514,7.054,7.054,0,0,1,4.048,4.183,7.33,7.33,0,0,1,.233.735,6.54,6.54,0,0,1,.153.753c.037.257.061.508.074.766s.012.514,0,.772-.043.514-.086.766-.1.5-.165.753a6.474,6.474,0,0,1-.245.735c-.092.239-.2.472-.319.7-1.145,2.162-2.878,3.044-5.132,3.742a8.569,8.569,0,0,1-4.177-.759c-.214-.11-.416-.227-.618-.355a6.3,6.3,0,0,1-.582-.416,6.509,6.509,0,0,1-.533-.472c-.172-.165-.331-.343-.484-.521s-.294-.374-.429-.57a6.147,6.147,0,0,1-.367-.606c-.11-.208-.214-.423-.306-.643a6.26,6.26,0,0,1-.239-.674,7.022,7.022,0,0,1-.288-1.47c-.024-.251-.03-.5-.03-.747a6.051,6.051,0,0,1,.049-.747,7.132,7.132,0,0,1,.129-.735,7.5,7.5,0,0,1,.2-.723,6.864,6.864,0,0,1,.276-.7c.1-.227.22-.447.349-.668,1.329-2.217,3.3-3.038,5.708-3.626Z" transform="translate(-194.049 -94.099)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79907" data-name="Path 79907" d="M240.965,269.071a7.046,7.046,0,0,1,3.509.631,6.879,6.879,0,0,1,3.687,4.085,7.092,7.092,0,0,1-.392,5.53c-1.268,2.444-3.221,3.185-5.7,3.907a11.086,11.086,0,0,1-3.319-.759c-2.989-1.231-3.956-3.932-5.034-6.718a13.244,13.244,0,0,1,1.317-3.454c1.372-2.174,3.583-2.7,5.922-3.221Z" transform="translate(-66.471 -83.42)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79908" data-name="Path 79908" d="M596.586,111.33c2.217.269,3.552,3.515,5.408,4.844a23.481,23.481,0,0,1,3.221.263,1.858,1.858,0,0,1,1.451,1.109c-.306,1.66-2.223,3.8-3.136,5.31.337,1.2.876,2.695.484,3.932-.159.5-.392.5-.808.747a7.143,7.143,0,0,1-3.338-1.115c-2.646-.735-3.828,1.556-6.253,1.556-.5,0-.582-.251-.906-.57a21.983,21.983,0,0,1,.08-5.6c-.906-.894-2.548-2.168-2.928-3.387-.141-.453.08-.606.294-.992a7.061,7.061,0,0,1,3.075-.949c1.237-1.611,2.272-3.423,3.35-5.144Z" transform="translate(-204.485 -22.289)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79909" data-name="Path 79909" d="M854.637,261.666a12.215,12.215,0,0,1,5.163.717,8.044,8.044,0,0,1,4.4,4.391,7.867,7.867,0,0,1-.19,6.1c-1.133,2.4-2.9,3.46-5.31,4.33a8.514,8.514,0,0,1-5.383-.576,8.236,8.236,0,0,1-.79-.429,8.429,8.429,0,0,1-.747-.508,8.886,8.886,0,0,1-1.311-1.231c-.2-.227-.38-.465-.557-.71a8.672,8.672,0,0,1-1.188-2.413,7.32,7.32,0,0,1,5.916-9.676Z" transform="translate(-304.689 -80.554)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79910" data-name="Path 79910" d="M857.277,269.006a4.739,4.739,0,0,1,3.129.343c.318.416.539.563.582,1.115.092,1.317-.68,2.18-1.458,3.142a2.841,2.841,0,0,1-2.352-.061,2.364,2.364,0,0,1-.778-1.28,3.868,3.868,0,0,1,.87-3.252Z" transform="translate(-307.77 -83.361)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79911" data-name="Path 79911" d="M622.268,276.82c8.439,4.673,24.142,7.061,28.4,16.168-.435,1.158-.827,2.37-1.366,3.485-1.243-.276-2.48-.49-3.742-.686q-3.133-1.865-6.431-3.411c-3.944-1.831-16.995-6.633-18.483-10.344-.717-1.788.073-3.411.778-5.065l.839-.147Z" transform="translate(-216.326 -86.429)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79912" data-name="Path 79912" d="M676.045,210.05c4.183.367,10.485,2,14.1,4.128,1.2,2.211,8.623,2.652,9.6,4.422a16.366,16.366,0,0,1-3.926,5.267c-1.745.239-7.312-2.174-7.906-1.494-4.122-1.409-8.28-2.823-12.475-3.993-1.96-1.874-5.732-.367-7.374-2.125-.2-1.409.919-4.4,1.158-6.081,2.033-.178,5.065.606,6.822-.123Z" transform="translate(-234.803 -60.551)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79915" data-name="Path 79915" d="M296.088,99.551a12.482,12.482,0,0,1,5.72.508,7.936,7.936,0,0,1,.8.374,7.311,7.311,0,0,1,.759.453,8.988,8.988,0,0,1,1.366,1.121c.208.208.4.429.594.655s.361.465.527.71a7.94,7.94,0,0,1,.447.759c.135.263.263.527.374.8a8.965,8.965,0,0,1,.092,6.914c-1.1,2.584-3.215,4.03-5.745,5.01a10.349,10.349,0,0,1-5.647-.331,8.89,8.89,0,0,1-4.789-5.01,9.494,9.494,0,0,1,.172-7.551,8.973,8.973,0,0,1,5.334-4.422Z" transform="translate(-88.241 -17.688)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79916" data-name="Path 79916" d="M299.786,107.78a2.764,2.764,0,0,1,.637.153c.778.318.833.472,1.133,1.206a5.266,5.266,0,0,1-1.617,2.548c-.845-.49-1.286-.606-1.739-1.488A4.921,4.921,0,0,1,299.786,107.78Z" transform="translate(-91.461 -20.914)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79917" data-name="Path 79917" d="M664.169,86.6a10.339,10.339,0,0,1,4.716.974,8.418,8.418,0,0,1,4.183,5.4,9.385,9.385,0,0,1-1.164,7.514c-1.592,2.407-3.791,3.35-6.51,3.944a11.5,11.5,0,0,1-5.236-1.255,8.493,8.493,0,0,1-4.232-5.31,8.986,8.986,0,0,1,1.139-6.816C658.768,88.278,661.15,87.268,664.169,86.6Z" transform="translate(-230.013 -12.705)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79918" data-name="Path 79918" d="M665.584,94.57a5.169,5.169,0,0,1,3.136,1.311,2.716,2.716,0,0,1,.808,2.358,4.306,4.306,0,0,1-2.143,2.8,3.882,3.882,0,0,1-3.209-1.28,3.067,3.067,0,0,1-.643-2.358c.184-1.415.943-2.039,2.052-2.829Z" transform="translate(-233.044 -15.794)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79919" data-name="Path 79919" d="M613.72,328.542c-.1-1.066-.2-2.137-.233-3.2a16.9,16.9,0,0,1,4.789-11.544c4.085-4.262,10.883-6.363,16.67-6.467,2.56-.043,5.108.337,7.668.288h.276c1.262.184,2.5.4,3.742.674-.735,1.99-1.727,6.308-3.785,7.2-1.213.521-2.517.233-3.577,1.115l-.735,1.678a3.416,3.416,0,0,0-.122-1.935l-.478-.116-.184-.582c-2.333-1.231-7.974.141-10.43.857-7.037,2.07-10.172,5.781-13.608,12.04Z" transform="translate(-213.657 -98.252)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79920" data-name="Path 79920" d="M777.631,266.72c5.732.839,10.748,3.246,14.257,8a19.328,19.328,0,0,1,3.16,14.435A26.731,26.731,0,0,1,783.713,306.3a22.885,22.885,0,0,1-17.6,4.018,14.619,14.619,0,0,1-3.528-1.041c2.021-.894,5.34-.337,7.594-.508a18.664,18.664,0,0,0,12.708-6.969c.288-.343.563-.692.833-1.047s.533-.717.784-1.09.5-.741.729-1.127.459-.766.667-1.157.417-.79.613-1.194.38-.808.551-1.219.337-.827.49-1.243.294-.845.429-1.268.251-.857.367-1.286.208-.87.3-1.3.166-.876.233-1.317.123-.882.172-1.329.08-.888.1-1.335.037-.894.037-1.341-.006-.894-.031-1.335-.055-.894-.1-1.335c-.263-2.664-1.011-5.236-3.142-6.976-.913-3.846-5.267-6.149-8.292-8.17Z" transform="translate(-271.447 -82.515)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79921" data-name="Path 79921" d="M648.505,223.73l1.347.343c7.815,8.629,22.733,14.772,27.841,24.093-.808,2.633-2.78,8.813-5.285,10.2-.1.055-.214.092-.319.135l-.239-.19c.214-.22.435-.441.637-.674,1-1.151,2.713-3.307,2.6-4.844-1.2-1.041-4.024-.723-5.659-1.372l.024-.355,4.25.576c-9.052-7.061-23.517-12.977-26.647-24.981l1.445-2.934Z" transform="translate(-226.671 -65.853)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79922" data-name="Path 79922" d="M70.419,546.592a10.815,10.815,0,0,1,6.014,1.433,8.514,8.514,0,0,1,1.427,1.041,7.511,7.511,0,0,1,.625.625c.2.221.386.447.563.686a8.6,8.6,0,0,1,.49.741,7.934,7.934,0,0,1,.41.784c.122.269.233.545.331.821a7.994,7.994,0,0,1,.245.851,9.44,9.44,0,0,1-1.36,7.306c-1.611,2.358-3.858,3.35-6.559,3.938a11.4,11.4,0,0,1-6.222-1.347,8.665,8.665,0,0,1-3.907-5.585,9.431,9.431,0,0,1,1.286-7.453c1.672-2.4,3.907-3.283,6.669-3.834Z" transform="translate(0 -190.981)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79923" data-name="Path 79923" d="M77.192,559.992c.208-.006.423-.012.631-.012a3.159,3.159,0,0,1,2.738,1.109,2.212,2.212,0,0,1,.4,1.813c-.19,1.176-1.115,1.776-2.015,2.413a3.029,3.029,0,0,1-2.529-.441,2.3,2.3,0,0,1-.894-1.947C75.538,561.523,76.224,560.862,77.192,559.992Z" transform="translate(-5.156 -196.175)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79924" data-name="Path 79924" d="M688.12,180.654c.588-1.666.165-3.705.888-5.3.214-.472.178-.318.741-.49a3.613,3.613,0,0,1,1.7,1.752l.2.325.472-.38c2.039,2.2,4.183,4.232,6.43,6.222,2.995,2.652,14.839,11.691,14.4,15.531-.276,2.413-3.564,4.875-4.912,6.835-.931,1.353-1.641,2.909-2.689,4.171a9.115,9.115,0,0,1-2.046,1.739,36.168,36.168,0,0,1-7.619-2.327c.594-.68,6.161,1.727,7.906,1.494a16.364,16.364,0,0,0,3.926-5.267c-.974-1.77-8.4-2.211-9.6-4.422,2.878.8,5.542,2.358,8.482,3.007a2.048,2.048,0,0,0,1.776-.116C706.517,198.536,689.376,189.093,688.12,180.654Z" transform="translate(-242.585 -46.912)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79925" data-name="Path 79925" d="M894.77,409.19c6.669,1.837,11.477,8.868,14.692,14.551,4.734,8.378,7.815,20.418,5.12,29.923a14.486,14.486,0,0,1-2.248,4.789c-.471-2.627-1.066-5.23-1.635-7.839-2.364-10.46-5.083-20.884-10.264-30.358-1.721-3.717-4.44-7.159-5.659-11.067Z" transform="translate(-322.677 -137.732)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79926" data-name="Path 79926" d="M827.126,163.38c1.4,2.646,2.627,5.273,3.785,8.029-6.578,3.374-14.012,5.794-20.945,8.4a74.6,74.6,0,0,0-7.441,2.86,111.116,111.116,0,0,0-4.2-10.95c3.387-1.3,6.969-2.248,10.436-3.319,6.106-1.886,12.089-3.73,18.36-5.016Z" transform="translate(-285.299 -42.463)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79927" data-name="Path 79927" d="M622.1,270.537c1.072-3.05,2.542-7.2,5.585-8.727,7.962-4,23.958.508,32.1,3.4l-.025.355c1.635.643,4.458.325,5.659,1.372.11,1.537-1.6,3.693-2.6,4.844-.2.233-.423.453-.637.674l.239.19c-5.426,2.535-27.473-8.017-39.477-2.254l-.839.147Z" transform="translate(-216.997 -79.999)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79928" data-name="Path 79928" d="M833.57,176.49c1.415,3.791,2.664,7.582,3.8,11.465q-9.967,4.161-20.161,7.759c-2.952,1.029-6.063,1.684-8.948,2.866-.919-3.638-1.862-7.276-3.074-10.828a74.648,74.648,0,0,1,7.441-2.86c6.933-2.6,14.368-5.028,20.945-8.4Z" transform="translate(-287.958 -47.544)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79929" data-name="Path 79929" d="M839.315,195.2c1.035,4.569,1.966,9.107,2.639,13.749-9.78,2.113-19.757,4.471-29.335,7.367a79.649,79.649,0,0,0-2.419-10.491c2.885-1.182,6-1.837,8.948-2.866q10.188-3.592,20.161-7.759Z" transform="translate(-289.9 -54.795)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79930" data-name="Path 79930" d="M810.216,480.032c4.2.233,8.849-.962,13.1-1.2q19.5-1.259,39.048-1.752c4.967-.135,10.105-.68,15.059-.325.57,2.609,1.17,5.212,1.635,7.839-.123.184-.1.2-.325.3-1.109.5-46.2-1.011-51.542-1.415-5.91-.447-12.622-.631-18.262-2.48A7.934,7.934,0,0,1,810.216,480.032Z" transform="translate(-289.408 -163.873)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79931" data-name="Path 79931" d="M188.6,463.6c6.314,3.834,12.248,8.445,18.214,12.806,2.891,2.113,6.535,4.269,8.954,6.9a10.059,10.059,0,0,1,2.309,4.36,5.751,5.751,0,0,1-.857,4.624c-1.96,2.811-5.6,3.687-8.8,4.146-.943.135-1.874.233-2.829.282a18.657,18.657,0,0,0-3.038.031c-2.309-6.345-13.639-28.833-13.957-33.144Z" transform="translate(-48.979 -158.82)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79932" data-name="Path 79932" d="M730.43,110.052c1.611-.563,3.2-1.311,4.777-1.972.2,1.458.282,2.94.38,4.409.576,8.8-1.2,16.64-7.165,23.364-5.818,6.553-15.213,10.081-23.787,10.834-3.693.325-7.392.135-11.085.38-1.084.073-2.039.073-2.787.925a5.875,5.875,0,0,0,1.3,2.689l-.471.38-.2-.325a3.639,3.639,0,0,0-1.7-1.752c-.563.165-.527.018-.741.49-.729,1.6-.306,3.632-.888,5.3a11.637,11.637,0,0,1-.1-1.311c-.153-4.575,1.592-10.485,4.771-13.859,5.218-2.015,11.33-2.192,16.8-3.423,6.479-1.464,16.915-4.158,20.571-10.295,2.94-4.93,1.647-10.644.337-15.837Z" transform="translate(-242.521 -21.03)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79933" data-name="Path 79933" d="M242.291,353.55a26.846,26.846,0,0,1,5.763,2.223,22.2,22.2,0,0,1,3.724,2.872,6.865,6.865,0,0,0-2.217,2.076c-1.476,6.712-12.757,16.548-18.287,20.118a8.956,8.956,0,0,1-3.3,1.519,5.607,5.607,0,0,1-2.756-.1,13.4,13.4,0,0,1-2.707-4.275c-1.059-3.166-.355-6.314,1.164-9.2a31.879,31.879,0,0,1,18.618-15.225Z" transform="translate(-61.929 -116.168)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79934" data-name="Path 79934" d="M250.087,357.18a22.192,22.192,0,0,1,3.724,2.872,6.864,6.864,0,0,0-2.217,2.076c-8,2.8-17.289,11.005-20.841,18.746a11.225,11.225,0,0,0-.747,2.891,5.607,5.607,0,0,1-2.756-.1c.753-6.859,7.514-14.208,12.769-18.171,4.024-3.031,6.933-4.054,10.068-8.3Z" transform="translate(-63.963 -117.575)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79935" data-name="Path 79935" d="M253.348,365.26c-1.476,6.712-12.757,16.548-18.287,20.118a8.954,8.954,0,0,1-3.3,1.519,11.162,11.162,0,0,1,.747-2.891C236.059,376.271,245.344,368.059,253.348,365.26Z" transform="translate(-65.711 -120.706)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79936" data-name="Path 79936" d="M165.1,226.6a18.415,18.415,0,0,1,6.59,12.916,23.935,23.935,0,0,1-6.394,17.405,25.613,25.613,0,0,1-17.185,8.219,17.26,17.26,0,0,1-12.628-4.373,17.887,17.887,0,0,1-5.181-8.721,17.942,17.942,0,0,1,2.37,1.868c3.032,2.382,5.842,3.675,9.817,3.123,7.184-.992,14.374-6.388,18.575-12.12,4.624-6.308,5.248-10.675,4.042-18.324Z" transform="translate(-26.388 -66.961)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79937" data-name="Path 79937" d="M762.3,459.91h.809c.024.11.055.208.08.306.968,3.877.986,8.255,1.311,12.236.674,8.188,2.174,19.236,7.2,25.918a32.428,32.428,0,0,0,9,7.674c6.216,3.828,15.476,7.1,17.338,14.925.073.306.141.612.208.919a28.377,28.377,0,0,1,.508,3.374c-2.86,6.89-3.822,3.626-7.22,6.645-2.781,9.891,7.87,14.772,11.948,22.384,3.822,7.129,2.823,13.351.539,20.743-1.359-1.029-2.67-2.125-3.993-3.191a37.132,37.132,0,0,0,1.47-6.3c1.023-9.076-4.446-13.816-9.542-20.29-3.509-4.458-3.84-8.237-3.16-13.694-1.941-.435-3.785-.919-4.955-2.689a3.4,3.4,0,0,1-.423-2.695,7.153,7.153,0,0,1,3.73-4.054,10.434,10.434,0,0,1,8.531-.71l.153-.367c-4.207-9.848-22.39-11.74-29.929-28.159-2.983-6.5-6.124-26.23-3.607-32.973Z" transform="translate(-270.975 -157.39)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79938" data-name="Path 79938" d="M808.359,563.2a8.271,8.271,0,0,1,1.274.214,2.613,2.613,0,0,1,1.99,1.286,2.368,2.368,0,0,1-.619,1.525c-1.452,1.721-3.087,2.039-5.218,2.26a6.127,6.127,0,0,1-2.241-.2c-.821-.288-.906-.484-1.255-1.213a2.678,2.678,0,0,1,.6-1.519c1.549-1.9,3.154-2.156,5.469-2.358Z" transform="translate(-286.834 -197.419)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79939" data-name="Path 79939" d="M159.7,443.9c3.626,1.868,6.963,4.7,10.35,7.006,2.419,1.648,5.169,3.117,7.349,5.059.318,4.311,11.648,26.8,13.957,33.144a17.968,17.968,0,0,1,3.038-.031c-2.664,1.047-13.1-.122-16.48-.576-.588-3.644-2.774-8.053-4.22-11.465C169.137,466.309,162.946,455,159.7,443.9Z" transform="translate(-37.782 -151.185)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79941" data-name="Path 79941" d="M516.78,53.826c1.3,1.176,3.479,5.506,4.7,7.2,3.577-1.335,7.349-2.3,11.011-3.387-2.695,6.449-4.4,13.394-6.443,20.075l-9.768,31.717c-1.66,5.31-3.154,10.675-4.728,16.009.043-5.371.545-10.809.839-16.18,1.017-18.544,2.848-36.935,4.391-55.437Z" transform="translate(-174.151)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79942" data-name="Path 79942" d="M744.942,431.536a22.286,22.286,0,0,1-11.109-1.562c-.435-.184-.864-.386-1.286-.594s-.839-.429-1.255-.655-.821-.472-1.219-.717-.8-.508-1.182-.778-.766-.551-1.139-.839-.741-.588-1.1-.894-.71-.625-1.053-.949-.674-.655-1-1-.643-.692-.949-1.047-.606-.723-.894-1.1-.57-.753-.839-1.139-.533-.778-.784-1.182-.49-.808-.723-1.219-.447-.827-.661-1.255-.4-.851-.594-1.286-.361-.87-.527-1.311c-2.321-6.057-3-13.394-.165-19.4a15.234,15.234,0,0,1,8.947-7.919l-.135.374c-.514,1.39-1.329,2.591-1.794,4.024-1.654,5.157-.374,10.956,1.764,15.8,3.252,7.349,9.768,14.031,17.313,16.964,3.8,1.476,8.078,1.972,11.906.276a20.76,20.76,0,0,0,3.724-2.4,14.177,14.177,0,0,1-8.782,8.157,47.882,47.882,0,0,1-5.659,1.654h-.808Z" transform="translate(-253.622 -129.016)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79943" data-name="Path 79943" d="M808.216,454.859a2.4,2.4,0,0,1-.288-.27,4.35,4.35,0,0,1-.8-3.074,4.6,4.6,0,0,1,1.66-2.272c2.529-2.382,5.463-4.446,8.17-6.626,6.945-5.6,13.988-11,21.067-16.419,7.478-5.726,14.606-12.206,22.758-17.007,1.219,3.913,3.938,7.349,5.659,11.067-5.947,4.642-12.971,7.649-19.506,11.336-7.429,4.189-14.637,8.739-22.109,12.879-4.177,2.315-12.065,6.032-15.329,9.419a7.942,7.942,0,0,0-1.286.968Z" transform="translate(-288.694 -137.732)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79944" data-name="Path 79944" d="M891.546,305.48c.018,2.566-1.1,5.763-1.69,8.286a129.289,129.289,0,0,1,12.463,4.073C881.937,324.128,861,332.047,843.1,343.83q-3.748,2.985-7.453,6.026c4.428-4.765,8.029-10.27,12.426-15.078,11.961-13.094,27.62-21.8,43.47-29.292Z" transform="translate(-299.764 -97.537)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79945" data-name="Path 79945" d="M202.085,330.685a10.8,10.8,0,0,1-.7-3.436c-.11-3.044,1.543-5.947,3.515-8.145,3.252-3.632,15.084-11.948,19.873-11.955,1.458,0,2.542,1.059,3.454,2.058a18.95,18.95,0,0,1,2.07,4.391c-.092.092-.19.184-.282.276-3.834,3.656-12.089,8.286-16.878,11.281-2.29,1.427-5.016,2.811-6.786,4.893l.1.294c7.233-1.268,19.585-11.667,26.6-6.5a9.98,9.98,0,0,1,3.852,5.934,5.492,5.492,0,0,1-.968,4.036,17.226,17.226,0,0,1-1.445,1.543c-1.084-.539-2.156-1.09-3.289-1.537-6.773,1.739-16.952,7.478-23.731,3.693-2.7-1.507-4.2-4.079-5.4-6.816Z" transform="translate(-53.937 -98.184)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79946" data-name="Path 79946" d="M221.736,148.136c1.917.919,3.84,3.583,5.261,5.181,9.37,10.546,17.43,22,25.422,33.6,5.487,7.968,11.146,16.046,15.868,24.491l-4.863-4.177c-4.06-3.619-8.347-7.025-12.481-10.57l-21.9-18.942c-5.444-4.63-11.373-8.99-16.382-14.074,3.16-.233,6.277-.294,9.437-.294.073-5.077-.374-10.136-.361-15.219Z" transform="translate(-58.308 -36.552)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79947" data-name="Path 79947" d="M70.967,305.1c2.952,1.145,5.83,3.013,8.7,4.416,10.932,5.346,21.57,11.158,32.385,16.713l18.379,9.272c2.934,1.476,6.5,2.891,9.095,4.887l-.061.484c-1.886.668-5.285-.759-7.135-1.3-8.611-2.713-18.06-4.085-26.879-6.173-5.212-1.237-10.295-2.94-15.476-4.3-8.409-2.211-16.866-4.122-25.25-6.449A103.8,103.8,0,0,0,73.906,314a32.6,32.6,0,0,1-2.952-8.892Z" transform="translate(-0.97 -97.39)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79948" data-name="Path 79948" d="M552.618,151.35c1.513,1.482,1.752,5.941,2.8,8.029,2,3.981,6.535,5.683,10.515,6.969q-21.569,22.47-43.966,44.119c-2.56,2.272-5.016,4.691-7.5,7.037,4.091-10.595,10.509-20.767,16.493-30.376,7.367-11.838,14.986-23.529,21.668-35.778Z" transform="translate(-175.282 -37.8)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79951" data-name="Path 79951" d="M253.819,361.862,256.2,365a8.059,8.059,0,0,1,1.1,6.669c-1.874,7.521-9.1,13.136-13.773,18.942a8.352,8.352,0,0,0,.913.674,3.962,3.962,0,0,0,2.462-1.035c7.588-4.924,11.691-15.3,17.264-18.759a7.686,7.686,0,0,1,6.075-.943,15.117,15.117,0,0,1,4.2,1.911c7.172,4.385,2.352,9.866,5.438,14.312a2.324,2.324,0,0,0,1.721,1.115,4.61,4.61,0,0,0,2.621-1c4.036-2.78,4.5-7.441,5.242-11.875.68-.514.68-.68,1.507-.576a2.9,2.9,0,0,1,.57,1.255c.594,3.338-1.06,7.674-2.934,10.417a8.243,8.243,0,0,1-5.61,3.822,6.8,6.8,0,0,1-5.138-1.219c-2.989-2.5-1.849-7.233-2.2-10.638a2.758,2.758,0,0,0-.876-2.046,2.951,2.951,0,0,0-2.7.851,25.449,25.449,0,0,0-4.177,5.7c-3.209,5.1-6.841,10.969-12.138,14.123a8.166,8.166,0,0,1-6.6,1.09c-3.834-1.06-5.916-4.471-7.747-7.7a9.281,9.281,0,0,1-5.377,1.586c-3.773-.257-6.473-3.577-8.776-6.222a5.469,5.469,0,0,0,2.756.1,8.814,8.814,0,0,0,3.3-1.519c5.53-3.57,16.811-13.412,18.287-20.118a6.865,6.865,0,0,1,2.217-2.076Z" transform="translate(-63.971 -119.385)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79952" data-name="Path 79952" d="M265.37,366.986a8.059,8.059,0,0,1,1.1,6.669c-1.874,7.521-9.1,13.137-13.773,18.942l-.674.312a3.7,3.7,0,0,1-1.09-1.525c-.19-1.372,3.307-4.4,4.318-5.463,3.381-3.552,8.317-8.69,9.431-13.626.367-1.635.27-3.35.619-5.016l.067-.3Z" transform="translate(-73.14 -121.373)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79953" data-name="Path 79953" d="M129.935,249.537a8.026,8.026,0,0,1-.392-1.654c-.925-6.345,1.739-13.271,5.53-18.25a23.869,23.869,0,0,1,15.984-9.358,18,18,0,0,1,13.675,3.815c1.206,7.649.582,12.016-4.042,18.324-4.2,5.726-11.391,11.128-18.575,12.12-3.969.545-6.78-.741-9.817-3.123a17.439,17.439,0,0,0-2.37-1.868Z" transform="translate(-26.023 -64.456)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79954" data-name="Path 79954" d="M158.534,239.75a4.638,4.638,0,0,1,1.066.484c.2.429.4.637.318,1.133-.227,1.341-1.225,2.046-2.29,2.756l-1.047-.588a1.71,1.71,0,0,1-.294-1.47,9.148,9.148,0,0,1,2.241-2.315Z" transform="translate(-36.445 -72.062)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79955" data-name="Path 79955" d="M151.739,227.01a3.724,3.724,0,0,1,2.584.919,1.967,1.967,0,0,1-.031,1.782c-.8,1.843-2.713,2.842-4.422,3.638a5.942,5.942,0,0,1-1.458.184c-.8-.092-.9-.367-1.323-.968a3.279,3.279,0,0,1,.227-1.647,7.016,7.016,0,0,1,4.416-3.907Z" transform="translate(-32.89 -67.124)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79957" data-name="Path 79957" d="M129.41,424.6c2.284.992,5.365,2.027,7.251,3.632,3.626,2.842,7.906,5.163,11.3,8.188,3.24,11.1,9.431,22.409,13.994,33.138,1.451,3.411,3.632,7.821,4.22,11.465-2.609-.257-5.181-.815-7.753-1.317a72.967,72.967,0,0,1-14.821-3.8.464.464,0,0,0,.037-.1c.4-2.015-11.1-25.85-12.8-29.58-1.182-2.97.8-6.314.808-9.444C131.664,432.586,130.469,428.624,129.41,424.6Z" transform="translate(-26.043 -143.705)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79958" data-name="Path 79958" d="M141.25,430.516c3.626,2.842,7.906,5.163,11.3,8.188,3.24,11.1,9.431,22.409,13.994,33.138,1.451,3.411,3.632,7.821,4.22,11.465-2.609-.257-5.181-.815-7.753-1.317-.857-3.864-3.809-9.529-5.451-13.308l-10.044-22.966c-2.186-5.01-4.526-10.013-6.265-15.206Z" transform="translate(-30.632 -145.996)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79959" data-name="Path 79959" d="M732.561,386.3c4.593-1.482,9.952-.172,14.147,1.947,8.023,4.06,15.678,12.346,18.458,20.963,1.188,3.687,1.684,8.47.172,12.12a20.759,20.759,0,0,1-3.724,2.4c-3.828,1.7-8.1,1.2-11.906-.276-7.545-2.933-14.061-9.609-17.313-16.964-2.143-4.844-3.417-10.644-1.764-15.8.459-1.439,1.28-2.633,1.794-4.024l.135-.374Z" transform="translate(-258.768 -128.615)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79960" data-name="Path 79960" d="M745.1,390.058a13.5,13.5,0,0,1,3.479.165c6.118,1.311,12.561,7.521,15.739,12.677,1.366,2.217,2.676,5.126,1.923,7.766a6.233,6.233,0,0,1-3.313,3.809,11.386,11.386,0,0,1-5.1.067c-5.512-1.219-10.791-5.867-13.675-10.6-1.666-2.731-3.227-6.6-2.34-9.787a6.446,6.446,0,0,1,3.3-4.091Z" transform="translate(-263.293 -130.304)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79961" data-name="Path 79961" d="M749.956,395.49a6.771,6.771,0,0,1,3.968,1.176,3.831,3.831,0,0,1,1.862,2.707,3.312,3.312,0,0,1-1.151,2.731,5.745,5.745,0,0,1-3.656-.808,4.041,4.041,0,0,1-2-2.695,3.709,3.709,0,0,1,.98-3.105Z" transform="translate(-266.15 -132.423)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79962" data-name="Path 79962" d="M867.973,427.26c5.181,9.474,7.9,19.9,10.264,30.358-4.948-.355-10.093.19-15.06.325q-19.54.487-39.048,1.752c-4.256.233-8.9,1.427-13.1,1.2,3.264-3.381,11.158-7.1,15.329-9.419,7.471-4.14,14.674-8.69,22.109-12.879C855,434.909,862.027,431.908,867.973,427.26Z" transform="translate(-290.221 -144.736)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79963" data-name="Path 79963" d="M97.7,463.536c-3.485-1.353-7.87-2.437-9.774-5.947-2.18-4.024-1.2-9.358.092-13.528,2.352-7.619,7.833-15.85,15.127-19.5a11.415,11.415,0,0,1,9.682-.5c1.059,4.024,2.254,7.986,2.235,12.181-.012,3.123-1.99,6.473-.808,9.444,1.7,3.73,13.2,27.565,12.8,29.58a.466.466,0,0,1-.037.1,121.591,121.591,0,0,1-12.524-4.269A112.526,112.526,0,0,1,97.7,463.536Z" transform="translate(-9.466 -143.171)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79964" data-name="Path 79964" d="M104.7,483.825c2.462-1.457,6.149-3.246,7.937-5.438.686-.839,1.513-2.413,2.486-2.817l.551.533c1.972,4.789,4.752,10.215,5.818,15.28A112.528,112.528,0,0,1,104.7,483.825Z" transform="translate(-16.466 -163.46)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79965" data-name="Path 79965" d="M108.65,433.485a7.208,7.208,0,0,1,3.43.178,4.985,4.985,0,0,1,2.8,2.811c1.384,3.589-.1,8.856-1.549,12.261-2.352,5.518-5.683,10.136-11.373,12.414a6.864,6.864,0,0,1-2.891-.012,4.479,4.479,0,0,1-2.909-2.407c-1.647-3.472-.214-9.2,1.127-12.634,2.137-5.469,5.9-10.258,11.367-12.61Z" transform="translate(-12.87 -147.103)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79966" data-name="Path 79966" d="M744.193,296.121a18.832,18.832,0,0,1-.527-7.4,27.952,27.952,0,0,1,9.823-17.846c5.04-4.073,10.478-4.918,16.756-4.281,3.025,2.027,7.374,4.324,8.292,8.17,2.131,1.739,2.872,4.311,3.142,6.975.043.447.073.888.1,1.335s.031.894.031,1.335-.012.894-.037,1.341-.055.888-.1,1.335-.1.888-.172,1.329-.147.882-.233,1.317-.19.87-.3,1.3-.233.864-.367,1.286-.276.851-.429,1.268-.318.833-.49,1.243-.355.821-.551,1.219-.4.8-.613,1.194-.435.778-.668,1.158-.478.753-.729,1.127-.515.729-.784,1.09-.545.7-.833,1.047a18.665,18.665,0,0,1-12.708,6.969c-2.254.171-5.573-.386-7.594.508-.612-.251-1.206-.533-1.788-.839-5.108-2.707-7.686-6.8-9.217-12.181Z" transform="translate(-264.062 -82.39)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79967" data-name="Path 79967" d="M770.795,272.052a13.36,13.36,0,0,1,1.917.012,3.3,3.3,0,0,1,2.474,1.366,2.682,2.682,0,0,1,.282,2.2c-.4,1.831-1.874,2.7-3.387,3.552a13.423,13.423,0,0,1-2.211.2,2.157,2.157,0,0,1-1.843-.8,2.748,2.748,0,0,1-.515-2.413c.423-2.064,1.568-3.068,3.283-4.128Z" transform="translate(-273.326 -84.57)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79968" data-name="Path 79968" d="M757.156,282.89a2.981,2.981,0,0,1,.331.012,3.21,3.21,0,0,1,2.707,1.1,3.389,3.389,0,0,1,.288,2.4,12.126,12.126,0,0,1-5.365,7.3,7.335,7.335,0,0,1-1.421-.049,2.506,2.506,0,0,1-1.843-1.219,3.745,3.745,0,0,1-.159-2.842,11.191,11.191,0,0,1,5.463-6.7Z" transform="translate(-267.146 -88.782)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79969" data-name="Path 79969" d="M778.955,280.06c2.131,1.739,2.872,4.311,3.142,6.975.043.447.073.888.1,1.335s.031.894.031,1.335-.012.894-.037,1.341-.055.888-.1,1.335-.1.888-.172,1.329-.147.882-.233,1.317-.19.87-.3,1.3-.233.863-.367,1.286-.276.851-.429,1.268-.319.833-.49,1.243-.355.821-.551,1.219-.4.8-.613,1.194-.435.778-.667,1.158-.478.753-.729,1.127-.514.729-.784,1.09-.545.7-.833,1.047a18.664,18.664,0,0,1-12.708,6.969c-2.254.171-5.573-.386-7.594.508-.612-.251-1.206-.533-1.788-.839-5.108-2.707-7.686-6.8-9.217-12.181l.294-.355c1.157.245,4.924,5.212,7.116,6.528a14.754,14.754,0,0,0,11.373,1.274,22.7,22.7,0,0,0,13.737-10.638c3.607-6.185,3.607-11.391,1.825-18.165Z" transform="translate(-264.479 -87.685)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79970" data-name="Path 79970" d="M135.855,561.506a17.724,17.724,0,0,1,13.026,4.171,11.771,11.771,0,0,1,3.785,8.549c.392,8.935-4.165,16.021-9.927,22.341l6.241,1.519c.735,1.359.4,3.564.594,5.108-3.889-.251-8.923-.686-11.789-3.656-5.892,2.854-12.01,4.654-18.152,6.859-2.444.876-5.193,2.29-7.692,2.774-3.65,1.794-7.5,3.185-11.158,4.991-10.3,5.1-18.759,12.151-26.524,20.559l2.578-11.691c-3.57-1.9-7.5-3.062-11.005-5.034,8.066-4.612,17.5-9.462,26.524-11.679,13-5.108,27.5-5.163,40.23-11.079-4.042-6.865-6.773-13.89-4.679-21.925,1.2-4.6,3.766-9.352,7.949-11.808Z" transform="translate(-1.401 -196.751)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79971" data-name="Path 79971" d="M185.279,567.82a3.212,3.212,0,0,1,2.131.864,6.545,6.545,0,0,1,1.488,4.7c-.061,7.472-2.646,14.949-8.084,20.2a3.073,3.073,0,0,1-.3-.263c-2.3-2.713-3.509-9.854-3.038-13.271.717-5.249,3.748-9.088,7.8-12.218Z" transform="translate(-44.633 -199.213)" fill="#f8f9fc"/>
      <path id="Path_79974" data-name="Path 79974" d="M866.908,556.041c6.9-.68,13.719.429,19.561,4.3,6.143,4.066,11.1,10.779,12.505,18.067a24.2,24.2,0,0,1-3.576,18.342c-3.981,5.732-10.534,9.211-17.295,10.436a29.359,29.359,0,0,1-20.608-5.781c-5.891-4.318-11.146-11.134-12.261-18.507a22.87,22.87,0,0,1,4.048-16.909C853.563,560.212,859.908,557.082,866.908,556.041Z" transform="translate(-303.377 -194.576)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79975" data-name="Path 79975" d="M871.834,566.078c6.124-.361,11.471.717,16.486,4.41a21.944,21.944,0,0,1,8.807,14.263,17.347,17.347,0,0,1-3.295,13.112c-3.258,4.256-8.078,6.375-13.265,7.123a23.69,23.69,0,0,1-14.257-3.454c-5.414-3.295-9.885-8.77-11.275-15.035a15.082,15.082,0,0,1,2.052-11.924c3.393-5.028,8.978-7.453,14.747-8.507Z" transform="translate(-307.115 -198.513)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79976" data-name="Path 79976" d="M877.7,576.633a17.69,17.69,0,0,1,9.762,2.462,15.15,15.15,0,0,1,7.184,9.284,11.571,11.571,0,0,1-1.317,8.96c-1.88,2.872-4.936,4.256-8.182,4.942-4.134.141-7.325-.288-10.852-2.627a16.476,16.476,0,0,1-7.1-10.313,10,10,0,0,1,1.641-7.7,13.589,13.589,0,0,1,8.868-5.016Z" transform="translate(-311.919 -202.619)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79977" data-name="Path 79977" d="M881.172,586.093a11.046,11.046,0,0,1,4.857.772,11.542,11.542,0,0,1,6.43,6.5,4.513,4.513,0,0,1-.135,3.546,6.4,6.4,0,0,1-4.085,2.9,11.4,11.4,0,0,1-6.571-1.237c-2.964-1.666-4.189-4.293-5.028-7.423a12.574,12.574,0,0,1,1.151-3.007,4.873,4.873,0,0,1,3.387-2.052Z" transform="translate(-315.65 -206.291)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79978" data-name="Path 79978" d="M448.4,194.8c.367.7.263.325.312,1.115a2.292,2.292,0,0,1-.288.031c-1.445-.018-57.121-26.377-62.712-29.439a22.426,22.426,0,0,1-5.34-3.883,10.192,10.192,0,0,1-2.621-7.5c.171-7.086,4.936-13.988,9.811-18.771,5.187-5.089,13.559-11.122,21.153-10.9a9.239,9.239,0,0,1,6.957,3.172c9.07,10.681,28.141,52.068,32.734,66.179Z" transform="translate(-122.29 -27.758)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79979" data-name="Path 79979" d="M404,157.228c-2.836,1.8-6.259,3.944-9.578,4.642a3.017,3.017,0,0,1-2.7-.441,2.385,2.385,0,0,1-.49-1.3c-.318-3.491,4.122-10.675,6.234-13.467,6.008-7.925,13.106-13.541,23.156-14.968-1.776,10.472-8.3,19.138-16.621,25.532Z" transform="translate(-127.511 -30.18)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79980" data-name="Path 79980" d="M428.971,130.63c9.07,10.681,28.141,52.068,32.734,66.179a25.777,25.777,0,0,1-3.54-2.9q-5.99-4.777-11.844-9.719-11.07-9.388-22.494-18.342c-3.858-3.044-7.692-6.247-11.746-9.033,8.323-6.394,14.845-15.06,16.621-25.532l.263-.655Z" transform="translate(-135.599 -29.77)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79982" data-name="Path 79982" d="M409.043,379.05a8.446,8.446,0,0,1,2.45.263,5.216,5.216,0,0,1,3.117,2.609,3.577,3.577,0,0,1,.153,2.891c-.576,1.482-1.69,2.1-3.062,2.75a6.5,6.5,0,0,1-2,.2,4.319,4.319,0,0,1-2.921-1.764,4.435,4.435,0,0,1-.631-3.423C406.508,380.8,407.592,379.975,409.043,379.05Z" transform="translate(-133.26 -126.051)" fill="url(#linear-gradient-2)"/>
      <path id="Path_79983" data-name="Path 79983" d="M385.941,341.4c2.254,1.084,6.694,11.814,8.537,14.612l-4.7,3.258-.57.153c-1.69-1.243-6.651-11.777-8.151-14.5l4.887-3.521Z" transform="translate(-123.576 -111.459)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80029" data-name="Path 80029" d="M580.308,248.35a3.967,3.967,0,0,1,2.756,1.029,2.533,2.533,0,0,1,.68,2.021c-.1,1.421-.931,2.186-1.911,3.1a8.436,8.436,0,0,1-1.709-.233,3.613,3.613,0,0,1-1.9-1.69,2.866,2.866,0,0,1-.092-2.248C578.482,249.281,579.413,248.858,580.308,248.35Z" transform="translate(-311.46 154.299)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80030" data-name="Path 80030" d="M651.467,321.856l.478.116a3.412,3.412,0,0,1,.122,1.935,28.951,28.951,0,0,0-1.2,7.11c-.049,3.809,1.6,6.167,4.109,8.807a24.648,24.648,0,0,1-2.946-1.764,8.54,8.54,0,0,1-.729-.612,8.665,8.665,0,0,1-.661-.68,8.44,8.44,0,0,1-.582-.747c-.184-.257-.349-.527-.508-.8a8.436,8.436,0,0,1-.416-.851c-.122-.288-.233-.588-.331-.888s-.172-.606-.239-.919-.11-.625-.141-.937a14.535,14.535,0,0,1,3.038-9.774Z" transform="translate(-338.757 125.812)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80031" data-name="Path 80031" d="M945.549,392.42a3.327,3.327,0,0,1,.515.7c1.127,2.015,3.056,6.492,2.168,8.709-.453,1.121-1.709,1.439-2.738,1.794-.153-.037-.306-.067-.459-.1a3.38,3.38,0,0,1-2.431-1.549c-.637-2.94,1.764-6.853,2.946-9.542Z" transform="translate(-452.749 98.461)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80032" data-name="Path 80032" d="M958.349,384.989a5.38,5.38,0,0,1,1.433-.061,2.96,2.96,0,0,1,1.684,1.623,4.048,4.048,0,0,1,.49,3.209,2.282,2.282,0,0,1-1.317,1.262c-2.015.778-4.146-.423-6-1.157l-2.486-.986.374-.5-.22-.147a4.73,4.73,0,0,1-1.225.221l.165-.459a26.04,26.04,0,0,1,7.11-3.007Z" transform="translate(-456.075 101.378)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80033" data-name="Path 80033" d="M928.5,384.09c3.338.4,7.515,2.474,10.528,3.962l-3.92,1.139a26.493,26.493,0,0,1-4.33,1.415,3.248,3.248,0,0,1-2.737-.38,5.1,5.1,0,0,1-.839-3.65,3.229,3.229,0,0,1,1.292-2.48Z" transform="translate(-446.812 101.689)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80034" data-name="Path 80034" d="M940.674,685.9c-.037-.135-.08-.27-.116-.4a4.216,4.216,0,0,1,.4-3.711,4.823,4.823,0,0,1,3.111-1.7c1.47.4,2.633.759,3.393,2.18a4.031,4.031,0,0,1,.159,3.332,4.477,4.477,0,0,1-2.3,2.536,3.3,3.3,0,0,1-.312.122,1.952,1.952,0,0,1-.325.086,2.972,2.972,0,0,1-.337.055,3.062,3.062,0,0,1-1-.055,2.841,2.841,0,0,1-.325-.092,3.351,3.351,0,0,1-.312-.129,4.5,4.5,0,0,1-1.948-2.07l-.092-.147Z" transform="translate(-754.694 -156.006)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80035" data-name="Path 80035" d="M940.674,685.9c-.037-.135-.08-.27-.116-.4a4.216,4.216,0,0,1,.4-3.711,4.823,4.823,0,0,1,3.111-1.7c.753,1.047,1.617,2,1.47,3.375a1.557,1.557,0,0,1-.723,1.145c-.968.827-2.946.931-4.146,1.292Z" transform="translate(-754.694 -156.006)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80036" data-name="Path 80036" d="M943.919,369.018a9.713,9.713,0,0,1,5.493,1.292,50.65,50.65,0,0,1-2.995,9.2c-.159.5-.324.992-.453,1.5-.024.092-.043.184-.061.282l-.3.086c-.821-2.566-3.473-8.317-3.007-10.846.177-.943.557-1.01,1.317-1.519Z" transform="translate(-452.766 107.538)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80037" data-name="Path 80037" d="M689.78,342.432a6.425,6.425,0,0,1,1.237.006,4.131,4.131,0,0,1,3.338,5.414c-.447,1.629-1.531,2.327-2.9,3.172a5.149,5.149,0,0,1-.906-.031,4.517,4.517,0,0,1-3.258-1.837,3.919,3.919,0,0,1-.5-2.995c.337-1.862,1.5-2.707,2.983-3.73Z" transform="translate(-353.607 117.846)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80038" data-name="Path 80038" d="M759.539,557.951a6.753,6.753,0,0,1,2.523.263,4.359,4.359,0,0,1,2.4,5.72,4.976,4.976,0,0,1-3.007,2.787,13.57,13.57,0,0,1-1.868-.061,4.443,4.443,0,0,1-2.725-2.058,4.36,4.36,0,0,1-.429-3.644c.514-1.672,1.629-2.254,3.111-3.013Z" transform="translate(-380.552 34.31)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80039" data-name="Path 80039" d="M941.824,525.08a6.691,6.691,0,0,1,2.554.453,5.634,5.634,0,0,1,2.909,3.546,4.432,4.432,0,0,1-.441,3.466,4.99,4.99,0,0,1-3.46,2.217,8.272,8.272,0,0,1-2.836-.576,5.481,5.481,0,0,1-2.848-3.062,4.057,4.057,0,0,1,.245-3.35,5.9,5.9,0,0,1,3.877-2.689Z" transform="translate(-450.794 47.045)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80040" data-name="Path 80040" d="M909.275,793.486a6.858,6.858,0,0,1,2.419.257,5.912,5.912,0,0,1,3.546,3.025,4.724,4.724,0,0,1,.177,3.607,5.721,5.721,0,0,1-3.411,3.234,11.992,11.992,0,0,1-2.437-.073,5.2,5.2,0,0,1-3.276-2.266,4.79,4.79,0,0,1-.5-3.846c.515-1.947,1.825-2.94,3.479-3.938Z" transform="translate(-741.239 -199.954)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80041" data-name="Path 80041" d="M649.42,217.955c.9-2.995,1.99-5.726,4.8-7.41,3.785-2.272,10.5-1.948,14.612-.968-1.758.723-4.789-.055-6.822.122-.239,1.684-1.347,4.679-1.158,6.081,1.635,1.764,5.414.257,7.374,2.125-4.924-.778-13.13-2.731-17.454.386l-1.347-.343Z" transform="translate(-339.159 169.616)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80042" data-name="Path 80042" d="M861.141,669.571c.747-.049,1.494-.086,2.241-.122,1.66-.067,3.509-.018,4.722,1.335a3.7,3.7,0,0,1,.955,3c-.282,2.523-2.388,5.089-4.318,6.6a4.937,4.937,0,0,1-3.215.288,6.168,6.168,0,0,1-3.215-3.3,7.01,7.01,0,0,1-.318-5.475c.619-1.439,1.776-1.837,3.148-2.333Z" transform="translate(-722.623 -151.875)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80043" data-name="Path 80043" d="M795.366,165.493c4.5-1,8.978-2.364,13.431-3.577,5.083-1.384,10.24-2.652,15.255-4.256l1.923,3.5c-6.265,1.286-12.249,3.13-18.36,5.016-3.466,1.072-7.049,2.021-10.436,3.319-.655-1.311-1.231-2.658-1.819-4.005Z" transform="translate(-395.723 189.448)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80044" data-name="Path 80044" d="M809.784,394.57a9.6,9.6,0,0,1,2.958.631c.2.08.386.171.576.269s.367.208.545.325a6.094,6.094,0,0,1,.508.38,6.209,6.209,0,0,1,.465.429q.221.23.423.478a4.939,4.939,0,0,1,.367.515,6.078,6.078,0,0,1,.318.551,4.928,4.928,0,0,1,.257.582,5.854,5.854,0,0,1,.312,1.121,4.571,4.571,0,0,1,.073.582c.012.2.018.392.018.582a4.6,4.6,0,0,1-.043.582c-.025.2-.055.386-.1.576s-.1.38-.159.563-.135.367-.214.545A6.6,6.6,0,0,1,812,406.806a8.1,8.1,0,0,1-4.8-.864,6.122,6.122,0,0,1-2.039-8.6c1.188-1.782,2.584-2.333,4.606-2.768Z" transform="translate(-399.147 97.627)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80045" data-name="Path 80045" d="M569.608,296.616a8.723,8.723,0,0,1,2.774.514,7.054,7.054,0,0,1,4.048,4.183,7.33,7.33,0,0,1,.233.735,6.54,6.54,0,0,1,.153.753c.037.257.061.508.074.766s.012.514,0,.772-.043.514-.086.766-.1.5-.165.753a6.474,6.474,0,0,1-.245.735c-.092.239-.2.472-.319.7-1.145,2.162-2.878,3.044-5.132,3.742a8.569,8.569,0,0,1-4.177-.759c-.214-.11-.416-.227-.618-.355a6.3,6.3,0,0,1-.582-.416,6.509,6.509,0,0,1-.533-.472c-.172-.165-.331-.343-.484-.521s-.294-.374-.429-.57a6.147,6.147,0,0,1-.367-.606c-.11-.208-.214-.423-.306-.643a6.26,6.26,0,0,1-.239-.674,7.022,7.022,0,0,1-.288-1.47c-.024-.251-.03-.5-.03-.747a6.051,6.051,0,0,1,.049-.747,7.132,7.132,0,0,1,.129-.735,7.5,7.5,0,0,1,.2-.723,6.864,6.864,0,0,1,.276-.7c.1-.227.22-.447.349-.668,1.329-2.217,3.3-3.038,5.708-3.626Z" transform="translate(-305.623 135.594)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80046" data-name="Path 80046" d="M596.586,111.33c2.217.269,3.552,3.515,5.408,4.844a23.481,23.481,0,0,1,3.221.263,1.858,1.858,0,0,1,1.451,1.109c-.306,1.66-2.223,3.8-3.136,5.31.337,1.2.876,2.695.484,3.932-.159.5-.392.5-.808.747a7.143,7.143,0,0,1-3.338-1.115c-2.646-.735-3.828,1.556-6.253,1.556-.5,0-.582-.251-.906-.57a21.983,21.983,0,0,1,.08-5.6c-.906-.894-2.548-2.168-2.928-3.387-.141-.453.08-.606.294-.992a7.061,7.061,0,0,1,3.075-.949c1.237-1.611,2.272-3.423,3.35-5.144Z" transform="translate(-316.058 207.404)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80047" data-name="Path 80047" d="M854.637,261.666a12.215,12.215,0,0,1,5.163.717,8.044,8.044,0,0,1,4.4,4.391,7.867,7.867,0,0,1-.19,6.1c-1.133,2.4-2.9,3.46-5.31,4.33a8.514,8.514,0,0,1-5.383-.576,8.236,8.236,0,0,1-.79-.429,8.429,8.429,0,0,1-.747-.508,8.886,8.886,0,0,1-1.311-1.231c-.2-.227-.38-.465-.557-.71a8.672,8.672,0,0,1-1.188-2.413,7.32,7.32,0,0,1,5.916-9.676Z" transform="translate(-416.263 149.14)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80048" data-name="Path 80048" d="M857.277,269.006a4.739,4.739,0,0,1,3.129.343c.318.416.539.563.582,1.115.092,1.317-.68,2.18-1.458,3.142a2.841,2.841,0,0,1-2.352-.061,2.364,2.364,0,0,1-.778-1.28,3.868,3.868,0,0,1,.87-3.252Z" transform="translate(-419.344 146.332)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80049" data-name="Path 80049" d="M622.268,276.82c8.439,4.673,24.142,7.061,28.4,16.168-.435,1.158-.827,2.37-1.366,3.485-1.243-.276-2.48-.49-3.742-.686q-3.133-1.865-6.431-3.411c-3.944-1.831-16.995-6.633-18.483-10.344-.717-1.788.073-3.411.778-5.065l.839-.147Z" transform="translate(-327.9 143.264)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80050" data-name="Path 80050" d="M676.045,210.05c4.183.367,10.485,2,14.1,4.128,1.2,2.211,8.623,2.652,9.6,4.422a16.366,16.366,0,0,1-3.926,5.267c-1.745.239-7.312-2.174-7.906-1.494-4.122-1.409-8.28-2.823-12.475-3.993-1.96-1.874-5.732-.367-7.374-2.125-.2-1.409.919-4.4,1.158-6.081,2.033-.178,5.065.606,6.822-.123Z" transform="translate(-346.377 169.143)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80051" data-name="Path 80051" d="M664.169,86.6a10.339,10.339,0,0,1,4.716.974,8.418,8.418,0,0,1,4.183,5.4,9.385,9.385,0,0,1-1.164,7.514c-1.592,2.407-3.791,3.35-6.51,3.944a11.5,11.5,0,0,1-5.236-1.255,8.493,8.493,0,0,1-4.232-5.31,8.986,8.986,0,0,1,1.139-6.816C658.768,88.278,661.15,87.268,664.169,86.6Z" transform="translate(-341.586 216.989)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80052" data-name="Path 80052" d="M665.584,94.57a5.169,5.169,0,0,1,3.136,1.311,2.716,2.716,0,0,1,.808,2.358,4.306,4.306,0,0,1-2.143,2.8,3.882,3.882,0,0,1-3.209-1.28,3.067,3.067,0,0,1-.643-2.358c.184-1.415.943-2.039,2.052-2.829Z" transform="translate(-344.618 213.9)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80053" data-name="Path 80053" d="M613.72,328.542c-.1-1.066-.2-2.137-.233-3.2a16.9,16.9,0,0,1,4.789-11.544c4.085-4.262,10.883-6.363,16.67-6.467,2.56-.043,5.108.337,7.668.288h.276c1.262.184,2.5.4,3.742.674-.735,1.99-1.727,6.308-3.785,7.2-1.213.521-2.517.233-3.577,1.115l-.735,1.678a3.416,3.416,0,0,0-.122-1.935l-.478-.116-.184-.582c-2.333-1.231-7.974.141-10.43.857-7.037,2.07-10.172,5.781-13.608,12.04Z" transform="translate(-325.231 131.442)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80054" data-name="Path 80054" d="M777.631,266.72c5.732.839,10.748,3.246,14.257,8a19.328,19.328,0,0,1,3.16,14.435A26.731,26.731,0,0,1,783.713,306.3a22.885,22.885,0,0,1-17.6,4.018,14.619,14.619,0,0,1-3.528-1.041c2.021-.894,5.34-.337,7.594-.508a18.664,18.664,0,0,0,12.708-6.969c.288-.343.563-.692.833-1.047s.533-.717.784-1.09.5-.741.729-1.127.459-.766.667-1.157.417-.79.613-1.194.38-.808.551-1.219.337-.827.49-1.243.294-.845.429-1.268.251-.857.367-1.286.208-.87.3-1.3.166-.876.233-1.317.123-.882.172-1.329.08-.888.1-1.335.037-.894.037-1.341-.006-.894-.031-1.335-.055-.894-.1-1.335c-.263-2.664-1.011-5.236-3.142-6.976-.913-3.846-5.267-6.149-8.292-8.17Z" transform="translate(-383.021 147.179)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80055" data-name="Path 80055" d="M648.505,223.73l1.347.343c7.815,8.629,22.733,14.772,27.841,24.093-.808,2.633-2.78,8.813-5.285,10.2-.1.055-.214.092-.319.135l-.239-.19c.214-.22.435-.441.637-.674,1-1.151,2.713-3.307,2.6-4.844-1.2-1.041-4.024-.723-5.659-1.372l.024-.355,4.25.576c-9.052-7.061-23.517-12.977-26.647-24.981l1.445-2.934Z" transform="translate(-338.244 163.841)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80056" data-name="Path 80056" d="M688.12,180.654c.588-1.666.165-3.705.888-5.3.214-.472.178-.318.741-.49a3.613,3.613,0,0,1,1.7,1.752l.2.325.472-.38c2.039,2.2,4.183,4.232,6.43,6.222,2.995,2.652,14.839,11.691,14.4,15.531-.276,2.413-3.564,4.875-4.912,6.835-.931,1.353-1.641,2.909-2.689,4.171a9.115,9.115,0,0,1-2.046,1.739,36.168,36.168,0,0,1-7.619-2.327c.594-.68,6.161,1.727,7.906,1.494a16.364,16.364,0,0,0,3.926-5.267c-.974-1.77-8.4-2.211-9.6-4.422,2.878.8,5.542,2.358,8.482,3.007a2.048,2.048,0,0,0,1.776-.116C706.517,198.536,689.376,189.093,688.12,180.654Z" transform="translate(-354.158 182.781)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80057" data-name="Path 80057" d="M894.77,409.19c6.669,1.837,11.477,8.868,14.692,14.551,4.734,8.378,7.815,20.418,5.12,29.923a14.486,14.486,0,0,1-2.248,4.789c-.471-2.627-1.066-5.23-1.635-7.839-2.364-10.46-5.083-20.884-10.264-30.358-1.721-3.717-4.44-7.159-5.659-11.067Z" transform="translate(-434.25 91.961)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80058" data-name="Path 80058" d="M827.126,163.38c1.4,2.646,2.627,5.273,3.785,8.029-6.578,3.374-14.012,5.794-20.945,8.4a74.6,74.6,0,0,0-7.441,2.86,111.116,111.116,0,0,0-4.2-10.95c3.387-1.3,6.969-2.248,10.436-3.319,6.106-1.886,12.089-3.73,18.36-5.016Z" transform="translate(-396.872 187.231)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80059" data-name="Path 80059" d="M622.1,270.537c1.072-3.05,2.542-7.2,5.585-8.727,7.962-4,23.958.508,32.1,3.4l-.025.355c1.635.643,4.458.325,5.659,1.372.11,1.537-1.6,3.693-2.6,4.844-.2.233-.423.453-.637.674l.239.19c-5.426,2.535-27.473-8.017-39.477-2.254l-.839.147Z" transform="translate(-328.571 149.695)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80060" data-name="Path 80060" d="M833.57,176.49c1.415,3.791,2.664,7.582,3.8,11.465q-9.967,4.161-20.161,7.759c-2.952,1.029-6.063,1.684-8.948,2.866-.919-3.638-1.862-7.276-3.074-10.828a74.648,74.648,0,0,1,7.441-2.86c6.933-2.6,14.368-5.028,20.945-8.4Z" transform="translate(-399.532 182.15)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80061" data-name="Path 80061" d="M839.315,195.2c1.035,4.569,1.966,9.107,2.639,13.749-9.78,2.113-19.757,4.471-29.335,7.367a79.649,79.649,0,0,0-2.419-10.491c2.885-1.182,6-1.837,8.948-2.866q10.188-3.592,20.161-7.759Z" transform="translate(-401.473 174.898)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80062" data-name="Path 80062" d="M810.216,480.032c4.2.233,8.849-.962,13.1-1.2q19.5-1.259,39.048-1.752c4.967-.135,10.105-.68,15.059-.325.57,2.609,1.17,5.212,1.635,7.839-.123.184-.1.2-.325.3-1.109.5-46.2-1.011-51.542-1.415-5.91-.447-12.622-.631-18.262-2.48A7.934,7.934,0,0,1,810.216,480.032Z" transform="translate(-400.981 65.82)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80063" data-name="Path 80063" d="M730.43,110.052c1.611-.563,3.2-1.311,4.777-1.972.2,1.458.282,2.94.38,4.409.576,8.8-1.2,16.64-7.165,23.364-5.818,6.553-15.213,10.081-23.787,10.834-3.693.325-7.392.135-11.085.38-1.084.073-2.039.073-2.787.925a5.875,5.875,0,0,0,1.3,2.689l-.471.38-.2-.325a3.639,3.639,0,0,0-1.7-1.752c-.563.165-.527.018-.741.49-.729,1.6-.306,3.632-.888,5.3a11.637,11.637,0,0,1-.1-1.311c-.153-4.575,1.592-10.485,4.771-13.859,5.218-2.015,11.33-2.192,16.8-3.423,6.479-1.464,16.915-4.158,20.571-10.295,2.94-4.93,1.647-10.644.337-15.837Z" transform="translate(-354.094 208.664)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80064" data-name="Path 80064" d="M762.3,459.91h.809c.024.11.055.208.08.306.968,3.877.986,8.255,1.311,12.236.674,8.188,2.174,19.236,7.2,25.918a32.428,32.428,0,0,0,9,7.674c6.216,3.828,15.476,7.1,17.338,14.925.073.306.141.612.208.919a28.377,28.377,0,0,1,.508,3.374c-2.86,6.89-3.822,3.626-7.22,6.645-2.781,9.891,7.87,14.772,11.948,22.384,3.822,7.129,2.823,13.351.539,20.743-1.359-1.029-2.67-2.125-3.993-3.191a37.132,37.132,0,0,0,1.47-6.3c1.023-9.076-4.446-13.816-9.542-20.29-3.509-4.458-3.84-8.237-3.16-13.694-1.941-.435-3.785-.919-4.955-2.689a3.4,3.4,0,0,1-.423-2.695,7.153,7.153,0,0,1,3.73-4.054,10.434,10.434,0,0,1,8.531-.71l.153-.367c-4.207-9.848-22.39-11.74-29.929-28.159-2.983-6.5-6.124-26.23-3.607-32.973Z" transform="translate(-451.827 47.157)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80065" data-name="Path 80065" d="M808.359,563.2a8.271,8.271,0,0,1,1.274.214,2.613,2.613,0,0,1,1.99,1.286,2.368,2.368,0,0,1-.619,1.525c-1.452,1.721-3.087,2.039-5.218,2.26a6.127,6.127,0,0,1-2.241-.2c-.821-.288-.906-.484-1.255-1.213a2.678,2.678,0,0,1,.6-1.519c1.549-1.9,3.154-2.156,5.469-2.358Z" transform="translate(-398.408 32.275)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80066" data-name="Path 80066" d="M516.78,53.826c1.3,1.176,3.479,5.506,4.7,7.2,3.577-1.335,7.349-2.3,11.011-3.387-2.695,6.449-4.4,13.394-6.443,20.075l-9.768,31.717c-1.66,5.31-3.154,10.675-4.728,16.009.043-5.371.545-10.809.839-16.18,1.017-18.544,2.848-36.935,4.391-55.437Z" transform="translate(-285.724 229.693)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80067" data-name="Path 80067" d="M744.942,431.536a22.286,22.286,0,0,1-11.109-1.562c-.435-.184-.864-.386-1.286-.594s-.839-.429-1.255-.655-.821-.472-1.219-.717-.8-.508-1.182-.778-.766-.551-1.139-.839-.741-.588-1.1-.894-.71-.625-1.053-.949-.674-.655-1-1-.643-.692-.949-1.047-.606-.723-.894-1.1-.57-.753-.839-1.139-.533-.778-.784-1.182-.49-.808-.723-1.219-.447-.827-.661-1.255-.4-.851-.594-1.286-.361-.87-.527-1.311c-2.321-6.057-3-13.394-.165-19.4a15.234,15.234,0,0,1,8.947-7.919l-.135.374c-.514,1.39-1.329,2.591-1.794,4.024-1.654,5.157-.374,10.956,1.764,15.8,3.252,7.349,9.768,14.031,17.313,16.964,3.8,1.476,8.078,1.972,11.906.276a20.76,20.76,0,0,0,3.724-2.4,14.177,14.177,0,0,1-8.782,8.157,47.882,47.882,0,0,1-5.659,1.654h-.808Z" transform="translate(-365.196 100.678)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80068" data-name="Path 80068" d="M808.216,454.859a2.4,2.4,0,0,1-.288-.27,4.35,4.35,0,0,1-.8-3.074,4.6,4.6,0,0,1,1.66-2.272c2.529-2.382,5.463-4.446,8.17-6.626,6.945-5.6,13.988-11,21.067-16.419,7.478-5.726,14.606-12.206,22.758-17.007,1.219,3.913,3.938,7.349,5.659,11.067-5.947,4.642-12.971,7.649-19.506,11.336-7.429,4.189-14.637,8.739-22.109,12.879-4.177,2.315-12.065,6.032-15.329,9.419a7.942,7.942,0,0,0-1.286.968Z" transform="translate(-400.267 91.961)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80069" data-name="Path 80069" d="M891.546,305.48c.018,2.566-1.1,5.763-1.69,8.286a129.289,129.289,0,0,1,12.463,4.073C881.937,324.128,861,332.047,843.1,343.83q-3.748,2.985-7.453,6.026c4.428-4.765,8.029-10.27,12.426-15.078,11.961-13.094,27.62-21.8,43.47-29.292Z" transform="translate(-411.338 132.156)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80070" data-name="Path 80070" d="M552.618,151.35c1.513,1.482,1.752,5.941,2.8,8.029,2,3.981,6.535,5.683,10.515,6.969q-21.569,22.47-43.966,44.119c-2.56,2.272-5.016,4.691-7.5,7.037,4.091-10.595,10.509-20.767,16.493-30.376,7.367-11.838,14.986-23.529,21.668-35.778Z" transform="translate(-286.856 191.893)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80071" data-name="Path 80071" d="M732.561,386.3c4.593-1.482,9.952-.172,14.147,1.947,8.023,4.06,15.678,12.346,18.458,20.963,1.188,3.687,1.684,8.47.172,12.12a20.759,20.759,0,0,1-3.724,2.4c-3.828,1.7-8.1,1.2-11.906-.276-7.545-2.933-14.061-9.609-17.313-16.964-2.143-4.844-3.417-10.644-1.764-15.8.459-1.439,1.28-2.633,1.794-4.024l.135-.374Z" transform="translate(-370.342 101.078)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80072" data-name="Path 80072" d="M745.1,390.058a13.5,13.5,0,0,1,3.479.165c6.118,1.311,12.561,7.521,15.739,12.677,1.366,2.217,2.676,5.126,1.923,7.766a6.233,6.233,0,0,1-3.313,3.809,11.386,11.386,0,0,1-5.1.067c-5.512-1.219-10.791-5.867-13.675-10.6-1.666-2.731-3.227-6.6-2.34-9.787a6.446,6.446,0,0,1,3.3-4.091Z" transform="translate(-374.867 99.39)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80073" data-name="Path 80073" d="M749.956,395.49a6.771,6.771,0,0,1,3.968,1.176,3.831,3.831,0,0,1,1.862,2.707,3.312,3.312,0,0,1-1.151,2.731,5.745,5.745,0,0,1-3.656-.808,4.041,4.041,0,0,1-2-2.695,3.709,3.709,0,0,1,.98-3.105Z" transform="translate(-377.724 97.271)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80074" data-name="Path 80074" d="M867.973,427.26c5.181,9.474,7.9,19.9,10.264,30.358-4.948-.355-10.093.19-15.06.325q-19.54.487-39.048,1.752c-4.256.233-8.9,1.427-13.1,1.2,3.264-3.381,11.158-7.1,15.329-9.419,7.471-4.14,14.674-8.69,22.109-12.879C855,434.909,862.027,431.908,867.973,427.26Z" transform="translate(-401.795 84.958)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80075" data-name="Path 80075" d="M744.193,296.121a18.832,18.832,0,0,1-.527-7.4,27.952,27.952,0,0,1,9.823-17.846c5.04-4.073,10.478-4.918,16.756-4.281,3.025,2.027,7.374,4.324,8.292,8.17,2.131,1.739,2.872,4.311,3.142,6.975.043.447.073.888.1,1.335s.031.894.031,1.335-.012.894-.037,1.341-.055.888-.1,1.335-.1.888-.172,1.329-.147.882-.233,1.317-.19.87-.3,1.3-.233.864-.367,1.286-.276.851-.429,1.268-.318.833-.49,1.243-.355.821-.551,1.219-.4.8-.613,1.194-.435.778-.668,1.158-.478.753-.729,1.127-.515.729-.784,1.09-.545.7-.833,1.047a18.665,18.665,0,0,1-12.708,6.969c-2.254.171-5.573-.386-7.594.508-.612-.251-1.206-.533-1.788-.839-5.108-2.707-7.686-6.8-9.217-12.181Z" transform="translate(-375.636 147.303)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80076" data-name="Path 80076" d="M770.795,272.052a13.36,13.36,0,0,1,1.917.012,3.3,3.3,0,0,1,2.474,1.366,2.682,2.682,0,0,1,.282,2.2c-.4,1.831-1.874,2.7-3.387,3.552a13.423,13.423,0,0,1-2.211.2,2.157,2.157,0,0,1-1.843-.8,2.748,2.748,0,0,1-.515-2.413c.423-2.064,1.568-3.068,3.283-4.128Z" transform="translate(-384.899 145.123)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80077" data-name="Path 80077" d="M757.156,282.89a2.981,2.981,0,0,1,.331.012,3.21,3.21,0,0,1,2.707,1.1,3.389,3.389,0,0,1,.288,2.4,12.126,12.126,0,0,1-5.365,7.3,7.335,7.335,0,0,1-1.421-.049,2.506,2.506,0,0,1-1.843-1.219,3.745,3.745,0,0,1-.159-2.842,11.191,11.191,0,0,1,5.463-6.7Z" transform="translate(-378.72 140.912)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80078" data-name="Path 80078" d="M778.955,280.06c2.131,1.739,2.872,4.311,3.142,6.975.043.447.073.888.1,1.335s.031.894.031,1.335-.012.894-.037,1.341-.055.888-.1,1.335-.1.888-.172,1.329-.147.882-.233,1.317-.19.87-.3,1.3-.233.863-.367,1.286-.276.851-.429,1.268-.319.833-.49,1.243-.355.821-.551,1.219-.4.8-.613,1.194-.435.778-.667,1.158-.478.753-.729,1.127-.514.729-.784,1.09-.545.7-.833,1.047a18.664,18.664,0,0,1-12.708,6.969c-2.254.171-5.573-.386-7.594.508-.612-.251-1.206-.533-1.788-.839-5.108-2.707-7.686-6.8-9.217-12.181l.294-.355c1.157.245,4.924,5.212,7.116,6.528a14.754,14.754,0,0,0,11.373,1.274,22.7,22.7,0,0,0,13.737-10.638c3.607-6.185,3.607-11.391,1.825-18.165Z" transform="translate(-376.052 142.009)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80079" data-name="Path 80079" d="M866.908,556.041c6.9-.68,13.719.429,19.561,4.3,6.143,4.066,11.1,10.779,12.505,18.067a24.2,24.2,0,0,1-3.576,18.342c-3.981,5.732-10.534,9.211-17.295,10.436a29.359,29.359,0,0,1-20.608-5.781c-5.891-4.318-11.146-11.134-12.261-18.507a22.87,22.87,0,0,1,4.048-16.909C853.563,560.212,859.908,557.082,866.908,556.041Z" transform="translate(-414.951 35.118)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80080" data-name="Path 80080" d="M871.834,566.078c6.124-.361,11.471.717,16.486,4.41a21.944,21.944,0,0,1,8.807,14.263,17.347,17.347,0,0,1-3.295,13.112c-3.258,4.256-8.078,6.375-13.265,7.123a23.69,23.69,0,0,1-14.257-3.454c-5.414-3.295-9.885-8.77-11.275-15.035a15.082,15.082,0,0,1,2.052-11.924c3.393-5.028,8.978-7.453,14.747-8.507Z" transform="translate(-418.689 31.181)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80081" data-name="Path 80081" d="M877.7,576.633a17.69,17.69,0,0,1,9.762,2.462,15.15,15.15,0,0,1,7.184,9.284,11.571,11.571,0,0,1-1.317,8.96c-1.88,2.872-4.936,4.256-8.182,4.942-4.134.141-7.325-.288-10.852-2.627a16.476,16.476,0,0,1-7.1-10.313,10,10,0,0,1,1.641-7.7,13.589,13.589,0,0,1,8.868-5.016Z" transform="translate(-423.493 27.074)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80082" data-name="Path 80082" d="M881.172,586.093a11.046,11.046,0,0,1,4.857.772,11.542,11.542,0,0,1,6.43,6.5,4.513,4.513,0,0,1-.135,3.546,6.4,6.4,0,0,1-4.085,2.9,11.4,11.4,0,0,1-6.571-1.237c-2.964-1.666-4.189-4.293-5.028-7.423a12.574,12.574,0,0,1,1.151-3.007,4.873,4.873,0,0,1,3.387-2.052Z" transform="translate(-427.224 23.403)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80083" data-name="Path 80083" d="M940.674,685.9c-.037-.135-.08-.27-.116-.4a4.216,4.216,0,0,1,.4-3.711,4.823,4.823,0,0,1,3.111-1.7c1.47.4,2.633.759,3.393,2.18a4.031,4.031,0,0,1,.159,3.332,4.477,4.477,0,0,1-2.3,2.536,3.3,3.3,0,0,1-.312.122,1.952,1.952,0,0,1-.325.086,2.972,2.972,0,0,1-.337.055,3.062,3.062,0,0,1-1-.055,2.841,2.841,0,0,1-.325-.092,3.351,3.351,0,0,1-.312-.129,4.5,4.5,0,0,1-1.948-2.07l-.092-.147Z" transform="translate(-649.557 -133.258)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80084" data-name="Path 80084" d="M940.674,685.9c-.037-.135-.08-.27-.116-.4a4.216,4.216,0,0,1,.4-3.711,4.823,4.823,0,0,1,3.111-1.7c.753,1.047,1.617,2,1.47,3.375a1.557,1.557,0,0,1-.723,1.145c-.968.827-2.946.931-4.146,1.292Z" transform="translate(-649.557 -133.258)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80085" data-name="Path 80085" d="M909.275,793.486a6.858,6.858,0,0,1,2.419.257,5.912,5.912,0,0,1,3.546,3.025,4.724,4.724,0,0,1,.177,3.607,5.721,5.721,0,0,1-3.411,3.234,11.992,11.992,0,0,1-2.437-.073,5.2,5.2,0,0,1-3.276-2.266,4.79,4.79,0,0,1-.5-3.846c.515-1.947,1.825-2.94,3.479-3.938Z" transform="translate(-636.101 -177.205)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80086" data-name="Path 80086" d="M861.141,669.571c.747-.049,1.494-.086,2.241-.122,1.66-.067,3.509-.018,4.722,1.335a3.7,3.7,0,0,1,.955,3c-.282,2.523-2.388,5.089-4.318,6.6a4.937,4.937,0,0,1-3.215.288,6.168,6.168,0,0,1-3.215-3.3,7.01,7.01,0,0,1-.318-5.475c.619-1.439,1.776-1.837,3.148-2.333Z" transform="translate(-617.486 -129.127)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80087" data-name="Path 80087" d="M762.3,459.91h.809c.024.11.055.208.08.306.968,3.877.986,8.255,1.311,12.236.674,8.188,2.174,19.236,7.2,25.918a32.428,32.428,0,0,0,9,7.674c6.216,3.828,15.476,7.1,17.338,14.925.073.306.141.612.208.919a28.377,28.377,0,0,1,.508,3.374c-2.86,6.89-3.822,3.626-7.22,6.645-2.781,9.891,7.87,14.772,11.948,22.384,3.822,7.129,2.823,13.351.539,20.743-1.359-1.029-2.67-2.125-3.993-3.191a37.132,37.132,0,0,0,1.47-6.3c1.023-9.076-4.446-13.816-9.542-20.29-3.509-4.458-3.84-8.237-3.16-13.694-1.941-.435-3.785-.919-4.955-2.689a3.4,3.4,0,0,1-.423-2.695,7.153,7.153,0,0,1,3.73-4.054,10.434,10.434,0,0,1,8.531-.71l.153-.367c-4.207-9.848-22.39-11.74-29.929-28.159-2.983-6.5-6.124-26.23-3.607-32.973Z" transform="translate(-580.2 -47.922)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80088" data-name="Path 80088" d="M866.908,556.041c6.9-.68,13.719.429,19.561,4.3,6.143,4.066,11.1,10.779,12.505,18.067a24.2,24.2,0,0,1-3.576,18.342c-3.981,5.732-10.534,9.211-17.295,10.436a29.359,29.359,0,0,1-20.608-5.781c-5.891-4.318-11.146-11.134-12.261-18.507a22.87,22.87,0,0,1,4.048-16.909C853.563,560.212,859.908,557.082,866.908,556.041Z" transform="translate(-612.603 -85.107)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80089" data-name="Path 80089" d="M871.834,566.078c6.124-.361,11.471.717,16.486,4.41a21.944,21.944,0,0,1,8.807,14.263,17.347,17.347,0,0,1-3.295,13.112c-3.258,4.256-8.078,6.375-13.265,7.123a23.69,23.69,0,0,1-14.257-3.454c-5.414-3.295-9.885-8.77-11.275-15.035a15.082,15.082,0,0,1,2.052-11.924c3.393-5.028,8.978-7.453,14.747-8.507Z" transform="translate(-616.341 -89.045)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80090" data-name="Path 80090" d="M877.7,576.633a17.69,17.69,0,0,1,9.762,2.462,15.15,15.15,0,0,1,7.184,9.284,11.571,11.571,0,0,1-1.317,8.96c-1.88,2.872-4.936,4.256-8.182,4.942-4.134.141-7.325-.288-10.852-2.627a16.476,16.476,0,0,1-7.1-10.313,10,10,0,0,1,1.641-7.7,13.589,13.589,0,0,1,8.868-5.016Z" transform="translate(-621.145 -93.151)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80091" data-name="Path 80091" d="M881.172,586.093a11.046,11.046,0,0,1,4.857.772,11.542,11.542,0,0,1,6.43,6.5,4.513,4.513,0,0,1-.135,3.546,6.4,6.4,0,0,1-4.085,2.9,11.4,11.4,0,0,1-6.571-1.237c-2.964-1.666-4.189-4.293-5.028-7.423a12.574,12.574,0,0,1,1.151-3.007,4.873,4.873,0,0,1,3.387-2.052Z" transform="translate(-624.877 -96.823)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80092" data-name="Path 80092" d="M777.631,266.72c5.732.839,10.748,3.246,14.257,8a19.328,19.328,0,0,1,3.16,14.435A26.731,26.731,0,0,1,783.713,306.3a22.885,22.885,0,0,1-17.6,4.018,14.619,14.619,0,0,1-3.528-1.041c2.021-.894,5.34-.337,7.594-.508a18.664,18.664,0,0,0,12.708-6.969c.288-.343.563-.692.833-1.047s.533-.717.784-1.09.5-.741.729-1.127.459-.766.667-1.157.417-.79.613-1.194.38-.808.551-1.219.337-.827.49-1.243.294-.845.429-1.268.251-.857.367-1.286.208-.87.3-1.3.166-.876.233-1.317.123-.882.172-1.329.08-.888.1-1.335.037-.894.037-1.341-.006-.894-.031-1.335-.055-.894-.1-1.335c-.263-2.664-1.011-5.236-3.142-6.976-.913-3.846-5.267-6.149-8.292-8.17Z" transform="translate(-393.986 -13.822)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80093" data-name="Path 80093" d="M744.193,296.121a18.832,18.832,0,0,1-.527-7.4,27.952,27.952,0,0,1,9.823-17.846c5.04-4.073,10.478-4.918,16.756-4.281,3.025,2.027,7.374,4.324,8.292,8.17,2.131,1.739,2.872,4.311,3.142,6.975.043.447.073.888.1,1.335s.031.894.031,1.335-.012.894-.037,1.341-.055.888-.1,1.335-.1.888-.172,1.329-.147.882-.233,1.317-.19.87-.3,1.3-.233.864-.367,1.286-.276.851-.429,1.268-.318.833-.49,1.243-.355.821-.551,1.219-.4.8-.613,1.194-.435.778-.668,1.158-.478.753-.729,1.127-.515.729-.784,1.09-.545.7-.833,1.047a18.665,18.665,0,0,1-12.708,6.969c-2.254.171-5.573-.386-7.594.508-.612-.251-1.206-.533-1.788-.839-5.108-2.707-7.686-6.8-9.217-12.181Z" transform="translate(-386.6 -13.697)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80094" data-name="Path 80094" d="M757.156,282.89a2.981,2.981,0,0,1,.331.012,3.21,3.21,0,0,1,2.707,1.1,3.389,3.389,0,0,1,.288,2.4,12.126,12.126,0,0,1-5.365,7.3,7.335,7.335,0,0,1-1.421-.049,2.506,2.506,0,0,1-1.843-1.219,3.745,3.745,0,0,1-.159-2.842,11.191,11.191,0,0,1,5.463-6.7Z" transform="translate(-389.685 -20.089)" fill="url(#linear-gradient-2)"/>
      <path id="Path_80095" data-name="Path 80095" d="M778.955,280.06c2.131,1.739,2.872,4.311,3.142,6.975.043.447.073.888.1,1.335s.031.894.031,1.335-.012.894-.037,1.341-.055.888-.1,1.335-.1.888-.172,1.329-.147.882-.233,1.317-.19.87-.3,1.3-.233.863-.367,1.286-.276.851-.429,1.268-.319.833-.49,1.243-.355.821-.551,1.219-.4.8-.613,1.194-.435.778-.667,1.158-.478.753-.729,1.127-.514.729-.784,1.09-.545.7-.833,1.047a18.664,18.664,0,0,1-12.708,6.969c-2.254.171-5.573-.386-7.594.508-.612-.251-1.206-.533-1.788-.839-5.108-2.707-7.686-6.8-9.217-12.181l.294-.355c1.157.245,4.924,5.212,7.116,6.528a14.754,14.754,0,0,0,11.373,1.274,22.7,22.7,0,0,0,13.737-10.638c3.607-6.185,3.607-11.391,1.825-18.165Z" transform="translate(-387.017 -18.992)" fill="url(#linear-gradient-2)"/>
    </g>
  </g>
</svg>
