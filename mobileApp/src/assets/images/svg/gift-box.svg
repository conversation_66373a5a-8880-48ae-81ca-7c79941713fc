<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="68.234" height="68.644" viewBox="0 0 68.234 68.644">
  <defs>
    <linearGradient id="linear-gradient" x1="0.496" y1="1.564" x2="0.722" y2="-0.022" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#a5b5db"/>
      <stop offset="1" stop-color="#1e232e"/>
    </linearGradient>
    <filter id="Path_87803" x="0" y="0.411" width="68.234" height="68.233" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_87807" x="0" y="7.183" width="55.072" height="61.462" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-3" x1="0.334" y1="0.268" x2="0.864" y2="0.97" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffd945"/>
      <stop offset="0.304" stop-color="#ffcd3e"/>
      <stop offset="0.856" stop-color="#ffad2b"/>
      <stop offset="1" stop-color="#ffa325"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="0.189" y1="1.127" x2="0.864" y2="0.97" xlink:href="#linear-gradient-3"/>
  </defs>
  <g id="Group_14445" data-name="Group 14445" transform="translate(-50.182 -460.972)">
    <g id="offer_2268558" transform="translate(53.393 450.156)">
      <path id="Path_87803-2" data-name="Path 87803" d="M61.409,75.374a1.572,1.572,0,0,0,0-2.1l-3.2-3.569a1.571,1.571,0,0,1-.312-1.571l1.591-4.516a1.572,1.572,0,0,0-.8-1.94l-4.319-2.066a1.572,1.572,0,0,1-.891-1.332L53.216,53.5a1.571,1.571,0,0,0-1.484-1.484l-4.783-.262a1.571,1.571,0,0,1-1.332-.891l-2.066-4.319a1.572,1.572,0,0,0-1.94-.8L37.094,47.33a1.571,1.571,0,0,1-1.571-.312l-3.569-3.2a1.572,1.572,0,0,0-2.1,0l-3.569,3.2a1.572,1.572,0,0,1-1.571.312L20.2,45.738a1.572,1.572,0,0,0-1.94.8l-2.066,4.319a1.573,1.573,0,0,1-1.332.891l-4.783.262A1.572,1.572,0,0,0,8.6,53.5l-.262,4.783a1.572,1.572,0,0,1-.891,1.332L3.122,61.68a1.572,1.572,0,0,0-.8,1.94L3.91,68.137A1.572,1.572,0,0,1,3.6,69.708L.4,73.277a1.572,1.572,0,0,0,0,2.1l3.2,3.569a1.571,1.571,0,0,1,.312,1.571L2.318,85.03a1.572,1.572,0,0,0,.8,1.94l4.319,2.066a1.572,1.572,0,0,1,.891,1.332l.262,4.783a1.571,1.571,0,0,0,1.484,1.484l4.783.262a1.571,1.571,0,0,1,1.332.891l2.066,4.319a1.572,1.572,0,0,0,1.94.8l4.516-1.591a1.572,1.572,0,0,1,1.571.312l3.569,3.2a1.572,1.572,0,0,0,2.1,0l3.569-3.2a1.572,1.572,0,0,1,1.571-.312l4.516,1.591a1.572,1.572,0,0,0,1.94-.8l2.066-4.319a1.573,1.573,0,0,1,1.332-.891l4.783-.262a1.572,1.572,0,0,0,1.484-1.484l.262-4.783a1.572,1.572,0,0,1,.891-1.332l4.319-2.066a1.572,1.572,0,0,0,.8-1.94L57.9,80.514a1.572,1.572,0,0,1,.312-1.571Z" transform="translate(0 -32.605)" fill="#fff"/>
    </g>
    <g id="quality" transform="translate(54.553 462.863)">
      <g id="Group_14428" data-name="Group 14428" transform="translate(5.085 -0.863)">
        <g id="Group_14426" data-name="Group 14426" transform="translate(-5.446 51.552)">
          <path id="Path_87772" data-name="Path 87772" d="M48.127,413.8l1.588.337-1.837.521a2.734,2.734,0,0,0-1.923,2.04l-.341,1.54-.341-1.54a2.734,2.734,0,0,0-1.923-2.04l-1.837-.521L43.1,413.8a2.734,2.734,0,0,0,2.09-2.027l.423-1.737.423,1.737a2.732,2.732,0,0,0,2.089,2.027Z" transform="translate(-41.514 -410.041)" fill="#353b4b"/>
        </g>
        <g id="Group_14427" data-name="Group 14427" transform="matrix(0.966, 0.259, -0.259, 0.966, 47.632, 0)">
          <path id="Path_87773" data-name="Path 87773" d="M5.956,3.39l1.431.3-1.654.469A2.463,2.463,0,0,0,4,6L3.693,7.387,3.387,6A2.463,2.463,0,0,0,1.654,4.163L0,3.693l1.431-.3A2.463,2.463,0,0,0,3.313,1.564L3.694,0l.381,1.564A2.461,2.461,0,0,0,5.956,3.39Z" fill="#353b4b"/>
        </g>
      </g>
    </g>
    <g id="offer_2268558-2" data-name="offer_2268558" transform="translate(59.181 457.376)">
      <g transform="matrix(1, 0, 0, 1, -9, 3.6)" filter="url(#Path_87803)">
        <path id="Path_87803-3" data-name="Path 87803" d="M49.909,69.39a1.278,1.278,0,0,0,0-1.7l-2.6-2.9a1.277,1.277,0,0,1-.253-1.277l1.293-3.67a1.278,1.278,0,0,0-.654-1.577l-3.51-1.679a1.278,1.278,0,0,1-.724-1.083l-.213-3.888a1.277,1.277,0,0,0-1.206-1.206l-3.888-.213a1.277,1.277,0,0,1-1.083-.724L35.4,45.958a1.277,1.277,0,0,0-1.577-.654L30.147,46.6a1.277,1.277,0,0,1-1.277-.253l-2.9-2.6a1.278,1.278,0,0,0-1.7,0l-2.9,2.6a1.278,1.278,0,0,1-1.277.253L16.418,45.3a1.277,1.277,0,0,0-1.577.654l-1.679,3.51a1.279,1.279,0,0,1-1.083.724l-3.888.213a1.277,1.277,0,0,0-1.206,1.206L6.773,55.5a1.278,1.278,0,0,1-.724,1.083L2.538,58.26a1.277,1.277,0,0,0-.654,1.577l1.293,3.67a1.278,1.278,0,0,1-.253,1.277l-2.6,2.9a1.278,1.278,0,0,0,0,1.7l2.6,2.9a1.277,1.277,0,0,1,.253,1.277l-1.293,3.67a1.278,1.278,0,0,0,.654,1.577l3.51,1.679a1.277,1.277,0,0,1,.724,1.083l.213,3.888a1.277,1.277,0,0,0,1.206,1.206l3.888.213a1.277,1.277,0,0,1,1.083.724l1.679,3.51a1.277,1.277,0,0,0,1.577.654l3.67-1.293a1.278,1.278,0,0,1,1.277.253l2.9,2.6a1.277,1.277,0,0,0,1.7,0l2.9-2.6a1.278,1.278,0,0,1,1.277-.253l3.67,1.293a1.277,1.277,0,0,0,1.577-.654l1.679-3.51a1.279,1.279,0,0,1,1.083-.724l3.887-.213a1.277,1.277,0,0,0,1.206-1.206l.213-3.888a1.277,1.277,0,0,1,.724-1.083l3.51-1.679a1.277,1.277,0,0,0,.654-1.577l-1.293-3.67a1.278,1.278,0,0,1,.253-1.277Z" transform="translate(9 -37.01)" fill="url(#linear-gradient)"/>
      </g>
      <g transform="matrix(1, 0, 0, 1, -9, 3.6)" filter="url(#Path_87807)">
        <path id="Path_87807-2" data-name="Path 87807" d="M8.191,67.889a1.277,1.277,0,0,0-1.206,1.206l-.213,3.888a1.277,1.277,0,0,1-.724,1.083l-3.51,1.679a1.277,1.277,0,0,0-.654,1.577l1.293,3.67a1.278,1.278,0,0,1-.253,1.277l-2.6,2.9a1.278,1.278,0,0,0,0,1.7l2.6,2.9a1.277,1.277,0,0,1,.253,1.277l-1.293,3.67A1.278,1.278,0,0,0,2.538,96.3l3.51,1.679a1.277,1.277,0,0,1,.724,1.083l.213,3.888a1.277,1.277,0,0,0,1.206,1.206l3.888.213a1.277,1.277,0,0,1,1.083.724l1.679,3.51a1.277,1.277,0,0,0,1.577.654l3.67-1.293a1.278,1.278,0,0,1,1.277.253l2.9,2.6a1.277,1.277,0,0,0,1.7,0l2.9-2.6a1.278,1.278,0,0,1,1.277-.253l3.67,1.293a1.277,1.277,0,0,0,1.577-.654l1.679-3.51c-8.042-1.824-6.167-9.6-6.167-9.6s1.9-6.765-5.789-9.466S19.1,76.65,19.1,76.65c-7.429,0-7.019-8.974-7.019-8.974Z" transform="translate(9 -54.49)" fill="url(#linear-gradient)"/>
      </g>
    </g>
    <g id="_14-Gift" data-name="14-Gift" transform="translate(69.649 477)">
      <path id="Path_87808" data-name="Path 87808" d="M39.44,25h7.925V39.97A1.761,1.761,0,0,1,45.6,41.731H39V25Z" transform="translate(-21.269 -13.433)" fill="url(#linear-gradient-3)"/>
      <path id="Path_87809" data-name="Path 87809" d="M14.366,25V41.731h-6.6A1.761,1.761,0,0,1,6,39.97V25Z" transform="translate(-2.799 -13.433)" fill="url(#linear-gradient-3)"/>
      <path id="Path_87810" data-name="Path 87810" d="M11.127,13v5.284H1.881A.883.883,0,0,1,1,17.4V13.881A.883.883,0,0,1,1.881,13Z" transform="translate(0 -6.716)" fill="url(#linear-gradient-3)"/>
      <path id="Path_87811" data-name="Path 87811" d="M50.127,13.881V17.4a.883.883,0,0,1-.881.881H40V13h9.246A.883.883,0,0,1,50.127,13.881Z" transform="translate(-21.829 -6.716)" fill="url(#linear-gradient-3)"/>
      <path id="Path_87812" data-name="Path 87812" d="M31.4,9.881v.881H27V9.881A.883.883,0,0,1,27.881,9h2.642a.883.883,0,0,1,.881.881Z" transform="translate(-14.552 -4.478)" fill="#cb3541"/>
      <path id="Path_87813" data-name="Path 87813" d="M30.522,10.44H27.881a.881.881,0,0,1-.758-.44.87.87,0,0,0-.123.44v.881h4.4V10.44A.87.87,0,0,0,31.28,10,.881.881,0,0,1,30.522,10.44Z" transform="translate(-14.552 -5.037)" fill="#a81e29"/>
      <path id="Path_87814" data-name="Path 87814" d="M16.925,8.2v.881h-6.6A1.294,1.294,0,0,1,9,7.761C9,6,11.2,6,11.2,6a8.637,8.637,0,0,1,5.724,2.2Z" transform="translate(-4.477 -2.799)" fill="#e03e3e"/>
      <path id="Path_87815" data-name="Path 87815" d="M16.2,1c3.522,0,3.522,4.4,3.522,4.4A8.637,8.637,0,0,0,14,3.2,2.156,2.156,0,0,1,16.2,1Z" transform="translate(-7.276)" fill="#cb3541"/>
      <path id="Path_87816" data-name="Path 87816" d="M37,5.4S37,1,40.522,1a2.156,2.156,0,0,1,2.2,2.2A8.637,8.637,0,0,0,37,5.4Z" transform="translate(-20.149)" fill="#cb3541"/>
      <path id="Path_87817" data-name="Path 87817" d="M37,8.2A8.637,8.637,0,0,1,42.724,6s2.2,0,2.2,1.761A1.294,1.294,0,0,1,43.6,9.082H37Z" transform="translate(-20.149 -2.799)" fill="#e03e3e"/>
      <path id="Path_87818" data-name="Path 87818" d="M25,25h6.164V41.731H25Z" transform="translate(-13.433 -13.433)" fill="#cb3541"/>
      <path id="Path_87819" data-name="Path 87819" d="M31.045,13v5.284H24V13h7.045Z" transform="translate(-12.873 -6.716)" fill="#ff6161"/>
      <path id="Path_87820" data-name="Path 87820" d="M46.285,35.968c.092.418,1.043.749,1.043,1.2s-.951.779-1.043,1.2.616,1.154.436,1.541-1.184.273-1.453.616.075,1.308-.26,1.581-1.18-.282-1.567-.088-.484,1.211-.9,1.308-.938-.775-1.374-.775-.964.872-1.374.775-.524-1.118-.907-1.308-1.233.361-1.563.088,0-1.242-.264-1.581S35.8,40.3,35.608,39.9s.533-1.11.436-1.541S35,37.615,35,37.166s.951-.779,1.043-1.2-.616-1.154-.436-1.541,1.184-.273,1.453-.616-.075-1.308.26-1.581,1.18.282,1.567.088.484-1.211.9-1.308.938.775,1.374.775.964-.872,1.374-.775.524,1.118.907,1.308,1.233-.361,1.563-.088,0,1.242.264,1.581,1.259.22,1.449.616S46.188,35.537,46.285,35.968Z" transform="translate(-19.03 -16.793)" fill="#282e3b"/>
      <path id="Path_87822" data-name="Path 87822" d="M50.321,49.642a1.321,1.321,0,1,1,1.321-1.321,1.321,1.321,0,0,1-1.321,1.321Zm0-1.761a.44.44,0,1,0,.44.44A.44.44,0,0,0,50.321,47.881Z" transform="translate(-26.866 -25.746)" fill="#fbfbfb"/>
      <path id="Path_87823" data-name="Path 87823" d="M44.321,39.642a1.321,1.321,0,1,1,1.321-1.321A1.321,1.321,0,0,1,44.321,39.642Zm0-1.761a.44.44,0,1,0,.44.44A.44.44,0,0,0,44.321,37.881Z" transform="translate(-23.508 -20.149)" fill="#fbfbfb"/>
      <path id="Path_87824" data-name="Path 87824" d="M43.44,44.284a.44.44,0,0,1-.311-.752l4.4-4.4a.44.44,0,1,1,.623.623l-4.4,4.4A.44.44,0,0,1,43.44,44.284Z" transform="translate(-23.508 -21.269)" fill="#fbfbfb"/>
      <path id="Path_87825" data-name="Path 87825" d="M47.366,26.761V25H39v1.761Z" transform="translate(-21.269 -13.433)" fill="#e68c15"/>
      <path id="Path_87826" data-name="Path 87826" d="M45.6,56.761H39v1.761h6.6a1.761,1.761,0,0,0,1.761-1.761V55A1.761,1.761,0,0,1,45.6,56.761Z" transform="translate(-21.269 -30.224)" fill="#e68c15"/>
      <path id="Path_87827" data-name="Path 87827" d="M8.2,32.045V28.522a1.761,1.761,0,0,1,1.761-1.761h4.4V25H6V39.97a1.761,1.761,0,0,0,1.761,1.761h6.6V39.767A7.925,7.925,0,0,1,8.2,32.045Z" transform="translate(-2.799 -13.433)" fill="url(#linear-gradient-7)"/>
      <path id="Path_87829" data-name="Path 87829" d="M25,25h6.164v1.761H25Z" transform="translate(-13.433 -13.433)" fill="#a81e29"/>
      <path id="Path_87830" data-name="Path 87830" d="M5.623,16.082a1.541,1.541,0,0,1,0-3.082H1.881A.883.883,0,0,0,1,13.881V17.4a.883.883,0,0,0,.881.881h9.246v-2.2Z" transform="translate(0 -6.716)" fill="#ffa930"/>
      <path id="Path_87831" data-name="Path 87831" d="M49.246,18.881H40v2.2h9.246a.883.883,0,0,0,.881-.881V18A.881.881,0,0,1,49.246,18.881Z" transform="translate(-21.829 -9.515)" fill="#ffa930"/>
      <path id="Path_87832" data-name="Path 87832" d="M24,20v2.2h7.045V20Z" transform="translate(-12.873 -10.634)" fill="#cb3541"/>
      <path id="Path_87833" data-name="Path 87833" d="M17.963,4.67s-2.569-.855-3.906-.455A2.617,2.617,0,0,0,14,4.67a8.637,8.637,0,0,1,5.724,2.2,5.934,5.934,0,0,0-1.035-3.248C18.69,4.181,18.531,4.67,17.963,4.67Z" transform="translate(-7.276 -1.469)" fill="#a81e29"/>
      <path id="Path_87834" data-name="Path 87834" d="M39.2,4.053c-.731,0-.787-.809-.692-1.532C37,3.753,37,6.254,37,6.254a8.637,8.637,0,0,1,5.724-2.2,2.658,2.658,0,0,0-.076-.543A10.52,10.52,0,0,0,39.2,4.053Z" transform="translate(-20.149 -0.851)" fill="#a81e29"/>
      <path id="Path_87835" data-name="Path 87835" d="M15.109,8.043a.444.444,0,0,1-.431.537H10.32A.881.881,0,0,1,9.439,7.7V7.463A1.509,1.509,0,0,0,9,8.58,1.294,1.294,0,0,0,10.32,9.9h6.6V9.02a7.876,7.876,0,0,0-1.906-1.292,2.109,2.109,0,0,1,.091.315Z" transform="translate(-4.477 -3.617)" fill="#cb3541"/>
      <path id="Path_87836" data-name="Path 87836" d="M44.045,8.6H39.686a.444.444,0,0,1-.431-.537,2.064,2.064,0,0,1,.207-.564A8.245,8.245,0,0,0,37,9.041v.881h6.6A1.294,1.294,0,0,0,44.925,8.6a1.725,1.725,0,0,0-.079-.52A.881.881,0,0,1,44.045,8.6Z" transform="translate(-20.149 -3.638)" fill="#cb3541"/>
      <path id="Path_87837" data-name="Path 87837" d="M48.881,21.5V13H48v8.5a.565.565,0,0,0,.881,0Z" transform="translate(-26.306 -6.716)" fill="#fff"/>
      <path id="Path_87838" data-name="Path 87838" d="M33.928,44.718c-.181-.387.533-1.11.436-1.541s-1.043-.748-1.043-1.2.951-.779,1.043-1.2c.071-.318-.3-.793-.423-1.179-.366.22-1.166.167-1.334.518-.181.387.533,1.11.436,1.541S32,42.411,32,42.86s.951.779,1.043,1.2-.616,1.154-.436,1.541,1.184.273,1.449.616-.07,1.308.264,1.581c.165.136.46.066.762-.015V45.156c-.4-.134-1.01-.136-1.154-.438Z" transform="translate(-17.351 -21.606)" fill="#961b25"/>
      <g id="Group_14439" data-name="Group 14439" transform="translate(17.731 11.567)">
        <path id="Path_87839" data-name="Path 87839" d="M42.029,55.193c-.418-.1-.524-1.118-.907-1.308s-1.233.361-1.563.088,0-1.242-.264-1.581A.608.608,0,0,0,39,52.214v2.625a1.313,1.313,0,0,1,.8-.074.892.892,0,0,1,.327.436c.169.361.308.809.58.872.409.1.938-.775,1.374-.775s.964.872,1.374.775c.271-.063.411-.511.579-.872a1.642,1.642,0,0,1,.16-.28c-.268-.225-.545-.5-.793-.5C42.967,54.418,42.439,55.29,42.029,55.193Z" transform="translate(-39 -40.232)" fill="#e68c15"/>
        <path id="Path_87840" data-name="Path 87840" d="M53.859,55.933a.66.66,0,0,0-.4.022c-.247.124-.379.6-.557.944.315.085.629.167.8.025C53.916,56.756,53.865,56.321,53.859,55.933Z" transform="translate(-46.783 -42.302)" fill="#e68c15"/>
        <path id="Path_87841" data-name="Path 87841" d="M42.859,33.437a.5.5,0,0,0,.166-.158c-.206-.171-.406-.311-.584-.269-.145.033-.251.181-.346.364A1.233,1.233,0,0,0,42.859,33.437Z" transform="translate(-40.732 -29.479)" fill="#e68c15"/>
        <path id="Path_87842" data-name="Path 87842" d="M45.387,27.651a.643.643,0,0,1,.493.2V25H45v3.1C45.1,27.876,45.22,27.689,45.387,27.651Z" transform="translate(-42.358 -25)" fill="#e68c15"/>
      </g>
      <path id="Path_87843" data-name="Path 87843" d="M37.826,35.706c-.3-.08-.595-.15-.762-.014-.23.188-.144.7-.152,1.117.254-.112.632-.144.914-.238Z" transform="translate(-20.095 -19.374)" fill="#961b25"/>
    </g>
  </g>
</svg>
