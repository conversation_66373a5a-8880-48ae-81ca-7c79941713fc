<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="373" height="206" viewBox="0 0 373 206">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_8773" data-name="Rectangle 8773" width="373" height="206" transform="translate(0 28.408)"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="0.962" x2="0.975" y2="1.203" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#a5b5db"/>
      <stop offset="1" stop-color="#080b12"/>
    </linearGradient>
    <filter id="Rectangle_8695" x="15.871" y="753.03" width="346.621" height="74.762" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-color="#534b3e" flood-opacity="0.188"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="Path_79350" x="-9" y="752.609" width="391.326" height="106.079" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-2"/>
      <feFlood flood-color="#716d6d" flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-2"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <filter id="home_1_" x="34.283" y="778.059" width="45.666" height="47.823" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-3"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-3"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <clipPath id="clip-path-2">
      <path id="path1897" d="M0,29.821H27.5V0H0Z" fill="#29385b"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <path id="path81" d="M0-682.665H27.5v29.849H0Z" transform="translate(0 682.665)" fill="#29385b"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <rect id="Rectangle_8753" data-name="Rectangle 8753" width="91.798" height="76.361" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <path id="Path_87526" data-name="Path 87526" d="M50.742,600.972c5.913-2.394,12.5.868,14.7,7.286s-3.739,24.6-3.739,24.6-15.466-9.515-17.673-15.933.8-13.556,6.711-15.951Zm6.213,18.067a7.36,7.36,0,0,0,3.881-9.222,6.467,6.467,0,0,0-8.5-4.212,7.36,7.36,0,0,0-3.881,9.222,6.467,6.467,0,0,0,8.5,4.212Z" transform="translate(-43.308 -600.186)" fill="#ecaf4c"/>
    </clipPath>
    <clipPath id="clip-path-6">
      <path id="Path_87529" data-name="Path 87529" d="M155.1,361.743c5.913-2.394,12.494.867,14.7,7.283s-3.74,24.6-3.74,24.6S150.6,384.113,148.39,377.7s.8-13.559,6.711-15.954Zm6.21,18.067a7.36,7.36,0,0,0,3.881-9.222,6.468,6.468,0,0,0-8.5-4.212,7.36,7.36,0,0,0-3.881,9.222,6.467,6.467,0,0,0,8.5,4.211Z" transform="translate(-147.666 -360.957)" fill="#ecaf4c"/>
    </clipPath>
    <clipPath id="clip-path-7">
      <rect id="Rectangle_8771" data-name="Rectangle 8771" width="506.659" height="410.453" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-8">
      <path id="Path_87535" data-name="Path 87535" d="M583.306,718.149H516.667a12.88,12.88,0,0,0-7.561,2.485l-5.089,3.685a1.076,1.076,0,0,0-.4,1.09.988.988,0,0,0,.8.795l62.349,9.639a5.8,5.8,0,0,0,4.765-1.449l13.242-11.813a2.638,2.638,0,0,0,.707-2.775,2.323,2.323,0,0,0-2.169-1.658Z" transform="translate(-503.593 -718.147)" fill="#093f6b"/>
    </clipPath>
    <clipPath id="clip-path-9">
      <path id="Path_87540" data-name="Path 87540" d="M293.931,564.287l-155.4,26.143a3.368,3.368,0,0,0-2.386,1.994h0c-1.438,3.027.325,7.089,3.086,7.118l132.859,1.5a14.538,14.538,0,0,0,11.745-5.922l5.36-6.715a13.356,13.356,0,0,0,2.494-9.616l-.17-1.616a4.042,4.042,0,0,1,1.427-3.735l2.9-2.015a4.042,4.042,0,0,0,1.427-3.735h0c-.224-2.137-1.74-3.677-3.345-3.4Z" transform="translate(-135.634 -564.254)" fill="#d4ebff"/>
    </clipPath>
    <clipPath id="clip-path-10">
      <path id="Path_87563" data-name="Path 87563" d="M420.548,531.681c-17.844-11.857-69.172-47.692-83.349-56.624s-42.1-13.217-49.181-.709c0,0-11.053,2.029-5.847,17.477s60.442,55.969,98.924,77.821c19.314,10.967,37.819,21.346,54.681,24.729,30.567,6.136,53.764,1.473,57.519-.915,5.82-3.706-54.9-49.925-72.745-61.778Z" transform="translate(-280.777 -466.556)" fill="#fff"/>
    </clipPath>
    <clipPath id="clip-path-11">
      <path id="Path_87569" data-name="Path 87569" d="M540.544,577.467l207.62,14.912a4.627,4.627,0,0,1,3.353,1.821h0a4.564,4.564,0,0,1-3.334,7.329l-175.037,11.06a22.8,22.8,0,0,1-16.1-5.066l-7.762-6.321A11.628,11.628,0,0,1,545,591.78l.058-1.628a3.826,3.826,0,0,0-2.266-3.624l-4.033-1.8a3.824,3.824,0,0,1-2.266-3.624h0a4.1,4.1,0,0,1,1.324-2.747,3.477,3.477,0,0,1,2.737-.887Z" transform="translate(-536.486 -577.445)" fill="#d4ebff"/>
    </clipPath>
    <clipPath id="clip-path-12">
      <path id="Path_87574" data-name="Path 87574" d="M762.533,718.149h66.639a12.88,12.88,0,0,1,7.561,2.485l5.089,3.685a1.077,1.077,0,0,1,.4,1.09.988.988,0,0,1-.8.795l-62.35,9.639a5.8,5.8,0,0,1-4.765-1.449l-13.242-11.813a2.637,2.637,0,0,1-.707-2.775,2.323,2.323,0,0,1,2.169-1.658Z" transform="translate(-760.229 -718.147)" fill="#d4ebff"/>
    </clipPath>
    <clipPath id="clip-path-13">
      <path id="Path_87513" data-name="Path 87513" d="M888.678,392.3c-.55-2.18.682-4.542,2.89-5.958a2.237,2.237,0,0,0,.785-2.826c-3.366-6.99-12.707-10.309-22.243-7.476a24.868,24.868,0,0,0-3.816,1.484,2.489,2.489,0,0,1-2.917-.527,3.673,3.673,0,0,0-.916-.7c-1.557-.826-4.24,1.416-5.845,3.028a3.389,3.389,0,0,1-3.091.957,11.414,11.414,0,0,0-5.49.3c-5.083,1.51,4.271,6.949-2.721,9.132a3.223,3.223,0,0,0-2.043,1.974c-1.585,4.3,6.469,5.555,9.451,4.329a1.611,1.611,0,0,1,1.377.068,1.848,1.848,0,0,1,.9,1.136l.021.067a3.393,3.393,0,0,0,1.6,2.024,2.96,2.96,0,0,0,2.452.186c.131-.045.262-.089.4-.128,3.434-1.02,6.786.411,7.492,3.2a4.588,4.588,0,0,1-.179,2.725,2.049,2.049,0,0,0,.162,1.778,1.729,1.729,0,0,0,1.446.853,9.916,9.916,0,0,0,9.007-4.863,6.68,6.68,0,0,1,9.458-2.516c2.115,1.312,4.009,2.1,4.791,1.249a8.376,8.376,0,0,0,1.929-3.51,2.333,2.333,0,0,0-.186-1.676,2.027,2.027,0,0,0-1.236-1.024,4.683,4.683,0,0,1-3.472-3.29Z" transform="translate(-843.064 -375.025)" fill="#addc72"/>
    </clipPath>
    <clipPath id="clip-path-14">
      <path id="Path_87523" data-name="Path 87523" d="M911.038,316.76c-3.87.125-6.914,3.631-6.8,7.832s7.367,13.045,7.367,13.045,6.765-9.285,6.651-13.487-3.346-7.5-7.216-7.38Zm.32,11.827a4.212,4.212,0,0,1-4.168-4.268,4.261,4.261,0,0,1,3.932-4.524,4.211,4.211,0,0,1,4.17,4.265,4.26,4.26,0,0,1-3.933,4.527Z" transform="translate(-904.234 -316.76)" fill="#ecaf4c"/>
    </clipPath>
    <filter id="_28Nov_2024" x="244.609" y="683.183" width="75" height="29" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur-4"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur-4"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="fghjg" transform="translate(0 -28.408)" clip-path="url(#clip-path)">
    <path id="Path_61225" data-name="Path 61225" d="M0,0H373.326V302.4H0Z" transform="translate(0 -1.775)" fill="#f1f3f8"/>
    <g id="OS_HomeIndicator_OnLgiht" data-name="OS/HomeIndicator/OnLgiht" transform="translate(373.326 300.628) rotate(180)">
      <path id="_-boundary" data-name="-boundary" d="M0,0H373.326V220.162H0Z" transform="translate(0 51.22)" fill="url(#linear-gradient)"/>
    </g>
    <text id="New_user_Signup" data-name="New user? Signup" transform="translate(93.331 863.923)" fill="#29385b" font-size="7" font-family="Montserrat-Light, Montserrat" font-weight="300"><tspan x="0" y="0">Offer valid for the first </tspan><tspan y="0" font-family="Montserrat-SemiBold, Montserrat" font-weight="600">100</tspan><tspan y="0" xml:space="preserve"> customers only.</tspan></text>
    <g transform="matrix(1, 0, 0, 1, 0, 28.41)" filter="url(#Rectangle_8695)">
      <rect id="Rectangle_8695-2" data-name="Rectangle 8695" width="328.621" height="56.762" rx="12" transform="translate(24.87 759.03)" fill="#29385b"/>
    </g>
    <g id="Group_14073" data-name="Group 14073" transform="translate(161.381 808.969)">
      <text id="Grab_the_deal" data-name="Grab the deal" transform="translate(0 9)" fill="#fff" font-size="9" font-family="Montserrat-Medium, Montserrat" font-weight="500" letter-spacing="0.01em"><tspan x="0" y="0">Grab the deal</tspan></text>
    </g>
    <g id="discount" transform="translate(134.172 805.056)">
      <path id="Path_80829" data-name="Path 80829" d="M19.152,9.862a3.608,3.608,0,0,1-.342.94L12.555,22.56l.386.725A2.217,2.217,0,0,0,16.1,24.2l6.008-3.765a2.63,2.63,0,0,0,.847-3.425Z" transform="translate(-0.962 -0.874)" fill="#fff" fill-rule="evenodd" opacity="0.56"/>
      <path id="Path_80830" data-name="Path 80830" d="M19.216,9.808a2.7,2.7,0,0,1-.246,1.839L12.658,23.52a2.217,2.217,0,0,1-3.156.919L3.486,20.674a2.63,2.63,0,0,1-.847-3.425L8.952,5.376A2.355,2.355,0,0,1,10.3,4.225l4.839-1.563a2.154,2.154,0,0,1,1.815.232,2.5,2.5,0,0,1,1.093,1.59ZM16.164,6.882a1.108,1.108,0,0,1-1.579.461,1.314,1.314,0,0,1-.425-1.713,1.108,1.108,0,0,1,1.579-.461A1.314,1.314,0,0,1,16.164,6.882Zm-9,7.768a1.537,1.537,0,0,0,.71.946,1.34,1.34,0,0,0,1.11.2,1.512,1.512,0,0,0,.9-.815,1.9,1.9,0,0,0,.242-.863,1.459,1.459,0,0,0-.2-.77,1.606,1.606,0,0,0-.556-.571,1.5,1.5,0,0,0-.74-.242,1.2,1.2,0,0,0-.706.2,1.736,1.736,0,0,0-.567.659,1.716,1.716,0,0,0-.2,1.258Zm5.21.1a.5.5,0,0,0-.5-.023L7.123,17.149a.613.613,0,0,0-.261.786.527.527,0,0,0,.724.283l4.745-2.425a.59.59,0,0,0,.316-.51.6.6,0,0,0-.276-.536Zm-3.8.055a.26.26,0,0,1-.288.013.33.33,0,0,1-.14-.281A1.226,1.226,0,0,1,8.327,14a1.091,1.091,0,0,1,.335-.427.275.275,0,0,1,.3-.016.31.31,0,0,1,.13.284,1.1,1.1,0,0,1-.166.533,1.064,1.064,0,0,1-.351.429Zm.8,4.422a1.566,1.566,0,0,0,1.82,1.14,1.512,1.512,0,0,0,.9-.815,1.9,1.9,0,0,0,.242-.863,1.459,1.459,0,0,0-.2-.77,1.608,1.608,0,0,0-.556-.571,1.5,1.5,0,0,0-.74-.242,1.2,1.2,0,0,0-.706.2,1.735,1.735,0,0,0-.567.659,1.716,1.716,0,0,0-.2,1.258Zm1.413.152a.26.26,0,0,1-.288.013.33.33,0,0,1-.14-.281,1.227,1.227,0,0,1,.18-.536,1.093,1.093,0,0,1,.335-.427.275.275,0,0,1,.3-.016.31.31,0,0,1,.13.284,1.1,1.1,0,0,1-.166.533,1.064,1.064,0,0,1-.351.429Z" transform="translate(-2.331 -2.558)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_14077" data-name="Group 14077" transform="translate(0 787.017)">
      <g id="OS_HomeIndicator_OnLgiht-2" data-name="OS/HomeIndicator/OnLgiht" transform="translate(0 51.006)">
        <rect id="_-boundary-2" data-name="-boundary" width="373" height="37" transform="translate(0 0.385)" fill="#fff" opacity="0"/>
        <rect id="indicator" width="133" height="5" rx="2.5" transform="translate(120 21.385)"/>
      </g>
      <g id="Rectangle_5542" data-name="Rectangle 5542" transform="translate(40.579 15.184)" fill="#fff" stroke="#fff" stroke-width="1" opacity="0.1">
        <rect width="77.553" height="29.766" rx="8" stroke="none"/>
        <rect x="0.5" y="0.5" width="76.553" height="28.766" rx="7.5" fill="none"/>
      </g>
      <path id="Home_Indicator" data-name="Home Indicator" d="M239.581,21H123.254a2.455,2.455,0,0,0,0,4.893H239.581a2.455,2.455,0,0,0,0-4.893Z" transform="translate(5.246 50.441)"/>
      <g id="OS_HomeIndicator_OnLgiht-3" data-name="OS/HomeIndicator/OnLgiht" transform="translate(0 51.006)">
        <rect id="_-boundary-3" data-name="-boundary" width="373" height="37" transform="translate(0 0.385)" fill="#fff" opacity="0"/>
        <rect id="indicator-2" data-name="indicator" width="133" height="5" rx="2.5" transform="translate(120 21.385)"/>
      </g>
      <path id="Home_Indicator-2" data-name="Home Indicator" d="M239.581,21H123.254a2.455,2.455,0,0,0,0,4.893H239.581a2.455,2.455,0,0,0,0-4.893Z" transform="translate(5.246 50.441)"/>
      <g id="OS_HomeIndicator_OnLgiht-4" data-name="OS/HomeIndicator/OnLgiht" transform="translate(0 51.006)">
        <rect id="_-boundary-4" data-name="-boundary" width="373" height="37" transform="translate(0 0.385)" fill="#fff" opacity="0"/>
        <rect id="indicator-3" data-name="indicator" width="133" height="5" rx="2.5" transform="translate(120 21.385)"/>
      </g>
      <g id="Rectangle_5542-2" data-name="Rectangle 5542" transform="translate(40.579 15.184)" fill="#fff" stroke="#fff" stroke-width="1" opacity="0.1">
        <rect width="77.553" height="29.766" rx="8" stroke="none"/>
        <rect x="0.5" y="0.5" width="76.553" height="28.766" rx="7.5" fill="none"/>
      </g>
      <g id="Group_11019" data-name="Group 11019">
        <g id="Group_4881" data-name="Group 4881">
          <g id="Group_4878" data-name="Group 4878">
            <g transform="matrix(1, 0, 0, 1, 0, -758.61)" filter="url(#Path_79350)">
              <path id="Path_79350-2" data-name="Path 79350" d="M0,0H373.326V88.079H0Z" transform="translate(0 758.61)" fill="#fff"/>
            </g>
            <path id="home" d="M16.98,3.373a6.115,6.115,0,0,1,7.38,0L36.5,12.384a7.279,7.279,0,0,1,2.845,5.853V35.295a6.831,6.831,0,0,1-6.535,7.092H8.535A6.831,6.831,0,0,1,2,35.295V18.239a7.28,7.28,0,0,1,2.845-5.853ZM16,33.268a1.016,1.016,0,0,0,0,2.026h9.335a1.016,1.016,0,0,0,0-2.026Z" transform="translate(166.684 20.284)" fill="#fff" fill-rule="evenodd"/>
          </g>
        </g>
      </g>
      <g id="Group_11020" data-name="Group 11020" transform="translate(30 13.819)">
        <rect id="Rectangle_5415" data-name="Rectangle 5415" width="54" height="57" rx="20" transform="translate(0 -0.428)" fill="#f4f4f7"/>
        <g id="Group_10820" data-name="Group 10820" transform="translate(13.285 11.631)">
          <g transform="matrix(1, 0, 0, 1, -43.28, -784.06)" filter="url(#home_1_)">
            <path id="home_1_2" data-name="home (1)" d="M13.1,3.053a4.53,4.53,0,0,1,5.468,0l8.991,6.681a5.394,5.394,0,0,1,2.107,4.336V26.7a5.061,5.061,0,0,1-4.842,5.254H6.842A5.061,5.061,0,0,1,2,26.7V14.066A5.393,5.393,0,0,1,4.107,9.734ZM12.377,25.2a.753.753,0,0,0,0,1.5h6.916a.753.753,0,0,0,0-1.5Z" transform="translate(41.28 781.92)" fill="#29385b" fill-rule="evenodd"/>
          </g>
        </g>
        <g id="Layer_10" data-name="Layer 10" transform="translate(85.448 11.136)">
          <path id="Path_79353" data-name="Path 79353" d="M15.787,20.264H14.8V18.132a2.228,2.228,0,0,0-.576-1.509A1.89,1.89,0,0,0,12.837,16H8.911a2.054,2.054,0,0,0-1.965,2.132v2.132H5.965A2.054,2.054,0,0,0,4,22.4v6.4a2.054,2.054,0,0,0,1.965,2.132h9.822a2.054,2.054,0,0,0,1.965-2.132V22.4a2.054,2.054,0,0,0-1.965-2.132ZM8.911,18.132h3.926v2.132H8.911ZM5.965,28.792V22.4h9.822v6.4Z" transform="translate(-4 -1.076)"/>
          <path id="Path_79354" data-name="Path 79354" d="M23.787,8.4V4.132a1.027,1.027,0,0,0,.982-1.066A1.027,1.027,0,0,0,23.787,2H15.929a1.027,1.027,0,0,0-.982,1.066,1.027,1.027,0,0,0,.982,1.066V8.4A4.107,4.107,0,0,0,12,12.66v1.066a.986.986,0,1,0,1.965,0V12.66a2.054,2.054,0,0,1,1.965-2.132h7.858a2.054,2.054,0,0,1,1.965,2.132V25.452a2.054,2.054,0,0,1-1.965,2.132H20.837a1.07,1.07,0,0,0,0,2.132H22.8v1.066a.986.986,0,1,0,1.965,0V29.568a4.216,4.216,0,0,0,2.947-4.11V12.66A4.107,4.107,0,0,0,23.787,8.4Zm-5.894,0V4.132h3.929V8.4Z" transform="translate(-4.142 -2)"/>
          <path id="Path_79355" data-name="Path 79355" d="M17.982,12A1.026,1.026,0,0,0,17,13.066V17.33a.986.986,0,1,0,1.965,0V13.066A1.026,1.026,0,0,0,17.982,12Z" transform="translate(-4.23 -1.34)"/>
          <path id="Path_79356" data-name="Path 79356" d="M22.965,23.726V13.066a.986.986,0,1,0-1.965,0v10.66a.986.986,0,1,0,1.965,0Z" transform="translate(-4.301 -1.34)"/>
        </g>
        <g id="g1857" transform="translate(245.378 40.959) rotate(180)">
          <path id="path1859" d="M0,4.717H12.477V0H0Z" transform="translate(7.513 19.093)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
          <g id="g1861" transform="translate(7.617 14.774)">
            <path id="path1863" d="M0,0H.858" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
          </g>
          <g id="g1865" transform="translate(7.617 10.38)">
            <path id="path1867" d="M0,0H.858" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
          </g>
          <g id="g1869" transform="translate(7.617 5.985)">
            <path id="path1871" d="M0,0H.858" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
          </g>
          <g id="g1873" transform="translate(13.323 14.774)">
            <path id="path1875" d="M0,0H.858" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
          </g>
          <g id="g1877" transform="translate(13.323 10.38)">
            <path id="path1879" d="M0,0H.858" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
          </g>
          <g id="g1881" transform="translate(13.323 5.985)">
            <path id="path1883" d="M0,0H.858" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
          </g>
          <g id="g1885" transform="translate(19.029 14.774)">
            <path id="path1887" d="M0,0H.858" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
          </g>
          <g id="g1889" transform="translate(19.458 5.985)">
            <path id="path1891" d="M0,4.222V0" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
          </g>
          <g id="g1893">
            <g id="g1895" clip-path="url(#clip-path-2)">
              <g id="g1901" transform="translate(3.001 1.165)">
                <path id="path1903" d="M21.5,9.262V24a3.367,3.367,0,0,1-3.223,3.495H3.223A3.367,3.367,0,0,1,0,24V3.495A3.368,3.368,0,0,1,3.223,0H18.277A3.368,3.368,0,0,1,21.5,3.495h0" transform="translate(0 0)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
              </g>
            </g>
          </g>
        </g>
        <path id="wallet-arrow_1_" data-name="wallet-arrow (1)" d="M22.921,19.9A1.725,1.725,0,1,1,21.2,18.035,1.8,1.8,0,0,1,22.921,19.9Zm3.438-9.95a1.2,1.2,0,0,0-1.146,1.244V26.119a1.2,1.2,0,0,1-1.146,1.244H5.731a3.6,3.6,0,0,1-3.438-3.732V11.187a5.447,5.447,0,0,0,3.437,1.25h11.46a1.248,1.248,0,0,0,0-2.488H5.731A3.326,3.326,0,0,1,3.19,8.7,3.306,3.306,0,0,1,5.731,7.462h8.026A1.2,1.2,0,0,0,14.9,6.218a1.2,1.2,0,0,0-1.146-1.244H5.731A6,6,0,0,0,0,11.194V23.631a6,6,0,0,0,5.731,6.217H24.067a3.6,3.6,0,0,0,3.437-3.73V11.194A1.2,1.2,0,0,0,26.359,9.95ZM19.145,5.237l1.486-1.593v7.549a1.15,1.15,0,1,0,2.292,0V3.666l1.493,1.578a1.083,1.083,0,0,0,1.62-.023,1.319,1.319,0,0,0-.022-1.758L23.46.765a2.282,2.282,0,0,0-3.4,0L17.531,3.468a1.32,1.32,0,0,0-.01,1.76,1.084,1.084,0,0,0,1.621.011Z" transform="translate(153.52 11.135)"/>
      </g>
      <path id="Home_Indicator-3" data-name="Home Indicator" d="M239.581,21H123.254a2.455,2.455,0,0,0,0,4.893H239.581a2.455,2.455,0,0,0,0-4.893Z" transform="translate(5.246 50.441)"/>
      <g id="g75" transform="translate(316.122 24.956)">
        <g id="g77">
          <g id="g79" clip-path="url(#clip-path-3)">
            <g id="g85" transform="translate(1.5 1.166)">
              <path id="path87" d="M-291.869-200.6a4.417,4.417,0,0,0-4.226,4.586,4.417,4.417,0,0,0,4.226,4.586,4.417,4.417,0,0,0,4.226-4.586,4.417,4.417,0,0,0-4.226-4.586Zm11.861,1.313-1.144,1.023a3.018,3.018,0,0,0-.98,2.251,3.018,3.018,0,0,0,.98,2.251l1.144,1.023a1.235,1.235,0,0,1,.248,1.484l-2.259,4.246a1.035,1.035,0,0,1-1.308.509l-1.388-.565a2.5,2.5,0,0,0-2.285.206,2.925,2.925,0,0,0-1.306,2.045l-.243,1.586a1.1,1.1,0,0,1-1.06.979h-4.519a1.1,1.1,0,0,1-1.06-.979l-.243-1.586a2.924,2.924,0,0,0-1.306-2.046,2.5,2.5,0,0,0-2.286-.205l-1.388.565a1.036,1.036,0,0,1-1.308-.509l-2.259-4.246a1.234,1.234,0,0,1,.248-1.484l1.144-1.023a3.019,3.019,0,0,0,.979-2.251,3.019,3.019,0,0,0-.979-2.251l-1.144-1.023a1.234,1.234,0,0,1-.248-1.484l2.259-4.246a1.036,1.036,0,0,1,1.308-.509l1.388.565a2.5,2.5,0,0,0,2.286-.205,2.924,2.924,0,0,0,1.306-2.046l.243-1.586a1.1,1.1,0,0,1,1.06-.979h4.519a1.1,1.1,0,0,1,1.06.979l.243,1.586A2.925,2.925,0,0,0-287-205.167a2.5,2.5,0,0,0,2.285.206l1.388-.565a1.036,1.036,0,0,1,1.308.509l2.259,4.246a1.235,1.235,0,0,1-.247,1.48Z" transform="translate(304.121 209.777)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"/>
            </g>
          </g>
        </g>
      </g>
    </g>
    <g id="POST_1" data-name="POST 1" transform="translate(-99.017 -24.838)">
      <g id="Group_14300" data-name="Group 14300" transform="translate(0)">
        <g id="Group_14253" data-name="Group 14253" transform="translate(0 225.792)">
          <g id="Group_14237" data-name="Group 14237" transform="translate(0 0)">
            <path id="Path_87474" data-name="Path 87474" d="M208.8,828.131c46.025,78.407,138.946,19.321,175.5,72.47-29.678-69.4-123.367.6-168.55-82-40.624-74.263-133-30.8-168.159-10.559C92.868,787.43,171.338,764.311,208.8,828.129Z" transform="translate(-47.59 -776.889)" fill="#fff" opacity="0.5"/>
          </g>
        </g>
        <g id="Group_14297" data-name="Group 14297" transform="translate(36.106)">
          <g id="Group_14266" data-name="Group 14266" transform="translate(224.672 84.058)" clip-path="url(#clip-path-4)">
            <g id="Group_14265" data-name="Group 14265" transform="translate(4.067 6.313)">
              <g id="Group_14262" data-name="Group 14262" transform="translate(0 83.406)">
                <path id="Path_87524" data-name="Path 87524" d="M50.742,600.972c5.913-2.394,12.5.868,14.7,7.286s-3.739,24.6-3.739,24.6-15.466-9.515-17.673-15.933.8-13.556,6.711-15.951Zm6.213,18.067a7.36,7.36,0,0,0,3.881-9.222,6.467,6.467,0,0,0-8.5-4.212,7.36,7.36,0,0,0-3.881,9.222,6.467,6.467,0,0,0,8.5,4.212Z" transform="translate(-43.308 -600.186)" fill="#ecaf4c"/>
                <g id="Group_14261" data-name="Group 14261" transform="translate(0 0)" clip-path="url(#clip-path-5)">
                  <path id="Path_87525" data-name="Path 87525" d="M49.113,590.387s-5.076-7.459-13.339-4.114-7.665,13.185-6.994,15.138h0l-5.593,1.147-2.349-20.267,29.25-5.927-.977,14.029" transform="translate(-28.058 -584.668)" fill="#fff"/>
                  <path id="Ellipse_162" data-name="Ellipse 162" d="M7.04-.151a6.736,6.736,0,0,1,6.785,7.209,7.725,7.725,0,0,1-7.194,7.618A6.736,6.736,0,0,1-.153,7.467,7.725,7.725,0,0,1,7.04-.151Z" transform="translate(1.911 8.729) rotate(-21.97)" fill="#e8d556"/>
                </g>
              </g>
              <g id="Group_14264" data-name="Group 14264" transform="translate(33.525 0)">
                <path id="Path_87527" data-name="Path 87527" d="M155.1,361.743c5.913-2.394,12.494.867,14.7,7.283s-3.74,24.6-3.74,24.6S150.6,384.113,148.39,377.7s.8-13.559,6.711-15.954Zm6.21,18.067a7.36,7.36,0,0,0,3.881-9.222,6.468,6.468,0,0,0-8.5-4.212,7.36,7.36,0,0,0-3.881,9.222,6.467,6.467,0,0,0,8.5,4.211Z" transform="translate(-147.666 -360.957)" fill="#fff"/>
                <g id="Group_14263" data-name="Group 14263" transform="translate(0)" clip-path="url(#clip-path-6)">
                  <path id="Path_87528" data-name="Path 87528" d="M153.463,351.157s-5.076-7.459-13.339-4.114-7.665,13.185-6.994,15.138h0l-5.593,1.147L125.19,343.06l29.25-5.927-.977,14.029" transform="translate(-132.411 -345.438)" fill="#fff"/>
                  <path id="Ellipse_163" data-name="Ellipse 163" d="M7.47-.15a6.747,6.747,0,0,1,7.218,6.785A7.713,7.713,0,0,1,7.07,13.82,6.747,6.747,0,0,1-.148,7.035,7.713,7.713,0,0,1,7.47-.15Z" transform="translate(1.939 17.194) rotate(-68.646)" fill="#def0ff"/>
                </g>
              </g>
              <path id="Path_87530" data-name="Path 87530" d="M99.575,707.05A2.7,2.7,0,0,1,101,703.669a2.371,2.371,0,0,1,3.116,1.544,2.7,2.7,0,0,1-1.423,3.382,2.371,2.371,0,0,1-3.116-1.544Z" transform="translate(-81.395 -584.074)" fill="#fff"/>
              <path id="Path_87531" data-name="Path 87531" d="M205.245,471.32a2.7,2.7,0,0,1,1.423-3.381,2.372,2.372,0,0,1,3.116,1.544,2.7,2.7,0,0,1-1.423,3.382,2.371,2.371,0,0,1-3.116-1.544Z" transform="translate(-153.119 -430.531)" fill="#fff"/>
              <path id="Path_87532" data-name="Path 87532" d="M130.489,559.063a.865.865,0,0,0-.422-.5.759.759,0,0,0-.626-.031h0a.914.914,0,0,0-.492,1.14h0A.808.808,0,0,0,130,560.2H130a.914.914,0,0,0,.482-1.137Zm3.354-.452a.849.849,0,0,0,.448-.472.953.953,0,0,0,.008-.678h0a.853.853,0,0,0-.437-.487.754.754,0,0,0-.626,0h0a.915.915,0,0,0-.456,1.15h0a.8.8,0,0,0,1.041.506h0a.048.048,0,0,0,.019-.011Zm-7.2,1.924a.808.808,0,0,0-1.031-.565h0a.907.907,0,0,0-.521,1.119h0a.868.868,0,0,0,.407.515.758.758,0,0,0,.623.05h0l.032-.014h0a.914.914,0,0,0,.492-1.109Zm10.994-3.665a.862.862,0,0,0,.43-.5.957.957,0,0,0-.019-.68h0a.843.843,0,0,0-.453-.465.752.752,0,0,0-.623.022h0a.857.857,0,0,0-.433.492.954.954,0,0,0,.017.68h0a.786.786,0,0,0,1.028.467h0a.452.452,0,0,0,.048-.021Zm3.72-1.917a.865.865,0,0,0,.41-.512.951.951,0,0,0-.047-.677h0a.838.838,0,0,0-.472-.444.756.756,0,0,0-.623.05h0a.864.864,0,0,0-.41.512.951.951,0,0,0,.047.677h0a.787.787,0,0,0,1.012.436h0c.026-.01.055-.028.081-.041Zm3.636-2.116h0a.879.879,0,0,0,.382-.537.952.952,0,0,0-.08-.673h0a.829.829,0,0,0-.495-.415.76.76,0,0,0-.62.087h0a.877.877,0,0,0-.381.538.95.95,0,0,0,.082.672h0a.791.791,0,0,0,.993.394h0a.768.768,0,0,0,.119-.062Zm3.521-2.357a.887.887,0,0,0,.35-.562.942.942,0,0,0-.122-.665h0a.81.81,0,0,0-.518-.381.758.758,0,0,0-.613.133h0a.891.891,0,0,0-.349.562.947.947,0,0,0,.12.665h0a.786.786,0,0,0,.964.341h0a.686.686,0,0,0,.167-.1Zm3.36-2.646a.9.9,0,0,0,.3-.593.936.936,0,0,0-.174-.651h0a.8.8,0,0,0-.547-.329.769.769,0,0,0-.6.189h0a.944.944,0,0,0-.135,1.248h0a.777.777,0,0,0,.928.276h0a.832.832,0,0,0,.221-.136Zm3.124-3a.945.945,0,0,0,0-1.255h0a.775.775,0,0,0-1.157,0h0a.949.949,0,0,0,0,1.259h0a.772.772,0,0,0,.864.2h0a.838.838,0,0,0,.292-.206Zm2.75-3.455h0a.939.939,0,0,0-.18-1.238h0a.765.765,0,0,0-.607-.161.807.807,0,0,0-.533.354h0a.94.94,0,0,0,.18,1.238h0a.756.756,0,0,0,.768.115h0a.834.834,0,0,0,.372-.308Zm2.124-4h0a.918.918,0,0,0-.43-1.165h0a.754.754,0,0,0-.625-.006.85.85,0,0,0-.445.477h0a.914.914,0,0,0,.43,1.161h0a.752.752,0,0,0,.607.014h0a.852.852,0,0,0,.466-.481Zm1.06-4.546a.931.931,0,0,0-.189-.643.792.792,0,0,0-.554-.309h0a.774.774,0,0,0-.6.208.912.912,0,0,0-.285.6h0a.935.935,0,0,0,.191.647.8.8,0,0,0,.557.309h0a.8.8,0,0,0,.353-.052h0a.879.879,0,0,0,.527-.763Zm-.466-4.7h0a.872.872,0,0,0-.4-.524.758.758,0,0,0-.622-.065h0a.9.9,0,0,0-.543,1.105h0a.872.872,0,0,0,.4.524.757.757,0,0,0,.622.065h0a.348.348,0,0,0,.048-.013h0a.916.916,0,0,0,.5-1.091Zm-2.126-2.967a.9.9,0,0,0,.336-.572.944.944,0,0,0-.136-.662h0a.782.782,0,0,0-1.141-.216h0a.944.944,0,0,0-.193,1.238h0a.778.778,0,0,0,.954.317h0a.657.657,0,0,0,.18-.108Zm-2.628-3.371a.941.941,0,0,0-.122-1.245h0a.773.773,0,0,0-.6-.189.8.8,0,0,0-.549.328h0a.939.939,0,0,0,.128,1.245h0a.768.768,0,0,0,.8.139h0A.838.838,0,0,0,155.709,521.791Zm-3.354-2.51a.924.924,0,0,0-.367-1.189h0a.787.787,0,0,0-1.092.394h0a.95.95,0,0,0-.045.678.863.863,0,0,0,.412.511h0a.747.747,0,0,0,.649.035h0a.841.841,0,0,0,.444-.432Zm-3.73-1.8a.954.954,0,0,0-.034-.678.842.842,0,0,0-.467-.452h0a.807.807,0,0,0-1.041.551h0a.908.908,0,0,0,.5,1.126h0a.738.738,0,0,0,.555-.007h0a.845.845,0,0,0,.486-.537Zm-3.888-1.356a.9.9,0,0,0-.569-1.095h0a.761.761,0,0,0-.621.084.879.879,0,0,0-.384.537h0a.9.9,0,0,0,.572,1.088h0a.748.748,0,0,0,.5-.024h0a.852.852,0,0,0,.5-.593Zm-3.948-1.147a.951.951,0,0,0-.082-.672.827.827,0,0,0-.5-.412h0a.758.758,0,0,0-.62.088.878.878,0,0,0-.379.539h0a.95.95,0,0,0,.081.673.826.826,0,0,0,.5.411h0a.781.781,0,0,0,.5-.024h0a.861.861,0,0,0,.5-.6Zm-3.943-1.18a.907.907,0,0,0-.53-1.116h0a.81.81,0,0,0-1.028.576h0a.947.947,0,0,0,.052.675.831.831,0,0,0,.478.435h0a.748.748,0,0,0,.537-.014h0a.849.849,0,0,0,.492-.558ZM133,512.313a.922.922,0,0,0-.383-1.178h0a.756.756,0,0,0-.625-.037.842.842,0,0,0-.464.455h0a.922.922,0,0,0,.385,1.182h0a.773.773,0,0,0,.639.032h0a.824.824,0,0,0,.45-.452Zm-3.55-2.172a.933.933,0,0,0,.206-.643.916.916,0,0,0-.276-.612h0a.775.775,0,0,0-.59-.221.791.791,0,0,0-.561.3h0a.927.927,0,0,0-.205.64.91.91,0,0,0,.275.609h0a.765.765,0,0,0,.829.167h0a.807.807,0,0,0,.324-.24Zm-2.682-3.3a.86.86,0,0,0,.432-.492.957.957,0,0,0-.016-.68h0a.8.8,0,0,0-1.08-.452h0a.925.925,0,0,0-.417,1.172h0a.8.8,0,0,0,1.034.471h0a.263.263,0,0,0,.045-.024Zm-.392-5.135a.917.917,0,0,0-.257-.617.778.778,0,0,0-.584-.237h0a.784.784,0,0,0-.57.276.924.924,0,0,0-.22.634h0a.922.922,0,0,0,.256.62.781.781,0,0,0,.585.241h0a.733.733,0,0,0,.26-.06h0a.9.9,0,0,0,.533-.857Zm.63-3.978a.952.952,0,0,0-.01-.677.847.847,0,0,0-.449-.47h0a.753.753,0,0,0-.624.011.853.853,0,0,0-.433.488h0a.955.955,0,0,0,.011.68.85.85,0,0,0,.452.471h0a.748.748,0,0,0,.582.007h0a.842.842,0,0,0,.476-.509Zm1.992-3.494a.933.933,0,0,0,.2-.643.912.912,0,0,0-.28-.605h0a.773.773,0,0,0-1.15.087h0a.942.942,0,0,0,.087,1.251h0a.76.76,0,0,0,.819.16h0a.82.82,0,0,0,.331-.251Zm2.878-2.845a.936.936,0,0,0,.17-1.238h0a.776.776,0,0,0-1.141-.191h0a.944.944,0,0,0-.17,1.241h0a.777.777,0,0,0,.941.3h0a.843.843,0,0,0,.2-.118Zm15.273-7.765a.887.887,0,0,0-.35-.561.761.761,0,0,0-.613-.126h0a.812.812,0,0,0-.518.378.943.943,0,0,0-.118.665h0a.821.821,0,0,0,.961.694h0c.045-.014.083-.024.122-.038h0a.9.9,0,0,0,.517-1.007Zm-11.906,5.481a.87.87,0,0,0,.4-.523.949.949,0,0,0-.062-.673h0a.834.834,0,0,0-.482-.432.758.758,0,0,0-.623.066h0a.928.928,0,0,0-.334,1.2h0a.789.789,0,0,0,1.005.411h0a.628.628,0,0,0,.1-.045Zm7.938-4.417a.8.8,0,0,0-1.022-.579h0a.832.832,0,0,0-.479.434.95.95,0,0,0-.054.675h0a.869.869,0,0,0,.4.52.756.756,0,0,0,.622.059h0a.35.35,0,0,0,.045-.014h0a.914.914,0,0,0,.492-1.1Zm-4.315,2.583a.917.917,0,0,0,.45-1.157h0a.8.8,0,0,0-1.066-.484h0a.919.919,0,0,0-.45,1.157h0a.8.8,0,0,0,1.041.495h0l.022-.011Z" transform="translate(-98.579 -440.4)" fill="#fff"/>
            </g>
          </g>
          <g id="Group_14296" data-name="Group 14296" transform="translate(0)" clip-path="url(#clip-path-7)">
            <g id="Group_14295" data-name="Group 14295" transform="translate(49.225 32.722)">
              <g id="Group_14293" data-name="Group 14293" transform="translate(23.975 101.282)">
                <g id="Group_14271" data-name="Group 14271" transform="translate(116.224 86.613)">
                  <rect id="Rectangle_8754" data-name="Rectangle 8754" width="2.684" height="25.414" transform="translate(19.445 11.094)" fill="#303645"/>
                  <path id="Path_87533" data-name="Path 87533" d="M477.545,693.282l-39.358-7.477a2.606,2.606,0,0,1-2.027-2.619v-7.61a2.757,2.757,0,0,1,.874-2.035,2.316,2.316,0,0,1,2-.585l39.359,7.478a2.606,2.606,0,0,1,2.027,2.619v7.61a2.757,2.757,0,0,1-.875,2.035,2.316,2.316,0,0,1-2,.584Z" transform="translate(-436.16 -672.918)" fill="#303645"/>
                  <g id="Group_14270" data-name="Group 14270" transform="translate(3.662 32.134)">
                    <g id="Group_14267" data-name="Group 14267" transform="translate(19.418 0)">
                      <ellipse id="Ellipse_164" data-name="Ellipse 164" cx="5.387" cy="8.071" rx="5.387" ry="8.071" transform="translate(0)" fill="#303645"/>
                      <rect id="Rectangle_8755" data-name="Rectangle 8755" width="5.004" height="16.138" transform="translate(5.387 0)" fill="#303645"/>
                      <ellipse id="Ellipse_165" data-name="Ellipse 165" cx="5.387" cy="8.071" rx="5.387" ry="8.071" transform="translate(5.004)" fill="#303645"/>
                      <ellipse id="Ellipse_166" data-name="Ellipse 166" cx="2.966" cy="4.44" rx="2.966" ry="4.44" transform="translate(7.425 3.63)" fill="#303645"/>
                      <ellipse id="Ellipse_167" data-name="Ellipse 167" cx="2.467" cy="3.693" rx="2.467" ry="3.693" transform="translate(7.425 4.377)" fill="#303645"/>
                    </g>
                    <g id="Group_14268" data-name="Group 14268" transform="translate(0 0)">
                      <ellipse id="Ellipse_168" data-name="Ellipse 168" cx="5.387" cy="8.071" rx="5.387" ry="8.071" fill="#303645"/>
                      <rect id="Rectangle_8756" data-name="Rectangle 8756" width="5.004" height="16.138" transform="translate(5.387 0)" fill="#303645"/>
                      <ellipse id="Ellipse_169" data-name="Ellipse 169" cx="5.387" cy="8.071" rx="5.387" ry="8.071" transform="translate(5.004)" fill="#303645"/>
                      <ellipse id="Ellipse_170" data-name="Ellipse 170" cx="2.966" cy="4.44" rx="2.966" ry="4.44" transform="translate(7.425 3.63)" fill="#303645"/>
                      <ellipse id="Ellipse_171" data-name="Ellipse 171" cx="2.467" cy="3.693" rx="2.467" ry="3.693" transform="translate(7.425 4.377)" fill="#303645"/>
                    </g>
                    <g id="Group_14269" data-name="Group 14269" transform="translate(9.266 0)">
                      <ellipse id="Ellipse_172" data-name="Ellipse 172" cx="5.387" cy="8.071" rx="5.387" ry="8.071" transform="translate(0)" fill="#303645"/>
                      <rect id="Rectangle_8757" data-name="Rectangle 8757" width="5.004" height="16.138" transform="translate(5.387 0)" fill="#303645"/>
                      <ellipse id="Ellipse_173" data-name="Ellipse 173" cx="5.387" cy="8.071" rx="5.387" ry="8.071" transform="translate(5.004)" fill="#303645"/>
                      <ellipse id="Ellipse_174" data-name="Ellipse 174" cx="2.966" cy="4.44" rx="2.966" ry="4.44" transform="translate(7.425 3.63)" fill="#303645"/>
                      <ellipse id="Ellipse_175" data-name="Ellipse 175" cx="2.467" cy="3.693" rx="2.467" ry="3.693" transform="translate(7.425 4.377)" fill="#303645"/>
                    </g>
                  </g>
                  <rect id="Rectangle_8758" data-name="Rectangle 8758" width="5.07" height="17.284" rx="2.535" transform="translate(18.25 11.094)" fill="#303645"/>
                  <path id="Rectangle_8759" data-name="Rectangle 8759" d="M0,0,2.8.114l.586,14.339-2.8-.114Z" transform="translate(29.932 14.485) rotate(45)" fill="#303645"/>
                </g>
                <g id="Group_14273" data-name="Group 14273" transform="translate(142.303 105.595)">
                  <path id="Path_87534" data-name="Path 87534" d="M583.306,718.149H516.667a12.88,12.88,0,0,0-7.561,2.485l-5.089,3.685a1.076,1.076,0,0,0-.4,1.09.988.988,0,0,0,.8.795l62.349,9.639a5.8,5.8,0,0,0,4.765-1.449l13.242-11.813a2.638,2.638,0,0,0,.707-2.775,2.323,2.323,0,0,0-2.169-1.658Z" transform="translate(-503.593 -718.147)" fill="#303645"/>
                  <g id="Group_14272" data-name="Group 14272" transform="translate(0 0)" clip-path="url(#clip-path-8)">
                    <rect id="Rectangle_8760" data-name="Rectangle 8760" width="87.998" height="7.824" transform="translate(87.997 2.806) rotate(180)" fill="#425a72"/>
                  </g>
                </g>
                <path id="Path_87536" data-name="Path 87536" d="M693.81,613.217l40.159,28.4,23.39-71.826a4.982,4.982,0,0,0-.542-4.235,4.192,4.192,0,0,0-3.5-1.964h0a40.76,40.76,0,0,0-31.52,15.387L693.81,613.221Z" transform="translate(-477.943 -522.863)" fill="#fff"/>
                <g id="Group_14276" data-name="Group 14276" transform="translate(0 41.005)">
                  <path id="Path_87537" data-name="Path 87537" d="M149.949,657.971l147.5,2.1-.244-6.957-139.7-.054a11.315,11.315,0,0,0-5.342,1.36l-2.584,1.377c-.851.453-.553,2.162.379,2.174Z" transform="translate(-143.874 -615.787)" fill="#fff"/>
                  <g id="Group_14275" data-name="Group 14275" transform="translate(0 0)">
                    <path id="Path_87538" data-name="Path 87538" d="M293.931,564.287l-155.4,26.143a3.368,3.368,0,0,0-2.386,1.994h0c-1.438,3.027.325,7.089,3.086,7.118l132.859,1.5a14.538,14.538,0,0,0,11.745-5.922l5.36-6.715a13.356,13.356,0,0,0,2.494-9.616l-.17-1.616a4.042,4.042,0,0,1,1.427-3.735l2.9-2.015a4.042,4.042,0,0,0,1.427-3.735h0c-.224-2.137-1.74-3.677-3.345-3.4Z" transform="translate(-135.634 -564.254)" fill="#e6edf4"/>
                    <g id="Group_14274" data-name="Group 14274" clip-path="url(#clip-path-9)">
                      <path id="Path_87539" data-name="Path 87539" d="M106.631,590.087l180-22.92-1.311-12.436-180,22.921Z" transform="translate(-117.043 -558.727)" fill="#fff"/>
                    </g>
                  </g>
                  <path id="Path_87541" data-name="Path 87541" d="M362.976,624.246l13.88,18.518c.433.575-.046,1.473-.6,1.12l-10.43-6.715a6.951,6.951,0,0,1-2.843-5l-.92-7.35c-.085-.68.534-1.07.9-.575Z" transform="translate(-274.492 -598.935)" fill="#2d3442"/>
                </g>
                <g id="Group_14280" data-name="Group 14280" transform="translate(56.132 0)">
                  <path id="Path_87542" data-name="Path 87542" d="M420.548,531.681c-17.844-11.857-69.172-47.692-83.349-56.624s-42.1-13.217-49.181-.709c0,0-11.053,2.029-5.847,17.477s60.442,55.969,98.924,77.821c19.314,10.967,37.819,21.346,54.681,24.729,30.567,6.136,53.764,1.473,57.519-.915,5.82-3.706-54.9-49.925-72.745-61.778Z" transform="translate(-280.777 -466.556)" fill="#fff"/>
                  <g id="Group_14279" data-name="Group 14279" transform="translate(0)" clip-path="url(#clip-path-10)">
                    <path id="Path_87543" data-name="Path 87543" d="M456.877,644.94c-84.475-30.556-162.928-83.146-238.6-145.927l9.27-4.054a39.486,39.486,0,0,1,20.489-3.081l48.6,5.7a43.112,43.112,0,0,1,17.887,6.342l159.8,100.128L456.87,644.945Z" transform="translate(-242.45 -481.094)" fill="#d4ebff" opacity="0.5"/>
                    <path id="Path_87544" data-name="Path 87544" d="M456.877,644.94c-84.475-30.556-162.928-83.146-238.6-145.927l9.27-4.054a39.486,39.486,0,0,1,20.489-3.081l48.6,5.7a43.112,43.112,0,0,1,17.887,6.342l159.8,100.128L456.87,644.945Z" transform="translate(-242.45 -481.094)" fill="#e4e6e9" opacity="0.5"/>
                    <path id="Path_87545" data-name="Path 87545" d="M383.651,673.9c-92.875-35.819-180.2-93.97-264.961-162.446l9.866-3.685a43.421,43.421,0,0,1,22.06-2.094l52.91,8.4a52.717,52.717,0,0,1,19.662,7.45L400.752,632.781l-17.1,41.115Z" transform="translate(-181.375 -488.923)" fill="#ecaf4c"/>
                    <path id="Path_87546" data-name="Path 87546" d="M383.651,673.9c-92.875-35.819-180.2-93.97-264.961-162.446l9.866-3.685a43.421,43.421,0,0,1,22.06-2.094l52.91,8.4a52.717,52.717,0,0,1,19.662,7.45L400.752,632.781l-17.1,41.115Z" transform="translate(-181.375 -488.923)" fill="#fff"/>
                    <path id="Path_87547" data-name="Path 87547" d="M371.511,694.256c-92.875-35.818-180.2-93.97-264.961-162.446l9.866-3.685a43.422,43.422,0,0,1,22.06-2.094l52.91,8.4a52.718,52.718,0,0,1,19.662,7.45L388.612,653.141l-17.1,41.116Z" transform="translate(-173.93 -500.737)" fill="#ebb34d"/>
                    <g id="Group_14277" data-name="Group 14277" transform="translate(7.234 3.815)">
                      <path id="Path_87548" data-name="Path 87548" d="M305.7,476.7c-2.545.5-5,1.507-6.21,3.534a34.483,34.483,0,0,1,3.995-.6l2.208-2.938Z" transform="translate(-299.485 -476.258)" fill="#303645"/>
                      <path id="Path_87549" data-name="Path 87549" d="M350.869,482.452l-1.818-5.049c-.882-.217-1.852-.407-2.92-.562l-.92,3.874a44.582,44.582,0,0,1,5.658,1.739Z" transform="translate(-327.525 -476.339)" fill="#303645"/>
                      <path id="Path_87550" data-name="Path 87550" d="M358.754,479.11l1.477,4.919s3.806,1.855,5.782,2.984c0,0,1.121-5.284-7.263-7.9Z" transform="translate(-335.829 -477.656)" fill="#303645"/>
                      <path id="Path_87551" data-name="Path 87551" d="M325.324,475.974c-1.04-.117-2.158-.206-3.368-.26a38.47,38.47,0,0,0-5.859.092l-2.7,3.106a42.542,42.542,0,0,1,11.3.87l.63-3.815Z" transform="translate(-308.017 -475.644)" fill="#303645"/>
                    </g>
                    <g id="Group_14278" data-name="Group 14278" transform="translate(47.736 14.069)">
                      <path id="Path_87552" data-name="Path 87552" d="M405.761,507.164h0a1.621,1.621,0,0,1-1.551-1.683v-3.721a1.621,1.621,0,0,1,1.551-1.683h0a1.621,1.621,0,0,1,1.551,1.683v3.719a1.759,1.759,0,0,1-.453,1.191,1.492,1.492,0,0,1-1.1.494Z" transform="translate(-404.21 -500.078)" fill="#303645"/>
                      <path id="Path_87553" data-name="Path 87553" d="M421.071,515.815h0a1.621,1.621,0,0,1-1.551-1.683v-3.719a1.621,1.621,0,0,1,1.551-1.683h0a1.621,1.621,0,0,1,1.551,1.683v3.719A1.621,1.621,0,0,1,421.071,515.815Z" transform="translate(-413.599 -505.099)" fill="#303645"/>
                      <path id="Path_87554" data-name="Path 87554" d="M436.371,524.464h0a1.621,1.621,0,0,1-1.551-1.683v-3.721a1.621,1.621,0,0,1,1.551-1.683h0a1.621,1.621,0,0,1,1.551,1.683v3.719a1.759,1.759,0,0,1-.453,1.191,1.492,1.492,0,0,1-1.1.494Z" transform="translate(-422.982 -510.117)" fill="#303645"/>
                      <path id="Path_87555" data-name="Path 87555" d="M451.681,533.125h0a1.621,1.621,0,0,1-1.551-1.683v-3.719a1.621,1.621,0,0,1,1.551-1.683h0a1.621,1.621,0,0,1,1.551,1.683v3.719A1.621,1.621,0,0,1,451.681,533.125Z" transform="translate(-432.371 -515.143)" fill="#303645"/>
                      <path id="Path_87556" data-name="Path 87556" d="M466.981,541.775h0a1.621,1.621,0,0,1-1.551-1.683v-3.719a1.621,1.621,0,0,1,1.551-1.683h0a1.621,1.621,0,0,1,1.551,1.683v3.719A1.621,1.621,0,0,1,466.981,541.775Z" transform="translate(-441.754 -520.162)" fill="#303645"/>
                      <path id="Path_87557" data-name="Path 87557" d="M482.291,550.425h0a1.621,1.621,0,0,1-1.551-1.683v-3.719a1.621,1.621,0,0,1,1.551-1.683h0a1.621,1.621,0,0,1,1.551,1.683v3.719a1.621,1.621,0,0,1-1.551,1.683Z" transform="translate(-451.143 -525.182)" fill="#303645"/>
                      <path id="Path_87558" data-name="Path 87558" d="M497.591,559.075h0a1.621,1.621,0,0,1-1.551-1.683v-3.719a1.621,1.621,0,0,1,1.551-1.683h0a1.621,1.621,0,0,1,1.551,1.683v3.719A1.621,1.621,0,0,1,497.591,559.075Z" transform="translate(-460.526 -530.201)" fill="#303645"/>
                      <rect id="Rectangle_8761" data-name="Rectangle 8761" width="3.098" height="7.081" rx="1.549" transform="translate(41.435 25.428)" fill="#303645"/>
                      <path id="Path_87559" data-name="Path 87559" d="M528.211,576.385h0a1.621,1.621,0,0,1-1.551-1.683v-3.719a1.621,1.621,0,0,1,1.551-1.683h0a1.621,1.621,0,0,1,1.551,1.683V574.7A1.621,1.621,0,0,1,528.211,576.385Z" transform="translate(-479.304 -540.246)" fill="#303645"/>
                      <path id="Path_87560" data-name="Path 87560" d="M543.511,585.035h0a1.621,1.621,0,0,1-1.551-1.683v-3.719a1.621,1.621,0,0,1,1.551-1.683h0a1.621,1.621,0,0,1,1.551,1.683v3.716a1.758,1.758,0,0,1-.453,1.192A1.492,1.492,0,0,1,543.511,585.035Z" transform="translate(-488.687 -545.266)" fill="#303645"/>
                      <path id="Path_87561" data-name="Path 87561" d="M558.82,593.7h0a1.621,1.621,0,0,1-1.551-1.683v-3.719a1.621,1.621,0,0,1,1.551-1.683h0a1.621,1.621,0,0,1,1.551,1.683v3.719a1.621,1.621,0,0,1-1.551,1.683Z" transform="translate(-498.076 -550.291)" fill="#303645"/>
                      <path id="Path_87562" data-name="Path 87562" d="M574.121,602.345h0a1.621,1.621,0,0,1-1.551-1.683v-3.719a1.621,1.621,0,0,1,1.551-1.683h0a1.621,1.621,0,0,1,1.551,1.683v3.719a1.621,1.621,0,0,1-1.551,1.683Z" transform="translate(-507.459 -555.31)" fill="#303645"/>
                      <rect id="Rectangle_8762" data-name="Rectangle 8762" width="3.098" height="7.081" rx="1.549" transform="translate(71.032 43.589)" fill="#303645"/>
                    </g>
                  </g>
                </g>
                <g id="Group_14281" data-name="Group 14281" transform="translate(48.057 66.146)">
                  <path id="Path_87564" data-name="Path 87564" d="M339.342,681.157h0a11.024,11.024,0,0,0-2.556-13.8c-3.577-2.85-7.626-5.981-11.49-8.894-5.075-3.828-7.537.663-10.728,6.413h0a12.475,12.475,0,0,0-1.132,9.141,11.46,11.46,0,0,0,5.366,7.152c4.18,2.355,4.111,1.914,8.16,4.021A9.217,9.217,0,0,0,339.342,681.157Z" transform="translate(-292.483 -643.238)" fill="#303645"/>
                  <path id="Path_87565" data-name="Path 87565" d="M308.108,663.244h0a16.009,16.009,0,0,0-3.713-20.05c-4.355-3.467-9.935-8.176-11.641-9.62-.348-.294-.7-.58-1.075-.839a14.524,14.524,0,0,0-11.892-2.159,15.7,15.7,0,0,0-9.738,7.716h0a18.143,18.143,0,0,0-1.634,13.295,16.664,16.664,0,0,0,7.811,10.394c6.049,3.406,8.013,4.072,13.869,7.118,6.5,3.381,14.295.847,18.01-5.851Z" transform="translate(-264.757 -627.616)" fill="#ebb34d"/>
                  <path id="Ellipse_176" data-name="Ellipse 176" d="M14.986-.01C23.263-.016,29.968,7.556,29.962,16.9s-6.721,16.928-15,16.933S-.017,26.269-.011,16.923,6.71,0,14.986-.01Z" transform="translate(0 0.35) rotate(-0.63)" fill="#fff"/>
                  <path id="Path_87585" data-name="Path 87585" d="M12.63-.009C19.6-.015,25.252,6.363,25.245,14.237S19.579,28.5,12.606,28.506-.016,22.135-.009,14.261,5.657,0,12.63-.009Z" transform="translate(1.216 3.006) rotate(-0.63)" fill="#303745"/>
                  <path id="Path_87588" data-name="Path 87588" d="M4.81,0a5.142,5.142,0,0,1,4.8,5.427A5.164,5.164,0,0,1,4.8,10.859,5.142,5.142,0,0,1,0,5.432,5.164,5.164,0,0,1,4.81,0Z" transform="translate(15.792 15.118) rotate(-0.63)" fill="#fff" opacity="0.44"/>
                  <path id="Path_87566" data-name="Path 87566" d="M309.638,666.544a3.534,3.534,0,1,1-3.558-3.924A3.758,3.758,0,0,1,309.638,666.544Z" transform="translate(-286.088 -646.476)" fill="#fff"/>
                </g>
                <g id="Group_14284" data-name="Group 14284" transform="translate(155.025 46.538)">
                  <path id="Path_87567" data-name="Path 87567" d="M867.517,645.354,735.1,655.406l-.468-6.957,125.611-7.589a9.6,9.6,0,0,1,4.942,1.07l2.459,1.238c.808.407.712,2.128-.128,2.191Z" transform="translate(-658.007 -614.231)" fill="#fff"/>
                  <g id="Group_14283" data-name="Group 14283" transform="translate(0 0)">
                    <path id="Path_87568" data-name="Path 87568" d="M540.544,577.467l207.62,14.912a4.627,4.627,0,0,1,3.353,1.821h0a4.564,4.564,0,0,1-3.334,7.329l-175.037,11.06a22.8,22.8,0,0,1-16.1-5.066l-7.762-6.321A11.628,11.628,0,0,1,545,591.78l.058-1.628a3.826,3.826,0,0,0-2.266-3.624l-4.033-1.8a3.824,3.824,0,0,1-2.266-3.624h0a4.1,4.1,0,0,1,1.324-2.747,3.477,3.477,0,0,1,2.737-.887Z" transform="translate(-536.486 -577.445)" fill="#e6edf4"/>
                    <g id="Group_14282" data-name="Group 14282" clip-path="url(#clip-path-11)">
                      <path id="Rectangle_8763" data-name="Rectangle 8763" d="M0,0,239.931.57,239.96,13.3.028,12.726Z" transform="translate(-9.492 -3.301) rotate(2.18)" fill="#fff"/>
                    </g>
                  </g>
                  <path id="Path_87570" data-name="Path 87570" d="M787.557,625.13l14.966,20.784a.708.708,0,0,1-.974,1l-12.5-8.512a6.519,6.519,0,0,1-2.854-5.452l.067-7.421a.77.77,0,0,1,.518-.674.69.69,0,0,1,.769.271Z" transform="translate(-689.627 -604.936)" fill="#272d3a"/>
                  <path id="Path_87571" data-name="Path 87571" d="M860.647,619.092l14.966,20.785a.708.708,0,0,1-.974,1l-12.5-8.512a6.518,6.518,0,0,1-2.853-5.449l.067-7.42a.77.77,0,0,1,.518-.674.69.69,0,0,1,.769.271Z" transform="translate(-734.451 -601.433)" fill="#272d3a"/>
                  <path id="Path_87572" data-name="Path 87572" d="M935.357,615.93l14.966,20.785a.708.708,0,0,1-.974,1l-12.5-8.512A6.519,6.519,0,0,1,934,623.75l.067-7.421a.77.77,0,0,1,.518-.675.69.69,0,0,1,.77.272Z" transform="translate(-780.268 -599.598)" fill="#272d3a"/>
                </g>
                <g id="Group_14286" data-name="Group 14286" transform="translate(241.556 105.595)">
                  <path id="Path_87573" data-name="Path 87573" d="M762.533,718.149h66.639a12.88,12.88,0,0,1,7.561,2.485l5.089,3.685a1.077,1.077,0,0,1,.4,1.09.988.988,0,0,1-.8.795l-62.35,9.639a5.8,5.8,0,0,1-4.765-1.449l-13.242-11.813a2.637,2.637,0,0,1-.707-2.775,2.323,2.323,0,0,1,2.169-1.658Z" transform="translate(-760.229 -718.147)" fill="#e6edf4"/>
                  <g id="Group_14285" data-name="Group 14285" transform="translate(0 0)" clip-path="url(#clip-path-12)">
                    <rect id="Rectangle_8764" data-name="Rectangle 8764" width="87.998" height="7.824" transform="translate(-5.98 -5.017)" fill="#fff"/>
                  </g>
                </g>
                <g id="Group_14291" data-name="Group 14291" transform="translate(186.396 86.611)">
                  <path id="Path_87589" data-name="Path 87589" d="M0,0H2.684V25.414H0Z" transform="translate(24.815 36.514) rotate(180)" fill="#303645"/>
                  <path id="Path_87575" data-name="Path 87575" d="M620.479,693.281l39.358-7.479a2.606,2.606,0,0,0,2.027-2.619v-7.61a2.758,2.758,0,0,0-.874-2.035,2.315,2.315,0,0,0-2-.584l-39.358,7.479a2.606,2.606,0,0,0-2.027,2.619v7.61a2.757,2.757,0,0,0,.874,2.035,2.316,2.316,0,0,0,2,.584Z" transform="translate(-617.607 -672.915)" fill="#2b313f"/>
                  <g id="Group_14290" data-name="Group 14290" transform="translate(5.397 32.136)">
                    <g id="Group_14287" data-name="Group 14287" transform="translate(19.418 0)">
                      <ellipse id="Ellipse_179" data-name="Ellipse 179" cx="5.387" cy="8.071" rx="5.387" ry="8.071" transform="translate(0)" fill="#2b313f"/>
                      <rect id="Rectangle_8766" data-name="Rectangle 8766" width="5.004" height="16.138" transform="translate(5.387 0)" fill="#2b313f"/>
                      <ellipse id="Ellipse_180" data-name="Ellipse 180" cx="5.387" cy="8.071" rx="5.387" ry="8.071" transform="translate(5.004)" fill="#2b313f"/>
                      <ellipse id="Ellipse_181" data-name="Ellipse 181" cx="2.966" cy="4.44" rx="2.966" ry="4.44" transform="translate(7.425 3.63)" fill="#2b313f"/>
                      <ellipse id="Ellipse_182" data-name="Ellipse 182" cx="2.467" cy="3.693" rx="2.467" ry="3.693" transform="translate(7.425 4.377)" fill="#2b313f"/>
                    </g>
                    <g id="Group_14288" data-name="Group 14288" transform="translate(0 0)">
                      <ellipse id="Ellipse_183" data-name="Ellipse 183" cx="5.387" cy="8.071" rx="5.387" ry="8.071" fill="#2b313f"/>
                      <rect id="Rectangle_8767" data-name="Rectangle 8767" width="5.004" height="16.138" transform="translate(5.387 0)" fill="#2b313f"/>
                      <ellipse id="Ellipse_184" data-name="Ellipse 184" cx="5.387" cy="8.071" rx="5.387" ry="8.071" transform="translate(5.004)" fill="#2b313f"/>
                      <ellipse id="Ellipse_185" data-name="Ellipse 185" cx="2.966" cy="4.44" rx="2.966" ry="4.44" transform="translate(7.425 3.63)" fill="#2b313f"/>
                      <ellipse id="Ellipse_186" data-name="Ellipse 186" cx="2.467" cy="3.693" rx="2.467" ry="3.693" transform="translate(7.425 4.377)" fill="#2b313f"/>
                    </g>
                    <g id="Group_14289" data-name="Group 14289" transform="translate(9.266 0)">
                      <ellipse id="Ellipse_187" data-name="Ellipse 187" cx="5.387" cy="8.071" rx="5.387" ry="8.071" transform="translate(0)" fill="#2b313f"/>
                      <rect id="Rectangle_8768" data-name="Rectangle 8768" width="5.004" height="16.138" transform="translate(5.387 0)" fill="#2b313f"/>
                      <ellipse id="Ellipse_188" data-name="Ellipse 188" cx="5.387" cy="8.071" rx="5.387" ry="8.071" transform="translate(5.004)" fill="#2b313f"/>
                      <ellipse id="Ellipse_189" data-name="Ellipse 189" cx="2.966" cy="4.44" rx="2.966" ry="4.44" transform="translate(7.425 3.63)" fill="#2b313f"/>
                      <ellipse id="Ellipse_190" data-name="Ellipse 190" cx="2.467" cy="3.693" rx="2.467" ry="3.693" transform="translate(7.425 4.377)" fill="#2b313f"/>
                    </g>
                  </g>
                  <rect id="Rectangle_8769" data-name="Rectangle 8769" width="5.07" height="17.284" rx="2.535" transform="translate(26.01 28.384) rotate(180)" fill="#2b313f"/>
                  <path id="Rectangle_8770" data-name="Rectangle 8770" d="M.151.025l2.8-.114L2.363,14.249l-2.8.114Z" transform="translate(24.17 24.954) rotate(135)" fill="#2b313f"/>
                </g>
                <g id="Group_14292" data-name="Group 14292" transform="translate(184.656 54.741)">
                  <path id="Path_87576" data-name="Path 87576" d="M697.072,659.893h0a11.023,11.023,0,0,0-2.556-13.8c-3.577-2.85-7.626-5.981-11.49-8.894a10.027,10.027,0,0,0-8.342-1.817,10.778,10.778,0,0,0-6.926,5.364h0a12.476,12.476,0,0,0-1.132,9.141,11.46,11.46,0,0,0,5.366,7.152c4.18,2.355,8.651,4.783,12.7,6.885a9.217,9.217,0,0,0,12.38-4.033Z" transform="translate(-645.681 -619.115)" fill="#303645"/>
                  <path id="Path_87577" data-name="Path 87577" d="M665.85,638.923h0a16.009,16.009,0,0,0-3.713-20.05c-5.2-4.143-11.08-8.688-16.695-12.923a14.57,14.57,0,0,0-12.123-2.641,15.662,15.662,0,0,0-10.064,7.8h0a18.128,18.128,0,0,0-1.644,13.283,16.653,16.653,0,0,0,7.8,10.393c6.071,3.421,12.569,6.947,18.451,10.006,6.493,3.371,14.283.832,17.991-5.863Z" transform="translate(-617.962 -600.435)" fill="#ebb34d"/>
                  <path id="Ellipse_191" data-name="Ellipse 191" d="M14.986-.01C23.263-.016,29.968,7.556,29.962,16.9s-6.721,16.928-15,16.933S-.017,26.269-.011,16.923,6.71,0,14.986-.01Z" transform="translate(0 0.35) rotate(-0.63)" fill="#fff"/>
                  <path id="Path_87584" data-name="Path 87584" d="M12.63-.009C19.6-.015,25.252,6.363,25.245,14.237S19.579,28.5,12.606,28.506-.016,22.135-.009,14.261,5.657,0,12.63-.009Z" transform="translate(1.232 2.998) rotate(-0.63)" fill="#303645"/>
                  <path id="Ellipse_193" data-name="Ellipse 193" d="M4.81,0c2.656,0,4.808,2.428,4.806,5.427S7.459,10.855,4.8,10.857-.005,8.429,0,5.43,2.153,0,4.81,0Z" transform="translate(15.791 15.12) rotate(-0.63)" fill="#8f8f8f"/>
                  <path id="Path_87578" data-name="Path 87578" d="M662.838,639.354a3.534,3.534,0,1,1-3.558-3.924A3.758,3.758,0,0,1,662.838,639.354Z" transform="translate(-639.292 -619.292)" fill="#fff"/>
                </g>
              </g>
              <g id="Group_14294" data-name="Group 14294">
                <path id="Path_87579" data-name="Path 87579" d="M957.064,869.348a6.5,6.5,0,0,1-2.327-4.356,6.756,6.756,0,0,1,1.313-4.829,5.441,5.441,0,0,0,.744-3.983c-.44-3.975-7.895-6.263-16.643-5.12a33.526,33.526,0,0,0-5.785,1.272,2.216,2.216,0,0,0,.1-.927c-.266-2.4-4.589-3.817-9.792-3.3a3.752,3.752,0,0,0,1.639-3.159c-.2-1.762-2.247-3.044-5.108-3.464,1.974-1.5,3.066-3.205,2.881-4.877-.361-3.229-5.347-5.35-11.9-5.417a3.471,3.471,0,0,0,1.605-2.9c-.277-2.5-4.96-3.938-10.46-3.217-.345.045-.687.1-1.021.157a4.063,4.063,0,0,0,.509-2.727c-.766-3.915-8.376-5.481-17-3.5a34.514,34.514,0,0,0-4.574,1.384,2.152,2.152,0,0,0,.06-.791c-.216-1.957-5.716-4.836-10.411-5.074a24.56,24.56,0,0,1-9.253-2.236c-11.511-5.431-26.632-11.968-26.632-11.968L976.9,885.215S968.067,877.92,957.064,869.348Z" transform="translate(-641.642 -701.868)" fill="#fff" opacity="0.14"/>
                <path id="Path_87581" data-name="Path 87581" d="M652.837,824.884a4.294,4.294,0,0,1-1.539-2.877,4.461,4.461,0,0,1,.867-3.191,3.578,3.578,0,0,0,.493-2.633c-.292-2.628-5.215-4.14-11-3.386a22.159,22.159,0,0,0-3.822.841,1.469,1.469,0,0,0,.065-.614c-.175-1.582-3.033-2.525-6.471-2.18a2.483,2.483,0,0,0,1.082-2.088c-.129-1.162-1.484-2.01-3.378-2.29,1.306-.992,2.027-2.117,1.9-3.23-.239-2.133-3.534-3.534-7.868-3.58a2.285,2.285,0,0,0,1.062-1.919c-.18-1.652-3.28-2.6-6.912-2.126-.228.028-.455.066-.675.1a2.689,2.689,0,0,0,.338-1.8c-.5-2.586-5.534-3.621-11.23-2.31a23.307,23.307,0,0,0-3.021.914,1.438,1.438,0,0,0,.042-.523c-.144-1.294-3.776-3.2-6.881-3.353a16.209,16.209,0,0,1-6.115-1.479c-7.606-3.588-17.6-7.908-17.6-7.908l93.759,56.1s-5.833-4.824-13.106-10.487Z" transform="translate(-558.824 -740.372)" fill="#fff" opacity="0.14"/>
                <path id="Path_87582" data-name="Path 87582" d="M604.442,875.615a.489.489,0,0,1-.285-.091L537.3,829.633a.6.6,0,0,1,.021-1.022.5.5,0,0,1,.548.044l66.855,45.889a.612.612,0,0,1,.167.8.533.533,0,0,1-.451.272Z" transform="translate(-537.028 -769.368)" fill="#fff"/>
                <path id="Path_87583" data-name="Path 87583" d="M891.026,909.581a.489.489,0,0,1-.285-.091l-38.486-26.413a.611.611,0,0,1-.144-.783.51.51,0,0,1,.713-.2l38.486,26.416a.611.611,0,0,1,.167.8A.532.532,0,0,1,891.026,909.581Z" transform="translate(-791.771 -882.024)" fill="#fff"/>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_14301" data-name="Group 14301" transform="translate(255.428 74.804)">
      <g id="Group_14257" data-name="Group 14257" transform="translate(0 3.338)">
        <path id="Path_87504" data-name="Path 87504" d="M829.837,406.364l-15.9,11.032L800.55,364.385l15.9-11.032Z" transform="translate(-800.55 -343.012)" fill="#fff"/>
        <path id="Path_87505" data-name="Path 87505" d="M860.117,406.363l18.861.692L865.6,354.046l-18.865-.7Z" transform="translate(-830.825 -343.011)" fill="#d4ebff"/>
        <path id="Path_87506" data-name="Path 87506" d="M930.787,378.7l-15.907,11.032L901.5,336.726l15.9-11.033Z" transform="translate(-866.729 -325.691)" fill="#fff"/>
        <path id="Path_87507" data-name="Path 87507" d="M961.065,378.7l18.861.692-13.382-53.01-18.865-.7Z" transform="translate(-897.003 -325.69)" fill="#d4ebff"/>
        <path id="Path_87508" data-name="Path 87508" d="M931.18,380.583l-16.013,10.605L902.76,342.055l16.016-10.6Z" transform="translate(-867.555 -329.297)" fill="#93a1c3"/>
        <path id="Path_87509" data-name="Path 87509" d="M961.663,380.583l16.878.011-12.357-48.935-16.926-.209Z" transform="translate(-898.038 -329.297)" fill="#5c6a8a"/>
        <path id="Path_87510" data-name="Path 87510" d="M860.7,408.25l18.762.265-12.407-49.133L848.3,359.12Z" transform="translate(-831.853 -346.623)" fill="#5c6a8a"/>
        <path id="Path_87511" data-name="Path 87511" d="M833.747,408.25,819.583,418.3l-12.4-49.136,14.163-10.049Z" transform="translate(-804.896 -346.623)" fill="#93a1c3"/>
        <g id="Group_14256" data-name="Group 14256" transform="translate(14.643 18.442)">
          <g id="Group_14255" data-name="Group 14255" transform="translate(0 0)">
            <path id="Path_87512" data-name="Path 87512" d="M888.678,392.3c-.55-2.18.682-4.542,2.89-5.958a2.237,2.237,0,0,0,.785-2.826c-3.366-6.99-12.707-10.309-22.243-7.476a24.868,24.868,0,0,0-3.816,1.484,2.489,2.489,0,0,1-2.917-.527,3.673,3.673,0,0,0-.916-.7c-1.557-.826-4.24,1.416-5.845,3.028a3.389,3.389,0,0,1-3.091.957,11.414,11.414,0,0,0-5.49.3c-5.083,1.51,4.271,6.949-2.721,9.132a3.223,3.223,0,0,0-2.043,1.974c-1.585,4.3,6.469,5.555,9.451,4.329a1.611,1.611,0,0,1,1.377.068,1.848,1.848,0,0,1,.9,1.136l.021.067a3.393,3.393,0,0,0,1.6,2.024,2.96,2.96,0,0,0,2.452.186c.131-.045.262-.089.4-.128,3.434-1.02,6.786.411,7.492,3.2a4.588,4.588,0,0,1-.179,2.725,2.049,2.049,0,0,0,.162,1.778,1.729,1.729,0,0,0,1.446.853,9.916,9.916,0,0,0,9.007-4.863,6.68,6.68,0,0,1,9.458-2.516c2.115,1.312,4.009,2.1,4.791,1.249a8.376,8.376,0,0,0,1.929-3.51,2.333,2.333,0,0,0-.186-1.676,2.027,2.027,0,0,0-1.236-1.024,4.683,4.683,0,0,1-3.472-3.29Z" transform="translate(-843.064 -375.025)" fill="#addc72"/>
            <g id="Group_14254" data-name="Group 14254" clip-path="url(#clip-path-13)">
              <path id="Rectangle_8751" data-name="Rectangle 8751" d="M.18.1,18.312-.29,17.593,35.4-.539,35.79Z" transform="translate(4.264 4.55) rotate(-15.32)" fill="#85b66a" opacity="0.63"/>
              <path id="Rectangle_8752" data-name="Rectangle 8752" d="M.168.041,7.753-.123,7.085,33.023-.5,33.187Z" transform="translate(39.883 -2.476) rotate(-15.32)" fill="#85b66a" opacity="0.63"/>
            </g>
          </g>
          <path id="Path_87514" data-name="Path 87514" d="M982.345,417.749a1.988,1.988,0,0,0,1.359,1.8,1.788,1.788,0,0,0,2.018-.781,2.162,2.162,0,0,0,0-2.326,1.789,1.789,0,0,0-2.016-.788A1.825,1.825,0,0,0,982.345,417.749Z" transform="translate(-934.344 -400.414)" fill="#addc72"/>
          <path id="Path_87515" data-name="Path 87515" d="M871.285,379.429a1.988,1.988,0,0,0,1.359,1.8,1.788,1.788,0,0,0,2.018-.781,2.162,2.162,0,0,0,0-2.325,1.789,1.789,0,0,0-2.016-.788,1.825,1.825,0,0,0-1.364,2.1Z" transform="translate(-861.538 -376.419)" fill="#addc72"/>
          <path id="Path_87516" data-name="Path 87516" d="M948.474,393.546c-.3-1.2-2.4-1.622-4.684-.946s-3.892,2.206-3.589,3.405c.159.631.816,1.044,1.732,1.2a1.476,1.476,0,0,1,1.236,1.278.7.7,0,0,0,.021.116c.206.8,1.6,1.084,3.127.631s2.6-1.473,2.394-2.273a1.061,1.061,0,0,0-.737-.7.553.553,0,0,1-.346-.412.6.6,0,0,1,.146-.534,1.96,1.96,0,0,0,.7-1.77Z" transform="translate(-906.718 -385.829)" fill="#fff"/>
          <path id="Path_87517" data-name="Path 87517" d="M891.258,403.157c-1.7,1.577-2.78,4.737-4.121,4.535s-2.556-6.463-6.441-6.108-6.286.362-7.781-1.9-6.844-3.682-3.3-6.845,5.893-8.871,9.772-6.318,12.258-2.332,13.737-1.223-3.234,5.121-2.5,7.305,7.968,2.646,7.722,4.224,4.512,1.4,3.579,3.566-8.125.4-10.67,2.758Z" transform="translate(-859.688 -381.355)" fill="#85b66a" opacity="0.58"/>
          <path id="Path_87518" data-name="Path 87518" d="M896.877,394.728c-4.144,1.436-7.15-1.088-8.9.411s-5.935,5.372-4.936,6.893,5.6.965,8.87,1.794c1.874.475,3,6.231,5.408,5.017s8.139-5.062,5.728-6.441-5.564-1.028-5.5-2.045,2.125-6.594-.665-5.626Z" transform="translate(-869.17 -387.295)" fill="#85b66a"/>
          <path id="Path_87519" data-name="Path 87519" d="M947.333,382.257c-1.267-1.013.389-3.466,3.041-3.062,1.15.176,4.808,2.007,3.544,3.484S948.47,383.165,947.333,382.257Z" transform="translate(-911.134 -377.609)" fill="#85b66a" opacity="0.58"/>
        </g>
      </g>
      <g id="Group_14259" data-name="Group 14259" transform="translate(35.714)">
        <path id="Path_87520" data-name="Path 87520" d="M911.038,316.76c-3.87.125-6.914,3.631-6.8,7.832s7.367,13.045,7.367,13.045,6.765-9.285,6.651-13.487-3.346-7.5-7.216-7.38Zm.32,11.827a4.212,4.212,0,0,1-4.168-4.268,4.261,4.261,0,0,1,3.932-4.524,4.211,4.211,0,0,1,4.17,4.265,4.26,4.26,0,0,1-3.933,4.527Z" transform="translate(-904.234 -316.76)" fill="#ebb34d"/>
        <g id="Group_14258" data-name="Group 14258" transform="translate(0)" clip-path="url(#clip-path-14)">
          <path id="Path_87521" data-name="Path 87521" d="M897.824,313.8s1.295-5.516,6.706-5.685,7.164,5.57,7.2,6.848h0l3.424-.751L912.167,302.1l-17.887,3.97,3.544,7.726" transform="translate(-897.71 -307.58)" fill="#fff"/>
          <path id="Path_87522" data-name="Path 87522" d="M921.859,329.73a4.456,4.456,0,0,1-4.112,4.733A4.4,4.4,0,0,1,913.385,330a4.456,4.456,0,0,1,4.113-4.733A4.406,4.406,0,0,1,921.859,329.73Z" transform="translate(-910.234 -322.086)" fill="#e8d556"/>
        </g>
      </g>
    </g>
    <text id="Redeemed_Expiry_Date" data-name="Redeemed Expiry Date" transform="translate(143 704.408)" font-size="5" font-family="Montserrat-Medium, Montserrat" font-weight="500"><tspan x="0" y="0">Redeemed Expiry Date</tspan></text>
    <text id="Max_Value" data-name="Max Value" transform="translate(166.139 747.203)" font-size="5" font-family="Montserrat-Light, Montserrat" font-weight="300"><tspan x="0" y="0">Max Value</tspan></text>
    <text id="End_Date_" data-name="End Date " transform="translate(272.546 704.714)" font-size="5" font-family="Montserrat-Medium, Montserrat" font-weight="500"><tspan x="0" y="0">End Date</tspan></text>
    <g transform="matrix(1, 0, 0, 1, 0, 28.41)" filter="url(#_28Nov_2024)">
      <text id="_28Nov_2024-2" data-name="28Nov, 2024" transform="translate(253.61 698.18)" fill="#29385b" font-size="9" font-family="Montserrat-SemiBold, Montserrat" font-weight="600"><tspan x="0" y="0">28Nov, 2024</tspan></text>
    </g>
    <text id="Fri_2PM" data-name="Fri 2PM" transform="translate(277.055 746.577)" font-size="5" font-family="Montserrat-Light, Montserrat" font-weight="300"><tspan x="0" y="0">Fri 2PM</tspan></text>
    <line id="Line_273" data-name="Line 273" y2="40.614" transform="translate(241.781 705.857)" fill="none" stroke="#707070" stroke-width="1" opacity="0.1"/>
  </g>
</svg>
