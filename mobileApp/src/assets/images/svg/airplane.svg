<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="47.154" height="45.09" viewBox="0 0 47.154 45.09">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#29385b"/>
      <stop offset="1" stop-color="#1b2641"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.581" y1="0.581" x2="2.07" y2="1.774" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e5f3ff"/>
      <stop offset="1" stop-color="#b3ccff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="0.251" y1="0.449" x2="1.416" y2="0.585" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-4" x1="-1.002" y1="9.623" x2="-0.85" y2="9.868" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#d2a44c"/>
      <stop offset="1" stop-color="#b53759"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="-3.344" y1="16.889" x2="-1.684" y2="16.985" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffda45" stop-opacity="0"/>
      <stop offset="1" stop-color="#b53759"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="0.004" y1="0.682" x2="0.004" y2="0.847" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-8" x1="-0.019" y1="0.808" x2="-0.032" y2="0.574" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#e5f3ff" stop-opacity="0"/>
      <stop offset="1" stop-color="#fff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="-0.636" y1="5.181" x2="-1.734" y2="3.698" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#b3ccff" stop-opacity="0"/>
      <stop offset="1" stop-color="#9fb0cb"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="0.524" y1="0.494" x2="0.366" y2="-0.026" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#8b788a" stop-opacity="0"/>
      <stop offset="1" stop-color="#9e8c92"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="-13.111" y1="27.976" x2="-13.748" y2="26.954" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#555a66" stop-opacity="0"/>
      <stop offset="1" stop-color="#555a66"/>
    </linearGradient>
  </defs>
  <g id="airplane" transform="translate(0 -11.16)">
    <circle id="Ellipse_101" data-name="Ellipse 101" cx="22.226" cy="22.226" r="22.226" transform="translate(2.701 11.797)" fill="url(#linear-gradient)"/>
    <path id="Path_80834" data-name="Path 80834" d="M59.449,31.438A22.3,22.3,0,0,0,46.9,43.021a4.829,4.829,0,0,0-.436,2.022A4.9,4.9,0,0,0,47.9,48.507l.454.454a10.855,10.855,0,0,0,7.676,3.179.779.779,0,1,0,0-1.557H53.633a2,2,0,1,1,0-4H55.5a8.453,8.453,0,0,0,5.977-2.476l2.887-2.887a3.352,3.352,0,0,1,2.37-.982,3.352,3.352,0,0,0,3.018-1.894,2.137,2.137,0,0,0-1.924-3.067H64.106A1.983,1.983,0,0,1,62.123,33.3,1.985,1.985,0,0,0,59.449,31.438Z" transform="translate(-42.166 -18.291)" opacity="0.07" fill="url(#linear-gradient-2)"/>
    <path id="Path_80835" data-name="Path 80835" d="M377.29,183.226a3.054,3.054,0,0,0-2.988-2.386h-4.808a5.154,5.154,0,0,0-5.154,5.154v.627a4.527,4.527,0,0,0,4.527,4.527,2.3,2.3,0,0,1,2.3,2.3v6.011a1.588,1.588,0,0,0,2.9.888,22.1,22.1,0,0,0,3.515-9.2,22.472,22.472,0,0,0-.3-7.923Z" transform="translate(-330.654 -153.992)" opacity="0.07" fill="url(#linear-gradient-3)"/>
    <path id="Path_80846" data-name="Path 80846" d="M113.277,354.695l2.92,1.876,2.92-2.92-4.482-.608a.461.461,0,0,0-.372.146l-.952.952C113.128,354.325,113.111,354.588,113.277,354.695Z" transform="translate(-102.7 -310.27)" fill="#fff"/>
    <path id="Path_80847" data-name="Path 80847" d="M113.6,360.406l-.285.285c-.184.184-.2.448-.034.554l2.92,1.876,2.253-2.253-4.482-.608A.462.462,0,0,0,113.6,360.406Z" transform="translate(-102.7 -316.82)" fill="#fff"/>
    <path id="Path_80848" data-name="Path 80848" d="M160.686,378.314l-1.876-2.92,2.92-2.92.608,4.482a.461.461,0,0,1-.146.372l-.952.952C161.056,378.463,160.793,378.479,160.686,378.314Z" transform="translate(-144.127 -327.907)" fill="#fff"/>
    <path id="Path_80849" data-name="Path 80849" d="M161.525,384.544l-.285.285c-.184.184-.447.2-.554.034l-1.876-2.92,2.253-2.253.608,4.482A.461.461,0,0,1,161.525,384.544Z" transform="translate(-144.127 -334.456)" fill="rgba(255,255,255,0)"/>
    <path id="Path_80851" data-name="Path 80851" d="M145.5,210.736l-.725.725c-.215.215-.214.526,0,.619l9.293,4.005,4.22-4.22-11.933-1.471A1.078,1.078,0,0,0,145.5,210.736Z" transform="translate(-131.241 -180.808)" fill="url(#linear-gradient-4)"/>
    <path id="Path_80855" data-name="Path 80855" d="M283.4,270.307l-.725.725c-.215.215-.525.214-.619,0l-4.005-9.293,4.22-4.22,1.471,11.933A1.078,1.078,0,0,1,283.4,270.307Z" transform="translate(-252.341 -223.579)" fill="#b48429"/>
    <path id="Path_80856" data-name="Path 80856" d="M308.026,338.408l.549,4.454a1.078,1.078,0,0,1-.342.857l-.725.725c-.215.215-.526.214-.619,0l-1.475-3.423Z" transform="translate(-277.176 -296.991)" fill="url(#linear-gradient-5)"/>
    <path id="Path_80857" data-name="Path 80857" d="M282.054,293.77l-4.005-9.293,1.9-1.9,3.616,10.3-.9.9C282.458,293.987,282.147,293.986,282.054,293.77Z" transform="translate(-252.341 -246.319)" fill="url(#linear-gradient-4)"/>
    <path id="Path_80858" data-name="Path 80858" d="M152.286,165.963c-6.492,6.492-15.742,13.734-16.98,12.5s6-10.489,12.5-16.98,9.774-7.767,11.013-6.528S158.777,159.471,152.286,165.963Z" transform="translate(-122.669 -130.111)" fill="url(#linear-gradient-7)"/>
    <path id="Path_80859" data-name="Path 80859" d="M157.767,172.347c-6.175,6.175-14.872,13.165-15.925,12.111s5.937-9.751,12.111-15.925,9.2-7.488,10.249-6.435S163.942,166.172,157.767,172.347Z" transform="translate(-128.631 -136.684)" fill="url(#linear-gradient-8)"/>
    <path id="Path_80860" data-name="Path 80860" d="M153.654,170.122c6.492-6.492,7.767-9.774,6.528-11.013l-21.159,21.159-2.349,2.349C137.912,183.856,147.163,176.614,153.654,170.122Z" transform="translate(-124.037 -134.27)" fill="url(#linear-gradient-9)"/>
    <path id="Path_80861" data-name="Path 80861" d="M318.076,182.346q-.6.519-1.252,1.119c-.112.1-.1.261.019.283A3.544,3.544,0,0,1,319.8,186.7c.022.121.181.131.283.019q.6-.656,1.119-1.252a1,1,0,0,0,.261-.886,3.51,3.51,0,0,0-2.5-2.5A1,1,0,0,0,318.076,182.346Z" transform="translate(-287.46 -155.099)" fill="#1e2a47"/>
    <path id="Path_80862" data-name="Path 80862" d="M327.581,189.657q-.456.526-.976,1.1l-.142.156c-.1.112-.262.1-.284-.019a3.63,3.63,0,0,0-.995-1.957c-.031-.031-.063-.062-.1-.091a3.548,3.548,0,0,0-1.308-.765q.656-.6,1.249-1.115a1,1,0,0,1,.886-.261,3.7,3.7,0,0,1,.921.393c.032.03.064.06.1.092a3.561,3.561,0,0,1,.911,1.585A1,1,0,0,1,327.581,189.657Z" transform="translate(-293.844 -159.289)" fill="url(#linear-gradient-10)"/>
    <path id="Path_80863" data-name="Path 80863" d="M339.956,195.893c.022.121.181.131.283.019q.6-.656,1.119-1.252a1,1,0,0,0,.261-.886,3.566,3.566,0,0,0-.911-1.585l-1.747,1.747A3.636,3.636,0,0,1,339.956,195.893Z" transform="translate(-307.621 -164.291)" fill="url(#linear-gradient-11)"/>
    <path id="Path_80865" data-name="Path 80865" d="M2.954,53.265A4.292,4.292,0,0,1,2.4,53.23a.415.415,0,0,1-.347-.522l.185-.668a.412.412,0,0,1,.436-.3,4.538,4.538,0,0,0,1.371-.12.414.414,0,0,1,.488.3l.178.662a.416.416,0,0,1-.314.513A6.7,6.7,0,0,1,2.954,53.265Zm3.318-1.218-.257-.635a.413.413,0,0,1,.22-.535c.465-.2.957-.434,1.471-.7a.414.414,0,0,1,.555.173l.322.6a.415.415,0,0,1-.176.564c-.553.285-1.085.538-1.589.755a.413.413,0,0,1-.546-.226Zm-5.6-.53A.414.414,0,0,1,.1,51.236,3.934,3.934,0,0,1,0,50.31a6.293,6.293,0,0,1,.114-1.142.415.415,0,0,1,.5-.33L1.285,49a.413.413,0,0,1,.311.475,4.738,4.738,0,0,0-.084.837,2.587,2.587,0,0,0,.04.475.414.414,0,0,1-.244.459ZM10,50.155l-.349-.589A.413.413,0,0,1,9.8,49c.457-.277.928-.574,1.408-.888a.414.414,0,0,1,.57.117l.379.57a.415.415,0,0,1-.119.576c-.5.325-.985.633-1.459.92A.414.414,0,0,1,10,50.155Zm4.068-2.173a.414.414,0,0,1-.58-.1M1.753,47.5l-.639-.246a.414.414,0,0,1-.235-.543c.209-.507.456-1.043.737-1.6a.415.415,0,0,1,.562-.182l.607.317a.414.414,0,0,1,.178.553c-.26.516-.487,1.01-.679,1.475a.413.413,0,0,1-.531.228Zm1.8-3.654L2.966,43.5a.414.414,0,0,1-.147-.569l.011-.019a.415.415,0,0,1,.571-.142l.585.357a.414.414,0,0,1,.139.566l0,.007A.413.413,0,0,1,3.558,43.85Zm33.29-19.374-.561-.393a.414.414,0,0,1-.1-.573c.324-.47.635-.934.928-1.387A.413.413,0,0,1,37.679,22l.578.367a.414.414,0,0,1,.126.575c-.3.467-.622.946-.956,1.43A.414.414,0,0,1,36.848,24.476Zm2.257-3.5-.591-.346a.414.414,0,0,1-.15-.562c.286-.5.547-.986.78-1.45a.413.413,0,0,1,.549-.186l.617.3a.414.414,0,0,1,.19.559c-.247.492-.524,1.006-.825,1.534a.415.415,0,0,1-.57.153Zm1.861-3.749-.638-.249a.415.415,0,0,1-.238-.527,9.683,9.683,0,0,0,.415-1.48.412.412,0,0,1,.454-.333l.682.08A.414.414,0,0,1,42,15.2a11.171,11.171,0,0,1-.494,1.782.415.415,0,0,1-.54.245Zm-5.637-3.89-.283-.624a.414.414,0,0,1,.212-.551,16.134,16.134,0,0,1,1.684-.625.415.415,0,0,1,.522.291l.174.663a.413.413,0,0,1-.277.5,14.6,14.6,0,0,0-1.49.555A.414.414,0,0,1,35.329,13.339Zm5.478-.106a.408.408,0,0,1-.472-.129.876.876,0,0,0-.081-.094,1.52,1.52,0,0,0-1.1-.338h-.014V11.594a.414.414,0,0,1,.453-.412,2.758,2.758,0,0,1,1.729.759,2.411,2.411,0,0,1,.346.438.414.414,0,0,1-.206.6Z" fill="#b48429"/>
  </g>
</svg>
