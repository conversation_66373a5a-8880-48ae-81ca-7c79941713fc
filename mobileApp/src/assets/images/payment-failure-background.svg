<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="623" height="1080" viewBox="0 0 623 1080">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_14" data-name="Rectangle 14" width="623" height="1080" transform="translate(83 135)"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#ff830c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-81" y1="1" y2="0" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-100" x1="0.146" y1="0.854" x2="0.854" y2="0.146" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff6ee"/>
      <stop offset="1" stop-color="#ff9c3d"/>
    </linearGradient>
  </defs>
  <g id="Group_67" data-name="Group 67" transform="translate(-83 -135)">
    <g id="Group_65" data-name="Group 65">
      <path id="Path_31" data-name="Path 31" d="M0,0H623V1080H0Z" transform="translate(83 135)" fill="#e41414"/>
      <g id="Mask_Group_3" data-name="Mask Group 3" clip-path="url(#clip-path)">
        <g id="Group_63" data-name="Group 63" transform="translate(-1093)" opacity="0.4">
          <g id="Group_57" data-name="Group 57" transform="translate(-8160.648 -1499.842)">
            <path id="Path_79866" data-name="Path 79866" d="M0,8.02A40.43,40.43,0,0,1,.676,3.194,4.928,4.928,0,0,1,2.537,0l.807.21a4.139,4.139,0,0,1,.4,2.913C2.5,4.738,1.361,6.52,0,8.018Z" transform="translate(9573.823 1802.169) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79867" data-name="Path 79867" d="M3.236,6.124c-.035-.088-.105-.158-.105-.245-.079-1.018.193-1.579-.36-2.527A7.859,7.859,0,0,1,.243,1.474C-.187.9.059.64.182,0,.349.069.507.149.674.219c3.053,1.31,9.494,1.8,11.345,4.291C10.457,6.265,5.526,5.867,3.236,6.124Z" transform="translate(9575.248 1824.606) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79868" data-name="Path 79868" d="M6.124.006A74.414,74.414,0,0,0,8.081,10.755c-.492.562-.377.667-1.158.737A18.216,18.216,0,0,0,0,13.353,46.948,46.948,0,0,0,2.387,4.895C3.756,3.395,4.887,1.613,6.133,0Z" transform="translate(9570.414 1804.111) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79869" data-name="Path 79869" d="M14.649,0,12.6,4.23a8.946,8.946,0,0,1-.728,3.764C9,6.88,5.632,7.125,2.632,6.38,1.509,6.1.614,5.941,0,4.949,1.316,3.949,11.872,1.124,14.654,0Z" transform="translate(9550.066 1811.769) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79870" data-name="Path 79870" d="M3.382,0A5.684,5.684,0,0,1,7.331,1.475a3.629,3.629,0,0,1,.973,2.9C8.166,6.407,6.971,7.5,5.567,8.81a12.092,12.092,0,0,1-2.448-.334A5.176,5.176,0,0,1,.4,6.055a4.106,4.106,0,0,1-.132-3.22C.767,1.335,2.1.73,3.382,0Z" transform="translate(9804.702 1863.989) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79871" data-name="Path 79871" d="M0,.219C.869-.044.456-.07,1.088.14c2.474.833,3.94.386,6.37.228a1.952,1.952,0,0,1,.492.1C6.9,3.948,6.923,7.748,6.195,11.328c-.8.728-.827.816-1.887.921C1.763,10.371,1.395,3.834.219.754.15.57.081.395.009.219Z" transform="translate(9560.018 1829.768) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79872" data-name="Path 79872" d="M3.342,0c.158.026.316.052.465.088a6.9,6.9,0,0,1,4.7,2.992c.079.132.149.263.219.4a3.593,3.593,0,0,1,.176.421,3.716,3.716,0,0,1,.211.878,3.848,3.848,0,0,1,.035.447c0,.149,0,.3-.009.456a3.914,3.914,0,0,1-.052.447,3.562,3.562,0,0,1-.105.438C8.45,8.424,7.133,9.178,5.563,10.091A8.026,8.026,0,0,1,1.711,8.678,4.5,4.5,0,0,1,.009,4.87C.175,2.571,1.6,1.264,3.342,0Z" transform="translate(9634.089 1712.539) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79873" data-name="Path 79873" d="M3.34,0c1.9.307,3.975.86,5.2,2.457a4.2,4.2,0,0,1,.781,3.4C8.9,8.038,7.5,8.968,5.762,10.179A6.816,6.816,0,0,1,1.172,8.433,4,4,0,0,1,.005,5.4C.074,3.022,1.646,1.451,3.348.012Z" transform="translate(9677.683 1853.978) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79874" data-name="Path 79874" d="M4.4.011l.685.167a4.889,4.889,0,0,1,.175,2.773A41.476,41.476,0,0,0,3.54,13.138c-.069,5.458,2.3,8.836,5.887,12.616a35.319,35.319,0,0,1-4.22-2.527,12.248,12.248,0,0,1-1.044-.878,12.425,12.425,0,0,1-.948-.974A12.064,12.064,0,0,1,2.381,20.3c-.263-.369-.5-.755-.729-1.149a12.082,12.082,0,0,1-.6-1.22c-.176-.414-.334-.842-.474-1.273s-.246-.869-.345-1.316A11.46,11.46,0,0,1,.036,14,20.825,20.825,0,0,1,4.388,0Z" transform="translate(9836.427 1947.558) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79875" data-name="Path 79875" d="M2.625.137a21.444,21.444,0,0,1,2.9-.132A6.273,6.273,0,0,1,9.443,2.023,4.353,4.353,0,0,1,10.592,5.34c-.184,2.176-1.466,3.4-3.053,4.738-.369.035-.746.061-1.114.079A6.161,6.161,0,0,1,1.537,8.49,4.9,4.9,0,0,1,0,4.8C.08,2.761,1.177,1.436,2.617.138Z" transform="translate(9462.297 1908.412) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79877" data-name="Path 79877" d="M3.806.046A16.642,16.642,0,0,1,6.763.09a4.826,4.826,0,0,1,3.3,2.15,4.61,4.61,0,0,1,.588,3.439c-.544,2.509-2.062,3.641-4.124,4.957-2.045-.193-4-.509-5.4-2.167A4.662,4.662,0,0,1,.059,4.687C.428,2.423,2,1.221,3.8.036Z" transform="translate(9426.107 1842.626) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79880" data-name="Path 79880" d="M5.016.012A8.932,8.932,0,0,1,6.771.4a6.118,6.118,0,0,1,3.808,3.317q.105.263.184.527a5.441,5.441,0,0,1,.132.544,3.971,3.971,0,0,1,.069.551c.017.184.017.368.017.552a3.9,3.9,0,0,1-.044.551,5.39,5.39,0,0,1-.465,1.6c-.886,1.921-2.4,2.773-4.291,3.51-1.843-.2-4.045-.588-5.239-2.185A4.253,4.253,0,0,1,.109,5.738C.644,2.9,2.723,1.448,5,0Z" transform="translate(9419.874 2011.056) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79881" data-name="Path 79881" d="M4.071,0A13.577,13.577,0,0,1,5.958.176,5.86,5.86,0,0,1,9.985,2.694a5.251,5.251,0,0,1,.64,4.115c-.544,2.264-2,3.431-3.861,4.668a6.756,6.756,0,0,1-2.782-.2A6.481,6.481,0,0,1,.518,7.94a6.365,6.365,0,0,1-.4-1.211,5.227,5.227,0,0,1-.1-.632Q0,5.782,0,5.466a5.1,5.1,0,0,1,.044-.632c.026-.211.061-.421.105-.62a5.429,5.429,0,0,1,.421-1.2A5.6,5.6,0,0,1,4.08,0Z" transform="translate(9322.706 2061.065) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79884" data-name="Path 79884" d="M11.951,0A22,22,0,0,0,16.5,6.212c-.123.64-.369.9.061,1.474a8.122,8.122,0,0,0,2.527,1.878c.562.947.281,1.509.36,2.527,0,.088.069.167.105.245a13.891,13.891,0,0,0-7.292,5.037,1.948,1.948,0,0,0-.492-.1c-2.422.149-3.9.605-6.37-.228-.62-.21-.219-.184-1.088.079A38.745,38.745,0,0,0,0,12.233,8.974,8.974,0,0,0,.728,8.469a14.548,14.548,0,0,0,1.825,3.317,7.121,7.121,0,0,0,4.9,3.133,6.067,6.067,0,0,0,4.7-1.711c2.3-2.027,2.281-4.571,2.551-7.405L9.947,3.135c-2.132-.035-4.256.334-6.405.351L3.858,2.6A18.1,18.1,0,0,1,10.781.738c.781-.069.667-.176,1.158-.737Z" transform="translate(9562.646 1812.503) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79886" data-name="Path 79886" d="M4.434.038A9.2,9.2,0,0,1,6.207.047,5.919,5.919,0,0,1,10.989,7.8c-.64,2.334-2.194,3.335-4.159,4.545a7.384,7.384,0,0,1-1.3-.044A6.472,6.472,0,0,1,.863,9.673,5.614,5.614,0,0,1,.152,5.382C.635,2.715,2.3,1.5,4.427.038Z" transform="translate(9860.299 1977.302) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79888" data-name="Path 79888" d="M4.416.035A10.492,10.492,0,0,1,7.725.29a7.4,7.4,0,0,1,4.458,3.756,6.268,6.268,0,0,1,.334,4.992c-.781,2.185-2.088,3.141-4.089,4.159a13.149,13.149,0,0,1-2.817-.009A6.677,6.677,0,0,1,1.232,10.53,6.549,6.549,0,0,1,.2,5.107C.8,2.536,2.266,1.384,4.419.044Z" transform="translate(9313.953 1884.422) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79889" data-name="Path 79889" d="M4.414.263A22.315,22.315,0,0,1,7.573,0a6.446,6.446,0,0,1,4.265,2.4,6.136,6.136,0,0,1,1.246,4.764c-.345,2.747-1.956,4.01-4.054,5.59a15.244,15.244,0,0,1-3.632,0A7.071,7.071,0,0,1,.975,9.951,5.529,5.529,0,0,1,.115,5.7C.624,2.993,2.256,1.72,4.423.255Z" transform="translate(9313.999 1985.149) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79890" data-name="Path 79890" d="M4.77,0A10.228,10.228,0,0,1,6.4.107,8.3,8.3,0,0,1,12.307,3.3a6.016,6.016,0,0,1,.948,4.712c-.552,2.746-2.3,4.045-4.562,5.458a11.511,11.511,0,0,1-2.957,0,7.324,7.324,0,0,1-4.8-3.264,6.866,6.866,0,0,1-.72-5.17C.866,2.4,2.542,1.308,4.761.009Z" transform="translate(9311.405 1959.164) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79891" data-name="Path 79891" d="M4.568.067c3.018-.281,4.8.334,7.485,1.7,1.211,2.167,2.482,4.274,1.781,6.835-.685,2.5-2.843,3.825-4.931,5.063a6.281,6.281,0,0,1-2.176.184,7.945,7.945,0,0,1-5.572-3.3q-.2-.3-.369-.631A7.1,7.1,0,0,1,.26,8.56,6.561,6.561,0,0,1,.1,7.842C.066,7.6.032,7.36.013,7.114a5.134,5.134,0,0,1,0-.737,6.635,6.635,0,0,1,.069-.728,6.537,6.537,0,0,1,.138-.72C.813,2.5,2.506,1.251,4.568.067Z" transform="translate(9737.635 1671.063) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79892" data-name="Path 79892" d="M1.993,0c.245.1.5.167.737.281A4.057,4.057,0,0,1,4.722,2.65c-.351,1.632-.685,1.8-1.843,2.931a9.349,9.349,0,0,1-1.852-.132C.272,4.712-.087,4.458.018,3.3A5.318,5.318,0,0,1,1.992,0Z" transform="translate(9739.99 1674.758) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79897" data-name="Path 79897" d="M6.037,0c3.2.605,6.335,1.351,9.485,2.2q-3.461,10.464-6.3,21.111A89.986,89.986,0,0,0,0,22.34Q3.317,11.258,6.045,0Z" transform="translate(9611.423 1775.049) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79897-2" data-name="Path 79897" d="M10.452,0C16,1.048,21.419,2.339,26.874,3.813q-5.993,18.116-10.908,36.55A155.794,155.794,0,0,0,0,38.677Q5.742,19.491,10.466,0Z" transform="translate(9637.204 2118.76) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79899" data-name="Path 79899" d="M6.447.1a11.37,11.37,0,0,1,5.291.465,8.646,8.646,0,0,1,4.688,4.659c.105.263.2.527.281.8a6.612,6.612,0,0,1,.193.827c.052.281.088.552.114.842a8.115,8.115,0,0,1-.035,1.685c-.035.281-.079.562-.138.833a7.9,7.9,0,0,1-.228.816,7.779,7.779,0,0,1-.307.79c-1.255,2.834-3.361,3.843-6.125,4.9a16.053,16.053,0,0,1-3.615-.088,6.287,6.287,0,0,1-.807-.184c-.263-.079-.527-.167-.781-.263a7.707,7.707,0,0,1-.755-.345,7.622,7.622,0,0,1-.711-.421,7.236,7.236,0,0,1-.667-.492c-.211-.176-.421-.36-.614-.551s-.377-.4-.552-.614A7.159,7.159,0,0,1,1.2,13.1,8.044,8.044,0,0,1,.346,6.54c.965-3.264,3.24-4.87,6.1-6.431Z" transform="translate(9648.251 1818.609) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79901" data-name="Path 79901" d="M6.78.063a13.1,13.1,0,0,1,4.378.307,8.633,8.633,0,0,1,5.107,4.481,9.1,9.1,0,0,1,.158,7.037c-1.193,3.045-3.343,4.475-6.265,5.677A12.485,12.485,0,0,1,6,17.206a8.971,8.971,0,0,1-5.17-4.553c-.123-.272-.237-.544-.334-.827a7.232,7.232,0,0,1-.245-.851c-.069-.29-.123-.579-.167-.878A7.4,7.4,0,0,1,0,9.211c-.009-.3,0-.6.009-.887a7.8,7.8,0,0,1,.1-.887,7.655,7.655,0,0,1,.184-.869c.079-.29.167-.57.272-.851C1.706,2.656,3.882,1.261,6.777.05Z" transform="translate(9437.604 1972.451) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79902" data-name="Path 79902" d="M1.306,0A7.374,7.374,0,0,1,3.5.535,3.428,3.428,0,0,1,4.879,3.079,5.047,5.047,0,0,1,3.4,6.7a6.273,6.273,0,0,1-1.439-.465A3.615,3.615,0,0,1,.053,3.757C-.2,2.22.5,1.22,1.306,0Z" transform="translate(9439.712 1979.299) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79906" data-name="Path 79906" d="M9.629.011A12.5,12.5,0,0,1,13.6.748a10.106,10.106,0,0,1,5.8,5.993,10.5,10.5,0,0,1,.333,1.053,9.35,9.35,0,0,1,.219,1.079c.052.369.088.728.105,1.1s.017.737,0,1.1a9.136,9.136,0,0,1-.123,1.1,10.941,10.941,0,0,1-.237,1.079,9.268,9.268,0,0,1-.351,1.053c-.132.345-.289.676-.456,1.009-1.641,3.1-4.124,4.361-7.353,5.361a12.276,12.276,0,0,1-5.984-1.088c-.307-.158-.6-.325-.886-.509a9.011,9.011,0,0,1-.833-.6,9.3,9.3,0,0,1-.764-.676c-.245-.237-.474-.492-.694-.746a9.386,9.386,0,0,1-.614-.816,8.826,8.826,0,0,1-.527-.869c-.158-.3-.307-.605-.438-.921a8.968,8.968,0,0,1-.345-.965,10.053,10.053,0,0,1-.414-2.106C.008,11.019,0,10.668,0,10.308A8.684,8.684,0,0,1,.069,9.238,10.221,10.221,0,0,1,.253,8.185c.079-.351.176-.693.289-1.034a9.834,9.834,0,0,1,.395-1c.149-.325.316-.64.5-.956C3.341,2.019,6.167.842,9.615,0Z" transform="translate(9775.975 1897.821) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79907" data-name="Path 79907" d="M10.381.031a10.094,10.094,0,0,1,5.028.9,9.858,9.858,0,0,1,5.282,5.852,10.161,10.161,0,0,1-.562,7.923c-1.817,3.5-4.615,4.563-8.16,5.6A15.883,15.883,0,0,1,7.212,19.22C2.931,17.456,1.544,13.587,0,9.595A18.975,18.975,0,0,1,1.886,4.646C3.852,1.531,7.019.777,10.371.031Z" transform="translate(9519.424 1762.617) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79908" data-name="Path 79908" d="M22.076,0C29.3.879,33.657,11.462,39.708,15.795a76.548,76.548,0,0,1,10.5.858,6.06,6.06,0,0,1,4.733,3.608c-1,5.411-7.248,12.379-10.224,17.312,1.1,3.914,2.855,8.785,1.577,12.819-.519,1.617-1.279,1.638-2.636,2.436-4.134.06-7.289-1.857-10.883-3.633-8.628-2.4-12.48,5.072-20.394,5.072-1.617,0-1.9-.819-2.956-1.857-1.217-5.63-.08-12.48.26-18.25C6.734,31.244,1.383,27.09.145,23.118c-.46-1.478.26-1.977.959-3.235,3.175-2.137,6.291-2.476,10.025-3.1C15.161,11.537,18.536,5.626,22.05.016Z" transform="translate(9876.685 1668.821) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79908-2" data-name="Path 79908" d="M17.722,0c5.8.705,9.3,9.2,14.154,12.679a61.45,61.45,0,0,1,8.432.689,4.865,4.865,0,0,1,3.8,2.9c-.8,4.344-5.818,9.938-8.207,13.9.882,3.142,2.292,7.053,1.266,10.29-.417,1.3-1.026,1.315-2.116,1.956-3.318.048-5.851-1.491-8.736-2.917C19.387,37.568,16.3,43.563,9.942,43.563c-1.3,0-1.523-.657-2.373-1.491-.977-4.52-.064-10.018.209-14.65C5.406,25.082,1.11,21.747.116,18.558c-.369-1.186.209-1.587.77-2.6,2.549-1.715,5.05-1.987,8.047-2.485C12.171,9.261,14.88,4.516,17.7.013Z" transform="translate(9745.791 2010.372) rotate(23.003)" opacity="0.69" fill="url(#linear-gradient)"/>
            <path id="Path_79909" data-name="Path 79909" d="M9,.01a17.5,17.5,0,0,1,7.4,1.027,11.525,11.525,0,0,1,6.309,6.291,11.271,11.271,0,0,1-.272,8.739c-1.624,3.439-4.159,4.957-7.607,6.2-2.826.414-5.089.438-7.713-.827a11.8,11.8,0,0,1-1.132-.614A12.076,12.076,0,0,1,4.914,20.1a12.735,12.735,0,0,1-1.878-1.763c-.281-.325-.544-.667-.8-1.018a12.42,12.42,0,0,1-1.7-3.457A11.109,11.109,0,0,1,1.378,5.1C3.1,2.134,5.79.755,9.011,0Z" transform="translate(10018.654 1967.515) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79910" data-name="Path 79910" d="M1.392.146C3.155-.03,4.261-.205,5.873.637c.456.6.772.807.833,1.6.132,1.886-.974,3.124-2.088,4.5a4.067,4.067,0,0,1-3.369-.088A3.387,3.387,0,0,1,.134,4.813C-.3,2.891.389,1.725,1.38.154Z" transform="translate(10022.597 1976.099) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79915" data-name="Path 79915" d="M8.882.134c2.86-.167,5.466-.377,8.2.728a11.372,11.372,0,0,1,1.149.535,10.484,10.484,0,0,1,1.088.649,12.879,12.879,0,0,1,1.956,1.606c.3.3.579.614.851.939a12.217,12.217,0,0,1,.755,1.018,11.36,11.36,0,0,1,.64,1.088c.193.377.377.755.535,1.149a12.844,12.844,0,0,1,.132,9.906c-1.57,3.7-4.606,5.774-8.231,7.177a14.823,14.823,0,0,1-8.09-.474A12.737,12.737,0,0,1,1,17.279,13.6,13.6,0,0,1,1.249,6.46C2.834,3.22,5.584,1.336,8.891.125Z" transform="translate(9622.997 1644.84) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79916" data-name="Path 79916" d="M2.272,0a3.96,3.96,0,0,1,.913.219C4.3.676,4.378.895,4.808,1.948c-.325,1.6-1.255,2.465-2.316,3.65C1.282,4.9.649,4.729,0,3.466.225,2,1.313,1.062,2.272,0Z" transform="translate(9626.862 1654.418) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79917" data-name="Path 79917" d="M12.146.007A14.813,14.813,0,0,1,18.9,1.4,12.064,12.064,0,0,1,24.9,9.133,13.443,13.443,0,0,1,23.228,19.9c-2.281,3.447-5.431,4.8-9.327,5.653a16.471,16.471,0,0,1-7.5-1.8A12.169,12.169,0,0,1,.337,16.146,12.874,12.874,0,0,1,1.968,6.38C4.407,2.411,7.821.963,12.146.007Z" transform="translate(9923.029 1759.944) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79918" data-name="Path 79918" d="M2.985,0C4.678.307,6.258.579,7.477,1.878A3.891,3.891,0,0,1,8.636,5.256C8.311,7.169,7.065,8.2,5.564,9.274,3.731,9.187,2.2,8.959.966,7.441A4.395,4.395,0,0,1,.045,4.063C.309,2.036,1.4,1.141,2.985.009Z" transform="translate(9926.598 1769.069) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79922" data-name="Path 79922" d="M11.773.024a15.5,15.5,0,0,1,8.617,2.053,12.193,12.193,0,0,1,2.045,1.492,10.759,10.759,0,0,1,.9.9c.281.316.552.64.807.983a12.326,12.326,0,0,1,.7,1.062,11.363,11.363,0,0,1,.588,1.123c.176.386.334.781.474,1.175a11.463,11.463,0,0,1,.351,1.22A13.525,13.525,0,0,1,24.3,20.495c-2.307,3.378-5.528,4.8-9.4,5.642-3.106.035-6.212-.281-8.914-1.93a12.415,12.415,0,0,1-5.6-8A13.512,13.512,0,0,1,2.236,5.526c2.4-3.439,5.6-4.7,9.556-5.492Z" transform="translate(9381.995 1890.242) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79923" data-name="Path 79923" d="M2.4.018C2.7.009,3.006,0,3.3,0A4.531,4.531,0,0,1,7.226,1.586a3.171,3.171,0,0,1,.57,2.6C7.524,5.867,6.2,6.727,4.91,7.64a4.34,4.34,0,0,1-3.624-.631A3.294,3.294,0,0,1,0,4.219C.031,2.212,1.014,1.263,2.4.018Z" transform="translate(9291.762 1943.451) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79934" data-name="Path 79934" d="M32.72,0a31.8,31.8,0,0,1,5.335,4.115A9.832,9.832,0,0,0,34.879,7.09C23.411,11.1,10.108,22.857,5.019,33.948A16.086,16.086,0,0,0,3.949,38.09,8.033,8.033,0,0,1,0,37.941c1.08-9.827,10.766-20.357,18.295-26.034C24.06,7.564,28.228,6.1,32.72.009Z" transform="translate(9553.95 2137.842) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79955" data-name="Path 79955" d="M6.693,0a5.336,5.336,0,0,1,3.7,1.316,2.812,2.812,0,0,1-.044,2.551C9.2,6.511,6.465,7.941,4.017,9.081a8.512,8.512,0,0,1-2.088.263C.788,9.213.639,8.818.034,7.958A4.7,4.7,0,0,1,.358,5.6,10.062,10.062,0,0,1,6.685,0Z" transform="translate(9463.817 1698.916) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79967" data-name="Path 79967" d="M4.831.041A19.137,19.137,0,0,1,7.577.058a4.73,4.73,0,0,1,3.545,1.956,3.843,3.843,0,0,1,.4,3.159c-.57,2.623-2.685,3.87-4.852,5.089a19.232,19.232,0,0,1-3.168.29A3.091,3.091,0,0,1,.865,9.412,3.938,3.938,0,0,1,.128,5.955C.733,3,2.374,1.559,4.831.041Z" transform="translate(9949.693 1948.124) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79968" data-name="Path 79968" d="M8.122,0A4.255,4.255,0,0,1,8.6.019C10,.1,11.64.3,12.474,1.6a4.855,4.855,0,0,1,.414,3.439C12.019,9.3,8.782,13.207,5.2,15.5a10.509,10.509,0,0,1-2.036-.069A3.59,3.59,0,0,1,.524,13.682,5.366,5.366,0,0,1,.3,9.61C1.463,5.284,4.385,2.24,8.123.011Z" transform="translate(9933.091 1951.439) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79971" data-name="Path 79971" d="M11.323,0a4.6,4.6,0,0,1,3.053,1.237c1.72,1.792,2.15,4.334,2.132,6.73-.088,10.7-3.792,21.419-11.582,28.938a4.4,4.4,0,0,1-.43-.377C1.206,32.64-.531,22.41.144,17.514,1.171,9.994,5.514,4.493,11.323.009Z" transform="translate(9371.388 1984.734) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79979" data-name="Path 79979" d="M18.318,36.589c-4.063,2.58-8.968,5.653-13.723,6.651a4.323,4.323,0,0,1-3.87-.631,3.418,3.418,0,0,1-.7-1.869c-.456-5,5.905-15.294,8.932-19.3C17.563,10.091,27.733,2.045,42.132,0c-2.545,15-11.9,27.42-23.813,36.58Z" transform="translate(9693.822 1705.651) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79983" data-name="Path 79983" d="M6.993,0c3.229,1.553,9.591,16.926,12.232,20.936L12.5,25.6l-.816.219C9.257,24.042,2.15,8.95,0,5.046L7,0Z" transform="translate(9613.668 1871.637) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_79983-2" data-name="Path 79983" d="M15.795,0c7.293,3.508,21.662,38.229,27.627,47.286l-15.2,10.543-1.844.5C20.908,54.3,4.855,20.214,0,11.4L15.815,0Z" transform="translate(9510.414 2250.637) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_80035" data-name="Path 80035" d="M.511,8.327C.458,8.134.4,7.941.344,7.748-.121,5.887-.27,4.072.914,2.431A6.911,6.911,0,0,1,5.372,0C6.451,1.5,7.688,2.869,7.478,4.835A2.231,2.231,0,0,1,6.444,6.475C5.057,7.66,2.223,7.809.5,8.327Z" transform="translate(9430.682 2039.056) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_80040" data-name="Path 80040" d="M5.272.026A9.825,9.825,0,0,1,8.739.394a8.471,8.471,0,0,1,5.08,4.335,6.768,6.768,0,0,1,.255,5.17c-.9,2.413-2.641,3.6-4.887,4.633a17.183,17.183,0,0,1-3.492-.106A7.457,7.457,0,0,1,1,11.181a6.862,6.862,0,0,1-.72-5.51C1.018,2.881,2.9,1.459,5.264.029Z" transform="translate(9363.724 2118.76) rotate(23.003)" fill="url(#linear-gradient)"/>
            <path id="Path_80042" data-name="Path 80042" d="M5.115.2C6.186.133,7.256.08,8.326.027,10.7-.07,13.354,0,15.091,1.939a5.3,5.3,0,0,1,1.369,4.3c-.4,3.615-3.422,7.292-6.186,9.459a7.08,7.08,0,0,1-4.606.414c-2.08-.711-3.723-2.8-4.607-4.73A10.043,10.043,0,0,1,.605,3.537C1.491,1.475,3.15.9,5.115.194Z" transform="translate(9367.469 2002.052) rotate(23.003)" fill="url(#linear-gradient)"/>
          </g>
          <g id="Group_58" data-name="Group 58" transform="translate(1086.756 706.735)">
            <path id="Path_79866-2" data-name="Path 79866" d="M0,8.024A40.453,40.453,0,0,1,.676,3.2,4.93,4.93,0,0,1,2.538,0l.808.21a4.141,4.141,0,0,1,.4,2.915C2.5,4.741,1.362,6.523,0,8.023Z" transform="translate(466.439 335.672) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79870-2" data-name="Path 79870" d="M3.384,0A5.687,5.687,0,0,1,7.335,1.476a3.631,3.631,0,0,1,.974,2.9C8.171,6.41,6.975,7.508,5.57,8.815A12.1,12.1,0,0,1,3.12,8.482,5.179,5.179,0,0,1,.4,6.058,4.108,4.108,0,0,1,.267,2.836C.767,1.336,2.1.731,3.384,0Z" transform="translate(235.56 273.853) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79872-2" data-name="Path 79872" d="M3.344,0c.158.026.316.052.466.088A6.9,6.9,0,0,1,8.516,3.081c.079.132.149.263.219.4a3.6,3.6,0,0,1,.176.421,3.719,3.719,0,0,1,.211.878,3.85,3.85,0,0,1,.035.448c0,.149,0,.3-.009.457a3.915,3.915,0,0,1-.052.448,3.564,3.564,0,0,1-.106.439C8.455,8.429,7.137,9.183,5.566,10.1A8.031,8.031,0,0,1,1.712,8.683a4.5,4.5,0,0,1-1.7-3.81C.176,2.572,1.6,1.264,3.344,0Z" transform="translate(406.173 425.303) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79873-2" data-name="Path 79873" d="M3.342,0c1.9.308,3.977.86,5.206,2.458a4.2,4.2,0,0,1,.781,3.4C8.908,8.042,7.5,8.973,5.765,10.184A6.82,6.82,0,0,1,1.173,8.437,4,4,0,0,1,.005,5.4C.074,3.023,1.647,1.452,3.35.012Z" transform="translate(362.579 283.864) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79874-2" data-name="Path 79874" d="M4.4.011l.685.167a4.892,4.892,0,0,1,.175,2.774,41.5,41.5,0,0,0-1.72,10.193c-.069,5.461,2.3,8.841,5.891,12.623A35.339,35.339,0,0,1,5.21,23.239a12.254,12.254,0,0,1-1.045-.878,12.432,12.432,0,0,1-.948-.975,12.071,12.071,0,0,1-.834-1.071c-.264-.369-.5-.755-.729-1.15a12.089,12.089,0,0,1-.6-1.22C.881,17.531.723,17.1.583,16.672S.336,15.8.238,15.355a11.467,11.467,0,0,1-.2-1.343A20.837,20.837,0,0,1,4.39,0Z" transform="translate(203.835 190.283) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79875-2" data-name="Path 79875" d="M2.626.137A21.456,21.456,0,0,1,5.532.005,6.277,6.277,0,0,1,9.448,2.024,4.356,4.356,0,0,1,10.6,5.343c-.184,2.177-1.466,3.406-3.055,4.741-.369.035-.746.061-1.115.079A6.165,6.165,0,0,1,1.538,8.5,4.906,4.906,0,0,1,0,4.8C.08,2.762,1.178,1.437,2.618.138Z" transform="translate(577.965 229.43) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79877-2" data-name="Path 79877" d="M3.808.046A16.651,16.651,0,0,1,6.766.09a4.828,4.828,0,0,1,3.3,2.151,4.612,4.612,0,0,1,.588,3.441c-.544,2.511-2.063,3.643-4.126,4.96-2.046-.193-4-.509-5.4-2.169A4.665,4.665,0,0,1,.059,4.689C.428,2.424,2,1.222,3.8.036Z" transform="translate(614.154 295.216) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79880-2" data-name="Path 79880" d="M5.018.012A8.937,8.937,0,0,1,6.775.4a6.121,6.121,0,0,1,3.81,3.319q.106.264.184.527a5.444,5.444,0,0,1,.132.544,3.973,3.973,0,0,1,.069.552c.017.184.017.368.017.552a3.9,3.9,0,0,1-.044.552,5.393,5.393,0,0,1-.465,1.6c-.886,1.922-2.4,2.774-4.293,3.512-1.844-.2-4.047-.588-5.242-2.186A4.255,4.255,0,0,1,.109,5.742C.644,2.906,2.725,1.449,5.006,0Z" transform="translate(620.388 126.786) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79881-2" data-name="Path 79881" d="M4.073,0A13.585,13.585,0,0,1,5.961.176,5.863,5.863,0,0,1,9.991,2.7a5.254,5.254,0,0,1,.641,4.117c-.544,2.265-2,3.433-3.863,4.67a6.76,6.76,0,0,1-2.783-.2A6.484,6.484,0,0,1,.518,7.945a6.368,6.368,0,0,1-.4-1.212,5.23,5.23,0,0,1-.1-.632Q0,5.785,0,5.469a5.1,5.1,0,0,1,.044-.633c.026-.211.061-.421.106-.621a5.432,5.432,0,0,1,.421-1.2A5.6,5.6,0,0,1,4.083,0Z" transform="translate(717.556 76.777) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79886-2" data-name="Path 79886" d="M4.437.038A9.209,9.209,0,0,1,6.21.047,5.922,5.922,0,0,1,11,7.808c-.641,2.336-2.195,3.336-4.161,4.548a7.388,7.388,0,0,1-1.3-.044A6.476,6.476,0,0,1,.864,9.678,5.617,5.617,0,0,1,.153,5.385C.635,2.716,2.3,1.5,4.429.038Z" transform="translate(179.963 160.54) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79888-2" data-name="Path 79888" d="M4.418.035A10.5,10.5,0,0,1,7.729.29a7.4,7.4,0,0,1,4.46,3.758,6.272,6.272,0,0,1,.334,5c-.782,2.186-2.089,3.143-4.091,4.161A13.157,13.157,0,0,1,5.614,13.2a6.681,6.681,0,0,1-4.381-2.66A6.553,6.553,0,0,1,.2,5.11C.8,2.538,2.268,1.385,4.421.044Z" transform="translate(726.309 253.419) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79889-2" data-name="Path 79889" d="M4.416.264A22.328,22.328,0,0,1,7.577,0a6.45,6.45,0,0,1,4.267,2.406A6.139,6.139,0,0,1,13.09,7.173c-.345,2.748-1.958,4.012-4.056,5.593a15.252,15.252,0,0,1-3.634,0A7.075,7.075,0,0,1,.975,9.956,5.532,5.532,0,0,1,.115,5.7c.509-2.7,2.142-3.977,4.31-5.443Z" transform="translate(726.263 152.693) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79890-2" data-name="Path 79890" d="M4.772,0A10.234,10.234,0,0,1,6.405.107a8.3,8.3,0,0,1,5.909,3.2,6.02,6.02,0,0,1,.948,4.715c-.552,2.748-2.3,4.047-4.565,5.461a11.518,11.518,0,0,1-2.958,0,7.328,7.328,0,0,1-4.8-3.266,6.869,6.869,0,0,1-.72-5.173C.866,2.406,2.543,1.308,4.764.009Z" transform="translate(728.857 178.678) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79891-2" data-name="Path 79891" d="M4.571.067c3.02-.281,4.8.334,7.489,1.7,1.211,2.169,2.483,4.277,1.782,6.839-.685,2.5-2.845,3.828-4.934,5.066a6.284,6.284,0,0,1-2.177.184,7.95,7.95,0,0,1-5.575-3.3q-.2-.3-.369-.632A7.1,7.1,0,0,1,.26,8.565,6.565,6.565,0,0,1,.1,7.847C.066,7.6.032,7.364.013,7.118a5.137,5.137,0,0,1,0-.737,6.639,6.639,0,0,1,.069-.728,6.54,6.54,0,0,1,.138-.72C.813,2.5,2.507,1.252,4.571.067Z" transform="translate(302.627 466.778) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79892-2" data-name="Path 79892" d="M1.994,0c.246.1.5.167.737.281a4.059,4.059,0,0,1,1.993,2.37c-.351,1.633-.685,1.8-1.844,2.932a9.354,9.354,0,0,1-1.853-.132C.272,4.715-.087,4.46.018,3.3A5.321,5.321,0,0,1,1.993,0Z" transform="translate(300.272 463.084) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79896" data-name="Path 79896" d="M4.249,0C11.255,3.134,17.918,7.094,25,10.026c-1.4,2.66-3.134,5.173-4.767,7.7-3.327-.43-16.279-8-20.237-9.973A76.309,76.309,0,0,1,4.25.008Z" transform="translate(322.222 240.655) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79897-3" data-name="Path 79897" d="M6.04,0c3.2.606,6.338,1.352,9.491,2.2q-3.463,10.469-6.3,21.123A90.037,90.037,0,0,0,0,22.352Q3.319,11.264,6.049,0Z" transform="translate(428.839 362.792) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79899-2" data-name="Path 79899" d="M6.45.1a11.376,11.376,0,0,1,5.294.466,8.651,8.651,0,0,1,4.69,4.661c.106.263.2.527.281.8a6.615,6.615,0,0,1,.193.828c.052.281.088.552.115.843a8.12,8.12,0,0,1-.035,1.686c-.035.281-.079.562-.138.834a7.9,7.9,0,0,1-.228.817,7.783,7.783,0,0,1-.307.79c-1.255,2.836-3.363,3.845-6.128,4.908a16.062,16.062,0,0,1-3.617-.088,6.291,6.291,0,0,1-.808-.184c-.263-.079-.527-.167-.782-.263a7.711,7.711,0,0,1-.755-.345,7.626,7.626,0,0,1-.711-.421,7.24,7.24,0,0,1-.667-.492c-.211-.176-.421-.36-.615-.552s-.377-.4-.552-.615A7.163,7.163,0,0,1,1.2,13.1,8.049,8.049,0,0,1,.346,6.544c.966-3.266,3.242-4.873,6.1-6.435Z" transform="translate(392.011 319.233) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79901-2" data-name="Path 79901" d="M6.784.063a13.1,13.1,0,0,1,4.381.308,8.638,8.638,0,0,1,5.11,4.484,9.11,9.11,0,0,1,.158,7.041c-1.194,3.047-3.345,4.477-6.269,5.68A12.492,12.492,0,0,1,6,17.216,8.976,8.976,0,0,1,.829,12.66c-.123-.272-.237-.544-.334-.828a7.236,7.236,0,0,1-.246-.852C.18,10.691.127,10.4.082,10.1A7.4,7.4,0,0,1,0,9.216c-.009-.3,0-.6.009-.887a7.808,7.808,0,0,1,.1-.887,7.659,7.659,0,0,1,.184-.869c.079-.29.167-.57.272-.851C1.707,2.657,3.884,1.261,6.781.05Z" transform="translate(602.658 165.391) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79902-2" data-name="Path 79902" d="M1.307,0A7.378,7.378,0,0,1,3.5.535a3.43,3.43,0,0,1,1.38,2.546A5.05,5.05,0,0,1,3.407,6.707a6.276,6.276,0,0,1-1.44-.466A3.617,3.617,0,0,1,.053,3.759C-.2,2.221.5,1.22,1.307,0Z" transform="translate(600.55 158.543) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79906-2" data-name="Path 79906" d="M9.635.011a12.5,12.5,0,0,1,3.977.737,10.112,10.112,0,0,1,5.8,6A10.5,10.5,0,0,1,19.748,7.8a9.355,9.355,0,0,1,.219,1.079c.052.369.088.728.106,1.1s.017.737,0,1.1a9.14,9.14,0,0,1-.123,1.1,10.947,10.947,0,0,1-.237,1.079,9.273,9.273,0,0,1-.351,1.053c-.132.345-.29.676-.457,1.01-1.642,3.1-4.126,4.363-7.357,5.364a12.283,12.283,0,0,1-5.987-1.088c-.307-.158-.6-.325-.886-.509a9.016,9.016,0,0,1-.834-.6,9.31,9.31,0,0,1-.764-.676c-.246-.237-.474-.492-.694-.746a9.391,9.391,0,0,1-.615-.817,8.831,8.831,0,0,1-.527-.869c-.158-.3-.307-.606-.439-.922a8.973,8.973,0,0,1-.345-.966,10.059,10.059,0,0,1-.414-2.107C.008,11.025,0,10.674,0,10.314A8.689,8.689,0,0,1,.069,9.243,10.227,10.227,0,0,1,.253,8.19c.079-.351.176-.693.29-1.035a9.84,9.84,0,0,1,.4-1c.149-.325.316-.641.5-.957C3.343,2.02,6.17.843,9.62,0Z" transform="translate(264.287 240.02) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79907-2" data-name="Path 79907" d="M10.387.031a10.1,10.1,0,0,1,5.031.9A9.864,9.864,0,0,1,20.7,6.79a10.166,10.166,0,0,1-.562,7.928c-1.818,3.5-4.618,4.566-8.165,5.6a15.892,15.892,0,0,1-4.759-1.088C2.932,17.466,1.545,13.594,0,9.6A18.986,18.986,0,0,1,1.887,4.649C3.854,1.532,7.023.777,10.377.031Z" transform="translate(520.838 375.225) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79907-3" data-name="Path 79907" d="M10.387.031a10.1,10.1,0,0,1,5.031.9A9.864,9.864,0,0,1,20.7,6.79a10.166,10.166,0,0,1-.562,7.928c-1.818,3.5-4.618,4.566-8.165,5.6a15.892,15.892,0,0,1-4.759-1.088C2.932,17.466,1.545,13.594,0,9.6A18.986,18.986,0,0,1,1.887,4.649C3.854,1.532,7.023.777,10.377.031Z" transform="translate(520.838 214.099) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79907-4" data-name="Path 79907" d="M29.968.089A29.138,29.138,0,0,1,44.482,2.7,28.458,28.458,0,0,1,59.73,19.591a29.331,29.331,0,0,1-1.622,22.872C52.864,52.57,44.784,55.635,34.551,58.622a45.851,45.851,0,0,1-13.731-3.14C8.46,50.391,4.458,39.221,0,27.7A54.776,54.776,0,0,1,5.445,13.412C11.118,4.421,20.263,2.242,29.938.089Z" transform="translate(551.334 132.446) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79908-3" data-name="Path 79908" d="M9.707,0c3.178.386,5.092,5.039,7.752,6.945a33.657,33.657,0,0,1,4.618.377,2.665,2.665,0,0,1,2.081,1.586c-.439,2.379-3.187,5.443-4.5,7.612.483,1.721,1.255,3.863.693,5.636-.228.711-.562.72-1.159,1.071-1.818.026-3.2-.817-4.785-1.6-3.794-1.053-5.487,2.23-8.967,2.23-.711,0-.834-.36-1.3-.817-.535-2.476-.035-5.487.115-8.024-1.3-1.282-3.652-3.108-4.2-4.855-.2-.65.115-.869.421-1.422A10.126,10.126,0,0,1,4.893,7.381C6.666,5.073,8.15,2.474,9.7.007Z" transform="translate(178.961 380.509) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79909-2" data-name="Path 79909" d="M9.009.01a17.514,17.514,0,0,1,7.4,1.027,11.531,11.531,0,0,1,6.312,6.295,11.277,11.277,0,0,1-.272,8.744c-1.624,3.441-4.161,4.96-7.612,6.208-2.828.414-5.092.439-7.717-.828a11.8,11.8,0,0,1-1.133-.615,12.083,12.083,0,0,1-1.071-.728,12.742,12.742,0,0,1-1.879-1.764c-.282-.325-.544-.667-.8-1.019a12.427,12.427,0,0,1-1.7-3.459A11.115,11.115,0,0,1,1.379,5.1C3.1,2.135,5.793.755,9.016,0Z" transform="translate(21.607 170.327) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79910-2" data-name="Path 79910" d="M1.392.146C3.157-.03,4.263-.205,5.876.637c.457.6.773.808.834,1.6.132,1.887-.975,3.125-2.089,4.5A4.07,4.07,0,0,1,1.25,6.651,3.389,3.389,0,0,1,.134,4.816C-.3,2.893.389,1.726,1.381.155Z" transform="translate(17.665 161.742) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79915-2" data-name="Path 79915" d="M8.887.135c2.862-.167,5.469-.377,8.2.728a11.378,11.378,0,0,1,1.15.535,10.49,10.49,0,0,1,1.088.65,12.886,12.886,0,0,1,1.958,1.606c.3.3.579.615.852.939a12.223,12.223,0,0,1,.755,1.018A11.367,11.367,0,0,1,23.531,6.7c.193.377.377.755.535,1.15a12.851,12.851,0,0,1,.132,9.912c-1.571,3.7-4.609,5.777-8.235,7.181a14.832,14.832,0,0,1-8.094-.474A12.744,12.744,0,0,1,1,17.289,13.61,13.61,0,0,1,1.249,6.464C2.836,3.222,5.587,1.337,8.9.125Z" transform="translate(417.265 493.002) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79916-2" data-name="Path 79916" d="M2.273,0a3.963,3.963,0,0,1,.913.219C4.3.676,4.381.9,4.811,1.949,4.486,3.546,3.556,4.415,2.494,5.6,1.282,4.9.65,4.732,0,3.467.226,2,1.314,1.062,2.273,0Z" transform="translate(413.4 483.423) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79917-2" data-name="Path 79917" d="M12.153.007a14.821,14.821,0,0,1,6.76,1.4,12.071,12.071,0,0,1,6,7.734A13.451,13.451,0,0,1,23.241,19.91c-2.282,3.449-5.434,4.8-9.332,5.656a16.48,16.48,0,0,1-7.506-1.8A12.175,12.175,0,0,1,.337,16.155,12.881,12.881,0,0,1,1.969,6.384C4.41,2.413,7.825.964,12.153.007Z" transform="translate(117.232 377.897) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79918-2" data-name="Path 79918" d="M2.986,0c1.695.307,3.275.579,4.5,1.879a3.893,3.893,0,0,1,1.159,3.38C8.316,7.173,7.069,8.209,5.568,9.28,3.733,9.192,2.2,8.964.967,7.445a4.4,4.4,0,0,1-.921-3.38C.309,2.037,1.4,1.142,2.986.009Z" transform="translate(113.664 368.773) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79919" data-name="Path 79919" d="M.34,30.416C.2,28.888.05,27.352.006,25.824c-.158-6,2.792-12.3,6.865-16.549C12.727,3.165,22.472.154,30.769,0c3.67-.061,7.322.483,10.992.414h.4c1.809.263,3.582.57,5.364.966-1.053,2.853-2.476,9.043-5.426,10.316-1.738.746-3.608.334-5.127,1.6L35.914,15.7a4.9,4.9,0,0,0-.175-2.774l-.685-.167-.264-.834c-3.345-1.764-11.431.2-14.952,1.229C9.751,16.123,5.256,21.445.331,30.418Z" transform="translate(200.465 259.446) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79919-2" data-name="Path 79919" d="M.34,0C.2,1.529.05,3.065.006,4.593c-.158,6,2.792,12.3,6.865,16.549,5.856,6.111,15.6,9.122,23.9,9.271,3.67.061,7.322-.483,10.992-.414h.4c1.809-.263,3.582-.57,5.364-.966-1.053-2.853-2.476-9.043-5.426-10.316-1.738-.746-3.608-.334-5.127-1.6l-1.053-2.405a4.9,4.9,0,0,1-.175,2.774l-.685.167-.264.834c-3.345,1.764-11.431-.2-14.952-1.229C9.751,14.294,5.256,8.973.331,0Z" transform="translate(139.525 49.808) rotate(-23.003)" fill="url(#linear-gradient-81)"/>
            <path id="Path_79919-3" data-name="Path 79919" d="M.34,30.416C.2,28.888.05,27.352.006,25.824c-.158-6,2.792-12.3,6.865-16.549C12.727,3.165,22.472.154,30.769,0c3.67-.061,7.322.483,10.992.414h.4c1.809.263,3.582.57,5.364.966-1.053,2.853-2.476,9.043-5.426,10.316-1.738.746-3.608.334-5.127,1.6L35.914,15.7a4.9,4.9,0,0,0-.175-2.774l-.685-.167-.264-.834c-3.345-1.764-11.431.2-14.952,1.229C9.751,16.123,5.256,21.445.331,30.418Z" transform="translate(522.252 -72.637) rotate(44)" fill="url(#linear-gradient)"/>
            <path id="Path_79922-2" data-name="Path 79922" d="M26.317.047A39.241,39.241,0,0,1,45.58,3.993,27.482,27.482,0,0,1,50.151,6.86a22.685,22.685,0,0,1,2,1.722c.627.607,1.233,1.231,1.8,1.889a23.733,23.733,0,0,1,1.569,2.04,20.958,20.958,0,0,1,1.314,2.158c.393.742.746,1.5,1.059,2.259a19.718,19.718,0,0,1,.784,2.344,23.04,23.04,0,0,1-4.359,20.117c-5.158,6.492-12.357,9.224-21.007,10.844-6.944.068-13.886-.539-19.927-3.71-6.44-3.442-10.961-9-12.513-15.38A22.978,22.978,0,0,1,5,10.62C10.352,4.01,17.51,1.581,26.357.064Z" transform="translate(685.917 285.36) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79923-2" data-name="Path 79923" d="M2.4.018C2.7.009,3.007,0,3.306,0A4.534,4.534,0,0,1,7.23,1.586a3.173,3.173,0,0,1,.57,2.6C7.528,5.871,6.2,6.731,4.913,7.644a4.342,4.342,0,0,1-3.626-.632A3.3,3.3,0,0,1,0,4.221C.031,2.213,1.015,1.264,2.4.018Z" transform="translate(748.5 194.39) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79934-2" data-name="Path 79934" d="M32.738,0a31.814,31.814,0,0,1,5.338,4.117A9.837,9.837,0,0,0,34.9,7.094C23.424,11.106,10.114,22.87,5.022,33.967a16.1,16.1,0,0,0-1.071,4.144A8.038,8.038,0,0,1,0,37.962C1.08,28.13,10.772,17.594,18.305,11.914,24.073,7.568,28.243,6.1,32.739.009Z" transform="translate(556.303 306.22) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79934-3" data-name="Path 79934" d="M55.213,0a53.655,53.655,0,0,1,9,6.944c-1.717,1.14-4.472,3.154-5.359,5.02C39.505,18.731,17.058,38.571,8.47,57.286a27.145,27.145,0,0,0-1.807,6.989A13.556,13.556,0,0,1,0,64.024C1.822,47.441,18.168,29.673,30.872,20.093,40.6,12.764,47.633,10.291,55.215.015Z" transform="translate(404.382 130.451) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79955-2" data-name="Path 79955" d="M6.7,0A5.339,5.339,0,0,1,10.4,1.32a2.814,2.814,0,0,1-.044,2.552C9.208,6.514,6.469,7.945,4.019,9.086A8.517,8.517,0,0,1,1.93,9.35C.788,9.218.639,8.823.034,7.963A4.7,4.7,0,0,1,.358,5.6,10.067,10.067,0,0,1,6.689,0Z" transform="translate(595.956 364.807) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79967-2" data-name="Path 79967" d="M4.833.041A19.148,19.148,0,0,1,7.582.058a4.733,4.733,0,0,1,3.547,1.958,3.845,3.845,0,0,1,.4,3.161C10.961,7.8,8.845,9.048,6.677,10.268a19.243,19.243,0,0,1-3.169.29A3.092,3.092,0,0,1,.865,9.417,3.94,3.94,0,0,1,.128,5.958C.734,3,2.375,1.56,4.834.041Z" transform="translate(90.568 189.718) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79968-2" data-name="Path 79968" d="M8.127,0A4.257,4.257,0,0,1,8.6.019C10,.1,11.647.3,12.481,1.6a4.858,4.858,0,0,1,.414,3.441C12.026,9.307,8.787,13.214,5.2,15.505a10.515,10.515,0,0,1-2.037-.069A3.592,3.592,0,0,1,.525,13.689,5.369,5.369,0,0,1,.3,9.615C1.464,5.287,4.387,2.241,8.127.011Z" transform="translate(107.171 186.402) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79971-2" data-name="Path 79971" d="M11.329,0a4.6,4.6,0,0,1,3.055,1.238c1.721,1.793,2.151,4.337,2.133,6.734C16.43,18.683,12.724,29.4,4.929,36.926a4.4,4.4,0,0,1-.43-.377C1.207,32.659-.532,22.422.144,17.524,1.171,10,5.517,4.5,11.329.009Z" transform="translate(668.874 153.108) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79971-3" data-name="Path 79971" d="M21.341,0A8.673,8.673,0,0,1,27.1,2.332c3.242,3.378,4.051,8.169,4.019,12.684-.165,20.176-7.146,40.369-21.829,54.54a8.291,8.291,0,0,1-.811-.711C2.273,61.519-1,42.237.272,33.009,2.207,18.836,10.392,8.468,21.341.017Z" transform="translate(213.436 -7.099) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79979-2" data-name="Path 79979" d="M18.328,36.61C14.263,39.191,9.355,42.266,4.6,43.265a4.326,4.326,0,0,1-3.872-.632,3.419,3.419,0,0,1-.7-1.87c-.457-5,5.909-15.3,8.937-19.306C17.573,10.1,27.748,2.046,42.155,0c-2.546,15.013-11.9,27.436-23.827,36.6Z" transform="translate(346.44 432.191) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_79983-3" data-name="Path 79983" d="M7,0c3.231,1.554,9.6,16.935,12.239,20.948L12.5,25.618l-.817.219C9.262,24.055,2.151,8.955,0,5.048L7.006,0Z" transform="translate(426.594 266.204) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_80035-2" data-name="Path 80035" d="M.511,8.332C.459,8.139.4,7.946.344,7.752-.121,5.89-.27,4.074.915,2.432A6.915,6.915,0,0,1,5.375,0C6.454,1.5,7.692,2.871,7.482,4.837A2.232,2.232,0,0,1,6.447,6.479C5.06,7.664,2.224,7.814.5,8.332Z" transform="translate(609.58 98.786) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_80040-2" data-name="Path 80040" d="M5.275.026A9.83,9.83,0,0,1,8.744.394a8.476,8.476,0,0,1,5.083,4.337A6.772,6.772,0,0,1,14.081,9.9c-.9,2.414-2.643,3.6-4.89,4.636A17.192,17.192,0,0,1,5.7,14.435,7.461,7.461,0,0,1,1,11.187a6.866,6.866,0,0,1-.72-5.513C1.018,2.882,2.9,1.46,5.267.029Z" transform="translate(676.538 19.082) rotate(-156.997)" fill="url(#linear-gradient)"/>
            <path id="Path_80042-2" data-name="Path 80042" d="M5.118.2C6.189.134,7.26.08,8.331.027,10.71-.07,13.361,0,15.1,1.94a5.3,5.3,0,0,1,1.37,4.3c-.4,3.617-3.424,7.3-6.189,9.464a7.084,7.084,0,0,1-4.609.414c-2.081-.711-3.725-2.8-4.609-4.733A10.049,10.049,0,0,1,.605,3.539C1.492,1.476,3.151.905,5.118.194Z" transform="translate(672.793 135.79) rotate(-156.997)" fill="url(#linear-gradient)"/>
          </g>
          <g id="Group_60" data-name="Group 60" transform="matrix(0.966, -0.259, 0.259, 0.966, -7959.467, -1662.513)">
            <path id="Subtraction_4" data-name="Subtraction 4" d="M98.693,106.637H18.724a19.007,19.007,0,0,1-5.123-.694,18.287,18.287,0,0,1-4.491-1.936A18.915,18.915,0,0,1,0,88.232a18.287,18.287,0,0,1,.57-4.857,19.007,19.007,0,0,1,1.961-4.784L42.518,9.347a19.006,19.006,0,0,1,3.162-4.089A18.288,18.288,0,0,1,49.6,2.337a18.916,18.916,0,0,1,18.214,0,18.288,18.288,0,0,1,3.921,2.921A19.005,19.005,0,0,1,74.9,9.347l39.984,69.245a19.007,19.007,0,0,1,1.961,4.784,18.288,18.288,0,0,1,.57,4.857,18.915,18.915,0,0,1-9.107,15.776,18.289,18.289,0,0,1-4.491,1.935A19.009,19.009,0,0,1,98.693,106.637ZM58.769,14.359a14.682,14.682,0,0,0-12.845,7.416L14.2,76.711a14.833,14.833,0,0,0,12.845,22.25H90.491a14.833,14.833,0,0,0,12.845-22.25L71.614,21.775A14.682,14.682,0,0,0,58.769,14.359Z" transform="translate(8768 4389)" fill="url(#linear-gradient)"/>
            <g id="Group_59" data-name="Group 59" transform="translate(8823.244 4425.212)">
              <circle id="Ellipse_7" data-name="Ellipse 7" cx="4.546" cy="4.546" r="4.546" transform="translate(0 40.07)" fill="url(#linear-gradient)"/>
              <path id="Path_34" data-name="Path 34" d="M238.046,164.991a4.546,4.546,0,0,0-4.546,4.546v21.888a4.546,4.546,0,1,0,9.091,0V169.537A4.546,4.546,0,0,0,238.046,164.991Z" transform="translate(-233.5 -164.991)" fill="url(#linear-gradient)"/>
            </g>
          </g>
          <g id="ESSENTIAL_UI" data-name="ESSENTIAL UI" transform="translate(1164.979 157.492)">
            <path id="Path_35" data-name="Path 35" d="M151.909,35.481,116.978,70.413l34.931,34.931a8.426,8.426,0,1,1-11.916,11.916L105.062,82.328,70.131,117.26a8.426,8.426,0,1,1-11.916-11.916L93.146,70.413,58.215,35.481A8.426,8.426,0,1,1,70.131,23.566L105.062,58.5l34.931-34.931a8.426,8.426,0,1,1,11.916,11.916Z" transform="translate(0 0)" fill="none" stroke="#ffbf82" stroke-width="4"/>
          </g>
          <g id="ESSENTIAL_UI-2" data-name="ESSENTIAL UI" transform="translate(1588.881 1041.801)">
            <path id="Path_36" data-name="Path 36" d="M145.441,34.514,112.859,67.1l32.582,32.582a7.859,7.859,0,1,1-11.114,11.114L101.745,78.21,69.163,110.792A7.859,7.859,0,1,1,58.049,99.677L90.631,67.1,58.049,34.514A7.859,7.859,0,1,1,69.163,23.4l32.582,32.582L134.327,23.4a7.859,7.859,0,1,1,11.114,11.114Z" transform="translate(22.422 22.422)" fill="url(#linear-gradient-100)"/>
          </g>
          <g id="Group_62" data-name="Group 62" transform="matrix(0.966, -0.259, 0.259, 0.966, -8481.95, -829.208)">
            <path id="Subtraction_5" data-name="Subtraction 5" d="M107.609,116.271H20.416a20.724,20.724,0,0,1-5.586-.757,19.939,19.939,0,0,1-4.9-2.11A20.624,20.624,0,0,1,0,96.2a19.939,19.939,0,0,1,.621-5.3,20.724,20.724,0,0,1,2.138-5.216l43.6-75.5a20.723,20.723,0,0,1,3.448-4.459,19.94,19.94,0,0,1,4.275-3.185,20.624,20.624,0,0,1,19.86,0,19.94,19.94,0,0,1,4.275,3.185,20.722,20.722,0,0,1,3.448,4.459l43.6,75.5a20.724,20.724,0,0,1,2.138,5.216,19.94,19.94,0,0,1,.621,5.3,20.623,20.623,0,0,1-9.93,17.2,19.941,19.941,0,0,1-4.9,2.11A20.726,20.726,0,0,1,107.609,116.271ZM64.078,15.657a16.008,16.008,0,0,0-14.006,8.085l-34.588,59.9A16.173,16.173,0,0,0,29.49,107.9H98.666a16.173,16.173,0,0,0,14.006-24.26l-34.588-59.9A16.008,16.008,0,0,0,64.078,15.657Z" transform="translate(8733.227 4379.682)" fill="url(#linear-gradient)"/>
            <g id="Group_61" data-name="Group 61" transform="translate(8934.526 4377.793)">
              <circle id="Ellipse_8" data-name="Ellipse 8" cx="8.951" cy="8.951" r="8.951" transform="translate(0 78.908)" fill="url(#linear-gradient)"/>
              <circle id="Ellipse_9" data-name="Ellipse 9" cx="8.951" cy="8.951" r="8.951" transform="translate(570.357 -314.892)" fill="url(#linear-gradient)"/>
              <path id="Path_39" data-name="Path 39" d="M242.451,164.991a8.952,8.952,0,0,0-8.951,8.951v43.1a8.951,8.951,0,1,0,17.9,0v-43.1A8.952,8.952,0,0,0,242.451,164.991Z" transform="translate(-233.5 -164.991)" fill="url(#linear-gradient)"/>
              <path id="Path_42" data-name="Path 42" d="M242.451,164.991a8.952,8.952,0,0,0-8.951,8.951v43.1a8.951,8.951,0,1,0,17.9,0v-43.1A8.952,8.952,0,0,0,242.451,164.991Z" transform="translate(336.857 -558.79)" fill="url(#linear-gradient)"/>
            </g>
          </g>
          <g id="Group_64" data-name="Group 64" transform="matrix(0.966, -0.259, 0.259, 0.966, -8260.95, -1477.208)">
            <path id="Subtraction_5-2" data-name="Subtraction 5" d="M71.466,77.271H13.559a13.755,13.755,0,0,1-3.71-.5,13.238,13.238,0,0,1-3.252-1.4A13.708,13.708,0,0,1,0,63.934a13.26,13.26,0,0,1,.412-3.519,13.778,13.778,0,0,1,1.42-3.466L30.789,6.773a13.771,13.771,0,0,1,2.29-2.963,13.243,13.243,0,0,1,2.839-2.117,13.689,13.689,0,0,1,13.189,0A13.243,13.243,0,0,1,51.947,3.81a13.77,13.77,0,0,1,2.29,2.963L83.191,56.949a13.778,13.778,0,0,1,1.42,3.466,13.26,13.26,0,0,1,.412,3.519,13.708,13.708,0,0,1-6.595,11.431,13.239,13.239,0,0,1-3.252,1.4A13.756,13.756,0,0,1,71.466,77.271ZM42.556,10.405a10.63,10.63,0,0,0-9.3,5.373L10.284,55.586a10.748,10.748,0,0,0,9.3,16.123H65.527a10.748,10.748,0,0,0,9.3-16.123L51.858,15.778A10.63,10.63,0,0,0,42.556,10.405Z" transform="translate(8752.857 4356.126)" fill="url(#linear-gradient)"/>
            <g id="Group_61-2" data-name="Group 61" transform="translate(8970.313 4368.091) rotate(30)">
              <circle id="Ellipse_8-2" data-name="Ellipse 8" cx="8.951" cy="8.951" r="8.951" transform="translate(0 78.908)" fill="url(#linear-gradient)"/>
              <path id="Path_39-2" data-name="Path 39" d="M242.451,164.991a8.952,8.952,0,0,0-8.951,8.951v43.1a8.951,8.951,0,1,0,17.9,0v-43.1A8.952,8.952,0,0,0,242.451,164.991Z" transform="translate(-233.5 -164.991)" fill="url(#linear-gradient)"/>
            </g>
          </g>
          <path id="Subtraction_6" data-name="Subtraction 6" d="M75.669,80.271H14.356a14.826,14.826,0,0,1-3.928-.522,14.189,14.189,0,0,1-8.486-6.515A13.976,13.976,0,0,1,0,66.416,13.527,13.527,0,0,1,.439,62.76a14.162,14.162,0,0,1,1.5-3.6L32.6,7.036a14.343,14.343,0,0,1,2.425-3.078,14.006,14.006,0,0,1,3.006-2.2A14.739,14.739,0,0,1,52,1.759,14.006,14.006,0,0,1,55,3.958a14.342,14.342,0,0,1,2.425,3.078L88.083,59.16a14.162,14.162,0,0,1,1.5,3.6,13.528,13.528,0,0,1,.437,3.656,13.976,13.976,0,0,1-1.939,6.817A14.189,14.189,0,0,1,79.6,79.748,14.828,14.828,0,0,1,75.669,80.271ZM45.059,10.809a11.3,11.3,0,0,0-9.848,5.582L10.888,57.744a10.878,10.878,0,0,0,0,11.166,11.3,11.3,0,0,0,9.849,5.583H69.38A11.3,11.3,0,0,0,79.23,68.91a10.878,10.878,0,0,0,0-11.166L54.907,16.391A11.3,11.3,0,0,0,45.059,10.809Z" transform="translate(1336.913 934.95) rotate(-45)" fill="url(#linear-gradient)"/>
        </g>
      </g>
    </g>
  </g>
</svg>
