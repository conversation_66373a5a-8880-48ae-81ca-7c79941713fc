<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
      style="margin:auto;background:#fff;display:block;" width="80px" height="80px" viewBox="0 0 100 100"
      preserveAspectRatio="xMidYMid">
      <defs>
        <mask id="ldio-py8akb5p3x-mask">
          <circle cx="50" cy="50" r="20" stroke="#fff" stroke-linecap="round"
            stroke-dasharray="94.24777960769379 31.41592653589793" stroke-width="9">
            <animateTransform attributeName="transform" type="rotate" values="0 50 50;360 50 50" times="0;1" dur="1s"
              repeatCount="indefinite"></animateTransform>
          </circle>
        </mask>
      </defs>
      <g mask="url(#ldio-py8akb5p3x-mask)">
        <rect x="20.5" y="0" width="15.6" height="100" fill="#e15b64">
          <animate attributeName="fill" values="#e15b64;#f47e60;#f8b26a;#abbd81;#849b87" times="0;0.35;0.5;0.65;1"
            dur="1s" repeatCount="indefinite" begin="-0.8s"></animate>
        </rect>
        <rect x="32.1" y="0" width="15.6" height="100" fill="#f47e60">
          <animate attributeName="fill" values="#e15b64;#f47e60;#f8b26a;#abbd81;#849b87" times="0;0.35;0.5;0.65;1"
            dur="1s" repeatCount="indefinite" begin="-0.6s"></animate>
        </rect>
        <rect x="43.7" y="0" width="15.6" height="100" fill="#f8b26a">
          <animate attributeName="fill" values="#e15b64;#f47e60;#f8b26a;#abbd81;#849b87" times="0;0.35;0.5;0.65;1"
            dur="1s" repeatCount="indefinite" begin="-0.4s"></animate>
        </rect>
        <rect x="55.3" y="0" width="15.6" height="100" fill="#abbd81">
          <animate attributeName="fill" values="#e15b64;#f47e60;#f8b26a;#abbd81;#849b87" times="0;0.35;0.5;0.65;1"
            dur="1s" repeatCount="indefinite" begin="-0.2s"></animate>
        </rect>
        <rect x="66.9" y="0" width="15.6" height="100" fill="#849b87">
          <animate attributeName="fill" values="#e15b64;#f47e60;#f8b26a;#abbd81;#849b87" times="0;0.35;0.5;0.65;1"
            dur="1s" repeatCount="indefinite" begin="0s"></animate>
        </rect>
      </g>
    </svg>
