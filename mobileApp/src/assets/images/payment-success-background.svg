<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="623" height="1081" viewBox="0 0 623 1081">
  <defs>
    <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ff6e04"/>
      <stop offset="1" stop-color="#ff8f10"/>
    </linearGradient>
    <clipPath id="clip-path">
      <rect id="Rectangle_8" data-name="Rectangle 8" width="623" height="1080" transform="translate(1487 136)"/>
    </clipPath>
    <linearGradient id="linear-gradient-2" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff"/>
      <stop offset="1" stop-color="#ff830c"/>
    </linearGradient>
  </defs>
  <g id="Group_66" data-name="Group 66" transform="translate(-1975 -135)">
    <rect id="Rectangle_7" data-name="Rectangle 7" width="623" height="1080" transform="translate(1975 135)" fill="url(#linear-gradient)"/>
    <g id="Mask_Group_2" data-name="Mask Group 2" transform="translate(488)" clip-path="url(#clip-path)">
      <g id="Group_56" data-name="Group 56">
        <g id="Layer_1" data-name="Layer 1" transform="translate(1480.193 -143.855) rotate(23.003)" opacity="0.45">
          <path id="Path_79866" data-name="Path 79866" d="M308.36,297.933a58.643,58.643,0,0,1,.98-7c.445-1.82,1.082-3.564,2.7-4.633l1.171.3c.9,1.54.713,2.495.585,4.226-1.807,2.342-3.462,4.926-5.435,7.1Z" transform="translate(67.144 63.417)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79867" data-name="Path 79867" d="M324.536,318.074c-.051-.127-.153-.229-.153-.356-.114-1.476.28-2.291-.522-3.666a11.4,11.4,0,0,1-3.666-2.724c-.624-.827-.267-1.209-.089-2.138.242.1.471.216.713.318,4.429,1.9,13.772,2.609,16.457,6.224C335.011,318.278,327.858,317.7,324.536,318.074Z" transform="translate(70.276 69.661)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79868" data-name="Path 79868" d="M314.534,289.863a107.943,107.943,0,0,0,2.838,15.592c-.713.815-.547.967-1.68,1.069a26.423,26.423,0,0,0-10.042,2.7,68.1,68.1,0,0,0,3.462-12.27c1.986-2.176,3.627-4.76,5.435-7.1Z" transform="translate(66.405 64.385)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79869" data-name="Path 79869" d="M308.975,306.94,306,313.075a12.977,12.977,0,0,1-1.056,5.46c-4.162-1.616-9.049-1.26-13.4-2.342-1.629-.407-2.927-.636-3.818-2.075,1.909-1.451,17.221-5.549,21.256-7.179Z" transform="translate(61.514 69.047)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79870" data-name="Path 79870" d="M582.851,248.35a8.245,8.245,0,0,1,5.728,2.138,5.264,5.264,0,0,1,1.412,4.2c-.2,2.953-1.934,4.544-3.971,6.44a17.539,17.539,0,0,1-3.551-.484,7.508,7.508,0,0,1-3.946-3.513,5.956,5.956,0,0,1-.191-4.671C579.057,250.285,580.992,249.407,582.851,248.35Z" transform="translate(140.684 53.065)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79871" data-name="Path 79871" d="M306.17,321.7c1.26-.382.662-.42,1.578-.115,3.589,1.209,5.715.56,9.24.331a2.831,2.831,0,0,1,.713.14c-1.527,5.053-1.489,10.564-2.545,15.757-1.158,1.056-1.2,1.184-2.737,1.336-3.691-2.724-4.226-12.206-5.931-16.673-.1-.267-.2-.522-.3-.776Z" transform="translate(66.547 72.986)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79872" data-name="Path 79872" d="M336.5,165.5c.229.038.458.076.675.127,2.775.624,5.307,1.82,6.822,4.34.115.191.216.382.318.573a5.21,5.21,0,0,1,.255.611,5.391,5.391,0,0,1,.306,1.273,5.579,5.579,0,0,1,.051.649c0,.216,0,.433-.013.662a5.674,5.674,0,0,1-.076.649,5.17,5.17,0,0,1-.153.636c-.776,2.7-2.686,3.793-4.964,5.117-1.744-.242-4.238-.853-5.588-2.049a6.524,6.524,0,0,1-2.469-5.524C331.906,169.229,333.968,167.333,336.5,165.5Z" transform="translate(73.496 30.465)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79873" data-name="Path 79873" d="M445.157,294.39c2.749.446,5.766,1.247,7.548,3.564a6.093,6.093,0,0,1,1.133,4.926c-.611,3.169-2.647,4.518-5.168,6.275-2.24-.051-5.078-.84-6.657-2.533a5.8,5.8,0,0,1-1.693-4.4c.1-3.449,2.38-5.727,4.849-7.815Z" transform="translate(103.14 65.624)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79874" data-name="Path 79874" d="M654.795,321.863l.993.242c.624,1.527.407,2.431.254,4.022a60.165,60.165,0,0,0-2.494,14.777c-.1,7.917,3.335,12.817,8.54,18.3a51.231,51.231,0,0,1-6.122-3.666,17.766,17.766,0,0,1-1.515-1.273,18.025,18.025,0,0,1-1.375-1.413,17.5,17.5,0,0,1-1.209-1.553c-.382-.535-.726-1.1-1.057-1.667a17.532,17.532,0,0,1-.865-1.769c-.255-.6-.484-1.222-.687-1.846s-.357-1.26-.5-1.909a16.623,16.623,0,0,1-.293-1.947,30.209,30.209,0,0,1,6.313-20.314Z" transform="translate(159.896 73.114)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79875" data-name="Path 79875" d="M242.527,447.535a31.1,31.1,0,0,1,4.213-.191,9.1,9.1,0,0,1,5.677,2.927,6.315,6.315,0,0,1,1.667,4.811c-.267,3.156-2.126,4.938-4.429,6.873-.535.051-1.082.089-1.616.115a8.937,8.937,0,0,1-7.09-2.418,7.113,7.113,0,0,1-2.227-5.358c.114-2.953,1.705-4.875,3.793-6.758Z" transform="translate(48.146 107.342)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79877" data-name="Path 79877" d="M177.014,394.534a24.146,24.146,0,0,1,4.289.064,7,7,0,0,1,4.786,3.118,6.687,6.687,0,0,1,.853,4.989c-.789,3.64-2.991,5.282-5.982,7.191-2.966-.28-5.8-.738-7.828-3.144a6.763,6.763,0,0,1-1.553-5.486c.535-3.284,2.813-5.027,5.422-6.746Z" transform="translate(29.808 92.924)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79880" data-name="Path 79880" d="M247.2,573.853a12.96,12.96,0,0,1,2.546.56,8.874,8.874,0,0,1,5.524,4.811q.153.382.267.764a7.894,7.894,0,0,1,.191.789,5.762,5.762,0,0,1,.1.8c.025.267.025.534.025.8a5.661,5.661,0,0,1-.064.8,7.819,7.819,0,0,1-.674,2.317c-1.285,2.787-3.475,4.022-6.224,5.091-2.673-.293-5.867-.853-7.6-3.169a6.169,6.169,0,0,1-1.209-5.257c.776-4.111,3.793-6.224,7.1-8.324Z" transform="translate(48.48 141.853)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79881" data-name="Path 79881" d="M166.216,669.52a19.693,19.693,0,0,1,2.737.255,8.5,8.5,0,0,1,5.842,3.653,7.617,7.617,0,0,1,.929,5.969c-.789,3.284-2.9,4.977-5.6,6.771a9.8,9.8,0,0,1-4.035-.293,9.4,9.4,0,0,1-5.027-4.837,9.231,9.231,0,0,1-.573-1.757,7.582,7.582,0,0,1-.14-.916q-.038-.458-.038-.916a7.387,7.387,0,0,1,.064-.917c.038-.306.089-.611.153-.9a7.876,7.876,0,0,1,.611-1.743c1.171-2.431,2.66-3.4,5.091-4.366Z" transform="translate(26.758 167.953)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79882" data-name="Path 79882" d="M941.059,692.169c-.076-.28-.165-.56-.242-.84-.675-2.7-.891-5.333.827-7.713,1.591-2.189,3.959-2.966,6.466-3.526,3.055.827,5.473,1.578,7.051,4.531a8.377,8.377,0,0,1,.331,6.924,9.3,9.3,0,0,1-4.773,5.27,6.882,6.882,0,0,1-.649.254,4.043,4.043,0,0,1-.675.178,6.168,6.168,0,0,1-.7.114,6.378,6.378,0,0,1-2.087-.114,5.911,5.911,0,0,1-.675-.191,6.945,6.945,0,0,1-.649-.267c-2.024-.942-2.927-2.456-4.048-4.3l-.191-.3Z" transform="translate(184.61 605.796)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79883" data-name="Path 79883" d="M941.059,692.169c-.076-.28-.165-.56-.242-.84-.675-2.7-.891-5.333.827-7.713,1.591-2.189,3.959-2.966,6.466-3.526,1.565,2.176,3.36,4.162,3.055,7.013a3.236,3.236,0,0,1-1.5,2.38c-2.011,1.718-6.122,1.935-8.617,2.686Z" transform="translate(184.61 605.796)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79884" data-name="Path 79884" d="M318.575,302.11a31.919,31.919,0,0,0,6.6,9.011c-.178.929-.535,1.311.089,2.138a11.781,11.781,0,0,0,3.666,2.724c.815,1.374.407,2.189.522,3.666,0,.127.1.242.153.356a20.149,20.149,0,0,0-10.577,7.306,2.826,2.826,0,0,0-.713-.14c-3.513.216-5.651.878-9.24-.331-.9-.3-.318-.267-1.578.115a56.2,56.2,0,0,0-6.262-7.1,13.017,13.017,0,0,0,1.056-5.46,21.1,21.1,0,0,0,2.647,4.811c1.782,2.342,4.073,4.251,7.115,4.544a8.8,8.8,0,0,0,6.822-2.482c3.335-2.94,3.309-6.631,3.7-10.742l-6.911-3.869c-3.093-.051-6.173.484-9.291.509l.458-1.285a26.254,26.254,0,0,1,10.042-2.7c1.133-.1.967-.255,1.68-1.069Z" transform="translate(65.202 67.73)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79886" data-name="Path 79886" d="M693.1,342.463a13.353,13.353,0,0,1,2.571.013,8.586,8.586,0,0,1,6.937,11.251c-.929,3.386-3.182,4.837-6.033,6.593a10.71,10.71,0,0,1-1.884-.064,9.388,9.388,0,0,1-6.771-3.818,8.144,8.144,0,0,1-1.031-6.224c.7-3.869,3.118-5.626,6.2-7.751Z" transform="translate(170.349 78.721)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79888" data-name="Path 79888" d="M78.911,711.568a15.219,15.219,0,0,1,4.8.369,10.731,10.731,0,0,1,6.466,5.448,9.093,9.093,0,0,1,.484,7.242c-1.133,3.169-3.029,4.556-5.931,6.033a19.074,19.074,0,0,1-4.086-.013,9.685,9.685,0,0,1-6.351-3.856,9.5,9.5,0,0,1-1.5-7.866c.878-3.729,3-5.4,6.122-7.344Z" transform="translate(2.809 -104.81)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79889" data-name="Path 79889" d="M123.79,594.183a32.369,32.369,0,0,1,4.582-.382c2.266.064,4.811,1.782,6.186,3.488a8.9,8.9,0,0,1,1.807,6.911c-.5,3.984-2.838,5.817-5.88,8.108a22.11,22.11,0,0,1-5.269,0,10.257,10.257,0,0,1-6.415-4.073,8.02,8.02,0,0,1-1.247-6.173c.738-3.92,3.106-5.766,6.249-7.891Z" transform="translate(15.053 147.297)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79890" data-name="Path 79890" d="M110.021,791.02a14.836,14.836,0,0,1,2.367.153c3.36.5,6.555,1.744,8.566,4.634a8.727,8.727,0,0,1,1.375,6.835c-.8,3.984-3.335,5.867-6.618,7.917a16.7,16.7,0,0,1-4.289,0,10.624,10.624,0,0,1-6.962-4.735,9.959,9.959,0,0,1-1.044-7.5c.942-3.818,3.373-5.409,6.593-7.293Z" transform="translate(11.155 -83.127)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79891" data-name="Path 79891" d="M428.38,76.021c4.378-.407,6.962.484,10.857,2.469,1.756,3.144,3.6,6.2,2.584,9.915-.993,3.627-4.124,5.549-7.153,7.344a9.111,9.111,0,0,1-3.156.267,11.525,11.525,0,0,1-8.082-4.786q-.287-.439-.535-.916a10.292,10.292,0,0,1-.764-1.973A9.51,9.51,0,0,1,421.9,87.3c-.051-.356-.1-.7-.127-1.056a7.453,7.453,0,0,1,0-1.069,9.618,9.618,0,0,1,.1-1.056,9.478,9.478,0,0,1,.2-1.044C422.933,79.546,425.389,77.739,428.38,76.021Z" transform="translate(98.077 6.03)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79892" data-name="Path 79892" d="M428.759,78.75c.356.14.726.242,1.069.407a5.885,5.885,0,0,1,2.889,3.436c-.509,2.367-.993,2.609-2.673,4.251a13.561,13.561,0,0,1-2.686-.191c-1.1-1.069-1.616-1.438-1.464-3.118.191-2.075,1.464-3.4,2.864-4.786Z" transform="translate(99.199 6.801)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79895" data-name="Path 79895" d="M649.42,227.795c1.871-6.224,4.137-11.9,9.966-15.4,7.866-4.722,21.816-4.047,30.369-2.011-3.653,1.5-9.953-.115-14.179.255-.5,3.5-2.8,9.724-2.406,12.639,3.4,3.666,11.252.535,15.324,4.417-10.233-1.616-27.288-5.677-36.274.8l-2.8-.713Z" transform="translate(160.18 42.284)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79896" data-name="Path 79896" d="M508.02,321.73c10.157,4.544,19.817,10.284,30.089,14.535-2.036,3.857-4.544,7.5-6.911,11.162-4.824-.624-23.6-11.6-29.338-14.459a110.63,110.63,0,0,1,6.161-11.226Z" transform="translate(119.928 73.082)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79897" data-name="Path 79897" d="M344.467,241.13c4.646.878,9.189,1.96,13.759,3.2q-5.021,15.178-9.139,30.623a130.531,130.531,0,0,0-13.377-1.413q4.811-16.075,8.769-32.4Z" transform="translate(74.605 51.095)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79899" data-name="Path 79899" d="M403.064,270.551c2.533-.216,5.282-.318,7.675.675a12.542,12.542,0,0,1,6.8,6.758c.153.382.293.764.407,1.158a9.594,9.594,0,0,1,.28,1.2c.076.407.127.8.166,1.222a11.773,11.773,0,0,1-.051,2.444c-.051.407-.115.815-.2,1.209a11.458,11.458,0,0,1-.331,1.184,11.289,11.289,0,0,1-.445,1.146c-1.82,4.111-4.875,5.575-8.884,7.115a23.286,23.286,0,0,1-5.244-.127,9.122,9.122,0,0,1-1.171-.267c-.382-.114-.764-.242-1.133-.382a11.187,11.187,0,0,1-1.1-.5,11.054,11.054,0,0,1-1.031-.611,10.5,10.5,0,0,1-.967-.713c-.306-.255-.611-.522-.891-.8s-.547-.585-.8-.891a10.387,10.387,0,0,1-.7-.968,11.669,11.669,0,0,1-1.235-9.508c1.4-4.735,4.7-7.064,8.846-9.329Z" transform="translate(90.427 59.082)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79900" data-name="Path 79900" d="M795.373,173.939c9.355-2.075,18.659-4.913,27.912-7.433,10.564-2.876,21.281-5.511,31.705-8.846l4,7.268c-13.021,2.673-25.456,6.5-38.158,10.424-7.2,2.227-14.65,4.2-21.689,6.9-1.362-2.724-2.558-5.524-3.78-8.324Z" transform="translate(199.99 28.326)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79901" data-name="Path 79901" d="M251.167,525.552a19,19,0,0,1,6.351.446,12.523,12.523,0,0,1,7.408,6.5,13.207,13.207,0,0,1,.229,10.208c-1.731,4.417-4.849,6.491-9.088,8.235a18.11,18.11,0,0,1-6.033-.522,13.012,13.012,0,0,1-7.5-6.6c-.178-.4-.344-.789-.484-1.2a10.49,10.49,0,0,1-.356-1.235c-.1-.42-.178-.84-.242-1.273a10.729,10.729,0,0,1-.114-1.285c-.013-.433,0-.866.013-1.286a11.325,11.325,0,0,1,.14-1.286,11.1,11.1,0,0,1,.267-1.26c.115-.42.242-.827.394-1.234,1.655-4.442,4.811-6.466,9.011-8.222Z" transform="translate(48.861 128.666)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79902" data-name="Path 79902" d="M248.487,531.71a10.7,10.7,0,0,1,3.182.776c1.222,1.222,1.884,1.909,2,3.691.14,2.164-.853,3.64-2.138,5.257a9.1,9.1,0,0,1-2.087-.675,5.244,5.244,0,0,1-2.775-3.6C246.3,534.93,247.316,533.479,248.487,531.71Z" transform="translate(50.294 130.361)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79906" data-name="Path 79906" d="M576.852,296.623a18.125,18.125,0,0,1,5.766,1.069,14.66,14.66,0,0,1,8.413,8.693,15.237,15.237,0,0,1,.483,1.527,13.567,13.567,0,0,1,.318,1.565c.076.535.127,1.056.153,1.591s.025,1.069,0,1.6a13.243,13.243,0,0,1-.178,1.591,15.88,15.88,0,0,1-.344,1.565,13.449,13.449,0,0,1-.509,1.527c-.191.5-.42.98-.662,1.464-2.38,4.493-5.982,6.326-10.666,7.777-3.131,0-5.842-.127-8.68-1.578-.445-.229-.866-.471-1.285-.738a13.077,13.077,0,0,1-1.209-.866,13.5,13.5,0,0,1-1.108-.98c-.356-.344-.687-.713-1.006-1.082a13.618,13.618,0,0,1-.891-1.184,12.8,12.8,0,0,1-.764-1.26c-.229-.433-.445-.878-.636-1.336a13.017,13.017,0,0,1-.5-1.4,14.583,14.583,0,0,1-.6-3.055c-.051-.522-.063-1.031-.063-1.553a12.6,12.6,0,0,1,.1-1.553,14.819,14.819,0,0,1,.267-1.527,15.572,15.572,0,0,1,.42-1.5,14.272,14.272,0,0,1,.573-1.451c.216-.471.458-.929.725-1.387,2.762-4.607,6.86-6.313,11.862-7.535Z" transform="translate(136.576 66.229)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79907" data-name="Path 79907" d="M248.777,269.086a14.642,14.642,0,0,1,7.293,1.311,14.3,14.3,0,0,1,7.662,8.489,14.739,14.739,0,0,1-.815,11.493c-2.635,5.079-6.7,6.619-11.837,8.12a23.04,23.04,0,0,1-6.9-1.578c-6.211-2.558-8.222-8.171-10.462-13.962a27.525,27.525,0,0,1,2.736-7.178c2.851-4.518,7.446-5.613,12.308-6.7Z" transform="translate(46.784 58.713)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79908" data-name="Path 79908" d="M603.885,111.33c4.607.56,7.382,7.306,11.239,10.068a48.793,48.793,0,0,1,6.7.547,3.863,3.863,0,0,1,3.017,2.3c-.637,3.449-4.62,7.891-6.517,11.035.7,2.495,1.82,5.6,1.005,8.171-.331,1.031-.815,1.044-1.68,1.553-2.635.038-4.646-1.184-6.937-2.316-5.5-1.527-7.955,3.233-13,3.233-1.031,0-1.209-.522-1.884-1.184-.776-3.589-.051-7.955.166-11.633-1.884-1.858-5.295-4.506-6.084-7.038-.293-.942.166-1.26.611-2.062,2.024-1.362,4.01-1.578,6.39-1.973,2.571-3.347,4.722-7.115,6.962-10.691Z" transform="translate(143.921 15.688)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79909" data-name="Path 79909" d="M861.407,261.673c3.678-.051,7.293,0,10.73,1.489a16.717,16.717,0,0,1,9.151,9.126,16.349,16.349,0,0,1-.395,12.677c-2.355,4.989-6.033,7.191-11.035,9-4.1.6-7.382.636-11.188-1.2a17.113,17.113,0,0,1-1.642-.891,17.529,17.529,0,0,1-1.553-1.056,18.473,18.473,0,0,1-2.724-2.558c-.408-.471-.789-.967-1.159-1.477a18.019,18.019,0,0,1-2.469-5.015,16.115,16.115,0,0,1,1.222-12.715c2.495-4.3,6.4-6.3,11.073-7.4Z" transform="translate(214.447 56.695)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79910" data-name="Path 79910" d="M858.322,269.115c2.558-.254,4.162-.509,6.5.713.662.865,1.12,1.171,1.209,2.316.191,2.736-1.413,4.531-3.029,6.529a5.9,5.9,0,0,1-4.887-.127,4.913,4.913,0,0,1-1.617-2.66c-.636-2.788.369-4.48,1.807-6.758Z" transform="translate(216.615 58.672)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79911" data-name="Path 79911" d="M624.316,276.82c17.539,9.711,50.173,14.675,59.032,33.6-.9,2.406-1.718,4.926-2.838,7.242-2.584-.573-5.155-1.018-7.777-1.426q-6.51-3.876-13.364-7.089c-8.2-3.806-35.32-13.784-38.413-21.5-1.489-3.716.153-7.089,1.616-10.526l1.744-.306Z" transform="translate(152.255 60.831)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79912" data-name="Path 79912" d="M684.674,210.05c8.693.764,21.79,4.162,29.312,8.579,2.495,4.6,17.921,5.511,19.944,9.19-.993,2.8-5.766,9.355-8.159,10.946-3.627.5-15.2-4.518-16.431-3.106-8.566-2.927-17.208-5.868-25.927-8.3-4.073-3.9-11.914-.764-15.324-4.417-.407-2.927,1.909-9.138,2.406-12.639,4.226-.369,10.526,1.26,14.179-.255Z" transform="translate(165.26 42.617)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79915" data-name="Path 79915" d="M302.771,99.652c4.149-.242,7.929-.547,11.888,1.056a16.492,16.492,0,0,1,1.667.776,15.208,15.208,0,0,1,1.578.942,18.682,18.682,0,0,1,2.838,2.329c.433.433.84.891,1.235,1.362a17.716,17.716,0,0,1,1.1,1.476,16.478,16.478,0,0,1,.929,1.578c.28.547.547,1.1.776,1.667a18.631,18.631,0,0,1,.191,14.37c-2.278,5.371-6.682,8.375-11.939,10.411-3.908.624-8.006.866-11.735-.687a18.475,18.475,0,0,1-9.953-10.411,19.731,19.731,0,0,1,.357-15.693c2.3-4.7,6.288-7.433,11.086-9.19Z" transform="translate(62.106 12.449)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79916" data-name="Path 79916" d="M301.5,107.78a5.744,5.744,0,0,1,1.324.318c1.617.662,1.731.98,2.355,2.507-.471,2.316-1.82,3.576-3.36,5.295-1.756-1.018-2.673-1.26-3.615-3.093C298.531,110.682,300.109,109.32,301.5,107.78Z" transform="translate(64.373 14.719)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79917" data-name="Path 79917" d="M673.32,86.6a21.487,21.487,0,0,1,9.8,2.024,17.5,17.5,0,0,1,8.693,11.213,19.5,19.5,0,0,1-2.418,15.617c-3.309,5-7.878,6.962-13.529,8.2a23.892,23.892,0,0,1-10.882-2.609,17.651,17.651,0,0,1-8.795-11.035,18.675,18.675,0,0,1,2.367-14.166C662.094,90.087,667.045,87.987,673.32,86.6Z" transform="translate(161.888 8.942)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79918" data-name="Path 79918" d="M667.827,94.57c2.457.445,4.748.84,6.517,2.724a5.644,5.644,0,0,1,1.68,4.9c-.471,2.775-2.278,4.277-4.455,5.829-2.66-.127-4.875-.458-6.67-2.66a6.374,6.374,0,0,1-1.336-4.9c.382-2.94,1.96-4.238,4.264-5.88Z" transform="translate(164.022 11.116)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79919" data-name="Path 79919" d="M613.976,351.421c-.2-2.215-.42-4.442-.484-6.657-.229-8.693,4.047-17.832,9.953-23.992,8.489-8.859,22.617-13.224,34.645-13.44,5.32-.089,10.615.7,15.935.6h.573c2.622.382,5.193.827,7.777,1.4-1.527,4.136-3.589,13.11-7.866,14.955-2.52,1.082-5.231.484-7.433,2.317l-1.527,3.487c.153-1.591.369-2.495-.254-4.022l-.993-.242-.382-1.209c-4.849-2.558-16.572.293-21.676,1.782-14.624,4.3-21.141,12.015-28.281,25.023Z" transform="translate(150.377 69.152)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79920" data-name="Path 79920" d="M793.85,266.72c11.913,1.744,22.337,6.746,29.631,16.623,6.224,8.426,8.273,19.754,6.567,30-2.418,14.548-11.595,27.3-23.559,35.638-10.959,7.637-23.368,10.742-36.567,8.35a30.385,30.385,0,0,1-7.332-2.164c4.2-1.858,11.1-.7,15.783-1.056,10.424-.8,19.843-6.542,26.41-14.484.6-.713,1.171-1.438,1.731-2.176s1.108-1.489,1.629-2.266,1.031-1.54,1.515-2.342.954-1.591,1.387-2.4.866-1.642,1.273-2.482.789-1.68,1.145-2.533.7-1.718,1.018-2.584.611-1.756.891-2.635.522-1.782.763-2.673.433-1.807.624-2.711.344-1.82.484-2.736.255-1.833.356-2.762.165-1.845.216-2.775.076-1.858.076-2.787-.012-1.858-.063-2.775-.115-1.858-.2-2.775c-.547-5.537-2.1-10.882-6.529-14.5-1.9-7.993-10.946-12.779-17.234-16.979Z" transform="translate(191.051 58.076)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79921" data-name="Path 79921" d="M650.064,223.73l2.8.713c16.241,17.933,47.246,30.7,57.861,50.071-1.68,5.473-5.779,18.315-10.984,21.2-.216.115-.446.191-.662.28l-.5-.395c.446-.458.9-.916,1.324-1.4,2.074-2.393,5.638-6.873,5.409-10.068-2.495-2.164-8.362-1.5-11.76-2.851l.05-.738,8.833,1.2c-18.812-14.675-48.875-26.97-55.379-51.917l3-6.1Z" transform="translate(159.536 46.349)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79922" data-name="Path 79922" d="M79.264,546.606a22.477,22.477,0,0,1,12.5,2.978,17.686,17.686,0,0,1,2.966,2.164,15.609,15.609,0,0,1,1.3,1.3c.407.458.8.929,1.171,1.426a17.877,17.877,0,0,1,1.018,1.54,16.478,16.478,0,0,1,.853,1.629c.255.56.484,1.133.687,1.705a16.63,16.63,0,0,1,.509,1.769A19.619,19.619,0,0,1,97.439,576.3c-3.347,4.9-8.019,6.962-13.632,8.184-4.506.051-9.011-.407-12.931-2.8a18.008,18.008,0,0,1-8.12-11.608,19.6,19.6,0,0,1,2.673-15.49c3.475-4.989,8.12-6.822,13.861-7.967Z" transform="translate(0 134.417)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79923" data-name="Path 79923" d="M79,560.006c.433-.013.878-.026,1.311-.026,2.151,0,4.327.433,5.689,2.3a4.6,4.6,0,0,1,.827,3.767c-.395,2.444-2.316,3.691-4.187,5.015-1.935.293-3.627.344-5.257-.916A4.778,4.778,0,0,1,75.52,566.1C75.558,563.188,76.984,561.812,79,560.006Z" transform="translate(3.629 138.072)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79924" data-name="Path 79924" d="M688.12,186.9c1.222-3.462.344-7.7,1.846-11.022.445-.98.369-.662,1.54-1.018,1.871.827,2.495,1.973,3.539,3.64l.42.675.98-.789c4.239,4.569,8.693,8.795,13.364,12.931,6.224,5.511,30.839,24.3,29.936,32.278-.573,5.015-7.408,10.131-10.208,14.2-1.935,2.813-3.411,6.046-5.588,8.668a18.938,18.938,0,0,1-4.251,3.615c-5.269-1.2-10.946-2.482-15.833-4.837,1.234-1.413,12.8,3.589,16.432,3.106,2.393-1.591,7.166-8.146,8.158-10.946-2.024-3.678-17.462-4.6-19.944-9.19,5.982,1.667,11.519,4.9,17.628,6.249,1.426.306,2.418.573,3.691-.242C726.354,224.066,690.729,204.44,688.12,186.9Z" transform="translate(170.737 33.018)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79926" data-name="Path 79926" d="M858.176,163.38c2.9,5.5,5.46,10.959,7.866,16.686-13.67,7.013-29.121,12.041-43.529,17.45-5.04,1.9-10.679,3.513-15.464,5.944a230.918,230.918,0,0,0-8.719-22.76c7.038-2.7,14.484-4.671,21.688-6.9,12.69-3.92,25.125-7.751,38.158-10.424Z" transform="translate(200.8 29.886)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79927" data-name="Path 79927" d="M622.1,281.651c2.227-6.339,5.282-14.955,11.608-18.137,16.546-8.311,49.791,1.056,66.719,7.064l-.051.738c3.4,1.337,9.266.675,11.76,2.851.229,3.2-3.334,7.675-5.409,10.068-.42.484-.878.942-1.324,1.4l.5.395c-11.277,5.269-57.1-16.661-82.044-4.684l-1.743.3Z" transform="translate(152.728 56.305)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79928" data-name="Path 79928" d="M864.171,176.49c2.94,7.879,5.536,15.757,7.9,23.827q-20.714,8.649-41.9,16.126c-6.135,2.138-12.6,3.5-18.6,5.957-1.909-7.56-3.869-15.121-6.389-22.5,4.786-2.431,10.424-4.047,15.464-5.944,14.408-5.409,29.86-10.45,43.53-17.45Z" transform="translate(202.672 33.462)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79929" data-name="Path 79929" d="M870.708,195.2c2.151,9.5,4.085,18.926,5.485,28.574-20.326,4.391-41.06,9.291-60.966,15.312-1.375-7.28-2.673-14.764-5.028-21.8,5.995-2.456,12.461-3.818,18.6-5.957q21.173-7.465,41.9-16.126Z" transform="translate(204.038 38.566)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79931" data-name="Path 79931" d="M188.6,463.6c13.122,7.967,25.456,17.552,37.853,26.614,6.007,4.391,13.581,8.871,18.608,14.332a20.905,20.905,0,0,1,4.8,9.062,11.952,11.952,0,0,1-1.782,9.61c-4.073,5.842-11.646,7.662-18.29,8.617-1.96.28-3.9.484-5.88.585a38.774,38.774,0,0,0-6.313.064C212.8,519.3,189.252,472.56,188.59,463.6Z" transform="translate(34.473 111.781)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79932" data-name="Path 79932" d="M776.229,112.178c3.347-1.171,6.644-2.724,9.927-4.1.408,3.029.586,6.109.789,9.164,1.2,18.29-2.495,34.582-14.891,48.557-12.092,13.619-31.616,20.95-49.435,22.516-7.675.675-15.362.28-23.037.789-2.253.153-4.238.153-5.791,1.922.191,2.2,1.527,3.793,2.711,5.587l-.98.789-.42-.675c-1.044-1.655-1.667-2.813-3.539-3.64-1.171.344-1.094.038-1.54,1.018-1.515,3.322-.636,7.548-1.845,11.022a24.248,24.248,0,0,1-.2-2.724c-.318-9.508,3.309-21.79,9.915-28.8,10.844-4.187,23.546-4.557,34.912-7.115,13.466-3.042,35.154-8.642,42.753-21.4,6.109-10.246,3.424-22.121.7-32.914Z" transform="translate(170.692 14.801)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79933" data-name="Path 79933" d="M264.167,353.55a55.8,55.8,0,0,1,11.977,4.62,46.126,46.126,0,0,1,7.738,5.969c-1.476.98-3.844,2.711-4.607,4.315-3.067,13.949-26.512,34.391-38.005,41.811-2.1,1.349-4.366,2.762-6.86,3.156a11.652,11.652,0,0,1-5.728-.216c-2.278-2.7-4.493-5.5-5.626-8.884-2.2-6.58-.738-13.122,2.418-19.13C233.429,370.07,248.015,358.513,264.167,353.55Z" transform="translate(43.587 81.762)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79934" data-name="Path 79934" d="M274.712,357.18a46.12,46.12,0,0,1,7.739,5.969c-1.476.98-3.844,2.711-4.607,4.315-16.635,5.817-35.931,22.872-43.313,38.96a23.333,23.333,0,0,0-1.553,6.008,11.653,11.653,0,0,1-5.728-.216c1.566-14.255,15.617-29.529,26.538-37.764,8.362-6.3,14.408-8.426,20.925-17.259Z" transform="translate(45.019 82.752)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79935" data-name="Path 79935" d="M276.626,365.26c-3.067,13.95-26.512,34.39-38.005,41.811-2.1,1.349-4.366,2.762-6.86,3.157a23.2,23.2,0,0,1,1.553-6.008C240.7,388.145,259.99,371.077,276.626,365.26Z" transform="translate(46.249 84.956)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79936" data-name="Path 79936" d="M202.62,226.6c8.018,7.026,13.033,16.1,13.7,26.843.827,13.39-4.378,26.308-13.288,36.172-8.833,9.775-22.5,16.5-35.714,17.081-9.724.433-18.99-2.482-26.245-9.088A37.173,37.173,0,0,1,130.3,279.487c1.7.891,3.386,2.673,4.926,3.882,6.3,4.951,12.142,7.637,20.4,6.491,14.93-2.062,29.872-13.275,38.6-25.188,9.61-13.11,10.908-22.185,8.4-38.082Z" transform="translate(18.572 47.129)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79939" data-name="Path 79939" d="M159.7,443.9c7.535,3.882,14.472,9.775,21.51,14.56,5.028,3.424,10.742,6.479,15.273,10.513.662,8.96,24.208,55.7,29.007,68.883a37.364,37.364,0,0,1,6.313-.064c-5.537,2.176-27.225-.255-34.251-1.2-1.222-7.573-5.766-16.737-8.769-23.827C179.314,490.471,166.446,466.963,159.7,443.9Z" transform="translate(26.592 106.408)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79941" data-name="Path 79941" d="M522.42,53.833c2.711,2.444,7.23,11.442,9.762,14.955,7.433-2.775,15.273-4.786,22.885-7.038-5.6,13.4-9.151,27.836-13.389,41.722l-20.3,65.917c-3.45,11.035-6.555,22.185-9.826,33.271.089-11.162,1.133-22.465,1.744-33.627,2.113-38.54,5.919-76.762,9.126-115.212Z" transform="translate(122.571)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79945" data-name="Path 79945" d="M202.843,356.063a22.442,22.442,0,0,1-1.451-7.14c-.229-6.326,3.207-12.359,7.306-16.928,6.758-7.548,31.349-24.832,41.3-24.845,3.029,0,5.282,2.2,7.178,4.276a39.385,39.385,0,0,1,4.3,9.126c-.191.191-.395.382-.585.573-7.968,7.6-25.125,17.221-35.078,23.445-4.76,2.966-10.424,5.842-14.1,10.17l.216.611c15.032-2.635,40.7-24.246,55.29-13.517,3.666,2.7,7.382,7.726,8.006,12.333a11.413,11.413,0,0,1-2.011,8.388,35.789,35.789,0,0,1-3,3.207c-2.253-1.12-4.48-2.266-6.835-3.2-14.077,3.615-35.231,15.541-49.32,7.675-5.613-3.131-8.719-8.477-11.226-14.166Z" transform="translate(37.962 69.104)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79946" data-name="Path 79946" d="M231.523,148.143c3.984,1.909,7.98,7.446,10.933,10.768,19.474,21.917,36.224,45.731,52.833,69.825,11.4,16.559,23.165,33.347,32.978,50.9l-10.106-8.68c-8.439-7.522-17.348-14.6-25.939-21.968l-45.515-39.367C235.392,200,223.071,190.934,212.66,180.37c6.568-.484,13.046-.611,19.614-.611.153-10.551-.776-21.065-.751-31.629Z" transform="translate(41.039 25.726)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79947" data-name="Path 79947" d="M77.7,305.1c6.135,2.38,12.117,6.262,18.074,9.177,22.719,11.112,44.827,23.19,67.3,34.734l38.2,19.27c6.1,3.067,13.517,6.007,18.9,10.157l-.127,1.005c-3.92,1.387-10.984-1.578-14.828-2.711-17.9-5.638-37.534-8.49-55.863-12.83-10.831-2.571-21.4-6.109-32.163-8.935-17.475-4.6-35.052-8.566-52.477-13.4,6.886-5.448,12.931-11.735,19.092-17.959a67.742,67.742,0,0,1-6.135-18.481Z" transform="translate(0.683 68.545)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79948" data-name="Path 79948" d="M593.752,151.35c3.144,3.08,3.64,12.346,5.829,16.686,4.162,8.273,13.581,11.811,21.854,14.484q-44.827,46.7-91.373,91.691c-5.32,4.722-10.424,9.749-15.592,14.624,8.5-22.019,21.841-43.16,34.276-63.13,15.312-24.6,31.145-48.9,45.031-74.356Z" transform="translate(123.368 26.605)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79951" data-name="Path 79951" d="M282.445,361.875l4.951,6.53c2.749,4.582,3.6,8.591,2.291,13.861-3.895,15.63-18.914,27.3-28.625,39.367a17.357,17.357,0,0,0,1.9,1.4c2.036.153,3.487-1.082,5.117-2.151,15.77-10.233,24.3-31.794,35.88-38.985a15.974,15.974,0,0,1,12.626-1.96,31.417,31.417,0,0,1,8.719,3.971c14.9,9.113,4.887,20.5,11.3,29.745a4.83,4.83,0,0,0,3.576,2.316c1.871.14,3.971-1.069,5.448-2.075,8.388-5.778,9.342-15.464,10.895-24.679,1.413-1.069,1.413-1.413,3.131-1.2a6.017,6.017,0,0,1,1.184,2.609c1.235,6.937-2.2,15.948-6.1,21.65-2.775,4.073-6.657,7.255-11.659,7.942-3.564.484-7.853-.153-10.679-2.533-6.211-5.193-3.844-15.031-4.569-22.108-.178-1.731-.458-3.055-1.82-4.251A6.133,6.133,0,0,0,320.4,393.1c-3.627,3.22-6.122,7.777-8.68,11.85-6.669,10.59-14.217,22.8-25.227,29.35-4.3,2.558-8.808,3.628-13.708,2.266-7.968-2.2-12.3-9.291-16.1-16.012-3.322,2.049-7.217,3.564-11.175,3.3-7.84-.534-13.453-7.433-18.239-12.931a11.365,11.365,0,0,0,5.728.217,18.317,18.317,0,0,0,6.86-3.157c11.493-7.42,34.938-27.874,38-41.811.764-1.6,3.131-3.335,4.607-4.315Z" transform="translate(45.024 84.026)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79952" data-name="Path 79952" d="M280.943,366.993c2.749,4.582,3.6,8.591,2.291,13.861-3.895,15.63-18.913,27.3-28.625,39.367l-1.4.649a7.691,7.691,0,0,1-2.266-3.169c-.4-2.851,6.873-9.139,8.973-11.353,7.026-7.382,17.284-18.061,19.6-28.319.764-3.4.56-6.962,1.286-10.424l.14-.624Z" transform="translate(51.478 85.425)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79953" data-name="Path 79953" d="M130.557,281.25a16.682,16.682,0,0,1-.815-3.437c-1.922-13.186,3.615-27.581,11.493-37.929a49.606,49.606,0,0,1,33.22-19.448,37.414,37.414,0,0,1,28.421,7.93c2.507,15.9,1.209,24.972-8.4,38.082-8.731,11.9-23.674,23.127-38.6,25.188-8.248,1.133-14.09-1.54-20.4-6.491-1.54-1.209-3.22-3-4.926-3.882Z" transform="translate(18.316 45.366)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79954" data-name="Path 79954" d="M161,239.75a9.64,9.64,0,0,1,2.215,1.006c.42.891.84,1.324.662,2.354-.471,2.787-2.546,4.251-4.76,5.728l-2.177-1.222c-.8-1.336-.738-1.476-.611-3.055a19.012,19.012,0,0,1,4.658-4.811Z" transform="translate(25.651 50.719)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79955" data-name="Path 79955" d="M156.767,227.01c2.291.165,3.615.4,5.371,1.909.471,1.6.649,2.075-.064,3.7-1.667,3.831-5.638,5.906-9.189,7.56a12.347,12.347,0,0,1-3.029.382c-1.655-.191-1.871-.764-2.749-2.011a6.815,6.815,0,0,1,.471-3.424c1.9-4.3,4.913-6.529,9.177-8.12Z" transform="translate(23.149 47.244)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79957" data-name="Path 79957" d="M129.41,424.6c4.747,2.062,11.15,4.213,15.07,7.548,7.535,5.906,16.432,10.73,23.483,17.017,6.733,23.063,19.6,46.571,29.083,68.871,3.016,7.089,7.548,16.253,8.77,23.827-5.422-.535-10.768-1.693-16.113-2.737a151.652,151.652,0,0,1-30.8-7.9.96.96,0,0,0,.076-.2c.827-4.187-23.063-53.724-26.6-61.475-2.456-6.173,1.655-13.122,1.68-19.626C134.094,441.2,131.612,432.962,129.41,424.6Z" transform="translate(18.329 101.143)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79958" data-name="Path 79958" d="M141.25,430.523c7.535,5.906,16.432,10.73,23.483,17.017,6.733,23.063,19.6,46.571,29.083,68.87,3.016,7.089,7.548,16.253,8.77,23.826-5.422-.535-10.768-1.693-16.113-2.737-1.782-8.031-7.917-19.8-11.328-27.658l-20.874-47.729c-4.544-10.411-9.406-20.81-13.021-31.6Z" transform="translate(21.559 102.755)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79963" data-name="Path 79963" d="M109.626,507.007c-7.242-2.812-16.355-5.066-20.314-12.359-4.531-8.362-2.5-19.448.191-28.116,4.887-15.833,16.279-32.94,31.438-40.526,6.911-3.462,12.931-3.564,20.123-1.044,2.2,8.362,4.684,16.6,4.646,25.316-.025,6.491-4.137,13.453-1.68,19.626,3.538,7.751,27.428,57.288,26.6,61.475a.965.965,0,0,1-.076.2,252.726,252.726,0,0,1-26.028-8.871A233.868,233.868,0,0,1,109.626,507Z" transform="translate(6.663 100.767)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79964" data-name="Path 79964" d="M104.7,492.727c5.117-3.029,12.779-6.746,16.5-11.3,1.426-1.744,3.144-5.015,5.167-5.855l1.145,1.107c4.1,9.953,9.877,21.23,12.091,31.756a233.86,233.86,0,0,1-34.9-15.708Z" transform="translate(11.589 115.047)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79965" data-name="Path 79965" d="M122.911,433.613c2.38-.191,4.862-.585,7.128.369a10.36,10.36,0,0,1,5.829,5.842c2.876,7.458-.216,18.4-3.22,25.481-4.887,11.468-11.811,21.064-23.636,25.8a14.265,14.265,0,0,1-6.008-.026,9.309,9.309,0,0,1-6.046-5c-3.424-7.216-.445-19.117,2.342-26.257,4.442-11.366,12.27-21.319,23.623-26.207Z" transform="translate(9.058 103.535)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79966" data-name="Path 79966" d="M744.9,328.168c-1.566-4.646-1.566-10.526-1.095-15.388,1.362-13.759,9.7-28.421,20.416-37.089,10.475-8.464,21.777-10.221,34.823-8.9,6.288,4.213,15.324,8.986,17.233,16.979,4.429,3.615,5.969,8.96,6.529,14.5.089.929.152,1.846.2,2.775s.064,1.858.064,2.775-.026,1.858-.077,2.787-.115,1.846-.216,2.775-.216,1.845-.356,2.762-.3,1.833-.484,2.737-.394,1.807-.624,2.711-.484,1.8-.764,2.673-.573,1.769-.891,2.635-.662,1.731-1.018,2.584-.738,1.7-1.145,2.533-.828,1.667-1.273,2.482-.9,1.616-1.387,2.406-.993,1.565-1.514,2.342-1.069,1.515-1.629,2.266-1.133,1.464-1.731,2.177c-6.568,7.942-15.986,13.682-26.41,14.484-4.684.356-11.583-.8-15.783,1.056-1.273-.522-2.507-1.107-3.717-1.744-10.615-5.626-15.974-14.128-19.155-25.316Z" transform="translate(185.853 57.988)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79967" data-name="Path 79967" d="M774.416,272.083a27.762,27.762,0,0,1,3.984.025,6.861,6.861,0,0,1,5.142,2.838,5.575,5.575,0,0,1,.585,4.582c-.827,3.8-3.9,5.613-7.038,7.382a27.9,27.9,0,0,1-4.595.42,4.483,4.483,0,0,1-3.831-1.654,5.712,5.712,0,0,1-1.069-5.015c.878-4.289,3.258-6.376,6.822-8.578Z" transform="translate(192.373 59.522)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79968" data-name="Path 79968" d="M763.263,282.89a6.176,6.176,0,0,1,.687.026c2.036.115,4.416.407,5.626,2.291a7.043,7.043,0,0,1,.6,4.989c-1.26,6.186-5.956,11.85-11.15,15.172a15.244,15.244,0,0,1-2.953-.1,5.208,5.208,0,0,1-3.831-2.533,7.784,7.784,0,0,1-.331-5.906c1.693-6.275,5.931-10.691,11.353-13.924Z" transform="translate(188.023 62.487)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79969" data-name="Path 79969" d="M815.988,280.06c4.429,3.615,5.969,8.96,6.529,14.5.089.929.153,1.846.2,2.775s.064,1.858.064,2.775-.025,1.858-.077,2.787-.114,1.846-.216,2.775-.216,1.846-.357,2.762-.3,1.833-.484,2.737-.394,1.807-.623,2.711-.484,1.8-.764,2.673-.572,1.769-.891,2.635-.662,1.731-1.018,2.584-.738,1.7-1.145,2.533-.828,1.667-1.273,2.482-.9,1.616-1.387,2.406-.993,1.566-1.515,2.342-1.069,1.515-1.629,2.266-1.133,1.464-1.731,2.176c-6.567,7.942-15.986,13.682-26.41,14.484-4.684.356-11.582-.8-15.783,1.056-1.273-.522-2.507-1.107-3.717-1.744-10.615-5.626-15.973-14.128-19.155-25.316l.611-.738c2.405.509,10.233,10.831,14.789,13.568,7.2,4.315,15.642,4.684,23.636,2.647,12.04-3.067,22.337-11.442,28.548-22.108,7.5-12.855,7.5-23.674,3.793-37.751Z" transform="translate(186.146 61.715)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79970" data-name="Path 79970" d="M211.36,561.547c9.711-.585,19.728,2.024,27.072,8.668,5.193,4.7,7.56,10.857,7.866,17.768.815,18.57-8.655,33.3-20.632,46.431l12.97,3.156c1.527,2.825.84,7.408,1.235,10.615-8.082-.522-18.544-1.426-24.5-7.6-12.244,5.931-24.959,9.673-37.725,14.255-5.078,1.82-10.793,4.76-15.986,5.766-7.586,3.729-15.592,6.619-23.19,10.373-21.408,10.6-38.985,25.252-55.124,42.728l5.358-24.3c-7.42-3.958-15.592-6.364-22.872-10.462,16.763-9.584,36.376-19.665,55.124-24.272,27.021-10.615,57.148-10.73,83.609-23.024-8.4-14.268-14.077-28.867-9.724-45.566,2.495-9.559,7.828-19.436,16.521-24.539Z" transform="translate(0.986 138.478)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79971" data-name="Path 79971" d="M193.8,567.82a6.675,6.675,0,0,1,4.429,1.795c2.5,2.6,3.118,6.287,3.093,9.762-.127,15.528-5.5,31.069-16.8,41.976a6.384,6.384,0,0,1-.624-.547c-4.773-5.639-7.293-20.479-6.313-27.581,1.489-10.908,7.789-18.888,16.215-25.392Z" transform="translate(31.414 140.211)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79978" data-name="Path 79978" d="M524.58,269.583c.764,1.451.547.675.649,2.316a4.763,4.763,0,0,1-.6.064c-3-.038-118.712-54.819-130.333-61.183a46.608,46.608,0,0,1-11.1-8.069c-4.047-4.149-5.587-9.877-5.447-15.592.356-14.726,10.259-29.07,20.39-39.011,10.781-10.577,28.18-23.114,43.962-22.656,6.2.178,10.2,2.253,14.459,6.593,18.85,22.2,58.485,108.212,68.03,137.537Z" transform="translate(86.071 19.537)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79979" data-name="Path 79979" d="M417.785,184.765c-5.893,3.742-13.008,8.2-19.906,9.648a6.271,6.271,0,0,1-5.613-.916,4.957,4.957,0,0,1-1.018-2.711c-.662-7.255,8.566-22.185,12.957-27.989,12.486-16.47,27.237-28.141,48.124-31.107-3.691,21.765-17.259,39.775-34.543,53.062Z" transform="translate(89.745 21.242)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79980" data-name="Path 79980" d="M447.183,130.63c18.85,22.2,58.484,108.212,68.03,137.537-2.571-1.349-5.053-4.162-7.357-6.02q-12.448-9.928-24.616-20.2-23.006-19.512-46.749-38.12c-8.019-6.326-15.986-12.982-24.412-18.774,17.3-13.288,30.852-31.3,34.543-53.062l.547-1.362Z" transform="translate(95.437 20.953)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79983" data-name="Path 79983" d="M391.2,341.4c4.684,2.253,13.912,24.552,17.743,30.369l-9.762,6.771-1.184.318c-3.513-2.584-13.823-24.476-16.941-30.139l10.157-7.318Z" transform="translate(86.976 78.447)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80035" data-name="Path 80035" d="M941.059,692.169c-.076-.28-.165-.56-.242-.84-.675-2.7-.891-5.333.827-7.713,1.591-2.189,3.959-2.966,6.466-3.526,1.565,2.176,3.36,4.162,3.055,7.013a3.236,3.236,0,0,1-1.5,2.38c-2.011,1.718-6.122,1.935-8.617,2.686Z" transform="translate(-621.628 66.841)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80039" data-name="Path 80039" d="M9.084,0a13.9,13.9,0,0,1,5.308.942,11.71,11.71,0,0,1,6.045,7.369,9.212,9.212,0,0,1-.916,7.2c-1.68,2.762-4.162,3.894-7.191,4.607a17.186,17.186,0,0,1-5.893-1.2A11.39,11.39,0,0,1,.519,12.562,8.431,8.431,0,0,1,1.028,5.6C2.758,2.431,5.762.967,9.084.013Z" transform="matrix(0.891, -0.454, 0.454, 0.891, 972.293, 1256.442)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80040" data-name="Path 80040" d="M913.236,793.493a14.252,14.252,0,0,1,5.028.534,12.288,12.288,0,0,1,7.369,6.288,9.818,9.818,0,0,1,.369,7.5c-1.3,3.5-3.831,5.219-7.089,6.721a24.926,24.926,0,0,1-5.065-.153,10.816,10.816,0,0,1-6.809-4.709A9.954,9.954,0,0,1,906,801.681c1.069-4.047,3.793-6.109,7.229-8.184Z" transform="translate(-631.097 97.772)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80042" data-name="Path 80042" d="M864.99,669.723c1.553-.1,3.106-.178,4.658-.255,3.449-.14,7.293-.038,9.813,2.774a7.681,7.681,0,0,1,1.986,6.237c-.586,5.244-4.964,10.577-8.973,13.721-2.138.738-4.455,1.362-6.682.6-3.017-1.031-5.4-4.06-6.682-6.861-1.566-3.411-2.189-7.827-.662-11.379,1.285-2.991,3.691-3.818,6.542-4.849Z" transform="translate(-644.2 63.933)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80079" data-name="Path 80079" d="M45.588.39C59.919-1.023,74.1,1.281,86.241,9.325c12.766,8.451,23.063,22.4,25.99,37.547,2.521,13.1.242,27.072-7.433,38.12-8.273,11.913-21.892,19.143-35.943,21.688-15.184,1.833-30.572-3.042-42.83-12.015S2.86,71.526.544,56.2A47.529,47.529,0,0,1,8.957,21.06C17.854,9.057,31.04,2.553,45.588.39Z" transform="matrix(0.891, -0.454, 0.454, 0.891, 885.196, 1344.779)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80080" data-name="Path 80080" d="M35.782.137C48.51-.614,59.621,1.626,70.045,9.3c9.61,7.064,16.559,17.768,18.3,29.643A36.053,36.053,0,0,1,81.5,66.195C74.729,75.04,64.712,79.444,53.932,81A49.233,49.233,0,0,1,24.3,73.818C13.05,66.971,3.759,55.592.87,42.572-1.052,33.942.144,25.2,5.133,17.791,12.184,7.341,23.792,2.3,35.782.111Z" transform="matrix(0.891, -0.454, 0.454, 0.891, 902.003, 1350.727)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80081" data-name="Path 80081" d="M889.219,576.662a36.764,36.764,0,0,1,20.288,5.117c7.178,4.226,12.969,11.1,14.929,19.3,1.489,6.224.828,13.186-2.736,18.621-3.908,5.969-10.259,8.846-17,10.271-8.591.293-15.223-.6-22.554-5.46-7.217-4.786-13.237-12.83-14.765-21.434a20.792,20.792,0,0,1,3.411-16c4.328-5.969,11.34-9.2,18.43-10.424Z" transform="translate(-67.265 770.709)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80082" data-name="Path 80082" d="M886.058,586.1a22.957,22.957,0,0,1,10.093,1.6c5.829,2.533,11.3,7.344,13.364,13.5a9.378,9.378,0,0,1-.28,7.369c-1.782,3.373-5.015,4.913-8.49,6.021-4.862.292-9.342-.14-13.656-2.571-6.161-3.462-8.706-8.922-10.45-15.426a26.141,26.141,0,0,1,2.393-6.249c1.668-2.685,4.112-3.538,7.039-4.264Z" transform="translate(-64.641 773.293)" fill="url(#linear-gradient-2)"/>
        </g>
        <g id="Layer_1-2" data-name="Layer 1" transform="translate(1480.193 261.145) rotate(23.003)" opacity="0.45">
          <path id="Path_79866-2" data-name="Path 79866" d="M308.36,297.933a58.643,58.643,0,0,1,.98-7c.445-1.82,1.082-3.564,2.7-4.633l1.171.3c.9,1.54.713,2.495.585,4.226-1.807,2.342-3.462,4.926-5.435,7.1Z" transform="translate(67.144 63.417)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79867-2" data-name="Path 79867" d="M324.536,318.074c-.051-.127-.153-.229-.153-.356-.114-1.476.28-2.291-.522-3.666a11.4,11.4,0,0,1-3.666-2.724c-.624-.827-.267-1.209-.089-2.138.242.1.471.216.713.318,4.429,1.9,13.772,2.609,16.457,6.224C335.011,318.278,327.858,317.7,324.536,318.074Z" transform="translate(70.276 69.661)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79868-2" data-name="Path 79868" d="M314.534,289.863a107.943,107.943,0,0,0,2.838,15.592c-.713.815-.547.967-1.68,1.069a26.423,26.423,0,0,0-10.042,2.7,68.1,68.1,0,0,0,3.462-12.27c1.986-2.176,3.627-4.76,5.435-7.1Z" transform="translate(66.405 64.385)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79869-2" data-name="Path 79869" d="M308.975,306.94,306,313.075a12.977,12.977,0,0,1-1.056,5.46c-4.162-1.616-9.049-1.26-13.4-2.342-1.629-.407-2.927-.636-3.818-2.075,1.909-1.451,17.221-5.549,21.256-7.179Z" transform="translate(61.514 69.047)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79870-2" data-name="Path 79870" d="M582.851,248.35a8.245,8.245,0,0,1,5.728,2.138,5.264,5.264,0,0,1,1.412,4.2c-.2,2.953-1.934,4.544-3.971,6.44a17.539,17.539,0,0,1-3.551-.484,7.508,7.508,0,0,1-3.946-3.513,5.956,5.956,0,0,1-.191-4.671C579.057,250.285,580.992,249.407,582.851,248.35Z" transform="translate(140.684 53.065)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79871-2" data-name="Path 79871" d="M306.17,321.7c1.26-.382.662-.42,1.578-.115,3.589,1.209,5.715.56,9.24.331a2.831,2.831,0,0,1,.713.14c-1.527,5.053-1.489,10.564-2.545,15.757-1.158,1.056-1.2,1.184-2.737,1.336-3.691-2.724-4.226-12.206-5.931-16.673-.1-.267-.2-.522-.3-.776Z" transform="translate(66.547 72.986)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79872-2" data-name="Path 79872" d="M336.5,165.5c.229.038.458.076.675.127,2.775.624,5.307,1.82,6.822,4.34.115.191.216.382.318.573a5.21,5.21,0,0,1,.255.611,5.391,5.391,0,0,1,.306,1.273,5.579,5.579,0,0,1,.051.649c0,.216,0,.433-.013.662a5.674,5.674,0,0,1-.076.649,5.17,5.17,0,0,1-.153.636c-.776,2.7-2.686,3.793-4.964,5.117-1.744-.242-4.238-.853-5.588-2.049a6.524,6.524,0,0,1-2.469-5.524C331.906,169.229,333.968,167.333,336.5,165.5Z" transform="translate(73.496 30.465)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79873-2" data-name="Path 79873" d="M445.157,294.39c2.749.446,5.766,1.247,7.548,3.564a6.093,6.093,0,0,1,1.133,4.926c-.611,3.169-2.647,4.518-5.168,6.275-2.24-.051-5.078-.84-6.657-2.533a5.8,5.8,0,0,1-1.693-4.4c.1-3.449,2.38-5.727,4.849-7.815Z" transform="translate(103.14 65.624)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79874-2" data-name="Path 79874" d="M654.795,321.863l.993.242c.624,1.527.407,2.431.254,4.022a60.165,60.165,0,0,0-2.494,14.777c-.1,7.917,3.335,12.817,8.54,18.3a51.231,51.231,0,0,1-6.122-3.666,17.766,17.766,0,0,1-1.515-1.273,18.025,18.025,0,0,1-1.375-1.413,17.5,17.5,0,0,1-1.209-1.553c-.382-.535-.726-1.1-1.057-1.667a17.532,17.532,0,0,1-.865-1.769c-.255-.6-.484-1.222-.687-1.846s-.357-1.26-.5-1.909a16.623,16.623,0,0,1-.293-1.947,30.209,30.209,0,0,1,6.313-20.314Z" transform="translate(159.896 73.114)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79875-2" data-name="Path 79875" d="M242.527,447.535a31.1,31.1,0,0,1,4.213-.191,9.1,9.1,0,0,1,5.677,2.927,6.315,6.315,0,0,1,1.667,4.811c-.267,3.156-2.126,4.938-4.429,6.873-.535.051-1.082.089-1.616.115a8.937,8.937,0,0,1-7.09-2.418,7.113,7.113,0,0,1-2.227-5.358c.114-2.953,1.705-4.875,3.793-6.758Z" transform="translate(48.146 107.342)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79877-2" data-name="Path 79877" d="M177.014,394.534a24.146,24.146,0,0,1,4.289.064,7,7,0,0,1,4.786,3.118,6.687,6.687,0,0,1,.853,4.989c-.789,3.64-2.991,5.282-5.982,7.191-2.966-.28-5.8-.738-7.828-3.144a6.763,6.763,0,0,1-1.553-5.486c.535-3.284,2.813-5.027,5.422-6.746Z" transform="translate(29.808 92.924)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79880-2" data-name="Path 79880" d="M247.2,573.853a12.96,12.96,0,0,1,2.546.56,8.874,8.874,0,0,1,5.524,4.811q.153.382.267.764a7.894,7.894,0,0,1,.191.789,5.762,5.762,0,0,1,.1.8c.025.267.025.534.025.8a5.661,5.661,0,0,1-.064.8,7.819,7.819,0,0,1-.674,2.317c-1.285,2.787-3.475,4.022-6.224,5.091-2.673-.293-5.867-.853-7.6-3.169a6.169,6.169,0,0,1-1.209-5.257c.776-4.111,3.793-6.224,7.1-8.324Z" transform="translate(48.48 141.853)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79881-2" data-name="Path 79881" d="M166.216,669.52a19.693,19.693,0,0,1,2.737.255,8.5,8.5,0,0,1,5.842,3.653,7.617,7.617,0,0,1,.929,5.969c-.789,3.284-2.9,4.977-5.6,6.771a9.8,9.8,0,0,1-4.035-.293,9.4,9.4,0,0,1-5.027-4.837,9.231,9.231,0,0,1-.573-1.757,7.582,7.582,0,0,1-.14-.916q-.038-.458-.038-.916a7.387,7.387,0,0,1,.064-.917c.038-.306.089-.611.153-.9a7.876,7.876,0,0,1,.611-1.743c1.171-2.431,2.66-3.4,5.091-4.366Z" transform="translate(26.758 167.953)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79882-2" data-name="Path 79882" d="M941.059,692.169c-.076-.28-.165-.56-.242-.84-.675-2.7-.891-5.333.827-7.713,1.591-2.189,3.959-2.966,6.466-3.526,3.055.827,5.473,1.578,7.051,4.531a8.377,8.377,0,0,1,.331,6.924,9.3,9.3,0,0,1-4.773,5.27,6.882,6.882,0,0,1-.649.254,4.043,4.043,0,0,1-.675.178,6.168,6.168,0,0,1-.7.114,6.378,6.378,0,0,1-2.087-.114,5.911,5.911,0,0,1-.675-.191,6.945,6.945,0,0,1-.649-.267c-2.024-.942-2.927-2.456-4.048-4.3l-.191-.3Z" transform="translate(184.61 605.796)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79883-2" data-name="Path 79883" d="M941.059,692.169c-.076-.28-.165-.56-.242-.84-.675-2.7-.891-5.333.827-7.713,1.591-2.189,3.959-2.966,6.466-3.526,1.565,2.176,3.36,4.162,3.055,7.013a3.236,3.236,0,0,1-1.5,2.38c-2.011,1.718-6.122,1.935-8.617,2.686Z" transform="translate(184.61 605.796)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79884-2" data-name="Path 79884" d="M318.575,302.11a31.919,31.919,0,0,0,6.6,9.011c-.178.929-.535,1.311.089,2.138a11.781,11.781,0,0,0,3.666,2.724c.815,1.374.407,2.189.522,3.666,0,.127.1.242.153.356a20.149,20.149,0,0,0-10.577,7.306,2.826,2.826,0,0,0-.713-.14c-3.513.216-5.651.878-9.24-.331-.9-.3-.318-.267-1.578.115a56.2,56.2,0,0,0-6.262-7.1,13.017,13.017,0,0,0,1.056-5.46,21.1,21.1,0,0,0,2.647,4.811c1.782,2.342,4.073,4.251,7.115,4.544a8.8,8.8,0,0,0,6.822-2.482c3.335-2.94,3.309-6.631,3.7-10.742l-6.911-3.869c-3.093-.051-6.173.484-9.291.509l.458-1.285a26.254,26.254,0,0,1,10.042-2.7c1.133-.1.967-.255,1.68-1.069Z" transform="translate(65.202 67.73)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79886-2" data-name="Path 79886" d="M693.1,342.463a13.353,13.353,0,0,1,2.571.013,8.586,8.586,0,0,1,6.937,11.251c-.929,3.386-3.182,4.837-6.033,6.593a10.71,10.71,0,0,1-1.884-.064,9.388,9.388,0,0,1-6.771-3.818,8.144,8.144,0,0,1-1.031-6.224c.7-3.869,3.118-5.626,6.2-7.751Z" transform="translate(170.349 78.721)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79888-2" data-name="Path 79888" d="M78.911,711.568a15.219,15.219,0,0,1,4.8.369,10.731,10.731,0,0,1,6.466,5.448,9.093,9.093,0,0,1,.484,7.242c-1.133,3.169-3.029,4.556-5.931,6.033a19.074,19.074,0,0,1-4.086-.013,9.685,9.685,0,0,1-6.351-3.856,9.5,9.5,0,0,1-1.5-7.866c.878-3.729,3-5.4,6.122-7.344Z" transform="translate(2.809 -104.81)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79889-2" data-name="Path 79889" d="M123.79,594.183a32.369,32.369,0,0,1,4.582-.382c2.266.064,4.811,1.782,6.186,3.488a8.9,8.9,0,0,1,1.807,6.911c-.5,3.984-2.838,5.817-5.88,8.108a22.11,22.11,0,0,1-5.269,0,10.257,10.257,0,0,1-6.415-4.073,8.02,8.02,0,0,1-1.247-6.173c.738-3.92,3.106-5.766,6.249-7.891Z" transform="translate(15.053 147.297)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79890-2" data-name="Path 79890" d="M110.021,791.02a14.836,14.836,0,0,1,2.367.153c3.36.5,6.555,1.744,8.566,4.634a8.727,8.727,0,0,1,1.375,6.835c-.8,3.984-3.335,5.867-6.618,7.917a16.7,16.7,0,0,1-4.289,0,10.624,10.624,0,0,1-6.962-4.735,9.959,9.959,0,0,1-1.044-7.5c.942-3.818,3.373-5.409,6.593-7.293Z" transform="translate(11.155 -83.127)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79891-2" data-name="Path 79891" d="M428.38,76.021c4.378-.407,6.962.484,10.857,2.469,1.756,3.144,3.6,6.2,2.584,9.915-.993,3.627-4.124,5.549-7.153,7.344a9.111,9.111,0,0,1-3.156.267,11.525,11.525,0,0,1-8.082-4.786q-.287-.439-.535-.916a10.292,10.292,0,0,1-.764-1.973A9.51,9.51,0,0,1,421.9,87.3c-.051-.356-.1-.7-.127-1.056a7.453,7.453,0,0,1,0-1.069,9.618,9.618,0,0,1,.1-1.056,9.478,9.478,0,0,1,.2-1.044C422.933,79.546,425.389,77.739,428.38,76.021Z" transform="translate(98.077 6.03)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79892-2" data-name="Path 79892" d="M428.759,78.75c.356.14.726.242,1.069.407a5.885,5.885,0,0,1,2.889,3.436c-.509,2.367-.993,2.609-2.673,4.251a13.561,13.561,0,0,1-2.686-.191c-1.1-1.069-1.616-1.438-1.464-3.118.191-2.075,1.464-3.4,2.864-4.786Z" transform="translate(99.199 6.801)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79895-2" data-name="Path 79895" d="M649.42,227.795c1.871-6.224,4.137-11.9,9.966-15.4,7.866-4.722,21.816-4.047,30.369-2.011-3.653,1.5-9.953-.115-14.179.255-.5,3.5-2.8,9.724-2.406,12.639,3.4,3.666,11.252.535,15.324,4.417-10.233-1.616-27.288-5.677-36.274.8l-2.8-.713Z" transform="translate(160.18 42.284)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79896-2" data-name="Path 79896" d="M508.02,321.73c10.157,4.544,19.817,10.284,30.089,14.535-2.036,3.857-4.544,7.5-6.911,11.162-4.824-.624-23.6-11.6-29.338-14.459a110.63,110.63,0,0,1,6.161-11.226Z" transform="translate(119.928 73.082)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79897-2" data-name="Path 79897" d="M344.467,241.13c4.646.878,9.189,1.96,13.759,3.2q-5.021,15.178-9.139,30.623a130.531,130.531,0,0,0-13.377-1.413q4.811-16.075,8.769-32.4Z" transform="translate(74.605 51.095)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79899-2" data-name="Path 79899" d="M403.064,270.551c2.533-.216,5.282-.318,7.675.675a12.542,12.542,0,0,1,6.8,6.758c.153.382.293.764.407,1.158a9.594,9.594,0,0,1,.28,1.2c.076.407.127.8.166,1.222a11.773,11.773,0,0,1-.051,2.444c-.051.407-.115.815-.2,1.209a11.458,11.458,0,0,1-.331,1.184,11.289,11.289,0,0,1-.445,1.146c-1.82,4.111-4.875,5.575-8.884,7.115a23.286,23.286,0,0,1-5.244-.127,9.122,9.122,0,0,1-1.171-.267c-.382-.114-.764-.242-1.133-.382a11.187,11.187,0,0,1-1.1-.5,11.054,11.054,0,0,1-1.031-.611,10.5,10.5,0,0,1-.967-.713c-.306-.255-.611-.522-.891-.8s-.547-.585-.8-.891a10.387,10.387,0,0,1-.7-.968,11.669,11.669,0,0,1-1.235-9.508c1.4-4.735,4.7-7.064,8.846-9.329Z" transform="translate(90.427 59.082)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79900-2" data-name="Path 79900" d="M795.373,173.939c9.355-2.075,18.659-4.913,27.912-7.433,10.564-2.876,21.281-5.511,31.705-8.846l4,7.268c-13.021,2.673-25.456,6.5-38.158,10.424-7.2,2.227-14.65,4.2-21.689,6.9-1.362-2.724-2.558-5.524-3.78-8.324Z" transform="translate(199.99 28.326)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79901-2" data-name="Path 79901" d="M251.167,525.552a19,19,0,0,1,6.351.446,12.523,12.523,0,0,1,7.408,6.5,13.207,13.207,0,0,1,.229,10.208c-1.731,4.417-4.849,6.491-9.088,8.235a18.11,18.11,0,0,1-6.033-.522,13.012,13.012,0,0,1-7.5-6.6c-.178-.4-.344-.789-.484-1.2a10.49,10.49,0,0,1-.356-1.235c-.1-.42-.178-.84-.242-1.273a10.729,10.729,0,0,1-.114-1.285c-.013-.433,0-.866.013-1.286a11.325,11.325,0,0,1,.14-1.286,11.1,11.1,0,0,1,.267-1.26c.115-.42.242-.827.394-1.234,1.655-4.442,4.811-6.466,9.011-8.222Z" transform="translate(48.861 128.666)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79902-2" data-name="Path 79902" d="M248.487,531.71a10.7,10.7,0,0,1,3.182.776c1.222,1.222,1.884,1.909,2,3.691.14,2.164-.853,3.64-2.138,5.257a9.1,9.1,0,0,1-2.087-.675,5.244,5.244,0,0,1-2.775-3.6C246.3,534.93,247.316,533.479,248.487,531.71Z" transform="translate(50.294 130.361)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79906-2" data-name="Path 79906" d="M576.852,296.623a18.125,18.125,0,0,1,5.766,1.069,14.66,14.66,0,0,1,8.413,8.693,15.237,15.237,0,0,1,.483,1.527,13.567,13.567,0,0,1,.318,1.565c.076.535.127,1.056.153,1.591s.025,1.069,0,1.6a13.243,13.243,0,0,1-.178,1.591,15.88,15.88,0,0,1-.344,1.565,13.449,13.449,0,0,1-.509,1.527c-.191.5-.42.98-.662,1.464-2.38,4.493-5.982,6.326-10.666,7.777-3.131,0-5.842-.127-8.68-1.578-.445-.229-.866-.471-1.285-.738a13.077,13.077,0,0,1-1.209-.866,13.5,13.5,0,0,1-1.108-.98c-.356-.344-.687-.713-1.006-1.082a13.618,13.618,0,0,1-.891-1.184,12.8,12.8,0,0,1-.764-1.26c-.229-.433-.445-.878-.636-1.336a13.017,13.017,0,0,1-.5-1.4,14.583,14.583,0,0,1-.6-3.055c-.051-.522-.063-1.031-.063-1.553a12.6,12.6,0,0,1,.1-1.553,14.819,14.819,0,0,1,.267-1.527,15.572,15.572,0,0,1,.42-1.5,14.272,14.272,0,0,1,.573-1.451c.216-.471.458-.929.725-1.387,2.762-4.607,6.86-6.313,11.862-7.535Z" transform="translate(136.576 66.229)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79907-2" data-name="Path 79907" d="M248.777,269.086a14.642,14.642,0,0,1,7.293,1.311,14.3,14.3,0,0,1,7.662,8.489,14.739,14.739,0,0,1-.815,11.493c-2.635,5.079-6.7,6.619-11.837,8.12a23.04,23.04,0,0,1-6.9-1.578c-6.211-2.558-8.222-8.171-10.462-13.962a27.525,27.525,0,0,1,2.736-7.178c2.851-4.518,7.446-5.613,12.308-6.7Z" transform="translate(46.784 58.713)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79908-2" data-name="Path 79908" d="M603.885,111.33c4.607.56,7.382,7.306,11.239,10.068a48.793,48.793,0,0,1,6.7.547,3.863,3.863,0,0,1,3.017,2.3c-.637,3.449-4.62,7.891-6.517,11.035.7,2.495,1.82,5.6,1.005,8.171-.331,1.031-.815,1.044-1.68,1.553-2.635.038-4.646-1.184-6.937-2.316-5.5-1.527-7.955,3.233-13,3.233-1.031,0-1.209-.522-1.884-1.184-.776-3.589-.051-7.955.166-11.633-1.884-1.858-5.295-4.506-6.084-7.038-.293-.942.166-1.26.611-2.062,2.024-1.362,4.01-1.578,6.39-1.973,2.571-3.347,4.722-7.115,6.962-10.691Z" transform="translate(143.921 15.688)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79909-2" data-name="Path 79909" d="M861.407,261.673c3.678-.051,7.293,0,10.73,1.489a16.717,16.717,0,0,1,9.151,9.126,16.349,16.349,0,0,1-.395,12.677c-2.355,4.989-6.033,7.191-11.035,9-4.1.6-7.382.636-11.188-1.2a17.113,17.113,0,0,1-1.642-.891,17.529,17.529,0,0,1-1.553-1.056,18.473,18.473,0,0,1-2.724-2.558c-.408-.471-.789-.967-1.159-1.477a18.019,18.019,0,0,1-2.469-5.015,16.115,16.115,0,0,1,1.222-12.715c2.495-4.3,6.4-6.3,11.073-7.4Z" transform="translate(214.447 56.695)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79910-2" data-name="Path 79910" d="M858.322,269.115c2.558-.254,4.162-.509,6.5.713.662.865,1.12,1.171,1.209,2.316.191,2.736-1.413,4.531-3.029,6.529a5.9,5.9,0,0,1-4.887-.127,4.913,4.913,0,0,1-1.617-2.66c-.636-2.788.369-4.48,1.807-6.758Z" transform="translate(216.615 58.672)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79911-2" data-name="Path 79911" d="M624.316,276.82c17.539,9.711,50.173,14.675,59.032,33.6-.9,2.406-1.718,4.926-2.838,7.242-2.584-.573-5.155-1.018-7.777-1.426q-6.51-3.876-13.364-7.089c-8.2-3.806-35.32-13.784-38.413-21.5-1.489-3.716.153-7.089,1.616-10.526l1.744-.306Z" transform="translate(152.255 60.831)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79912-2" data-name="Path 79912" d="M684.674,210.05c8.693.764,21.79,4.162,29.312,8.579,2.495,4.6,17.921,5.511,19.944,9.19-.993,2.8-5.766,9.355-8.159,10.946-3.627.5-15.2-4.518-16.431-3.106-8.566-2.927-17.208-5.868-25.927-8.3-4.073-3.9-11.914-.764-15.324-4.417-.407-2.927,1.909-9.138,2.406-12.639,4.226-.369,10.526,1.26,14.179-.255Z" transform="translate(165.26 42.617)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79915-2" data-name="Path 79915" d="M302.771,99.652c4.149-.242,7.929-.547,11.888,1.056a16.492,16.492,0,0,1,1.667.776,15.208,15.208,0,0,1,1.578.942,18.682,18.682,0,0,1,2.838,2.329c.433.433.84.891,1.235,1.362a17.716,17.716,0,0,1,1.1,1.476,16.478,16.478,0,0,1,.929,1.578c.28.547.547,1.1.776,1.667a18.631,18.631,0,0,1,.191,14.37c-2.278,5.371-6.682,8.375-11.939,10.411-3.908.624-8.006.866-11.735-.687a18.475,18.475,0,0,1-9.953-10.411,19.731,19.731,0,0,1,.357-15.693c2.3-4.7,6.288-7.433,11.086-9.19Z" transform="translate(62.106 12.449)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79916-2" data-name="Path 79916" d="M301.5,107.78a5.744,5.744,0,0,1,1.324.318c1.617.662,1.731.98,2.355,2.507-.471,2.316-1.82,3.576-3.36,5.295-1.756-1.018-2.673-1.26-3.615-3.093C298.531,110.682,300.109,109.32,301.5,107.78Z" transform="translate(64.373 14.719)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79917-2" data-name="Path 79917" d="M673.32,86.6a21.487,21.487,0,0,1,9.8,2.024,17.5,17.5,0,0,1,8.693,11.213,19.5,19.5,0,0,1-2.418,15.617c-3.309,5-7.878,6.962-13.529,8.2a23.892,23.892,0,0,1-10.882-2.609,17.651,17.651,0,0,1-8.795-11.035,18.675,18.675,0,0,1,2.367-14.166C662.094,90.087,667.045,87.987,673.32,86.6Z" transform="translate(161.888 8.942)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79918-2" data-name="Path 79918" d="M667.827,94.57c2.457.445,4.748.84,6.517,2.724a5.644,5.644,0,0,1,1.68,4.9c-.471,2.775-2.278,4.277-4.455,5.829-2.66-.127-4.875-.458-6.67-2.66a6.374,6.374,0,0,1-1.336-4.9c.382-2.94,1.96-4.238,4.264-5.88Z" transform="translate(164.022 11.116)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79919-2" data-name="Path 79919" d="M613.976,351.421c-.2-2.215-.42-4.442-.484-6.657-.229-8.693,4.047-17.832,9.953-23.992,8.489-8.859,22.617-13.224,34.645-13.44,5.32-.089,10.615.7,15.935.6h.573c2.622.382,5.193.827,7.777,1.4-1.527,4.136-3.589,13.11-7.866,14.955-2.52,1.082-5.231.484-7.433,2.317l-1.527,3.487c.153-1.591.369-2.495-.254-4.022l-.993-.242-.382-1.209c-4.849-2.558-16.572.293-21.676,1.782-14.624,4.3-21.141,12.015-28.281,25.023Z" transform="translate(150.377 69.152)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79920-2" data-name="Path 79920" d="M793.85,266.72c11.913,1.744,22.337,6.746,29.631,16.623,6.224,8.426,8.273,19.754,6.567,30-2.418,14.548-11.595,27.3-23.559,35.638-10.959,7.637-23.368,10.742-36.567,8.35a30.385,30.385,0,0,1-7.332-2.164c4.2-1.858,11.1-.7,15.783-1.056,10.424-.8,19.843-6.542,26.41-14.484.6-.713,1.171-1.438,1.731-2.176s1.108-1.489,1.629-2.266,1.031-1.54,1.515-2.342.954-1.591,1.387-2.4.866-1.642,1.273-2.482.789-1.68,1.145-2.533.7-1.718,1.018-2.584.611-1.756.891-2.635.522-1.782.763-2.673.433-1.807.624-2.711.344-1.82.484-2.736.255-1.833.356-2.762.165-1.845.216-2.775.076-1.858.076-2.787-.012-1.858-.063-2.775-.115-1.858-.2-2.775c-.547-5.537-2.1-10.882-6.529-14.5-1.9-7.993-10.946-12.779-17.234-16.979Z" transform="translate(191.051 58.076)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79921-2" data-name="Path 79921" d="M650.064,223.73l2.8.713c16.241,17.933,47.246,30.7,57.861,50.071-1.68,5.473-5.779,18.315-10.984,21.2-.216.115-.446.191-.662.28l-.5-.395c.446-.458.9-.916,1.324-1.4,2.074-2.393,5.638-6.873,5.409-10.068-2.495-2.164-8.362-1.5-11.76-2.851l.05-.738,8.833,1.2c-18.812-14.675-48.875-26.97-55.379-51.917l3-6.1Z" transform="translate(159.536 46.349)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79922-2" data-name="Path 79922" d="M79.264,546.606a22.477,22.477,0,0,1,12.5,2.978,17.686,17.686,0,0,1,2.966,2.164,15.609,15.609,0,0,1,1.3,1.3c.407.458.8.929,1.171,1.426a17.877,17.877,0,0,1,1.018,1.54,16.478,16.478,0,0,1,.853,1.629c.255.56.484,1.133.687,1.705a16.63,16.63,0,0,1,.509,1.769A19.619,19.619,0,0,1,97.439,576.3c-3.347,4.9-8.019,6.962-13.632,8.184-4.506.051-9.011-.407-12.931-2.8a18.008,18.008,0,0,1-8.12-11.608,19.6,19.6,0,0,1,2.673-15.49c3.475-4.989,8.12-6.822,13.861-7.967Z" transform="translate(0 134.417)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79923-2" data-name="Path 79923" d="M79,560.006c.433-.013.878-.026,1.311-.026,2.151,0,4.327.433,5.689,2.3a4.6,4.6,0,0,1,.827,3.767c-.395,2.444-2.316,3.691-4.187,5.015-1.935.293-3.627.344-5.257-.916A4.778,4.778,0,0,1,75.52,566.1C75.558,563.188,76.984,561.812,79,560.006Z" transform="translate(3.629 138.072)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79924-2" data-name="Path 79924" d="M688.12,186.9c1.222-3.462.344-7.7,1.846-11.022.445-.98.369-.662,1.54-1.018,1.871.827,2.495,1.973,3.539,3.64l.42.675.98-.789c4.239,4.569,8.693,8.795,13.364,12.931,6.224,5.511,30.839,24.3,29.936,32.278-.573,5.015-7.408,10.131-10.208,14.2-1.935,2.813-3.411,6.046-5.588,8.668a18.938,18.938,0,0,1-4.251,3.615c-5.269-1.2-10.946-2.482-15.833-4.837,1.234-1.413,12.8,3.589,16.432,3.106,2.393-1.591,7.166-8.146,8.158-10.946-2.024-3.678-17.462-4.6-19.944-9.19,5.982,1.667,11.519,4.9,17.628,6.249,1.426.306,2.418.573,3.691-.242C726.354,224.066,690.729,204.44,688.12,186.9Z" transform="translate(170.737 33.018)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79926-2" data-name="Path 79926" d="M858.176,163.38c2.9,5.5,5.46,10.959,7.866,16.686-13.67,7.013-29.121,12.041-43.529,17.45-5.04,1.9-10.679,3.513-15.464,5.944a230.918,230.918,0,0,0-8.719-22.76c7.038-2.7,14.484-4.671,21.688-6.9,12.69-3.92,25.125-7.751,38.158-10.424Z" transform="translate(200.8 29.886)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79927-2" data-name="Path 79927" d="M622.1,281.651c2.227-6.339,5.282-14.955,11.608-18.137,16.546-8.311,49.791,1.056,66.719,7.064l-.051.738c3.4,1.337,9.266.675,11.76,2.851.229,3.2-3.334,7.675-5.409,10.068-.42.484-.878.942-1.324,1.4l.5.395c-11.277,5.269-57.1-16.661-82.044-4.684l-1.743.3Z" transform="translate(152.728 56.305)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79928-2" data-name="Path 79928" d="M864.171,176.49c2.94,7.879,5.536,15.757,7.9,23.827q-20.714,8.649-41.9,16.126c-6.135,2.138-12.6,3.5-18.6,5.957-1.909-7.56-3.869-15.121-6.389-22.5,4.786-2.431,10.424-4.047,15.464-5.944,14.408-5.409,29.86-10.45,43.53-17.45Z" transform="translate(202.672 33.462)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79929-2" data-name="Path 79929" d="M870.708,195.2c2.151,9.5,4.085,18.926,5.485,28.574-20.326,4.391-41.06,9.291-60.966,15.312-1.375-7.28-2.673-14.764-5.028-21.8,5.995-2.456,12.461-3.818,18.6-5.957q21.173-7.465,41.9-16.126Z" transform="translate(204.038 38.566)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79931-2" data-name="Path 79931" d="M188.6,463.6c13.122,7.967,25.456,17.552,37.853,26.614,6.007,4.391,13.581,8.871,18.608,14.332a20.905,20.905,0,0,1,4.8,9.062,11.952,11.952,0,0,1-1.782,9.61c-4.073,5.842-11.646,7.662-18.29,8.617-1.96.28-3.9.484-5.88.585a38.774,38.774,0,0,0-6.313.064C212.8,519.3,189.252,472.56,188.59,463.6Z" transform="translate(34.473 111.781)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79932-2" data-name="Path 79932" d="M776.229,112.178c3.347-1.171,6.644-2.724,9.927-4.1.408,3.029.586,6.109.789,9.164,1.2,18.29-2.495,34.582-14.891,48.557-12.092,13.619-31.616,20.95-49.435,22.516-7.675.675-15.362.28-23.037.789-2.253.153-4.238.153-5.791,1.922.191,2.2,1.527,3.793,2.711,5.587l-.98.789-.42-.675c-1.044-1.655-1.667-2.813-3.539-3.64-1.171.344-1.094.038-1.54,1.018-1.515,3.322-.636,7.548-1.845,11.022a24.248,24.248,0,0,1-.2-2.724c-.318-9.508,3.309-21.79,9.915-28.8,10.844-4.187,23.546-4.557,34.912-7.115,13.466-3.042,35.154-8.642,42.753-21.4,6.109-10.246,3.424-22.121.7-32.914Z" transform="translate(170.692 14.801)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79933-2" data-name="Path 79933" d="M264.167,353.55a55.8,55.8,0,0,1,11.977,4.62,46.126,46.126,0,0,1,7.738,5.969c-1.476.98-3.844,2.711-4.607,4.315-3.067,13.949-26.512,34.391-38.005,41.811-2.1,1.349-4.366,2.762-6.86,3.156a11.652,11.652,0,0,1-5.728-.216c-2.278-2.7-4.493-5.5-5.626-8.884-2.2-6.58-.738-13.122,2.418-19.13C233.429,370.07,248.015,358.513,264.167,353.55Z" transform="translate(43.587 81.762)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79934-2" data-name="Path 79934" d="M274.712,357.18a46.12,46.12,0,0,1,7.739,5.969c-1.476.98-3.844,2.711-4.607,4.315-16.635,5.817-35.931,22.872-43.313,38.96a23.333,23.333,0,0,0-1.553,6.008,11.653,11.653,0,0,1-5.728-.216c1.566-14.255,15.617-29.529,26.538-37.764,8.362-6.3,14.408-8.426,20.925-17.259Z" transform="translate(45.019 82.752)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79935-2" data-name="Path 79935" d="M276.626,365.26c-3.067,13.95-26.512,34.39-38.005,41.811-2.1,1.349-4.366,2.762-6.86,3.157a23.2,23.2,0,0,1,1.553-6.008C240.7,388.145,259.99,371.077,276.626,365.26Z" transform="translate(46.249 84.956)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79936-2" data-name="Path 79936" d="M202.62,226.6c8.018,7.026,13.033,16.1,13.7,26.843.827,13.39-4.378,26.308-13.288,36.172-8.833,9.775-22.5,16.5-35.714,17.081-9.724.433-18.99-2.482-26.245-9.088A37.173,37.173,0,0,1,130.3,279.487c1.7.891,3.386,2.673,4.926,3.882,6.3,4.951,12.142,7.637,20.4,6.491,14.93-2.062,29.872-13.275,38.6-25.188,9.61-13.11,10.908-22.185,8.4-38.082Z" transform="translate(18.572 47.129)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79939-2" data-name="Path 79939" d="M159.7,443.9c7.535,3.882,14.472,9.775,21.51,14.56,5.028,3.424,10.742,6.479,15.273,10.513.662,8.96,24.208,55.7,29.007,68.883a37.364,37.364,0,0,1,6.313-.064c-5.537,2.176-27.225-.255-34.251-1.2-1.222-7.573-5.766-16.737-8.769-23.827C179.314,490.471,166.446,466.963,159.7,443.9Z" transform="translate(26.592 106.408)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79941-2" data-name="Path 79941" d="M522.42,53.833c2.711,2.444,7.23,11.442,9.762,14.955,7.433-2.775,15.273-4.786,22.885-7.038-5.6,13.4-9.151,27.836-13.389,41.722l-20.3,65.917c-3.45,11.035-6.555,22.185-9.826,33.271.089-11.162,1.133-22.465,1.744-33.627,2.113-38.54,5.919-76.762,9.126-115.212Z" transform="translate(122.571)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79945-2" data-name="Path 79945" d="M202.843,356.063a22.442,22.442,0,0,1-1.451-7.14c-.229-6.326,3.207-12.359,7.306-16.928,6.758-7.548,31.349-24.832,41.3-24.845,3.029,0,5.282,2.2,7.178,4.276a39.385,39.385,0,0,1,4.3,9.126c-.191.191-.395.382-.585.573-7.968,7.6-25.125,17.221-35.078,23.445-4.76,2.966-10.424,5.842-14.1,10.17l.216.611c15.032-2.635,40.7-24.246,55.29-13.517,3.666,2.7,7.382,7.726,8.006,12.333a11.413,11.413,0,0,1-2.011,8.388,35.789,35.789,0,0,1-3,3.207c-2.253-1.12-4.48-2.266-6.835-3.2-14.077,3.615-35.231,15.541-49.32,7.675-5.613-3.131-8.719-8.477-11.226-14.166Z" transform="translate(37.962 69.104)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79946-2" data-name="Path 79946" d="M231.523,148.143c3.984,1.909,7.98,7.446,10.933,10.768,19.474,21.917,36.224,45.731,52.833,69.825,11.4,16.559,23.165,33.347,32.978,50.9l-10.106-8.68c-8.439-7.522-17.348-14.6-25.939-21.968l-45.515-39.367C235.392,200,223.071,190.934,212.66,180.37c6.568-.484,13.046-.611,19.614-.611.153-10.551-.776-21.065-.751-31.629Z" transform="translate(41.039 25.726)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79947-2" data-name="Path 79947" d="M77.7,305.1c6.135,2.38,12.117,6.262,18.074,9.177,22.719,11.112,44.827,23.19,67.3,34.734l38.2,19.27c6.1,3.067,13.517,6.007,18.9,10.157l-.127,1.005c-3.92,1.387-10.984-1.578-14.828-2.711-17.9-5.638-37.534-8.49-55.863-12.83-10.831-2.571-21.4-6.109-32.163-8.935-17.475-4.6-35.052-8.566-52.477-13.4,6.886-5.448,12.931-11.735,19.092-17.959a67.742,67.742,0,0,1-6.135-18.481Z" transform="translate(0.683 68.545)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79948-2" data-name="Path 79948" d="M593.752,151.35c3.144,3.08,3.64,12.346,5.829,16.686,4.162,8.273,13.581,11.811,21.854,14.484q-44.827,46.7-91.373,91.691c-5.32,4.722-10.424,9.749-15.592,14.624,8.5-22.019,21.841-43.16,34.276-63.13,15.312-24.6,31.145-48.9,45.031-74.356Z" transform="translate(123.368 26.605)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79951-2" data-name="Path 79951" d="M282.445,361.875l4.951,6.53c2.749,4.582,3.6,8.591,2.291,13.861-3.895,15.63-18.914,27.3-28.625,39.367a17.357,17.357,0,0,0,1.9,1.4c2.036.153,3.487-1.082,5.117-2.151,15.77-10.233,24.3-31.794,35.88-38.985a15.974,15.974,0,0,1,12.626-1.96,31.417,31.417,0,0,1,8.719,3.971c14.9,9.113,4.887,20.5,11.3,29.745a4.83,4.83,0,0,0,3.576,2.316c1.871.14,3.971-1.069,5.448-2.075,8.388-5.778,9.342-15.464,10.895-24.679,1.413-1.069,1.413-1.413,3.131-1.2a6.017,6.017,0,0,1,1.184,2.609c1.235,6.937-2.2,15.948-6.1,21.65-2.775,4.073-6.657,7.255-11.659,7.942-3.564.484-7.853-.153-10.679-2.533-6.211-5.193-3.844-15.031-4.569-22.108-.178-1.731-.458-3.055-1.82-4.251A6.133,6.133,0,0,0,320.4,393.1c-3.627,3.22-6.122,7.777-8.68,11.85-6.669,10.59-14.217,22.8-25.227,29.35-4.3,2.558-8.808,3.628-13.708,2.266-7.968-2.2-12.3-9.291-16.1-16.012-3.322,2.049-7.217,3.564-11.175,3.3-7.84-.534-13.453-7.433-18.239-12.931a11.365,11.365,0,0,0,5.728.217,18.317,18.317,0,0,0,6.86-3.157c11.493-7.42,34.938-27.874,38-41.811.764-1.6,3.131-3.335,4.607-4.315Z" transform="translate(45.024 84.026)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79952-2" data-name="Path 79952" d="M280.943,366.993c2.749,4.582,3.6,8.591,2.291,13.861-3.895,15.63-18.913,27.3-28.625,39.367l-1.4.649a7.691,7.691,0,0,1-2.266-3.169c-.4-2.851,6.873-9.139,8.973-11.353,7.026-7.382,17.284-18.061,19.6-28.319.764-3.4.56-6.962,1.286-10.424l.14-.624Z" transform="translate(51.478 85.425)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79953-2" data-name="Path 79953" d="M130.557,281.25a16.682,16.682,0,0,1-.815-3.437c-1.922-13.186,3.615-27.581,11.493-37.929a49.606,49.606,0,0,1,33.22-19.448,37.414,37.414,0,0,1,28.421,7.93c2.507,15.9,1.209,24.972-8.4,38.082-8.731,11.9-23.674,23.127-38.6,25.188-8.248,1.133-14.09-1.54-20.4-6.491-1.54-1.209-3.22-3-4.926-3.882Z" transform="translate(18.316 45.366)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79954-2" data-name="Path 79954" d="M161,239.75a9.64,9.64,0,0,1,2.215,1.006c.42.891.84,1.324.662,2.354-.471,2.787-2.546,4.251-4.76,5.728l-2.177-1.222c-.8-1.336-.738-1.476-.611-3.055a19.012,19.012,0,0,1,4.658-4.811Z" transform="translate(25.651 50.719)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79955-2" data-name="Path 79955" d="M156.767,227.01c2.291.165,3.615.4,5.371,1.909.471,1.6.649,2.075-.064,3.7-1.667,3.831-5.638,5.906-9.189,7.56a12.347,12.347,0,0,1-3.029.382c-1.655-.191-1.871-.764-2.749-2.011a6.815,6.815,0,0,1,.471-3.424c1.9-4.3,4.913-6.529,9.177-8.12Z" transform="translate(23.149 47.244)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79957-2" data-name="Path 79957" d="M129.41,424.6c4.747,2.062,11.15,4.213,15.07,7.548,7.535,5.906,16.432,10.73,23.483,17.017,6.733,23.063,19.6,46.571,29.083,68.871,3.016,7.089,7.548,16.253,8.77,23.827-5.422-.535-10.768-1.693-16.113-2.737a151.652,151.652,0,0,1-30.8-7.9.96.96,0,0,0,.076-.2c.827-4.187-23.063-53.724-26.6-61.475-2.456-6.173,1.655-13.122,1.68-19.626C134.094,441.2,131.612,432.962,129.41,424.6Z" transform="translate(18.329 101.143)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79958-2" data-name="Path 79958" d="M141.25,430.523c7.535,5.906,16.432,10.73,23.483,17.017,6.733,23.063,19.6,46.571,29.083,68.87,3.016,7.089,7.548,16.253,8.77,23.826-5.422-.535-10.768-1.693-16.113-2.737-1.782-8.031-7.917-19.8-11.328-27.658l-20.874-47.729c-4.544-10.411-9.406-20.81-13.021-31.6Z" transform="translate(21.559 102.755)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79963-2" data-name="Path 79963" d="M109.626,507.007c-7.242-2.812-16.355-5.066-20.314-12.359-4.531-8.362-2.5-19.448.191-28.116,4.887-15.833,16.279-32.94,31.438-40.526,6.911-3.462,12.931-3.564,20.123-1.044,2.2,8.362,4.684,16.6,4.646,25.316-.025,6.491-4.137,13.453-1.68,19.626,3.538,7.751,27.428,57.288,26.6,61.475a.965.965,0,0,1-.076.2,252.726,252.726,0,0,1-26.028-8.871A233.868,233.868,0,0,1,109.626,507Z" transform="translate(6.663 100.767)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79964-2" data-name="Path 79964" d="M104.7,492.727c5.117-3.029,12.779-6.746,16.5-11.3,1.426-1.744,3.144-5.015,5.167-5.855l1.145,1.107c4.1,9.953,9.877,21.23,12.091,31.756a233.86,233.86,0,0,1-34.9-15.708Z" transform="translate(11.589 115.047)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79965-2" data-name="Path 79965" d="M122.911,433.613c2.38-.191,4.862-.585,7.128.369a10.36,10.36,0,0,1,5.829,5.842c2.876,7.458-.216,18.4-3.22,25.481-4.887,11.468-11.811,21.064-23.636,25.8a14.265,14.265,0,0,1-6.008-.026,9.309,9.309,0,0,1-6.046-5c-3.424-7.216-.445-19.117,2.342-26.257,4.442-11.366,12.27-21.319,23.623-26.207Z" transform="translate(9.058 103.535)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79966-2" data-name="Path 79966" d="M744.9,328.168c-1.566-4.646-1.566-10.526-1.095-15.388,1.362-13.759,9.7-28.421,20.416-37.089,10.475-8.464,21.777-10.221,34.823-8.9,6.288,4.213,15.324,8.986,17.233,16.979,4.429,3.615,5.969,8.96,6.529,14.5.089.929.152,1.846.2,2.775s.064,1.858.064,2.775-.026,1.858-.077,2.787-.115,1.846-.216,2.775-.216,1.845-.356,2.762-.3,1.833-.484,2.737-.394,1.807-.624,2.711-.484,1.8-.764,2.673-.573,1.769-.891,2.635-.662,1.731-1.018,2.584-.738,1.7-1.145,2.533-.828,1.667-1.273,2.482-.9,1.616-1.387,2.406-.993,1.565-1.514,2.342-1.069,1.515-1.629,2.266-1.133,1.464-1.731,2.177c-6.568,7.942-15.986,13.682-26.41,14.484-4.684.356-11.583-.8-15.783,1.056-1.273-.522-2.507-1.107-3.717-1.744-10.615-5.626-15.974-14.128-19.155-25.316Z" transform="translate(185.853 57.988)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79967-2" data-name="Path 79967" d="M774.416,272.083a27.762,27.762,0,0,1,3.984.025,6.861,6.861,0,0,1,5.142,2.838,5.575,5.575,0,0,1,.585,4.582c-.827,3.8-3.9,5.613-7.038,7.382a27.9,27.9,0,0,1-4.595.42,4.483,4.483,0,0,1-3.831-1.654,5.712,5.712,0,0,1-1.069-5.015c.878-4.289,3.258-6.376,6.822-8.578Z" transform="translate(192.373 59.522)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79968-2" data-name="Path 79968" d="M763.263,282.89a6.176,6.176,0,0,1,.687.026c2.036.115,4.416.407,5.626,2.291a7.043,7.043,0,0,1,.6,4.989c-1.26,6.186-5.956,11.85-11.15,15.172a15.244,15.244,0,0,1-2.953-.1,5.208,5.208,0,0,1-3.831-2.533,7.784,7.784,0,0,1-.331-5.906c1.693-6.275,5.931-10.691,11.353-13.924Z" transform="translate(188.023 62.487)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79969-2" data-name="Path 79969" d="M815.988,280.06c4.429,3.615,5.969,8.96,6.529,14.5.089.929.153,1.846.2,2.775s.064,1.858.064,2.775-.025,1.858-.077,2.787-.114,1.846-.216,2.775-.216,1.846-.357,2.762-.3,1.833-.484,2.737-.394,1.807-.623,2.711-.484,1.8-.764,2.673-.572,1.769-.891,2.635-.662,1.731-1.018,2.584-.738,1.7-1.145,2.533-.828,1.667-1.273,2.482-.9,1.616-1.387,2.406-.993,1.566-1.515,2.342-1.069,1.515-1.629,2.266-1.133,1.464-1.731,2.176c-6.567,7.942-15.986,13.682-26.41,14.484-4.684.356-11.582-.8-15.783,1.056-1.273-.522-2.507-1.107-3.717-1.744-10.615-5.626-15.973-14.128-19.155-25.316l.611-.738c2.405.509,10.233,10.831,14.789,13.568,7.2,4.315,15.642,4.684,23.636,2.647,12.04-3.067,22.337-11.442,28.548-22.108,7.5-12.855,7.5-23.674,3.793-37.751Z" transform="translate(186.146 61.715)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79970-2" data-name="Path 79970" d="M211.36,561.547c9.711-.585,19.728,2.024,27.072,8.668,5.193,4.7,7.56,10.857,7.866,17.768.815,18.57-8.655,33.3-20.632,46.431l12.97,3.156c1.527,2.825.84,7.408,1.235,10.615-8.082-.522-18.544-1.426-24.5-7.6-12.244,5.931-24.959,9.673-37.725,14.255-5.078,1.82-10.793,4.76-15.986,5.766-7.586,3.729-15.592,6.619-23.19,10.373-21.408,10.6-38.985,25.252-55.124,42.728l5.358-24.3c-7.42-3.958-15.592-6.364-22.872-10.462,16.763-9.584,36.376-19.665,55.124-24.272,27.021-10.615,57.148-10.73,83.609-23.024-8.4-14.268-14.077-28.867-9.724-45.566,2.495-9.559,7.828-19.436,16.521-24.539Z" transform="translate(0.986 138.478)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79971-2" data-name="Path 79971" d="M193.8,567.82a6.675,6.675,0,0,1,4.429,1.795c2.5,2.6,3.118,6.287,3.093,9.762-.127,15.528-5.5,31.069-16.8,41.976a6.384,6.384,0,0,1-.624-.547c-4.773-5.639-7.293-20.479-6.313-27.581,1.489-10.908,7.789-18.888,16.215-25.392Z" transform="translate(31.414 140.211)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79978-2" data-name="Path 79978" d="M524.58,269.583c.764,1.451.547.675.649,2.316a4.763,4.763,0,0,1-.6.064c-3-.038-118.712-54.819-130.333-61.183a46.608,46.608,0,0,1-11.1-8.069c-4.047-4.149-5.587-9.877-5.447-15.592.356-14.726,10.259-29.07,20.39-39.011,10.781-10.577,28.18-23.114,43.962-22.656,6.2.178,10.2,2.253,14.459,6.593,18.85,22.2,58.485,108.212,68.03,137.537Z" transform="translate(86.071 19.537)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79979-2" data-name="Path 79979" d="M417.785,184.765c-5.893,3.742-13.008,8.2-19.906,9.648a6.271,6.271,0,0,1-5.613-.916,4.957,4.957,0,0,1-1.018-2.711c-.662-7.255,8.566-22.185,12.957-27.989,12.486-16.47,27.237-28.141,48.124-31.107-3.691,21.765-17.259,39.775-34.543,53.062Z" transform="translate(89.745 21.242)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79980-2" data-name="Path 79980" d="M447.183,130.63c18.85,22.2,58.484,108.212,68.03,137.537-2.571-1.349-5.053-4.162-7.357-6.02q-12.448-9.928-24.616-20.2-23.006-19.512-46.749-38.12c-8.019-6.326-15.986-12.982-24.412-18.774,17.3-13.288,30.852-31.3,34.543-53.062l.547-1.362Z" transform="translate(95.437 20.953)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79983-2" data-name="Path 79983" d="M391.2,341.4c4.684,2.253,13.912,24.552,17.743,30.369l-9.762,6.771-1.184.318c-3.513-2.584-13.823-24.476-16.941-30.139l10.157-7.318Z" transform="translate(86.976 78.447)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80035-2" data-name="Path 80035" d="M941.059,692.169c-.076-.28-.165-.56-.242-.84-.675-2.7-.891-5.333.827-7.713,1.591-2.189,3.959-2.966,6.466-3.526,1.565,2.176,3.36,4.162,3.055,7.013a3.236,3.236,0,0,1-1.5,2.38c-2.011,1.718-6.122,1.935-8.617,2.686Z" transform="translate(-621.628 66.841)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80039-2" data-name="Path 80039" d="M9.084,0a13.9,13.9,0,0,1,5.308.942,11.71,11.71,0,0,1,6.045,7.369,9.212,9.212,0,0,1-.916,7.2c-1.68,2.762-4.162,3.894-7.191,4.607a17.186,17.186,0,0,1-5.893-1.2A11.39,11.39,0,0,1,.519,12.562,8.431,8.431,0,0,1,1.028,5.6C2.758,2.431,5.762.967,9.084.013Z" transform="matrix(0.891, -0.454, 0.454, 0.891, 972.293, 1256.442)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80040-2" data-name="Path 80040" d="M913.236,793.493a14.252,14.252,0,0,1,5.028.534,12.288,12.288,0,0,1,7.369,6.288,9.818,9.818,0,0,1,.369,7.5c-1.3,3.5-3.831,5.219-7.089,6.721a24.926,24.926,0,0,1-5.065-.153,10.816,10.816,0,0,1-6.809-4.709A9.954,9.954,0,0,1,906,801.681c1.069-4.047,3.793-6.109,7.229-8.184Z" transform="translate(-631.097 97.772)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80042-2" data-name="Path 80042" d="M864.99,669.723c1.553-.1,3.106-.178,4.658-.255,3.449-.14,7.293-.038,9.813,2.774a7.681,7.681,0,0,1,1.986,6.237c-.586,5.244-4.964,10.577-8.973,13.721-2.138.738-4.455,1.362-6.682.6-3.017-1.031-5.4-4.06-6.682-6.861-1.566-3.411-2.189-7.827-.662-11.379,1.285-2.991,3.691-3.818,6.542-4.849Z" transform="translate(-644.2 63.933)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80079-2" data-name="Path 80079" d="M45.588.39C59.919-1.023,74.1,1.281,86.241,9.325c12.766,8.451,23.063,22.4,25.99,37.547,2.521,13.1.242,27.072-7.433,38.12-8.273,11.913-21.892,19.143-35.943,21.688-15.184,1.833-30.572-3.042-42.83-12.015S2.86,71.526.544,56.2A47.529,47.529,0,0,1,8.957,21.06C17.854,9.057,31.04,2.553,45.588.39Z" transform="matrix(0.891, -0.454, 0.454, 0.891, 885.196, 1344.779)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80080-2" data-name="Path 80080" d="M35.782.137C48.51-.614,59.621,1.626,70.045,9.3c9.61,7.064,16.559,17.768,18.3,29.643A36.053,36.053,0,0,1,81.5,66.195C74.729,75.04,64.712,79.444,53.932,81A49.233,49.233,0,0,1,24.3,73.818C13.05,66.971,3.759,55.592.87,42.572-1.052,33.942.144,25.2,5.133,17.791,12.184,7.341,23.792,2.3,35.782.111Z" transform="matrix(0.891, -0.454, 0.454, 0.891, 902.003, 1350.727)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80081-2" data-name="Path 80081" d="M889.219,576.662a36.764,36.764,0,0,1,20.288,5.117c7.178,4.226,12.969,11.1,14.929,19.3,1.489,6.224.828,13.186-2.736,18.621-3.908,5.969-10.259,8.846-17,10.271-8.591.293-15.223-.6-22.554-5.46-7.217-4.786-13.237-12.83-14.765-21.434a20.792,20.792,0,0,1,3.411-16c4.328-5.969,11.34-9.2,18.43-10.424Z" transform="translate(-67.265 770.709)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80082-2" data-name="Path 80082" d="M886.058,586.1a22.957,22.957,0,0,1,10.093,1.6c5.829,2.533,11.3,7.344,13.364,13.5a9.378,9.378,0,0,1-.28,7.369c-1.782,3.373-5.015,4.913-8.49,6.021-4.862.292-9.342-.14-13.656-2.571-6.161-3.462-8.706-8.922-10.45-15.426a26.141,26.141,0,0,1,2.393-6.249c1.668-2.685,4.112-3.538,7.039-4.264Z" transform="translate(-64.641 773.293)" fill="url(#linear-gradient-2)"/>
        </g>
        <g id="Layer_1-3" data-name="Layer 1" transform="matrix(-0.848, -0.53, 0.53, -0.848, 1910.387, 1556.58)" opacity="0.45">
          <path id="Path_79866-3" data-name="Path 79866" d="M308.36,297.024a54.064,54.064,0,0,1,.9-6.453,6.6,6.6,0,0,1,2.487-4.271l1.08.282c.833,1.42.657,2.3.54,3.9-1.666,2.159-3.192,4.541-5.01,6.547Z" transform="translate(42.664 40.295)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79867-3" data-name="Path 79867" d="M324.169,317.38c-.047-.117-.141-.211-.141-.329-.105-1.361.258-2.112-.481-3.379a10.51,10.51,0,0,1-3.379-2.511c-.575-.762-.246-1.115-.082-1.971.223.094.434.2.657.293,4.083,1.748,12.7,2.4,15.171,5.738C333.826,317.567,327.232,317.04,324.169,317.38Z" transform="translate(44.654 44.263)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79868-3" data-name="Path 79868" d="M313.84,289.862a99.52,99.52,0,0,0,2.617,14.373c-.657.751-.5.892-1.549.986a24.357,24.357,0,0,0-9.258,2.487,62.777,62.777,0,0,0,3.191-11.311c1.83-2.006,3.344-4.388,5.01-6.547Z" transform="translate(42.194 40.911)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79869-3" data-name="Path 79869" d="M307.315,306.94l-2.746,5.655a11.963,11.963,0,0,1-.974,5.034c-3.837-1.49-8.342-1.161-12.355-2.159-1.5-.375-2.7-.587-3.52-1.912C289.48,312.22,303.6,308.442,307.315,306.94Z" transform="translate(39.086 43.873)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79870-3" data-name="Path 79870" d="M582.468,248.35a7.6,7.6,0,0,1,5.28,1.971,4.853,4.853,0,0,1,1.3,3.872c-.188,2.722-1.783,4.189-3.661,5.937a16.166,16.166,0,0,1-3.273-.446,6.922,6.922,0,0,1-3.638-3.238,5.491,5.491,0,0,1-.176-4.306C578.971,250.133,580.755,249.324,582.468,248.35Z" transform="translate(89.391 33.717)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79871-3" data-name="Path 79871" d="M306.17,321.673c1.162-.352.61-.387,1.455-.106,3.309,1.115,5.268.516,8.518.3a2.611,2.611,0,0,1,.657.129c-1.408,4.658-1.373,9.739-2.347,14.526-1.068.974-1.1,1.091-2.523,1.232-3.4-2.511-3.9-11.252-5.468-15.37-.094-.246-.188-.481-.282-.716Z" transform="translate(42.284 46.376)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79872-3" data-name="Path 79872" d="M336.121,165.5c.211.035.422.07.622.117,2.558.575,4.893,1.678,6.289,4,.106.176.2.352.293.528a5.06,5.06,0,0,1,.235.563,4.955,4.955,0,0,1,.282,1.173,5.16,5.16,0,0,1,.047.6c0,.2,0,.4-.012.61a5.227,5.227,0,0,1-.07.6,4.766,4.766,0,0,1-.141.587c-.716,2.487-2.476,3.5-4.576,4.717a10.724,10.724,0,0,1-5.151-1.889,6.014,6.014,0,0,1-2.276-5.092C331.885,168.938,333.786,167.19,336.121,165.5Z" transform="translate(46.7 19.357)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79873-3" data-name="Path 79873" d="M444.779,294.39c2.534.411,5.315,1.15,6.958,3.285a5.617,5.617,0,0,1,1.044,4.541c-.563,2.922-2.44,4.165-4.764,5.784a9.112,9.112,0,0,1-6.136-2.335,5.344,5.344,0,0,1-1.56-4.06c.094-3.18,2.194-5.28,4.47-7.2Z" transform="translate(65.535 41.697)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79874-3" data-name="Path 79874" d="M654.294,321.862l.915.223a6.538,6.538,0,0,1,.234,3.708,55.465,55.465,0,0,0-2.3,13.622c-.094,7.3,3.074,11.815,7.873,16.872a47.242,47.242,0,0,1-5.644-3.379,16.376,16.376,0,0,1-1.4-1.173,16.608,16.608,0,0,1-1.267-1.3,16.163,16.163,0,0,1-1.11-1.435c-.352-.493-.669-1.009-.974-1.537a16.162,16.162,0,0,1-.8-1.631c-.235-.551-.446-1.126-.634-1.7a17.481,17.481,0,0,1-.458-1.76,15.9,15.9,0,0,1-.27-1.8,27.849,27.849,0,0,1,5.82-18.726Z" transform="translate(101.599 46.457)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79875-3" data-name="Path 79875" d="M242.229,447.519a28.664,28.664,0,0,1,3.884-.176,8.391,8.391,0,0,1,5.233,2.7,5.822,5.822,0,0,1,1.537,4.435c-.246,2.91-1.96,4.552-4.083,6.336-.493.047-1,.082-1.49.106a8.239,8.239,0,0,1-6.536-2.229,6.557,6.557,0,0,1-2.053-4.94c.105-2.722,1.572-4.494,3.5-6.23Z" transform="translate(30.592 68.205)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79877-3" data-name="Path 79877" d="M176.582,394.529a22.25,22.25,0,0,1,3.954.059,6.453,6.453,0,0,1,4.412,2.875,6.164,6.164,0,0,1,.786,4.6c-.728,3.356-2.757,4.869-5.515,6.629-2.734-.258-5.35-.68-7.216-2.9a6.235,6.235,0,0,1-1.431-5.057c.493-3.027,2.593-4.635,5-6.219Z" transform="translate(18.94 59.044)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79880-3" data-name="Path 79880" d="M246.628,573.852a11.95,11.95,0,0,1,2.347.516,8.181,8.181,0,0,1,5.092,4.435q.141.352.246.7a7.277,7.277,0,0,1,.176.728,5.309,5.309,0,0,1,.094.739c.023.247.023.493.023.739a5.222,5.222,0,0,1-.059.739,7.2,7.2,0,0,1-.622,2.136c-1.185,2.569-3.2,3.708-5.738,4.693-2.464-.27-5.409-.786-7-2.921a5.687,5.687,0,0,1-1.115-4.846c.716-3.79,3.5-5.738,6.547-7.674Z" transform="translate(30.804 90.134)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79881-3" data-name="Path 79881" d="M165.754,669.52a18.141,18.141,0,0,1,2.523.235,7.833,7.833,0,0,1,5.386,3.367,7.022,7.022,0,0,1,.856,5.5c-.727,3.027-2.675,4.588-5.163,6.242a9.031,9.031,0,0,1-3.72-.27A8.66,8.66,0,0,1,161,680.139a8.514,8.514,0,0,1-.528-1.62,6.994,6.994,0,0,1-.129-.845q-.035-.422-.035-.845a6.813,6.813,0,0,1,.059-.845c.035-.282.082-.563.141-.833a7.257,7.257,0,0,1,.563-1.607c1.08-2.241,2.452-3.133,4.693-4.025Z" transform="translate(17.002 106.718)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79884-3" data-name="Path 79884" d="M317.221,302.11a29.425,29.425,0,0,0,6.089,8.307c-.164.857-.493,1.208.082,1.971a10.863,10.863,0,0,0,3.379,2.511c.751,1.267.375,2.018.481,3.379,0,.117.094.223.141.328a18.575,18.575,0,0,0-9.75,6.735,2.6,2.6,0,0,0-.657-.129c-3.238.2-5.21.81-8.518-.3-.833-.282-.293-.246-1.455.106a51.814,51.814,0,0,0-5.773-6.547,12,12,0,0,0,.974-5.033,19.459,19.459,0,0,0,2.44,4.435c1.643,2.159,3.755,3.919,6.559,4.189a8.114,8.114,0,0,0,6.289-2.288c3.074-2.71,3.051-6.113,3.414-9.9l-6.371-3.567c-2.851-.047-5.691.446-8.565.469l.422-1.185a24.2,24.2,0,0,1,9.258-2.487c1.044-.094.892-.235,1.549-.986Z" transform="translate(41.429 43.036)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79886-3" data-name="Path 79886" d="M692.6,342.458a12.309,12.309,0,0,1,2.37.012,7.915,7.915,0,0,1,6.395,10.372c-.856,3.121-2.933,4.459-5.562,6.078a9.876,9.876,0,0,1-1.736-.059,8.655,8.655,0,0,1-6.242-3.52,7.507,7.507,0,0,1-.95-5.738c.645-3.567,2.875-5.186,5.714-7.145Z" transform="translate(108.24 50.019)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79888-3" data-name="Path 79888" d="M78.411,711.565a14.028,14.028,0,0,1,4.423.34,9.893,9.893,0,0,1,5.961,5.022,8.383,8.383,0,0,1,.446,6.676c-1.044,2.922-2.793,4.2-5.468,5.561a17.6,17.6,0,0,1-3.766-.012,8.929,8.929,0,0,1-5.855-3.555,8.754,8.754,0,0,1-1.385-7.252c.81-3.438,2.769-4.975,5.644-6.77Z" transform="translate(1.785 -148.014)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79889-3" data-name="Path 79889" d="M123.291,594.153a29.831,29.831,0,0,1,4.224-.352,8.619,8.619,0,0,1,5.7,3.215,8.206,8.206,0,0,1,1.666,6.371c-.458,3.673-2.617,5.362-5.421,7.474a20.389,20.389,0,0,1-4.858,0,9.456,9.456,0,0,1-5.914-3.755,7.393,7.393,0,0,1-1.15-5.691c.681-3.614,2.863-5.315,5.761-7.274Z" transform="translate(9.564 93.593)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79890-3" data-name="Path 79890" d="M109.481,791.02a13.7,13.7,0,0,1,2.182.141c3.1.458,6.043,1.608,7.9,4.271a8.045,8.045,0,0,1,1.267,6.3c-.739,3.672-3.074,5.409-6.1,7.3a15.393,15.393,0,0,1-3.954,0,9.794,9.794,0,0,1-6.418-4.365,9.18,9.18,0,0,1-.962-6.911c.868-3.52,3.109-4.986,6.078-6.723Z" transform="translate(7.088 -134.237)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79891-3" data-name="Path 79891" d="M427.862,76.013c4.036-.375,6.418.446,10.008,2.276,1.619,2.9,3.321,5.714,2.382,9.14-.915,3.344-3.8,5.116-6.594,6.77a8.4,8.4,0,0,1-2.91.246,10.625,10.625,0,0,1-7.451-4.412,9.048,9.048,0,0,1-.493-.845,9.495,9.495,0,0,1-.7-1.819,8.776,8.776,0,0,1-.211-.962c-.047-.329-.094-.645-.117-.974a6.869,6.869,0,0,1,0-.986,8.851,8.851,0,0,1,.094-.974,8.728,8.728,0,0,1,.188-.962C422.841,79.263,425.1,77.6,427.862,76.013Z" transform="translate(62.318 3.831)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79892-3" data-name="Path 79892" d="M428.534,78.75c.329.129.669.223.986.375a5.425,5.425,0,0,1,2.663,3.168c-.469,2.182-.915,2.4-2.464,3.919a12.5,12.5,0,0,1-2.476-.176c-1.009-.986-1.49-1.326-1.349-2.875.176-1.913,1.349-3.133,2.64-4.412Z" transform="translate(63.031 4.321)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79895-3" data-name="Path 79895" d="M649.42,226.313c1.725-5.738,3.813-10.971,9.187-14.2,7.251-4.353,20.111-3.731,28-1.854-3.367,1.385-9.175-.106-13.071.235-.458,3.227-2.581,8.964-2.218,11.651,3.133,3.379,10.372.493,14.127,4.071-9.434-1.49-25.156-5.233-33.44.739l-2.581-.657Z" transform="translate(101.779 26.867)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79896-3" data-name="Path 79896" d="M507.539,321.73c9.363,4.189,18.269,9.48,27.738,13.4-1.877,3.555-4.189,6.911-6.371,10.29-4.447-.575-21.754-10.689-27.045-13.329a101.993,101.993,0,0,1,5.679-10.349Z" transform="translate(76.203 46.436)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79897-3" data-name="Path 79897" d="M343.783,241.13c4.283.81,8.471,1.807,12.684,2.945q-4.629,13.992-8.425,28.23A120.362,120.362,0,0,0,335.71,271q4.435-14.819,8.084-29.873Z" transform="translate(47.404 32.466)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79899-3" data-name="Path 79899" d="M402.334,270.539a15.2,15.2,0,0,1,7.075.622,11.562,11.562,0,0,1,6.266,6.23,10.882,10.882,0,0,1,.375,1.068,8.842,8.842,0,0,1,.258,1.1c.07.375.117.739.153,1.126a10.852,10.852,0,0,1-.047,2.253c-.047.376-.106.751-.188,1.115s-.188.728-.3,1.091a10.4,10.4,0,0,1-.411,1.056c-1.678,3.79-4.494,5.139-8.19,6.559a21.467,21.467,0,0,1-4.834-.117,8.418,8.418,0,0,1-1.08-.246q-.528-.157-1.044-.352a10.4,10.4,0,0,1-1.009-.458,9.938,9.938,0,0,1-.95-.563,9.676,9.676,0,0,1-.892-.657c-.282-.235-.563-.481-.821-.739s-.5-.54-.739-.821a9.583,9.583,0,0,1-.645-.892,10.757,10.757,0,0,1-1.138-8.765c1.291-4.365,4.33-6.512,8.155-8.6Z" transform="translate(57.458 37.541)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79900-3" data-name="Path 79900" d="M795.372,172.667c8.624-1.913,17.2-4.529,25.731-6.852,9.739-2.652,19.618-5.081,29.228-8.155l3.684,6.7c-12,2.464-23.467,6-35.176,9.609-6.641,2.053-13.505,3.872-19.994,6.359-1.255-2.511-2.358-5.092-3.485-7.674Z" transform="translate(127.074 17.998)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79901-3" data-name="Path 79901" d="M250.4,525.548a17.515,17.515,0,0,1,5.855.411,11.544,11.544,0,0,1,6.829,6,12.176,12.176,0,0,1,.211,9.41c-1.6,4.072-4.47,5.984-8.378,7.591a16.694,16.694,0,0,1-5.562-.481,12,12,0,0,1-6.911-6.089c-.164-.364-.317-.727-.446-1.1a9.669,9.669,0,0,1-.329-1.138c-.094-.387-.164-.774-.223-1.173a9.888,9.888,0,0,1-.1-1.185c-.012-.4,0-.8.012-1.185a10.483,10.483,0,0,1,.129-1.185,10.236,10.236,0,0,1,.246-1.162c.106-.387.223-.763.364-1.138,1.525-4.095,4.435-5.96,8.307-7.58Z" transform="translate(31.046 81.754)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79902-3" data-name="Path 79902" d="M248.338,531.71a9.867,9.867,0,0,1,2.933.716c1.126,1.126,1.737,1.76,1.842,3.4.129,1.995-.786,3.356-1.971,4.846a8.4,8.4,0,0,1-1.924-.622,4.835,4.835,0,0,1-2.558-3.32C246.32,534.679,247.259,533.341,248.338,531.71Z" transform="translate(31.957 82.832)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79906-3" data-name="Path 79906" d="M575.761,296.622a16.708,16.708,0,0,1,5.315.986,13.514,13.514,0,0,1,7.756,8.014,14.042,14.042,0,0,1,.445,1.408,12.528,12.528,0,0,1,.293,1.443c.07.493.117.974.141,1.467s.023.986,0,1.478a12.142,12.142,0,0,1-.164,1.467q-.123.722-.317,1.443a12.377,12.377,0,0,1-.469,1.408c-.176.458-.387.9-.61,1.349-2.194,4.142-5.514,5.831-9.832,7.169-2.886,0-5.385-.117-8-1.455-.411-.211-.8-.434-1.185-.681a12.059,12.059,0,0,1-1.115-.8,12.453,12.453,0,0,1-1.021-.9c-.329-.317-.634-.657-.927-1a13.163,13.163,0,0,1-.821-1.091,11.816,11.816,0,0,1-.7-1.162c-.211-.4-.411-.81-.586-1.232a11.963,11.963,0,0,1-.458-1.291,13.452,13.452,0,0,1-.552-2.816c-.047-.481-.058-.95-.058-1.431a11.625,11.625,0,0,1,.094-1.431,13.664,13.664,0,0,1,.246-1.408c.106-.469.235-.927.387-1.384a13.178,13.178,0,0,1,.528-1.338c.2-.434.422-.857.669-1.279,2.546-4.247,6.324-5.82,10.935-6.946Z" transform="translate(86.781 42.082)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79907-3" data-name="Path 79907" d="M247.6,269.084a13.5,13.5,0,0,1,6.723,1.209,13.18,13.18,0,0,1,7.063,7.826,13.588,13.588,0,0,1-.751,10.6c-2.429,4.682-6.172,6.1-10.912,7.486a21.237,21.237,0,0,1-6.359-1.455c-5.726-2.358-7.58-7.533-9.645-12.871a25.374,25.374,0,0,1,2.523-6.618c2.628-4.165,6.864-5.174,11.346-6.172Z" transform="translate(29.726 37.306)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79908-3" data-name="Path 79908" d="M602.786,111.33c4.247.516,6.8,6.735,10.361,9.281a44.985,44.985,0,0,1,6.172.5,3.561,3.561,0,0,1,2.781,2.124c-.587,3.18-4.259,7.275-6.008,10.173.645,2.3,1.678,5.163.927,7.533-.3.95-.751.962-1.549,1.431-2.429.035-4.283-1.091-6.4-2.135-5.069-1.408-7.333,2.98-11.98,2.98-.95,0-1.115-.481-1.737-1.091-.716-3.309-.047-7.333.153-10.724-1.736-1.713-4.881-4.154-5.609-6.488-.27-.868.153-1.162.563-1.9,1.866-1.255,3.7-1.455,5.89-1.819,2.37-3.086,4.353-6.559,6.418-9.856Z" transform="translate(91.448 9.968)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79909-3" data-name="Path 79909" d="M860.387,261.672c3.391-.047,6.723,0,9.891,1.373a15.411,15.411,0,0,1,8.436,8.413,15.071,15.071,0,0,1-.364,11.686c-2.171,4.6-5.561,6.629-10.172,8.3-3.778.552-6.806.587-10.314-1.1a15.768,15.768,0,0,1-1.514-.822,16.13,16.13,0,0,1-1.431-.974,17.036,17.036,0,0,1-2.511-2.358c-.376-.434-.728-.892-1.068-1.361a16.606,16.606,0,0,1-2.276-4.623,14.856,14.856,0,0,1,1.126-11.722c2.3-3.966,5.9-5.808,10.208-6.817Z" transform="translate(136.26 36.024)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79910-3" data-name="Path 79910" d="M858.165,269.1c2.358-.235,3.837-.469,6,.657.61.8,1.033,1.08,1.115,2.135.176,2.523-1.3,4.177-2.792,6.019a5.442,5.442,0,0,1-4.506-.117,4.529,4.529,0,0,1-1.49-2.452c-.586-2.57.34-4.13,1.666-6.23Z" transform="translate(137.638 37.28)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79911-3" data-name="Path 79911" d="M624.007,276.82c16.169,8.953,46.253,13.529,54.419,30.976-.833,2.218-1.584,4.541-2.616,6.676-2.382-.528-4.752-.939-7.169-1.314q-6-3.573-12.32-6.535c-7.556-3.508-32.56-12.707-35.411-19.818-1.373-3.426.141-6.535,1.49-9.7l1.608-.282Z" transform="translate(96.743 38.652)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79912-3" data-name="Path 79912" d="M683.375,210.05c8.014.7,20.087,3.837,27.022,7.908,2.3,4.236,16.521,5.081,18.386,8.471-.915,2.581-5.315,8.624-7.521,10.091-3.344.458-14.009-4.165-15.147-2.863-7.9-2.7-15.864-5.409-23.9-7.65-3.754-3.59-10.983-.7-14.127-4.072-.375-2.7,1.76-8.424,2.218-11.651,3.9-.34,9.7,1.162,13.071-.235Z" transform="translate(105.006 27.079)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79915-3" data-name="Path 79915" d="M301.764,99.637c3.825-.223,7.31-.5,10.959.974a15.193,15.193,0,0,1,1.537.716,14.018,14.018,0,0,1,1.455.868,17.218,17.218,0,0,1,2.617,2.147c.4.4.774.821,1.138,1.255a16.339,16.339,0,0,1,1.009,1.361,15.209,15.209,0,0,1,.857,1.455c.258.5.5,1.009.716,1.537a17.175,17.175,0,0,1,.176,13.247c-2.1,4.951-6.16,7.72-11.006,9.6-3.6.575-7.38.8-10.818-.634a17.031,17.031,0,0,1-9.175-9.6,18.189,18.189,0,0,1,.329-14.467c2.124-4.33,5.8-6.852,10.22-8.471Z" transform="translate(39.462 7.91)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79916-3" data-name="Path 79916" d="M301.239,107.78a5.3,5.3,0,0,1,1.22.293c1.49.61,1.6.9,2.171,2.311-.434,2.135-1.678,3.3-3.1,4.881-1.619-.939-2.464-1.162-3.332-2.851C298.505,110.455,299.96,109.2,301.239,107.78Z" transform="translate(40.903 9.353)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79917-3" data-name="Path 79917" d="M671.942,86.6a19.808,19.808,0,0,1,9.034,1.866A16.128,16.128,0,0,1,688.99,98.8a17.981,17.981,0,0,1-2.229,14.4c-3.05,4.611-7.263,6.418-12.472,7.556a22.026,22.026,0,0,1-10.032-2.405,16.272,16.272,0,0,1-8.108-10.173,17.216,17.216,0,0,1,2.182-13.059C661.593,89.815,666.157,87.879,671.942,86.6Z" transform="translate(102.864 5.682)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79918-3" data-name="Path 79918" d="M667.489,94.57c2.265.411,4.377.774,6.007,2.511a5.2,5.2,0,0,1,1.549,4.517c-.434,2.558-2.1,3.942-4.107,5.374-2.452-.117-4.494-.422-6.148-2.452a5.876,5.876,0,0,1-1.232-4.52c.352-2.71,1.807-3.907,3.931-5.421Z" transform="translate(104.22 7.063)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79919-3" data-name="Path 79919" d="M613.937,347.975c-.188-2.042-.387-4.1-.446-6.136-.211-8.014,3.731-16.438,9.176-22.117,7.826-8.166,20.85-12.191,31.938-12.39,4.9-.082,9.786.645,14.69.551h.528c2.417.352,4.787.763,7.169,1.291-1.408,3.813-3.309,12.085-7.251,13.787-2.323,1-4.823.446-6.852,2.136l-1.408,3.215a6.546,6.546,0,0,0-.234-3.708l-.915-.223-.352-1.115c-4.471-2.358-15.277.27-19.982,1.643-13.482,3.966-19.489,11.076-26.071,23.068Z" transform="translate(95.55 43.939)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79920-3" data-name="Path 79920" d="M791.407,266.72c10.982,1.608,20.592,6.219,27.315,15.324,5.737,7.767,7.626,18.21,6.054,27.655-2.229,13.411-10.689,25.168-21.718,32.853-10.1,7.04-21.542,9.9-33.71,7.7a28.012,28.012,0,0,1-6.759-1.995c3.872-1.713,10.232-.645,14.549-.974,9.61-.739,18.292-6.031,24.347-13.352.552-.657,1.08-1.326,1.6-2.006s1.021-1.373,1.5-2.089.95-1.42,1.4-2.159.88-1.467,1.279-2.217.8-1.514,1.174-2.288.728-1.549,1.056-2.335.645-1.584.939-2.382.563-1.619.821-2.429.481-1.643.7-2.464.4-1.666.575-2.5.317-1.678.446-2.522.235-1.69.328-2.546.152-1.7.2-2.558.07-1.713.07-2.57-.011-1.713-.058-2.558-.106-1.713-.187-2.558c-.5-5.1-1.936-10.032-6.019-13.364-1.748-7.368-10.091-11.78-15.887-15.652Z" transform="translate(121.394 36.902)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79921-3" data-name="Path 79921" d="M649.829,223.73l2.581.657c14.972,16.532,43.554,28.3,53.339,46.159-1.549,5.045-5.327,16.884-10.126,19.548-.2.106-.411.176-.61.258l-.458-.364c.411-.422.833-.845,1.22-1.291,1.912-2.206,5.2-6.336,4.987-9.281-2.3-1.995-7.709-1.384-10.841-2.628l.047-.681,8.143,1.1c-17.342-13.528-45.056-24.863-51.051-47.86l2.769-5.62Z" transform="translate(101.37 29.45)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79922-3" data-name="Path 79922" d="M77.932,546.6a20.721,20.721,0,0,1,11.522,2.746,16.3,16.3,0,0,1,2.734,1.995,14.394,14.394,0,0,1,1.2,1.2c.375.423.739.857,1.079,1.314a16.468,16.468,0,0,1,.939,1.42,15.172,15.172,0,0,1,.786,1.5c.235.516.446,1.044.634,1.572a15.331,15.331,0,0,1,.469,1.631,18.086,18.086,0,0,1-2.6,14c-3.086,4.517-7.392,6.418-12.566,7.544-4.154.047-8.307-.376-11.921-2.581a16.6,16.6,0,0,1-7.486-10.7,18.068,18.068,0,0,1,2.464-14.279c3.2-4.6,7.486-6.289,12.778-7.345Z" transform="translate(0 85.409)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79923-3" data-name="Path 79923" d="M78.723,560c.4-.012.81-.024,1.209-.024,1.983,0,3.989.4,5.245,2.124a4.238,4.238,0,0,1,.763,3.473c-.364,2.253-2.135,3.4-3.86,4.623-1.783.27-3.344.317-4.846-.845a4.4,4.4,0,0,1-1.713-3.731C75.555,562.937,76.869,561.669,78.723,560Z" transform="translate(2.306 87.732)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79924-3" data-name="Path 79924" d="M688.12,185.96c1.127-3.191.317-7.1,1.7-10.161.411-.9.34-.61,1.42-.939,1.725.763,2.3,1.819,3.262,3.356l.387.622.9-.728c3.907,4.212,8.014,8.108,12.32,11.921,5.738,5.08,28.43,22.4,27.6,29.756-.528,4.623-6.829,9.34-9.41,13.094-1.784,2.593-3.145,5.573-5.151,7.99a17.459,17.459,0,0,1-3.919,3.332c-4.857-1.1-10.091-2.288-14.6-4.459,1.138-1.3,11.8,3.309,15.148,2.863,2.206-1.467,6.606-7.509,7.521-10.091-1.866-3.391-16.1-4.236-18.386-8.472,5.515,1.537,10.619,4.517,16.251,5.761,1.314.282,2.229.528,3.4-.223C723.367,220.221,690.526,202.128,688.12,185.96Z" transform="translate(108.487 20.98)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79926-3" data-name="Path 79926" d="M853.5,163.38c2.675,5.069,5.034,10.1,7.251,15.382-12.6,6.465-26.846,11.1-40.128,16.086-4.646,1.748-9.844,3.238-14.256,5.479a212.89,212.89,0,0,0-8.037-20.979c6.488-2.487,13.352-4.306,19.993-6.359,11.7-3.614,23.161-7.146,35.176-9.61Z" transform="translate(127.589 18.99)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79927-3" data-name="Path 79927" d="M622.1,279.977c2.053-5.843,4.87-13.787,10.7-16.72,15.253-7.662,45.9.974,61.506,6.512l-.047.68c3.133,1.232,8.542.622,10.841,2.628.211,2.945-3.074,7.075-4.987,9.281-.387.446-.81.868-1.22,1.291l.458.364c-10.4,4.858-52.635-15.359-75.633-4.318l-1.607.282Z" transform="translate(97.043 35.776)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79928-3" data-name="Path 79928" d="M859.562,176.49c2.71,7.263,5.1,14.526,7.286,21.965q-19.1,7.973-38.626,14.866c-5.656,1.971-11.616,3.227-17.142,5.491-1.76-6.97-3.567-13.939-5.89-20.744,4.412-2.241,9.61-3.731,14.256-5.479,13.282-4.987,27.526-9.633,40.128-16.086Z" transform="translate(128.778 21.262)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79929-3" data-name="Path 79929" d="M865.98,195.2c1.983,8.753,3.766,17.447,5.057,26.341-18.738,4.048-37.851,8.565-56.2,14.115-1.267-6.711-2.464-13.611-4.635-20.1,5.526-2.265,11.487-3.52,17.142-5.491q19.518-6.882,38.626-14.866Z" transform="translate(129.646 24.505)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79931-3" data-name="Path 79931" d="M188.6,463.6c12.1,7.345,23.466,16.18,34.9,24.534,5.538,4.048,12.519,8.178,17.154,13.212a19.273,19.273,0,0,1,4.423,8.354,11.018,11.018,0,0,1-1.643,8.859c-3.755,5.385-10.736,7.064-16.861,7.943-1.807.258-3.59.446-5.421.54a35.739,35.739,0,0,0-5.82.059c-4.423-12.156-26.13-55.24-26.74-63.5Z" transform="translate(21.904 71.026)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79932-3" data-name="Path 79932" d="M769.331,111.858c3.086-1.079,6.125-2.511,9.152-3.778.376,2.793.54,5.632.728,8.448,1.1,16.861-2.3,31.879-13.728,44.762-11.147,12.555-29.145,19.313-45.572,20.756-7.075.622-14.162.258-21.237.727-2.077.141-3.907.141-5.339,1.772.176,2.03,1.408,3.5,2.5,5.151l-.9.727-.387-.622c-.962-1.525-1.537-2.593-3.262-3.356-1.08.317-1.009.035-1.42.939-1.4,3.062-.587,6.958-1.7,10.161a22.31,22.31,0,0,1-.188-2.511c-.293-8.765,3.051-20.087,9.14-26.552,10-3.86,21.706-4.2,32.184-6.559,12.414-2.8,32.407-7.967,39.412-19.724,5.632-9.445,3.156-20.392.645-30.342Z" transform="translate(108.458 9.405)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79933-3" data-name="Path 79933" d="M260.872,353.55a51.437,51.437,0,0,1,11.041,4.259,42.527,42.527,0,0,1,7.134,5.5c-1.361.9-3.543,2.5-4.247,3.978-2.828,12.859-24.44,31.7-35.036,38.544a17.161,17.161,0,0,1-6.324,2.91,10.742,10.742,0,0,1-5.28-.2c-2.1-2.487-4.142-5.069-5.186-8.19-2.03-6.066-.68-12.1,2.229-17.635,7.333-13.939,20.78-24.593,35.669-29.169Z" transform="translate(27.695 51.952)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79934-3" data-name="Path 79934" d="M271,357.18a42.514,42.514,0,0,1,7.134,5.5c-1.361.9-3.543,2.5-4.247,3.977-15.335,5.362-33.123,21.085-39.928,35.916a21.507,21.507,0,0,0-1.431,5.538,10.743,10.743,0,0,1-5.28-.2c1.443-13.141,14.4-27.221,24.464-34.813,7.709-5.808,13.282-7.767,19.289-15.91Z" transform="translate(28.605 52.581)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79935-3" data-name="Path 79935" d="M273.12,365.26c-2.828,12.86-24.44,31.7-35.036,38.544a17.157,17.157,0,0,1-6.324,2.91,21.384,21.384,0,0,1,1.431-5.538C240,386.356,257.785,370.622,273.12,365.26Z" transform="translate(29.387 53.981)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79936-3" data-name="Path 79936" d="M196.969,226.6c7.392,6.477,12.015,14.842,12.625,24.745.763,12.343-4.036,24.253-12.25,33.346C189.2,293.7,176.6,299.9,164.42,300.439a33.068,33.068,0,0,1-24.194-8.378,34.268,34.268,0,0,1-9.926-16.708c1.572.821,3.121,2.464,4.541,3.579,5.808,4.564,11.193,7.04,18.808,5.984,13.763-1.9,27.538-12.238,35.587-23.22,8.859-12.085,10.055-20.451,7.744-35.106Z" transform="translate(11.801 29.946)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79939-3" data-name="Path 79939" d="M159.7,443.9c6.946,3.579,13.341,9.011,19.829,13.423,4.635,3.156,9.9,5.972,14.08,9.692.61,8.26,22.317,51.345,26.74,63.5a34.435,34.435,0,0,1,5.82-.059c-5.1,2.006-25.1-.235-31.574-1.1-1.126-6.981-5.315-15.429-8.084-21.965C177.781,486.832,165.919,465.161,159.7,443.9Z" transform="translate(16.897 67.612)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79941-3" data-name="Path 79941" d="M521.57,53.832c2.5,2.253,6.665,10.548,9,13.787,6.852-2.558,14.08-4.412,21.1-6.488-5.163,12.355-8.436,25.661-12.343,38.462L520.612,160.36c-3.18,10.173-6.043,20.451-9.058,30.671.082-10.29,1.044-20.709,1.607-31,1.948-35.528,5.456-70.763,8.413-106.21Z" transform="translate(77.882)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79945-3" data-name="Path 79945" d="M202.729,352.241a20.69,20.69,0,0,1-1.338-6.582c-.211-5.831,2.957-11.393,6.735-15.605,6.23-6.958,28.9-22.892,38.075-22.9,2.792,0,4.869,2.03,6.618,3.942a36.31,36.31,0,0,1,3.966,8.413c-.176.176-.364.352-.54.528-7.345,7-23.162,15.875-32.337,21.613-4.388,2.734-9.61,5.386-13,9.375l.2.563c13.857-2.429,37.523-22.352,50.969-12.461,3.379,2.487,6.8,7.122,7.38,11.37a10.521,10.521,0,0,1-1.854,7.732,32.992,32.992,0,0,1-2.769,2.957c-2.077-1.033-4.13-2.089-6.3-2.945-12.977,3.332-32.478,14.326-45.466,7.075-5.174-2.886-8.037-7.814-10.349-13.059Z" transform="translate(24.121 43.909)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79946-3" data-name="Path 79946" d="M230.049,148.142c3.672,1.76,7.357,6.864,10.079,9.926,17.952,20.2,33.393,42.158,48.7,64.369,10.513,15.265,21.354,30.741,30.4,46.921l-9.316-8C302.138,254.422,293.925,247.9,286,241.1l-41.958-36.291c-10.431-8.87-21.789-17.224-31.387-26.963,6.054-.446,12.027-.563,18.081-.563.141-9.727-.716-19.419-.692-29.157Z" transform="translate(26.076 16.347)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79947-3" data-name="Path 79947" d="M76.688,305.1c5.655,2.194,11.17,5.773,16.661,8.46,20.944,10.243,41.325,21.378,62.046,32.02l35.212,17.764c5.62,2.828,12.461,5.538,17.424,9.363l-.117.927c-3.614,1.279-10.126-1.455-13.669-2.5-16.5-5.2-34.6-7.826-51.5-11.827-9.985-2.37-19.724-5.632-29.65-8.237-16.11-4.236-32.313-7.9-48.376-12.355,6.348-5.022,11.921-10.818,17.6-16.556a62.448,62.448,0,0,1-5.655-17.037Z" transform="translate(0.434 43.554)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79948-3" data-name="Path 79948" d="M587.556,151.35c2.9,2.839,3.356,11.381,5.374,15.382,3.837,7.627,12.52,10.888,20.146,13.352q-41.324,43.049-84.233,84.527c-4.9,4.353-9.61,8.988-14.373,13.481,7.838-20.3,20.134-39.788,31.6-58.2,14.115-22.68,28.711-45.079,41.512-68.546Z" transform="translate(78.388 16.905)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79951-3" data-name="Path 79951" d="M278.134,361.873l4.564,6.019c2.534,4.224,3.32,7.92,2.112,12.778-3.59,14.408-17.436,25.168-26.388,36.291a16,16,0,0,0,1.748,1.291c1.877.141,3.215-1,4.717-1.983,14.538-9.433,22.4-29.31,33.076-35.939a14.726,14.726,0,0,1,11.639-1.807,28.963,28.963,0,0,1,8.037,3.661c13.74,8.4,4.506,18.9,10.419,27.421a4.453,4.453,0,0,0,3.3,2.135c1.725.129,3.661-.986,5.022-1.913,7.732-5.327,8.612-14.256,10.044-22.751,1.3-.986,1.3-1.3,2.886-1.1a5.547,5.547,0,0,1,1.091,2.405c1.138,6.395-2.03,14.7-5.62,19.958-2.558,3.755-6.137,6.688-10.748,7.322-3.285.446-7.239-.141-9.844-2.335-5.726-4.787-3.543-13.857-4.212-20.381a5.284,5.284,0,0,0-1.678-3.919,5.654,5.654,0,0,0-5.174,1.631c-3.344,2.969-5.644,7.169-8,10.924-6.148,9.762-13.106,21.014-23.255,27.057-3.966,2.358-8.119,3.344-12.637,2.089-7.345-2.03-11.334-8.565-14.843-14.76a17.782,17.782,0,0,1-10.3,3.039c-7.228-.493-12.4-6.852-16.814-11.921a10.477,10.477,0,0,0,5.28.2,16.886,16.886,0,0,0,6.324-2.91c10.6-6.84,32.208-25.7,35.036-38.544.7-1.478,2.886-3.074,4.247-3.978Z" transform="translate(28.608 53.39)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79952-3" data-name="Path 79952" d="M278.6,366.992c2.534,4.224,3.321,7.92,2.112,12.778-3.59,14.409-17.436,25.168-26.388,36.291l-1.291.6a7.09,7.09,0,0,1-2.089-2.922c-.364-2.628,6.336-8.424,8.272-10.466,6.477-6.8,15.934-16.65,18.069-26.107.7-3.133.516-6.418,1.185-9.61l.129-.575Z" transform="translate(32.709 54.279)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79953-3" data-name="Path 79953" d="M130.463,276.474a15.381,15.381,0,0,1-.751-3.168c-1.772-12.156,3.332-25.426,10.6-34.965a45.73,45.73,0,0,1,30.624-17.928,34.49,34.49,0,0,1,26.2,7.31c2.311,14.655,1.115,23.021-7.744,35.106-8.049,10.971-21.824,21.319-35.587,23.22-7.6,1.044-12.989-1.42-18.808-5.984-1.42-1.115-2.969-2.769-4.541-3.579Z" transform="translate(11.638 28.825)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79954-3" data-name="Path 79954" d="M160.627,239.75a8.886,8.886,0,0,1,2.042.927c.387.821.774,1.22.61,2.171-.434,2.57-2.347,3.919-4.388,5.28L156.883,247a3.277,3.277,0,0,1-.563-2.816,17.526,17.526,0,0,1,4.294-4.435Z" transform="translate(16.299 32.227)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79955-3" data-name="Path 79955" d="M156.01,227.01c2.112.152,3.332.364,4.951,1.76.434,1.478.6,1.913-.059,3.414-1.537,3.532-5.2,5.444-8.471,6.97a11.384,11.384,0,0,1-2.792.352c-1.525-.176-1.725-.7-2.534-1.854a6.282,6.282,0,0,1,.434-3.156c1.748-3.966,4.529-6.019,8.46-7.486Z" transform="translate(14.709 30.019)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79957-3" data-name="Path 79957" d="M129.41,424.6c4.377,1.9,10.278,3.884,13.892,6.958,6.946,5.444,15.148,9.891,21.648,15.687,6.207,21.261,18.069,42.932,26.811,63.489,2.781,6.535,6.958,14.983,8.084,21.965-5-.493-9.926-1.56-14.854-2.523A139.8,139.8,0,0,1,156.6,522.89a.889.889,0,0,0,.07-.188c.763-3.86-21.261-49.526-24.523-56.672-2.264-5.691,1.525-12.1,1.549-18.093C133.728,439.9,131.44,432.309,129.41,424.6Z" transform="translate(11.647 64.266)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79958-3" data-name="Path 79958" d="M141.25,430.522c6.946,5.444,15.148,9.891,21.648,15.688,6.207,21.261,18.069,42.932,26.811,63.489,2.781,6.535,6.958,14.983,8.084,21.965-5-.493-9.926-1.56-14.854-2.523-1.643-7.4-7.3-18.257-10.443-25.5l-19.243-44c-4.189-9.6-8.671-19.184-12-29.134Z" transform="translate(13.699 65.291)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79963-3" data-name="Path 79963" d="M107.83,500.46c-6.676-2.593-15.077-4.67-18.726-11.393-4.177-7.709-2.3-17.929.176-25.919,4.506-14.6,15.007-30.366,28.981-37.359,6.371-3.191,11.921-3.285,18.55-.962,2.03,7.709,4.318,15.3,4.283,23.337-.023,5.984-3.813,12.4-1.549,18.093,3.262,7.145,25.285,52.812,24.523,56.672a.891.891,0,0,1-.07.188,232.96,232.96,0,0,1-24-8.178,215.577,215.577,0,0,1-32.17-14.479Z" transform="translate(4.233 64.027)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79964-3" data-name="Path 79964" d="M104.7,491.386c4.717-2.792,11.78-6.218,15.206-10.419,1.314-1.607,2.9-4.623,4.764-5.4l1.056,1.021c3.778,9.176,9.1,19.571,11.147,29.275A215.579,215.579,0,0,1,104.7,491.386Z" transform="translate(7.364 73.101)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79965-3" data-name="Path 79965" d="M120.763,433.593c2.194-.176,4.482-.54,6.571.34a9.55,9.55,0,0,1,5.374,5.385c2.652,6.876-.2,16.966-2.969,23.49-4.506,10.572-10.889,19.419-21.789,23.783a13.15,13.15,0,0,1-5.538-.023,8.582,8.582,0,0,1-5.573-4.611c-3.156-6.653-.411-17.623,2.159-24.206,4.1-10.478,11.311-19.653,21.777-24.159Z" transform="translate(5.756 65.786)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79966-3" data-name="Path 79966" d="M744.8,323.342c-1.443-4.283-1.443-9.7-1.009-14.186,1.255-12.684,8.941-26.2,18.82-34.191,9.656-7.8,20.075-9.422,32.1-8.2,5.8,3.884,14.127,8.284,15.887,15.652,4.083,3.332,5.5,8.26,6.019,13.364.082.857.141,1.7.188,2.558s.059,1.713.059,2.558-.024,1.713-.07,2.57-.106,1.7-.2,2.558-.2,1.7-.328,2.546-.281,1.69-.446,2.523-.364,1.666-.575,2.5-.446,1.654-.7,2.464-.528,1.631-.821,2.429-.61,1.6-.939,2.382-.681,1.572-1.056,2.335-.763,1.537-1.174,2.288-.833,1.49-1.279,2.218-.915,1.443-1.4,2.159-.986,1.4-1.5,2.089-1.044,1.349-1.6,2.007c-6.055,7.321-14.737,12.613-24.347,13.352-4.318.328-10.677-.739-14.549.974-1.173-.481-2.311-1.021-3.426-1.607-9.786-5.186-14.725-13.024-17.659-23.338Z" transform="translate(118.091 36.846)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79967-3" data-name="Path 79967" d="M773.87,272.078a25.6,25.6,0,0,1,3.672.023,6.325,6.325,0,0,1,4.74,2.617,5.139,5.139,0,0,1,.54,4.224c-.762,3.508-3.59,5.174-6.488,6.8a25.71,25.71,0,0,1-4.236.387,4.133,4.133,0,0,1-3.532-1.525,5.266,5.266,0,0,1-.986-4.623c.81-3.954,3-5.878,6.289-7.908Z" transform="translate(122.234 37.821)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79968-3" data-name="Path 79968" d="M762.343,282.89a5.7,5.7,0,0,1,.634.024c1.877.106,4.071.375,5.186,2.112a6.493,6.493,0,0,1,.552,4.6c-1.162,5.7-5.491,10.924-10.278,13.986a14.049,14.049,0,0,1-2.722-.094,4.8,4.8,0,0,1-3.532-2.335,7.176,7.176,0,0,1-.3-5.444c1.56-5.784,5.468-9.856,10.466-12.836Z" transform="translate(119.471 39.704)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79969-3" data-name="Path 79969" d="M810.41,280.06c4.083,3.332,5.5,8.26,6.019,13.364.082.857.141,1.7.188,2.558s.059,1.713.059,2.558-.023,1.713-.071,2.57-.1,1.7-.2,2.558-.2,1.7-.329,2.546-.282,1.69-.446,2.523-.364,1.666-.575,2.5-.446,1.654-.7,2.464-.528,1.631-.821,2.429-.611,1.6-.939,2.382-.681,1.572-1.056,2.335-.763,1.537-1.174,2.288-.833,1.49-1.279,2.218-.915,1.443-1.4,2.159-.986,1.4-1.5,2.089-1.044,1.349-1.6,2.006c-6.054,7.322-14.737,12.613-24.347,13.353-4.318.328-10.677-.739-14.549.974-1.173-.481-2.311-1.021-3.426-1.608-9.785-5.186-14.725-13.024-17.658-23.337l.563-.68c2.217.469,9.433,9.985,13.634,12.508,6.641,3.978,14.42,4.318,21.789,2.44,11.1-2.828,20.592-10.548,26.318-20.381,6.911-11.851,6.911-21.824,3.5-34.8Z" transform="translate(118.278 39.214)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79970-3" data-name="Path 79970" d="M199.988,561.541c8.953-.539,18.187,1.866,24.957,7.99,4.787,4.33,6.97,10.009,7.251,16.38.751,17.119-7.979,30.694-19.02,42.8l11.956,2.91c1.408,2.6.774,6.829,1.138,9.786-7.451-.481-17.1-1.314-22.587-7.005-11.287,5.468-23.009,8.917-34.777,13.141-4.682,1.678-9.95,4.388-14.737,5.315-6.993,3.438-14.373,6.1-21.378,9.563C113.056,672.2,96.853,685.7,81.975,701.813l4.94-22.4c-6.841-3.649-14.373-5.867-21.085-9.645,15.453-8.835,33.534-18.128,50.817-22.375,24.91-9.786,52.682-9.891,77.076-21.225-7.744-13.153-12.977-26.611-8.964-42.005,2.3-8.812,7.216-17.917,15.23-22.622Z" transform="translate(0.626 87.989)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79971-3" data-name="Path 79971" d="M192.517,567.82a6.153,6.153,0,0,1,4.083,1.655c2.3,2.393,2.875,5.8,2.851,9-.117,14.314-5.069,28.641-15.488,38.7a5.881,5.881,0,0,1-.575-.5c-4.4-5.2-6.723-18.879-5.82-25.426,1.373-10.056,7.181-17.412,14.948-23.408Z" transform="translate(19.96 89.09)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79978-3" data-name="Path 79978" d="M513.106,258.319c.7,1.337.5.622.6,2.135a4.393,4.393,0,0,1-.551.059c-2.769-.035-109.436-50.535-120.149-56.4a42.967,42.967,0,0,1-10.231-7.439c-3.731-3.825-5.151-9.1-5.022-14.373.328-13.575,9.457-26.8,18.8-35.963,9.938-9.75,25.978-21.308,40.527-20.885,5.714.164,9.4,2.077,13.329,6.078,17.377,20.463,53.915,99.756,62.715,126.79Z" transform="translate(54.69 12.414)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79979-3" data-name="Path 79979" d="M415.708,180.618c-5.432,3.45-11.991,7.556-18.351,8.894a5.781,5.781,0,0,1-5.174-.845,4.57,4.57,0,0,1-.939-2.5c-.61-6.688,7.9-20.451,11.944-25.8,11.51-15.183,25.109-25.942,44.364-28.676-3.4,20.064-15.91,36.667-31.844,48.916Z" transform="translate(57.024 13.497)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79980-3" data-name="Path 79980" d="M444.44,130.63c17.377,20.463,53.914,99.756,62.714,126.79-2.37-1.244-4.658-3.837-6.782-5.55q-11.475-9.152-22.692-18.621-21.208-17.987-43.1-35.141c-7.392-5.831-14.737-11.968-22.5-17.307,15.945-12.25,28.441-28.852,31.844-48.916l.5-1.256Z" transform="translate(60.641 13.313)" fill="url(#linear-gradient-2)"/>
          <path id="Path_79983-3" data-name="Path 79983" d="M390.411,341.4c4.318,2.077,12.825,22.634,16.356,28l-9,6.242-1.091.293c-3.238-2.382-12.742-22.563-15.617-27.784l9.363-6.747Z" transform="translate(55.265 49.846)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80035-3" data-name="Path 80035" d="M941,691.225c-.07-.258-.152-.517-.223-.775-.622-2.488-.821-4.916.762-7.11,1.467-2.018,3.649-2.734,5.961-3.25,1.443,2.006,3.1,3.837,2.816,6.465a2.983,2.983,0,0,1-1.384,2.194c-1.854,1.584-5.644,1.783-7.944,2.476Z" transform="translate(-641.669 12.68)" fill="url(#linear-gradient-2)"/>
          <path id="Path_80042-3" data-name="Path 80042" d="M864.41,669.7c1.431-.094,2.863-.164,4.294-.235,3.18-.129,6.723-.035,9.046,2.558a7.08,7.08,0,0,1,1.831,5.749c-.54,4.834-4.576,9.75-8.272,12.649-1.971.681-4.107,1.255-6.16.552-2.781-.951-4.975-3.743-6.16-6.325-1.443-3.144-2.018-7.216-.61-10.49,1.185-2.757,3.4-3.52,6.031-4.47Z" transform="translate(-656.011 10.833)" fill="url(#linear-gradient-2)"/>
        </g>
      </g>
    </g>
  </g>
</svg>
