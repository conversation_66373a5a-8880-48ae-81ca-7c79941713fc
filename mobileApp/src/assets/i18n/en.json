{"PROFILE": "Profile", "ENGLISH": "English", "ITALIAN": "Italian", "LOGOUT": "Logout", "CATEGORIES": "Categories", "ACCOUNT SETTINGS": "Account settings", "SETTINGS": "Settings", "FIRST NAME": "First name", "LAST NAME": "Last name", "EMAIL": "Email", "MOBILE NO": "Mobile", "SAVE": "Save", "CHANGE PASSWORD": "Change password", "OLD PASSWORD": "Current password", "NEW PASSWORD": "New password", "CONFIRM NEW PASSWORD": "Confirm new password", "RECOVER PASSWORD": "Reset", "PASSWORD": "Password", "CONFIRM PASSWORD": "Confirm password", "SIGN IN": "Log In", "STAY SIGNED IN": "Stay signed in", "FORGOT PASSWORD": "Forgot password", "RESET": "Reset", "BACK TO LOGIN": "Back to login", "ADMIN": "ADMIN", "ADMIN_SHORT": "AD", "WELCOME": "Welcome", "LOGIN": {"USERNAME": "Username", "EMAIL": "Email", "PASSWORD": "Password", "FORGOT_PASSWORD": "Forgot password", "SIGN_IN": "Log In", "BACK_TO_LOGIN": "Back to login", "RESET": "Reset", "RECOVER_PASSWORD": "Recover Password"}, "EDIT": {"BASIC_INFO": "Basic Info", "ACTIVE": "Active", "ADD_NEW_RECORD": "Add New Record"}, "DASHBOARD": {"ACCOUNT_SETTINGS": "Profile", "CHANGE_PASSWORD": "Change Password", "LOGOUT": "Logout", "CONTACT": "Contact", "objName": "Dashboard", "PROFILE": "Profile"}, "DASHBOARD_MENU": {"CUSTOMERS": "Customers", "DASHBOARD": "Dashboard", "USERS": "Users", "PACKAGES": "Packages", "EMI_MANAGEMENT": "EMI Management", "ASF_CHARGE": "ASF Charge", "COUPONS": "Coupons", "PAYMENT": "Payment", "LEADS": "Leads", "SUPPORT_REQUESTS": "Support Requests", "LOGOUT": "Logout", "PROFILE": "Dashboard"}, "COMMON": {"ADD": "Add", "NEW": "New", "UPDATE": "Update", "EDIT": "Edit", "DELETE": "Delete", "HOME": "Home", "CANCEL": "Cancel", "SAVE": "Save", "SELECT": "Select", "SELECT_OPTION": "Select Option", "RUNREPORT": "Run Report", "REPORT": "Report", "DETAIL": "Detail", "EXCELREPORT": "Export as EXCEL", "PDFREPORT": "Export as PDF", "PRINTREPORT": "Print Report", "NORECORDS": "No Records Available", "ACTION": "Action", "YES": "Yes", "NO": "No", "BACK": "Back", "CROP_IMAGES": "Crop Images", "APPLY_CROP": "Apply Changes", "UPLOAD_ALL": "Upload All", "REQUIRED_SELECT_VALIDATION_MESSAGE": "Please select a valid value.", "REQUIRED_INPUT_VALIDATION_MESSAGE": "Please provide a valid value.", "REQUIRED_EMAIL_VALIDATION_MESSAGE": "Please provide a valid email address.", "REQUIRED_MOBILE_VALIDATION_MESSAGE": "Please provide a valid mobile.", "REQUIRED_PATTERN_VALIDATION_MESSAGE": "Please provide a valid value.", "REQUIRED_FILE_VALIDATION_MESSAGE": "Please upload a file", "REQUIRED_INPUT_MIN_VALIDATION_MESSAGE": "Please provide a valid value. Min allowed value: ", "REQUIRED_INPUT_MAX_VALIDATION_MESSAGE": "Please provide a valid value. Max allowed value: ", "REQUIRED_MASK_VALIDATION_MESSAGE": "Please provide a valid value. Value will be like: ", "YEAR_INPUT_VALIDATION_MESSAGE": "Please provide a valid year value.", "MONTH_INPUT_VALIDATION_MESSAGE": "Please provide a valid month value."}, "USERS": {"objNames": "Users", "ADD_NEW_USERS": "Add New User", "NEW_USER": "New User", "Name": "Name", "FirstName": "First Name", "LastName": "Last Name", "Email": "Email", "Role": "Role", "SelectRole": "Select Role", "PhoneNumber": "Phone Number", "Active": "Active", "Action": "Action", "YES": "Yes", "NO": "No", "User_Role": "User Role", "Status": "Status", "Create_Date": "Create Date", "Manager": "Manager"}, "Category": {"objName": "Category", "objNames": "Categories", "Detail": "Category Detail", "ADD_NEW_CATEGORY": "Add New Category", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "name": "Name", "messageUrlValidator": "url is invalid", "category": "Categry", "sizeOfCategory": "Size Of Category", "isPublished": "Publish", "currency": "Currency (in USD)", "whe": "when", "profilePicture": "Profile Picture", "messageProfilePictureInvalid": "Please Upload At least 1 Profile Picture", "documents": "Documents of category", "messageDocumentsInvalid": "Please Upload At least 1 Documents of category", "country": "Country List", "verifiedDocs": "Verified documetns", "messageVerifiedDocsInvalid": "Please Upload At least 1 Verified documetns", "spousePhoto": "Spouse Photo", "messageSpousePhotoInvalid": "Please Upload At least 1 Spouse Photo", "tag": "tag", "SUBCATEGORY_MALE": "male", "SUBCATEGORY_FEMALE": "female", "subCategory": "Sub Category", "description": "Description", "htmlExample": "Html Example", "defineColor": "Define Color", "assignedTo": "Assigned To", "userEmail": "User Email"}, "SubCategory": {"objName": "Sub Category", "objNames": "Sub Categories", "Detail": "Sub Category Detail", "ADD_NEW_SUBCATEGORY": "Add New Sub Category", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "category": "Categories", "tag": "Tag", "name": "Name", "test": "Test", "messageemailValidator": "<PERSON><PERSON> is invalid"}, "Tag": {"objName": "Tag", "objNames": "Tags", "Detail": "Tag Detail", "ADD_NEW_TAG": "Add New Tag", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "name": "name", "tagFiles": "Tag Files", "messageTagFilesInvalid": "Please Upload At least 1 Tag Files", "tagFiles1": "Tag files1", "messageTagFiles1Invalid": "Please Upload At least 1 Tag files1", "keywords": "keywords", "tagDate": "Tag Date", "tagAnonyoumous": "Anony<PERSON><PERSON>", "priority": "Priority", "messagePhonNumberValidtor": "Phoen number is invalid", "value": "Value", "abc": "Abc", "myInterest": "My Interests"}, "MyProduct": {"objName": "My Product", "objNames": "My Products", "Detail": "My Product Detail", "ADD_NEW_MYPRODUCT": "Add New My Product", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "name": "Name", "tag": "Tag", "category": "Categories", "subCategory": "Sub Categories"}, "Abc": {"objName": "Abc", "objNames": "Abcies", "Detail": "Abc Detail", "ADD_NEW_ABC": "Add New Abc", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "name": "Name", "myRoot": "My Root"}, "MyStrategy": {"objName": "My Strategy", "objNames": "My Strategies", "Detail": "My Strategy Detail", "ADD_NEW_MYSTRATEGY": "Add New My Strategy", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "title": "Title"}, "ProjectManager": {"objName": "Project Manager", "objNames": "Project Managers", "Detail": "Project Manager <PERSON><PERSON>", "ADD_NEW_PROJECTMANAGER": "Add New Project Manager", "id": "id", "tenantId": "Tenant", "slug": "Slug", "createdBy": "Created By", "updatedBy": "Updated By", "createdOn": "Created On", "updatedOn": "Updated On", "isDeleted": "Deleted?", "isActive": "Active?", "user": "User", "projectManager": "Project Manager"}, "CategorySelectReport": {"objName": "Category Select Report", "inputCategoryName": "Category Name", "inputSubCategoryName": "Sub Category Name", "inputCategory": "Category", "inputSubCategory": "Sub Category", "outputCategoryName": "Category Name", "outputSubCategoryName": "Sub Category Name", "outputCategoryId": "Category Id"}, "MyProductReport": {"objName": "My Product Report", "inputMyProductName": "My Product Name", "inputCategoryName": "Category Name", "outputMyProductId": "My Product Id", "outputMyProductName": "My Product Name", "outputCategoryName": "Category Name"}, "ErrorCodeMessages": {"1001": "Some Error during selection of 'Categories'", "1002": "Some Error during fetch of 'Category'", "1003": "Some Error during creation of new 'Category'", "1004": "Some Error during modification of 'Category'", "1005": "Some Error during removal of 'Category'", "2001": "Some Error during selection of 'Sub Categories'", "2002": "Some Error during fetch of 'Sub Category'", "2003": "Some Error during creation of new 'Sub Category'", "2004": "Some Error during modification of 'Sub Category'", "2005": "Some Error during removal of 'Sub Category'", "3001": "Some Error during selection of 'Tags'", "3002": "Some Error during fetch of 'Tag'", "3003": "Some Error during creation of new 'Tag'", "3004": "Some Error during modification of 'Tag'", "3005": "Some Error during removal of 'Tag'", "4001": "Some Error during selection of 'Users'", "4002": "Some Error during fetch of 'Users'", "4003": "Some Error during creation of new 'Users'", "4004": "Some Error during modification of 'Users'", "4005": "Some Error during removal of 'Users'", "5001": "Some Error during selection of 'My Products'", "5002": "Some Error during fetch of 'My Product'", "5003": "Some Error during creation of new 'My Product'", "5004": "Some Error during modification of 'My Product'", "5005": "Some Error during removal of 'My Product'", "6001": "Some Error during selection of 'Abcies'", "6002": "Some Error during fetch of 'Abc'", "6003": "Some Error during creation of new 'Abc'", "6004": "Some Error during modification of 'Abc'", "6005": "Some Error during removal of 'Abc'", "7001": "Some Error during selection of 'My Strategies'", "7002": "Some Error during fetch of 'My Strategy'", "7003": "Some Error during creation of new 'My Strategy'", "7004": "Some Error during modification of 'My Strategy'", "7005": "Some Error during removal of 'My Strategy'", "8001": "Some Error during selection of 'Project Managers'", "8002": "Some Error during fetch of 'Project Manager'", "8003": "Some Error during creation of new 'Project Manager'", "8004": "Some Error during modification of 'Project Manager'", "8005": "Some Error during removal of 'Project Manager'"}}