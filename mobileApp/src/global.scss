/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import "~@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";

@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;900&&family=Oswald:wght@200..700&family=Pacifico&display=swap");
@import "theme/variables.scss";
@import "theme/common.scss";
@import "theme/login.scss";
@import "theme/sales-dashboard.scss";
@import "theme/customer-onboarding.scss";
@import "theme/customer-dashboard.scss";
@import "theme/customer-payment.scss";

ion-content {
  min-height: 100vh !important;
  // --offset-bottom: auto !important;
  // --overflow: hidden;
  // overflow: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  &.ios {
    --overflow: hidden;

    .scroll {
      --overflow: auto !important;
    }
  }
}

::ng-deep main {
  min-height: 100vh !important;
}

.loading-container {
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  position: fixed;
  z-index: 999999999;
  top: 0;

  .loader {
    margin: 0px auto;
  }

  svg {
    border-radius: 50%;
  }
}

ion-tab-button {
  --padding-start: 0 !important;
}

ion-toolbar {
  --background: white;
  --ion-toolbar-background: white;
  --border-width: 0px !important;
}

.login-header {
  .ios & {
    margin-top: -60px !important;
  }
}

.login-modal {
  margin-top: 60px !important;
}

/* Disable swipe gestures app-wide */
ion-router-outlet {
  --swipe-gesture: false !important;
}

/* Disable swipe-to-go-back on iOS */
.ios ion-router-outlet {
  --swipe-gesture: false !important;
}

/* Disable touch actions that could trigger swipe gestures */
ion-content, ion-page, ion-router-outlet {
  touch-action: pan-y !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -khtml-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* Prevent horizontal scrolling/swiping */
body, html {
  overflow-x: hidden !important;
  touch-action: pan-y !important;
}