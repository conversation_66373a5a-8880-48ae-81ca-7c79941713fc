import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { RouteReuseStrategy } from '@angular/router';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import { IonicStorageModule } from '@ionic/storage-angular';
import { MaskitoDirective } from '@maskito/angular';
import { ImageCropperModule } from 'ngx-image-cropper';
import { IConfig, NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { HttpAuthInterceptor } from 'src/shared/http.interceptor';
import { IconsModule } from 'src/shared/icon.module';
import { SharedModuleModule } from 'src/shared/shared-module.module';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { ForgotPasswordComponent } from './authentication/forgot-password/forgot-password.component';
import { LoginComponent } from './authentication/login/login.component';
import { TwoFactorAuthenticationComponent } from './authentication/mfa/two-factor-authentication/two-factor-authentication.component';
import { TwoFactorOtpComponent } from './authentication/mfa/two-factor-otp/two-factor-otp.component';
import { OtpForgotPasswordComponent } from './authentication/otp-forgot-password/otp-forgot-password.component';
import { CustomerBasicComponent } from './customer-layout/profile-management/customer-onboarding/customer-basic/customer-basic.component';
import { CustomerEmailComponent } from './customer-layout/profile-management/customer-onboarding/customer-email/customer-email.component';
import { CustomerPackagePaymentComponent } from './customer-layout/profile-management/customer-onboarding/customer-package-payment/customer-package-payment.component';
import { CustomerPackageSelectionComponent } from './customer-layout/profile-management/customer-onboarding/customer-package-selection/customer-package-selection.component';
import { CustomerPasswordComponent } from './customer-layout/profile-management/customer-onboarding/customer-password/customer-password.component';
import { CustomerProfilePictureComponent } from './customer-layout/profile-management/customer-onboarding/customer-profile-picture/customer-profile-picture.component';
import { CustomerReferalCodeComponent } from './customer-layout/profile-management/customer-onboarding/customer-referal-code/customer-referal-code.component';
import { CustomerRegistrationComponent } from './customer-layout/profile-management/customer-onboarding/customer-registration/customer-registration.component';
import { CustomerVerificationComponent } from './customer-layout/profile-management/customer-onboarding/customer-verification/customer-verification.component';
import { PaymentCallbackComponent } from './customer-layout/profile-management/customer-onboarding/payment-callback/payment-callback.component';
import { CustomerCoApplicantComponent } from './customer-layout/profile-management/customer-onboarding/customer-co-applicant/customer-co-applicant.component';
import { CustomerLocationComponent } from './customer-layout/profile-management/customer-onboarding/customer-location/customer-location.component';
import { ResetPasswordComponent } from './authentication/reset-password/reset-password.component';
import { SplashComponent } from './authentication/splash/splash.component';

@NgModule({
  declarations: [
    AppComponent,
    //  TwoFactorAuthenticationComponent,
    SplashComponent,
    TwoFactorOtpComponent,
    ForgotPasswordComponent,
    OtpForgotPasswordComponent,
    ResetPasswordComponent,
    CustomerRegistrationComponent,
    CustomerEmailComponent,
    CustomerProfilePictureComponent,
    CustomerPasswordComponent,
    CustomerReferalCodeComponent,
    CustomerVerificationComponent,
    CustomerCoApplicantComponent,
    PaymentCallbackComponent,
    CustomerBasicComponent,
    CustomerLocationComponent,
    CustomerPackageSelectionComponent,
    CustomerPackagePaymentComponent
  ],
  imports: [
    BrowserModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModuleModule,
    ImageCropperModule,
    IonicModule.forRoot(),
    IonicStorageModule.forRoot(),
    AppRoutingModule,
    MaskitoDirective,
    IconsModule,
    NgxMaskDirective
  ],
  providers: [
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    provideHttpClient(withInterceptorsFromDi()),
    //   provideEnvironmentNgxMask(maskConfig),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpAuthInterceptor,
      multi: true
    },
    provideNgxMask(),
  ],
  schemas: [],
  bootstrap: [AppComponent],
})
export class AppModule { }

const maskConfigFunction: () => Partial<IConfig> = () => {
  return {
    validation: false,
  };
};
