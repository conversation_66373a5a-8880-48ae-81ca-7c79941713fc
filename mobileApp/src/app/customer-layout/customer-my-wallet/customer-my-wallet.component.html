<ion-content class="customer-dashboard-page customer-payment-page my-wallet-page">
  <app-customer-header [innerPage]="true" [headingText]="'My Wallet'" (rightActionCallback)="openNotifications()"
    [backUrl]="backUrl"></app-customer-header>
  <!-- Tabs Section -->
  <div class="customer-body-section has-header-section-change-profile no-padding"
    [ngClass]="{'full-height': filter?.tabType === 'PROMOCASH'}">
    <div class="my-wallet-tabs-items-container">
      <div class="my-wallet-tabs-items">
        <div class="my-wallet-tab-item" [ngClass]="{'active': filter?.tabType === 'MYCASH'}"
          (click)="onChangeStatusTab('MYCASH')">MY CASH</div>
        <div class="my-wallet-tab-item" [ngClass]="{'active': filter?.tabType === 'PROMOCASH'}"
          (click)="onChangeStatusTab('PROMOCASH')">PROMO CASH</div>
      </div>
    </div>
    <div class="payment-body-section">
      <ion-content class="payment-body-ion-content scroll">
        @if (loading==='LOADING') {
        <div class="my-booking-card-container">
          <div class="my-booking-card margin-top-10">
            <ion-grid>
              <ion-row>
                <ion-col>
                  <ion-row class="check-in-out-con my-bookings-check">
                    <ion-col size="8" class="my-booking-code ion-no-padding">
                      <div class="my-booking-text">
                        <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                      </div>
                    </ion-col>
                    <ion-col size="4" class="my-booking-status">
                      <div class="my-booking-text">
                        <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                      </div>
                    </ion-col>
                  </ion-row>

                  <div>
                    <div class="hotel-name my-bookings-hotel-name">
                      <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                    </div>
                    <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                        style="width: 200px"></ion-skeleton-text></div>
                  </div>
                </ion-col>
              </ion-row>

              <ion-row class="check-in-out-con">
                <ion-col size="5">
                  <div class="check-in-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                    </div>
                    <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
                <ion-col size="2">
                  <div class="time-duration-con">
                    <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                    <div class="time-duration">
                      <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                    </div>
                  </div>
                </ion-col>
                <ion-col size="5">
                  <div class="check-out-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                    </div>
                    <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
              </ion-row>

              <ion-row>
                <ion-col>
                  <div class="guest-and-rooms-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                    </div>
                    <div class="rooms">
                      <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
              </ion-row>

            </ion-grid>
          </div>
        </div>
        }@else if (loading==='LOADED') {
        <div class="my-wallet-container">
          <div class="my-wallet-card">
            <div class="balance-container">
              <div class="balance-text-container">
                <span class="balance-text">Available Balance</span>
                <span class="balance-amount">
                  ₹{{ commonService.formatAmount(filter?.tabType === 'MYCASH' ? (walletDetailResponse?.walletCash || 0)
                  :
                  filter?.tabType === 'PROMOCASH' ? (promoCashDetailResponse?.promoCash || 0) : 0)
                  }}/-
                </span>
              </div>
              <div class="balance-icon-container">
                <ion-icon class="balance-icon" [src]="filter?.tabType === 'MYCASH' ? '/assets/images/svg/money-bag.svg' : 
                  filter?.tabType === 'PROMOCASH' ? '/assets/images/svg/promo-cash.svg' : ''" slot="start">
                </ion-icon>
              </div>
            </div>
            <hr class="separator-line" />
            <div class="additional-info">
              <span class="offer-text" *ngIf="filter?.tabType === 'MYCASH'">
                My Cash <strong>Wallet can be used for all your bookings across categories (Flights, Hotels, Cabs &
                  more).
                </strong>
              </span>
              <span class="offer-text" *ngIf="filter?.tabType === 'PROMOCASH'">
                Promo Cash <strong>Wallet can be used for all your bookings across categories (Flights, Hotels, Cabs &
                  more).
                </strong>
              </span>
            </div>
          </div>


          <div class="compaign-list-container"
            *ngIf="filter?.tabType === 'MYCASH' && walletDetailResponse?.campaignDetails.length > 0">
            <div class="campaign-slider-container padding-bottom-10">
              <swiper [config]="config">
                <ng-template swiperSlide
                  *ngFor="let campaigns of walletDetailResponse?.campaignDetails; let lastIndex = last">
                  <div class="my-wallet-card gift-voucher-container less-padding" [ngClass]="{
                    'slider-wallet-campaign': walletDetailResponse?.campaignDetails?.length > 1
                  }" (click)="seeCompaignDetail(campaigns.id)">
                    <div class="gift-voucher-bal-container">
                      <div class="balance-container">
                        <div class="balance-text-container margin-top-10">
                          <span class="balance-text voucher-text">{{ campaigns?.title }}</span>
                          <span class="balance-amount voucher-desc">{{ campaigns?.description }}</span>
                        </div>
                        <div class="balance-icon-container">
                          <!-- <div class="balance-image" *ngIf="campaigns?.attachments?.length > 0">
                            <img class="image" [src]="campaigns?.attachments[0]?.path" alt="">
                          </div> -->
                          <!-- <ion-icon class="balance-icon"
                            *ngIf="!campaigns?.attachments || campaigns?.attachments?.length === 0"
                            src="/assets/images/svg/gift-coupon.svg" slot="start"></ion-icon> -->
                          <ion-icon class="balance-icon" src="/assets/images/svg/gift-coupon.svg"
                            slot="start"></ion-icon>
                        </div>
                      </div>
                      <div class="additional-info voucher-info margin-top-15">
                        <div class="compaign-container">
                          <span class="offer-text voucher-offer-text">Buy a gift voucher</span>
                          <span class="buy-voucher-container">
                            <ion-icon class="voucher-icon" src="/assets/images/svg/arrow-right-white.svg"
                              slot="start"></ion-icon>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-template>

                <!-- "See More" only after the last 5 campaigns -->
                <ng-template swiperSlide *ngIf="walletDetailResponse?.campaignDetails.length >= 5">
                  <div class="my-wallet-card gift-voucher-container see-more-card" (click)="seeMoreCompaigns()">
                    <span class="see-more-campaigns">View All</span>
                    <ion-icon class="right-arrow" src="/assets/images/svg/right-arrow.svg"></ion-icon>
                  </div>
                </ng-template>

              </swiper>
              <!-- Pagination only when more than one slide -->
              <div class="swiper-pagination" *ngIf="walletDetailResponse?.campaignDetails.length > 1"></div>
            </div>
          </div>
          <div class="reward-card-container">
            <div class="reward-card"
              *ngIf="filter?.tabType === 'MYCASH' && availableCashDetails?.walletTopupCashbackPerc>0 && availableCashDetails?.walletTopupCashbackAmount>0">
              <img src="/assets/images/icons/tick-icon.png" class="reward-icon margin-left-5">
              <div class="reward-text-container">
                <span class="reward-text">Recharge now and get {{availableCashDetails?.walletTopupCashbackPerc}}% Promo
                  Cash (up to ₹{{availableCashDetails?.walletTopupCashbackAmount}}).</span>
              </div>
            </div>
          </div>
          <div class="compaign-list-button-container margin-top-10 margin-bottom-10"
            *ngIf="filter?.tabType === 'MYCASH'">
            <ion-button class="site-full-rounded-button add-money-button" expand="full" shape="round" type="submit"
              (click)="openAddMoneyPopup(true)">
              +Recharge Now
            </ion-button>
          </div>

          <div *ngIf="(filter?.tabType === 'MYCASH' && walletDetailResponse?.customerWalletTransactionDetail?.length > 0) || 
            (filter?.tabType === 'PROMOCASH' && promoCashDetailResponse?.customerWalletTransactionDetail?.length > 0)">
            <div class="transactions-container">
              <span class="transactions-text">
                Transactions
              </span>
              <div class="promo-cash-container" *ngFor="let transactions of (filter?.tabType === 'MYCASH' ? walletDetailResponse?.customerWalletTransactionDetail 
                                  : promoCashDetailResponse?.customerWalletTransactionDetail)">
                <div class="promo-applied">
                  <span class="promo-id">{{commonService.formatText(transactions?.targetType)}}</span>
                  <div class="promo-action-container">
                    <ion-icon class="promo-icon"
                      [src]="transactions?.actionType === 'CREDIT' ? '/assets/images/svg/coupon-applied-credit.svg' : '/assets/images/svg/coupon-applied.svg'"
                      slot="start"></ion-icon>
                    <span class="promo-text" [ngStyle]="{
                      'color': transactions?.actionType === 'CREDIT' ? '#228B22' : '#BC8421'
                    }">{{transactions?.actionType}}</span>
                  </div>
                </div>
                <div class="promo-id-amount-container">
                  <span class="promo-date">
                    <ng-container *ngIf="filter?.tabType === 'MYCASH'; else actionTypeTemplate">
                      {{ commonService.formatDate(transactions?.updatedOn) }}
                    </ng-container>
                  </span>

                  <ng-template #actionTypeTemplate>
                    <ng-container *ngIf="transactions?.actionType === 'DEBIT'; else expiryTemplate">
                      Paid On:- {{ commonService.formatDate(transactions?.updatedOn) }}
                    </ng-container>
                  </ng-template>

                  <ng-template #expiryTemplate>
                    <span class="promo-date">Expire On:- {{ commonService.formatDate(transactions?.expiryDate) }}</span>
                  </ng-template>

                  <span class="promo-amount" [ngClass]="{'cancelled-amount': transactions?.status === 'CANCELLED'}"
                    [ngStyle]="{ color: transactions?.status === 'CANCELLED' ? '#b3bed5' : '#29385B' }">
                    ₹{{commonService.formatAmount(transactions?.amount)}}
                  </span>
                </div>

                <span class="promo-note margin-top-4">{{transactions?.note}}</span>
              </div>
              <!-- <span class="see-more-text"
                *ngIf="(filter?.tabType === 'MYCASH' && walletDetailResponse?.customerWalletTransactionDetail[0]?.totalCount >= 6) || 
             (filter?.tabType === 'PROMOCASH' && promoCashDetailResponse?.customerWalletTransactionDetail[0]?.totalCount >= 6)"
                (click)="seeMoreTransactions(filter?.tabType)">
                See More({{ filter?.tabType === 'MYCASH'
                ? walletDetailResponse?.customerWalletTransactionDetail[0]?.totalCount
                : promoCashDetailResponse?.customerWalletTransactionDetail[0]?.totalCount }})
              </span> -->
              <span class="see-more-text"
                *ngIf="(filter?.tabType === 'MYCASH' && walletDetailResponse?.customerWalletTransactionDetail[0]?.totalCount >= 6) || 
             (filter?.tabType === 'PROMOCASH' && promoCashDetailResponse?.customerWalletTransactionDetail[0]?.totalCount >= 6)"
                (click)="seeMoreTransactions(filter?.tabType)">
                View All
              </span>
            </div>
          </div>

          <div class="no-recent-trans-container margin-top-30 margin-bottom-15" *ngIf="(filter?.tabType === 'MYCASH' && walletDetailResponse?.customerWalletTransactionDetail.length <= 0) || 
            (filter?.tabType === 'PROMOCASH' && promoCashDetailResponse?.customerWalletTransactionDetail.length <= 0)">
            <div class="no-recent-trans">
              <img class="no-recent-trans-image" src="/assets/images/svg/no-transaction.svg" alt="Upload Icon">
              <span class="no-recent-trans-text margin-top-5">
                You don`t have any recent transaction
              </span>
            </div>

            <div class="queries-container margin-top-30">
              <span class="how-it-works-text" (click)="openHowItWorkPage()">
                How It Works
              </span>
              <hr class="vertical-separator" />
              <span class="how-it-works-text" (click)="openFAQPage()">
                All FAQ's
              </span>
            </div>
          </div>

          <div class="no-recent-trans-container margin-top-10 margin-bottom-15" *ngIf="(filter?.tabType === 'MYCASH' && walletDetailResponse?.customerWalletTransactionDetail.length > 0) || 
            (filter?.tabType === 'PROMOCASH' && promoCashDetailResponse?.customerWalletTransactionDetail.length > 0)">

            <div class="queries-container margin-top-25">
              <span class="how-it-works-text" (click)="openHowItWorkPage()">
                How It Works
              </span>
              <hr class="vertical-separator" />
              <span class="how-it-works-text" (click)="openFAQPage()">
                All FAQ's
              </span>
            </div>
          </div>

        </div>
        }
      </ion-content>
    </div>
  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #isPaidPopup [isOpen]="isAddMoney" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeAddMoneyPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">Recharge Now</div>
          <div class="popup-normal-heading secondary-text">Add funds to your wallet and use for payments.
          </div>
          <div class="job-info margin-top-10">
            <div class="margin-top-20">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': payment.invalid && onClickValidation}">
                <ion-input label="Enter Amount" labelPlacement="floating" required="required" name="payment"
                  #payment="ngModel" [(ngModel)]="displayAmount" type="text"
                  (ionInput)="handleInputChange($event,payment)" (keypress)="commonService.restrictInput($event)">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="payment"
                [onClickValidation]="onClickValidation"></app-validation-message>
            </div>
            <span class="convenience-text">Convenience fee will be included.</span>

            <div class="amount-selection-container-wallet-modal">
              <div *ngFor="let amt of presetAmounts" class="amount-selection-modal margin-top-15" [ngClass]="{'selected-amount': selectedAmounts.includes(amt),
              'unselected-amount': selectedAmounts.length > 0 && !selectedAmounts.includes(amt)
               }" (click)="selectAmount(amt)">
                <span class="amount-text">{{ amt | currency : 'INR' : 'symbol' : '1.0-0' }}</span>
              </div>
            </div>

            <div class="privacy-container margin-top-10">
              <ion-button class="site-full-rounded-button less-border-radius text-capitalize" expand="full"
                shape="round" type="submit" [disabled]="isPaymentProcessing"
                (click)="proceedToAddMoney(recordForm.form)">
                Proceed & add money
              </ion-button>
            </div>

          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #isSuccessPopup [isOpen]="isSuccessfulResponse"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-body no-padding-top">
        <div class="campaign-card">
          <div class="campaign-card-header success-modal-card-header">
            <div class="campaign-image-slide success-modal-card-image-slide">
              <i-feather name="X" class="close-icon white-icon" (click)="closeDetails()"></i-feather>
            </div>
          </div>
          <div class="coin-container">
            <ion-icon class="balance-icon" [src]="'/assets/images/svg/success-icon.svg'"></ion-icon>
            <img [src]="'/assets/images/svg/reward.gif'" class="reward-overlay">
          </div>
          <div class="campaign-detail-container">
            <span class="campaign-detail-title success-text">Congratulations!</span>

            <div class="success-container margin-top-10" *ngIf="successPaymentDetails">
              <span class="earned-text" *ngIf="successPaymentDetails?.myCash > 0 || successPaymentDetails?.promo > 0">
                You've earned</span>

              <span class="earned-my-cash my-cash margin-top-7" *ngIf="successPaymentDetails?.myCash > 0">
                ₹{{ successPaymentDetails?.myCash }}
                <span class="cash-label">MyCash</span>
              </span>

              <span class="earned-promo-cash margin-top-2" *ngIf="successPaymentDetails?.promo > 0">
                ₹{{ successPaymentDetails?.promo }}
                <span>PromoCash</span>
              </span>
            </div>

          </div>

          <div class="detail-button" (click)="openDetails(successPaymentDetails)">
            <span class="detail-text">{{successPaymentDetails?.promo &&
              successPaymentDetails?.promo > 0 &&
              (!successPaymentDetails?.myCash || successPaymentDetails?.myCash === 0) ?
              'View Rewards' : 'View Detail' }}</span>
          </div>

          <!-- <div class="privacy-container margin-top-20">
            <ion-button class="site-full-rounded-button less-border-radius margin-left-20 margin-right-20" expand="full"
              shape="round" type="submit" (click)="openDetails(successPaymentDetails)">
              View Detail
            </ion-button>
          </div> -->

        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>