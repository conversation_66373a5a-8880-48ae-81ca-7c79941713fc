
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Checkout } from 'capacitor-razorpay';
import { environment } from 'src/environments/environment';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { ToastService } from 'src/shared/toast.service';
import { SwiperOptions } from 'swiper';
import { SwiperComponent } from 'swiper/angular';

@Component({
  selector: 'app-customer-my-wallet',
  templateUrl: './customer-my-wallet.component.html',
  styleUrls: ['./customer-my-wallet.component.scss'],
})
export class CustomerMyWalletComponent implements OnInit {
  @ViewChild(SwiperComponent) swiper!: SwiperComponent;
  config: SwiperOptions = {
    slidesPerView: 1,
    pagination: {
      clickable: true, // Dots will be clickable
      dynamicBullets: true, // Smaller bullets for multiple slides
    },
    watchSlidesProgress: true, // Updates pagination dynamically as slides move
    zoom: true,
  };

  user: any;
  filter: any;
  pageSize: number = 10;
  loading: string = "NOT_STARTED";
  showInfiniteScroll: boolean = false;
  totalCount: number = 0;
  appliedFilter: any;
  onClickValidation: boolean = false;
  isAddMoney: boolean = false;
  isSuccessfulResponse: boolean = false;
  selectedCampaign: any;
  amount: number | string = 1000;
  displayAmount: string = '';
  presetAmounts: number[] = [200, 500, 1000, 2000];
  selectedAmounts: number[] = [];
  userClearedInput: boolean = false; // Track if the user cleared the input
  isPaymentProcessing: boolean = false;
  isAddMoneyClicked: boolean = false;
  userSelectedBalanceOption: string | null = null;
  backScreen: string | null = null;
  backUrl: string = '';

  walletDetailResponse: any;
  promoCashDetailResponse: any;
  availableCashDetails: any;
  successPaymentDetails: any;
  successPaymentDetailForFreeTrip: any;

  constructor(private authService: AuthService,
    private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly dataService: DataService,
    public readonly commonService: CommonService,
    private readonly cdr: ChangeDetectorRef,
    private readonly loadingService: LoadingService,
    private route: ActivatedRoute,
    private router: Router
  ) {

  }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();

    this.showInfiniteScroll = true;
    this.filter = {} as any;
    this.filter.offset = 1;

    const tabFromQuery = this.route.snapshot.queryParamMap.get('tab');
    this.filter.tabType = tabFromQuery || 'MYCASH'; // default is MYCASH

    this.userSelectedBalanceOption = history.state.userSelectBalanceOption || null;
    this.backScreen = history.state.backScreen || null;
    this.successPaymentDetailForFreeTrip = history.state.successPaymentDetails || null;

    this.backUrl = this.backScreen === 'home-screen' ? `/dashboard` : `/account1`;

    if (this.userSelectedBalanceOption === 'PromoCash') {
      this.filter.tabType = 'PROMOCASH';
      this.fetchPromoCashDetails();
      return;
    }

    if (
      this.successPaymentDetailForFreeTrip?.promo &&
      this.successPaymentDetailForFreeTrip?.promo > 0 &&
      (!this.successPaymentDetailForFreeTrip?.myCash || this.successPaymentDetailForFreeTrip?.myCash === 0)
    ) {
      this.filter.tabType = 'PROMOCASH';
      this.fetchPromoCashDetails();
      this.closeSuccessPopup();
      return;
    }

    this.fetchWalletDetails();
    this.fetchAvailableCashDetails();
  }

  fetchWalletDetails() {
    this.loading = "LOADING";
    this.dataService.getWalletDetails().subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        const data = response.data;
        this.walletDetailResponse = data;

        if (
          this.walletDetailResponse?.campaignDetails &&
          this.walletDetailResponse.campaignDetails.length > 0 &&
          this.walletDetailResponse.campaignDetails[0]?.totalCount <= 1
        ) {
          this.config.pagination = false;
        }
      },
      error: (error: any) => {
        this.loading = "LOADED";
        //  this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  fetchPromoCashDetails() {
    this.loading = "LOADING";
    this.dataService.getPromoCashDetails().subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        const data = response.data;
        this.promoCashDetailResponse = data;
      },
      error: (error: any) => {
        this.loading = "LOADED";
      },
    });
  }

  fetchAvailableCashDetails() {
    const payload = {
      paymentType: "ADDITIONAL",
      expiryDays: 10
    };

    this.loading = "LOADING";
    this.dataService.availableCashDetails(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        const data = response.data;
        this.availableCashDetails = data;
      },
      error: (error: any) => {
        this.loading = "LOADED";
      },
    });
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

  openAddMoneyPopup(campaigns?: any, status: boolean = false) {
    this.selectedCampaign = campaigns;
    this.isAddMoneyClicked = status;

    if (campaigns?.type) {
      if (campaigns?.type === "BOOKING_CASHBACK") {
        this.navController.navigateForward('/portal/search/hotels', {
          animated: true,
          queryParams: {
            bookingType: "OTHERS",
            campaignId: campaigns?.id
          }
        });
        return;
      }

      if (campaigns?.type === "FREE_TRIP") {
        this.amount = campaigns?.minRechargeAmount > 0 ? campaigns?.minRechargeAmount : 0;
        this.selectedCampaign = campaigns;
        this.createOrder(this.walletDetailResponse.id, 'CAMPAIGN', this.amount);
        return;
      }

      // Handle "WALLET_TOPUP"
      if (campaigns?.type === "WALLET_TOPUP") {
        this.isAddMoney = true;
        this.selectedCampaign = campaigns;
        this.amount = (campaigns?.minRechargeAmount && campaigns?.minRechargeAmount > 0) ? campaigns?.minRechargeAmount : '';
      } else {
        this.isAddMoney = false;
        return;
      }
    } else {
      this.isAddMoney = true;
      this.isAddMoneyClicked = true;  // Ensure it's set to true
      this.amount = 1000;
    }

    this.onClickValidation = false;
    this.selectedAmounts = [];
    this.userClearedInput = false;
    this.displayAmount = this.commonService.formatAmount(this.amount);
  }

  seeCompaignDetail(campaignId: string) {
    this.navController.navigateForward('/portal/my/campaign/details', {
      state: {
        campaignId: campaignId,
        walletId: this.walletDetailResponse.id,
        fromScreen: 'my-wallet'
      },
      animated: true
    });
  }

  openHowItWorkPage() {
    this.navController.navigateForward(
      `/portal/how/it/works/${this.filter?.tabType}`,
      { animated: true }
    );
  }

  openFAQPage() {
    this.navController.navigateForward(
      `/portal/faq/${this.filter?.tabType}`,
      { animated: true }
    );
  }

  closeAddMoneyPopup() {
    this.isAddMoney = false;
  }

  openSuccessPopup() {
    this.isSuccessfulResponse = true;
  }

  closeSuccessPopup() {
    this.isSuccessfulResponse = false;
  }

  seeMoreTransactions(tabType: string) {
    if (this.filter?.tabType === 'MYCASH') {
      this.navController.navigateForward(
        `/portal/see/more/${this.walletDetailResponse.id}/${this.walletDetailResponse?.customerWalletTransactionDetail[0].type}/${tabType}/transactions`,
        { animated: true }
      );
    } else {
      this.navController.navigateForward(
        `/portal/see/more/${this.promoCashDetailResponse.id}/${this.promoCashDetailResponse?.customerWalletTransactionDetail[0].type}/${tabType}/transactions`,
        { animated: true }
      );
    }
  }

  seeMoreCompaigns() {
    this.navController.navigateForward(`/portal/see/more/${this.walletDetailResponse.id}/compaigns`, { animated: true });
  }

  onChangeStatusTab(status: string) {
    this.filter.tabType = status;

    if (this.filter.tabType === 'PROMOCASH') {
      this.fetchPromoCashDetails();
    } else {
      this.fetchWalletDetails();
    }
    this.filter.offset = 1; // Reset offset
  }

  handleInputChange(event: any, field: any) {
    let rawValue = event.target.value.replace(/,/g, ''); // Remove commas
    rawValue = rawValue.replace(/\D/g, ''); // Remove non-numeric characters

    if (rawValue === "") {
      this.amount = "";
      this.userClearedInput = true;
      this.selectedAmounts = [];
    } else {
      this.amount = parseInt(rawValue, 10);
      this.userClearedInput = false;
    }
    if (this.amount === 0) {
      field.control.setErrors({ customMinAmount: true, minAmount: 1 });
      this.onClickValidation = true;
      return;
    }
    // Update displayAmount with formatted value
    this.displayAmount = this.commonService.formatAmount(this.amount);
  }

  // selectAmount(amt: number) {
  //   if (this.userClearedInput) {
  //     this.amount = amt; // If input was cleared, start fresh
  //   } else {
  //     this.amount = Number(this.amount) + amt; // Otherwise, add to the existing amount
  //   }

  //   this.userClearedInput = false; // Reset flag since user clicked a button

  //   // Format the amount with commas for display
  //   this.displayAmount = this.commonService.formatAmount(this.amount);

  //   // If the amount is already selected, unselect it
  //   if (this.selectedAmounts.includes(amt)) {
  //     this.selectedAmounts = this.selectedAmounts.filter(value => value !== amt);
  //   } else {
  //     // If it's not selected, select it
  //     this.selectedAmounts = [amt];
  //   }
  // }

  selectAmount(amt: number) {
    this.amount = amt; // Set amount to the selected value
    this.displayAmount = this.commonService.formatAmount(this.amount); // Format and update display

    this.userClearedInput = false; // Reset flag since the user clicked a button

    // Ensure only one selected amount is stored
    this.selectedAmounts = [amt];
  }

  proceedToAddMoney(form: any) {
    this.onClickValidation = true;
    if (!form.valid) {
      return;
    }

    // **Add validation for max amount (500000)**
    if (Number(this.amount) > 500000) {
      form.controls.payment.setErrors({
        customMaxAmount: true,
        maxAmount: `₹ ${this.commonService.formatNumber(500000)}`
      });
      this.onClickValidation = true;
      return;
    }
    // setTimeout(() => {
    //   this.navController.navigateForward("/portal/select/payment/method", {
    //     animated: true,
    //     queryParams: {
    //       paymentType: 'ADDITIONAL',
    //       amount: this.amount,
    //       fromScreen: 'my-wallet',
    //       walletId: this.walletDetailResponse.id,
    //       walletType: 'WALLET_RECHARGE',
    //       campaigns: this.selectedCampaign
    //     }
    //   });
    // }, 300);

    this.createOrder(this.walletDetailResponse.id, 'WALLET_RECHARGE', this.amount)
  }

  async createOrder(walletId: any, paymentType: any, dueAmount: any) {
    this.isPaymentProcessing = true;
    const input = {} as any;
    if (this.selectedCampaign?.type === 'WALLET_TOPUP' || this.isAddMoneyClicked) {
      input.walletId = walletId;
    }
    input.campaignId = this.selectedCampaign?.id;
    input.paymentType = paymentType;
    input.wallet = 0;
    input.promo = 0;

    const additional = this.availableCashDetails?.convenienceFeeConfigurationDetail?.additional || 0;
    const gstRate = this.availableCashDetails?.convenienceFeeConfigurationDetail?.gstRate || 0;

    input.convenienceFee = dueAmount * (additional / 100);
    input.convenienceFeeGst = input.convenienceFee * (gstRate / 100);
    input.amount = dueAmount;

    this.dataService.createOrder(input)
      .subscribe({
        next: (response: RestResponse) => {
          this.closeAddMoneyPopup();
          this.openPaymentPopup(response.data, paymentType);
          setTimeout((() => this.isPaymentProcessing = false));
        },
        error: (error) => {
          setTimeout((() => this.isPaymentProcessing = false));
          this.toastService.show(error.message);
        }
      });
  }

  async openPaymentPopup(data: any, paymentType: any) {
    const options: any = {
      key: environment.PaymentGateway.Key,
      description: paymentType,
      image: environment.PaymentGateway.Image,
      order_id: data.orderId,
      currency: environment.PaymentGateway.Currency,
      name: environment.PaymentGateway.Name,
      prefill: {
        email: this.user.email,
        contact: `${this.user.countryCode}${this.user.phoneNumber}`
      },
      theme: {
        color: '#29385B'
      }
    }
    try {
      let paymentResponse = (await Checkout.open(options));
      this.paymentProcessing(data, paymentResponse.response, paymentType);
    } catch (error: any) {
      this.isPaymentProcessing = false;
      if (!error) {
        return;
      }
      let errorObj = JSON.parse(error)
      if (!errorObj || !errorObj.description) {
        return;
      }
      this.toastService.show(errorObj.description);
      this.selectedCampaign = null;
    }
  }

  paymentProcessing(data: any, paymentResponse: any, paymentType: string) {
    const payload = {
      orderId: paymentResponse.razorpay_order_id,
      paymentId: paymentResponse.razorpay_payment_id,
      signature: paymentResponse.razorpay_signature
    }
    this.loadingService.show();
    const method = this.selectedCampaign?.type === 'FREE_TRIP' ? 'freeTrip' : 'walletRecharge';
    this.dataService[method](payload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.isPaymentProcessing = false;
        this.closeAddMoneyPopup();

        this.successPaymentDetails = response.data;
        this.refreshWalletDetails();  // Refresh the wallet cash details
        this.openSuccessPopup();
        this.selectedCampaign = null;
      },
      error: (error) => {
        this.loadingService.hide();
        this.isPaymentProcessing = false;
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  closeDetails() {
    //this.fetchWalletDetails(); // Refresh the wallet cash details
    this.closeSuccessPopup();
  }

  openDetails(successPaymentDetails: any) {
    if (
      successPaymentDetails?.promo &&
      successPaymentDetails?.promo > 0 &&
      (!successPaymentDetails?.myCash || successPaymentDetails?.myCash === 0)
    ) {
      this.filter.tabType = 'PROMOCASH';
      this.refreshPromoDetails();
      this.closeSuccessPopup();
      return;
    }
    this.fetchWalletDetails(); // Refresh the wallet cash details
    this.closeSuccessPopup();
  }

  refreshWalletDetails() {
    this.dataService.getWalletDetails().subscribe({
      next: (response: RestResponse) => {
        this.walletDetailResponse = response.data;
      },
      error: (error: any) => {
        // Handle error without affecting UI state
      },
    });
  }

  refreshPromoDetails() {
    this.dataService.getPromoCashDetails().subscribe({
      next: (response: RestResponse) => {
        this.promoCashDetailResponse = response.data;
      },
      error: (error: any) => {
      },
    });
  }

}
