<!-- <p>
  customer-referral-reward works!
</p> -->

<ion-content class="customer-dashboard-page compaign-detail">
  <app-customer-header [innerPage]="true" [headingText]="'Refer friends'" (rightActionCallback)="openNotifications()"></app-customer-header>

  <ion-card class="offer-card">
    <ion-card-header>
      <ion-card-title class="offer-title">How it works?</ion-card-title>
    </ion-card-header>
    <ion-card-content class="card-content">
      <ion-list>
        <ion-item lines="inset" class="referal-font">
          <ion-icon slot="start" name="information-circle-outline"></ion-icon>
          <ion-label>Share the referral link <b>with your friend</b></ion-label>
        </ion-item>
        <ion-item lines="inset" class="referal-font">
          <ion-icon slot="start" name="gift-outline"></ion-icon>
          <ion-label>
            Earn 5% in My Cash when successfully refer a new customer.
          </ion-label>
        </ion-item>
        <ion-item lines="none" class="referal-font">
          <ion-icon slot="start" name="trophy-outline"></ion-icon>
          <ion-label>Upon successful referrals, <b>you will only earn if</b> the referred customer purchases a package</ion-label>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>
  <div class="compaign-list-button-container margin-bottom-10" (click)="shareViaWhatsApp()">
    <ion-button class="site-full-rounded-button add-money-button margin-top-8" expand="full" shape="round" type="submit">
      Invite Via WhatsApp
    </ion-button>
  </div>
  <div class="compaign-list-button-container margin-bottom-10" (click)="shareInviteLink()">
    <ion-button class="site-full-rounded-button add-money-button margin-top-8" expand="full" shape="round" type="submit">
      Share Invite Link
    </ion-button>
  </div>
</ion-content>