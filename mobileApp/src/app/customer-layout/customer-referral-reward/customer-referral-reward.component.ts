import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { Share } from '@capacitor/share';

@Component({
  selector: 'app-customer-referral-reward',
  templateUrl: './customer-referral-reward.component.html',
  styleUrls: ['./customer-referral-reward.component.scss'],
})
export class CustomerReferralRewardComponent implements OnInit {

  user: any;
  constructor(private readonly navController: NavController,
    private authService: AuthService
  ) {

  }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

  async shareInviteLink() {
    const message = `Check this out! ${this.user?.fullName} has sent invite link: ${this.user?.referralLink}`;

    try {
      await Share.share({
        title: 'Referral Invitation',
        text: message,
        // url: this.user?.referralLink,
        dialogTitle: 'Share Invite Link'
      });
    } catch (error) {
      console.error('Error sharing invite link', error);
    }
  }

  async shareViaWhatsApp() {
    const message = encodeURIComponent(`Hey! 🎉 Join using ${this.user?.fullName}'s referral link and get available rewards!\n\n Refer Code:${this.user?.referralCode}\n\n 👉 ${this.user?.referralLink}`);
    const whatsappUrl = `whatsapp://send?text=${message}`;

    window.open(whatsappUrl, '_system'); // Opens in WhatsApp (if installed)
  }

}
