import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-emi-detail',
  templateUrl: './emi-detail.component.html',
  styleUrls: ['./emi-detail.component.scss'],
})
export class EmiDetailComponent implements OnInit {
  loading: string = "NOT_STARTED";
  emiDetail: any;
  constructor(
    private readonly route: ActivatedRoute,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    public readonly commonService: CommonService,
  ) { }

  ngOnInit() { }

  ionViewWillEnter() {
    this.myEMIDetail();
  }


  myEMIDetail() {

    this.loading = "LOADING";
    this.dataService.myEMIDetail().subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        var emiDetailResponse = response.data[0];
        const date = new Date();
        const monthName = date.toLocaleString('en-US', { month: 'long' });
        console.log(monthName); // Output: e.g., "December"
        this.emiDetail = emiDetailResponse.installmentDetails?.map((installment: any) => {
          const dueDate = new Date(installment.paymentDueDate);
          const monthName = dueDate.toLocaleString('en-US', { month: 'short' });
          const year = dueDate.getFullYear(); // Extract the year

          return {
            ...installment,
            monthName, // Adding the month name
            year,      // Adding the year
          };
        });
      },
      error: (error: any) => {
        this.loading = "LOADED";
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'PARTIALLY_COMPLETED':
        return 'partially-completed';
      case 'PAID':
        return 'status-complete';
      case 'CANCELLED':
        return 'status-close';
      case 'DUE':
        return 'status-due';
      case 'OVERDUE':
        return 'status-overDue';
      case 'REFUND_IN_PROGRESS':
        return 'status-refund';
      default:
        return 'status-default';
    }
  }

}
