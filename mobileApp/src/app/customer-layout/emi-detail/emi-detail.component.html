<ion-content class="customer-dashboard-page customer-payment-page booking-page">
  <app-customer-header [innerPage]="true" [headingText]="'EMI Detail'" [rightAction]="true"></app-customer-header>

    <div class="table-container">
      @if (loading==='LOADING') {
        <table>
          <thead>
            <tr>
              <th><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></th>
              <th><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></th>
              <th><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></th>
              <th><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
            </tr>
            <tr>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
            </tr>
            <tr>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
            </tr>
            <tr>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
              <td><ion-skeleton-text [animated]="true" style="height: 30px;"></ion-skeleton-text></td>
            </tr>
          </tbody>
        </table>
      }@else if (loading==='LOADED') {
      <table>
        <thead>
          <tr>
            <th>Sr. No.</th>
            <th>Month</th>
            <th>Amount</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let emi of emiDetail; let i = index">
            <td>{{ i + 1 }}</td>
            <!-- {{packageDetail.expiryDate}}'{{packageDetail.expiryDate|date:"yy"}} -->
            <td>{{ emi.monthName}} ' {{ emi.year}}</td>
            <td>{{ emi.installmentAmount | currency :'INR':'symbol':'1.0-0' }}</td>
            <td class="emi-status" [ngClass]="getStatusClass(emi.status === 'COMPLETED' ? 'PAID' : emi.status)">{{ commonService.formatText(emi.status) }}</td>
          </tr>
        </tbody>
      </table>
    }
    </div>
  
 
</ion-content>