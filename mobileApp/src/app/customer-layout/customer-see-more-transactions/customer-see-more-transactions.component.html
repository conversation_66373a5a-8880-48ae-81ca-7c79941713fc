<ion-content class="customer-dashboard-page customer-payment-page my-wallet-page">
  <app-customer-header [innerPage]="true" [headingText]="'My Transactions'" (rightActionCallback)="openNotifications()"
    [backUrl]="backUrl"></app-customer-header>

  <div class="customer-body-section has-header-section-change-profile no-padding">

    <div class="payment-body-section less-height no-padding">
      <ion-content class="payment-body-ion-content scroll">
        <!-- Loading state -->
        @if (loading==='LOADING') {
        <div class="my-booking-card-container">
          <div class="my-booking-card margin-top-10">
            <ion-grid>
              <ion-row>
                <ion-col>
                  <ion-row class="check-in-out-con my-bookings-check">
                    <ion-col size="8" class="my-booking-code ion-no-padding">
                      <div class="my-booking-text">
                        <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                      </div>
                    </ion-col>
                    <ion-col size="4" class="my-booking-status">
                      <div class="my-booking-text">
                        <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                      </div>
                    </ion-col>
                  </ion-row>

                  <div>
                    <div class="hotel-name my-bookings-hotel-name">
                      <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                    </div>
                    <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                        style="width: 200px"></ion-skeleton-text></div>
                  </div>
                </ion-col>
              </ion-row>

              <ion-row class="check-in-out-con">
                <ion-col size="5">
                  <div class="check-in-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                    </div>
                    <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
                <ion-col size="2">
                  <div class="time-duration-con">
                    <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                    <div class="time-duration">
                      <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                    </div>
                  </div>
                </ion-col>
                <ion-col size="5">
                  <div class="check-out-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                    </div>
                    <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
              </ion-row>

              <ion-row>
                <ion-col>
                  <div class="guest-and-rooms-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                    </div>
                    <div class="rooms">
                      <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
              </ion-row>

            </ion-grid>
          </div>
        </div>
        }
        <!-- Loaded state -->
        @else if (loading==='LOADED') {
        <div class="my-wallet-container see-more-trans-container">
          <div class="transactions-container no-background" *ngFor="let transactions of allTransactions">

            <div class="promo-cash-container see-more-transactions-container">
              <div class="promo-applied">
                <span class="promo-id">{{commonService.formatText(transactions?.targetType)}}</span>
                <div class="promo-action-container">
                  <ion-icon class="promo-icon"
                    [src]="transactions?.actionType === 'CREDIT' ? '/assets/images/svg/coupon-applied-credit.svg' : '/assets/images/svg/coupon-applied.svg'"
                    slot="start"></ion-icon>
                  <span class="promo-text" [ngStyle]="{
                    'color': transactions?.actionType === 'CREDIT' ? '#228B22' : '#BC8421'
                  }">{{transactions?.actionType}}</span>
                </div>
              </div>
              <div class="promo-id-amount-container">
                <span class="promo-date">
                  <ng-container *ngIf="tabType === 'MYCASH'; else actionTypeTemplate">
                    {{ commonService.formatDate(transactions?.updatedOn) }}
                  </ng-container>
                </span>

                <ng-template #actionTypeTemplate>
                  <ng-container *ngIf="transactions?.actionType === 'DEBIT'; else expiryTemplate">
                    Paid On:- {{ commonService.formatDate(transactions?.updatedOn) }}
                  </ng-container>
                </ng-template>

                <ng-template #expiryTemplate>
                  <span class="promo-date">Expire On:- {{ commonService.formatDate(transactions?.expiryDate) }}</span>
                </ng-template>

                <span class="promo-amount" [ngClass]="{'cancelled-amount': transactions?.status === 'CANCELLED'}"
                  [ngStyle]="{ color: transactions?.status === 'CANCELLED' ? '#b3bed5' : '#29385B' }">
                  ₹{{commonService.formatAmount(transactions?.amount)}}
                </span>
              </div>

              <span class="promo-note margin-top-4">{{transactions?.note}}</span>
            </div>

          </div>
        </div>
        <ion-infinite-scroll threshold="50px" (ionInfinite)="onPageChanged($event)">
          <ion-infinite-scroll-content loadingSpinner="bubbles"
            loadingText="Loading more transactions..."></ion-infinite-scroll-content>
        </ion-infinite-scroll>
        }
      </ion-content>
    </div>

  </div>
</ion-content>