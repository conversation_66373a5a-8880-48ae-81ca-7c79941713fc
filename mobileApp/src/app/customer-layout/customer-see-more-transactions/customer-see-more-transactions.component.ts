import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-customer-see-more-transactions',
  templateUrl: './customer-see-more-transactions.component.html',
  styleUrls: ['./customer-see-more-transactions.component.scss'],
})
export class CustomerSeeMoreTransactionsComponent implements OnInit {

  user: any;
  filter: any;
  pageSize: number = 10;
  loading: string = "NOT_STARTED";
  allTransactions: Array<any> = new Array<any>();

  walletCashId!: string;
  walletCashType!: string;
  tabType!: string;
  backUrl: string = '';

  constructor(private readonly dataService: DataService,
    private readonly authService: AuthService,
    private readonly toastService: ToastService,
    public readonly commonService: CommonService,
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly navController: NavController
  ) {

  }

  ngOnInit() { }

  ionViewWillEnter() {
    this.user = this.authService.getUser();

    this.walletCashId = this.route.snapshot.paramMap.get('id') || "";
    this.walletCashType = this.route.snapshot.paramMap.get('type') || "";
    this.tabType = this.route.snapshot.paramMap.get('tabType') || "";

    this.backUrl = `/portal/my/wallet?tab=${this.tabType}`;

    this.allTransactions = new Array<any>();
    this.filter = {} as any;
    this.filter.offset = 1;

    this.myTransactions(null, true);
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

  myTransactions($event?: any, handleLoading?: boolean) {
    const payload = {
      pagination: {
        next: this.pageSize,
        offset: this.filter.offset,
      },
      walletId: this.walletCashId,
      tabType: this.walletCashType
    };

    if (handleLoading) { this.loading = "LOADING"; }
    this.dataService.getWalletCashTransactions(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        if (response.data.length > 0) {
          this.allTransactions.push(...response.data);
        }
        if ($event) {
          $event.target.complete();
          if (this.allTransactions.length > 0 && this.allTransactions.length >= this.allTransactions[0].totalCount) {
            $event.target.disabled = true;
          }
        }
        this.cdr.detectChanges();
      },
      error: (error: any) => {
        this.loading = "LOADED";
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  onPageChanged($event: any) {
    if (this.allTransactions.length > 0 && this.allTransactions.length >= this.allTransactions[0].totalCount) {
      $event.target.complete();
      $event.target.disabled = true;
      return;
    }
    this.filter.offset = this.filter.offset + 1;
    this.myTransactions($event, false);
  }

}
