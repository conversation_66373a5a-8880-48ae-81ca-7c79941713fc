<ion-content class="customer-dashboard-page customer-payment-page faq-page">
  <app-customer-header [innerPage]="true" [headingText]="'FAQ`S'" [rightAction]="true"
    [backUrl]="backUrl"></app-customer-header>

  <div class="customer-body-section has-header-section-change-profile no-padding">
    <div class="faq-container">
      <div class="faq-card margin-top-10" *ngFor="let faq of faqs; let i = index" (click)="toggleRoomDetails(i)">
        <div class="faq-question">
          <span class="question-text">{{ faq.question }}</span>
          <ion-icon [src]="faq.open ? '/assets/images/svg/chevron-up.svg' : '/assets/images/svg/chevron-down.svg'"
            class="chevron-icon">
          </ion-icon>
        </div>

        <div class="faq-answer" *ngIf="faq.open">
          <p>{{ faq.answer }}</p>
        </div>
      </div>
    </div>
  </div>
</ion-content>