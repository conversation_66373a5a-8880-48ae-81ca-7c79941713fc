import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-customer-faq',
  templateUrl: './customer-faq.component.html',
  styleUrls: ['./customer-faq.component.scss'],
})
export class CustomerFaqComponent implements OnInit {

  faqs: Array<any> = new Array<any>();
  tabType!: string;
  backUrl: string = '';

  constructor(private readonly route: ActivatedRoute
  ) {

  }

  ngOnInit() { }

  ionViewWillEnter() {
    this.tabType = this.route.snapshot.paramMap.get('tabType') || "";

    this.backUrl = `/portal/my/wallet?tab=${this.tabType}`;

    if (this.tabType === 'MYCASH') {
      this.faqs = [
        {
          question: 'What is MyCash?',
          answer: 'MyCash is your personal wallet on zforb. You can use it to store money, receive cashbacks, and make seamless payments.',
          open: false
        },
        {
          question: 'How can I add money to MyCash?',
          answer: 'You can recharge your MyCash wallet using our secure payment gateway via debit/credit cards, UPI, net banking, and other supported methods.',
          open: false
        },
        {
          question: 'Is there a limit on how much I can add to MyCash?',
          answer: 'There’s no fixed limit for most users, but in compliance with regulations, we may apply certain limits based on your usage and verification status.',
          open: false
        },
        {
          question: 'Where does cashback go?',
          answer: 'All eligible cashback rewards are automatically credited to your MyCash wallet.',
          open: false
        },
        {
          question: 'Can I use MyCash for any payment?',
          answer: 'Yes! You can use MyCash for any type of payment on zforb. There’s no restriction on how you use your available MyCash balance.',
          open: false
        },
        {
          question: 'What happens if my MyCash balance is not enough for a payment?',
          answer: 'No worries! You can use MyCash for a partial payment, and pay the remaining amount using any other available payment method.',
          open: false
        },
        {
          question: 'Does MyCash balance expire?',
          answer: 'No, your MyCash doesn’t expire at all.',
          open: false
        },
        {
          question: 'Can I transfer MyCash to another user or bank account?',
          answer: 'Currently, MyCash cannot be transferred to other users or withdrawn to a bank account. It can only be used for payments within zforb.',
          open: false
        },
        {
          question: 'How can I check my MyCash balance?',
          answer: 'You can view your current MyCash balance anytime in the My Wallet section of your profile/dashboard.',
          open: false
        },
        {
          question: 'What if a transaction fails after using MyCash?',
          answer: 'If a transaction fails and the amount was deducted from MyCash, the balance will be automatically refunded back to your wallet shortly.',
          open: false
        }
      ];
    } else {
      this.faqs = [
        {
          question: 'What is PromoCash?',
          answer: 'PromoCash is promotional credit given via offers, with limited usability and an expiry date.',
          open: false
        },
        {
          question: 'How do I get PromoCash?',
          answer: 'PromoCash is added automatically when you qualify for specific offers or campaigns.',
          open: false
        },
        {
          question: 'Can I recharge PromoCash?',
          answer: 'No, PromoCash cannot be recharged by the user.',
          open: false
        },
        {
          question: 'Does PromoCash expire?',
          answer: 'Yes, all PromoCash has an expiry date. Make sure to use it before it expires.',
          open: false
        },
        {
          question: 'Where can I use PromoCash?',
          answer: 'PromoCash can be used on select services or products, and sometimes only a specific percentage of your bill can be paid using PromoCash.',
          open: false
        },
        {
          question: 'Can I see PromoCash separately from MyCash?',
          answer: 'Yes, you can view your PromoCash balance separately in the wallet section.',
          open: false
        },
        {
          question: 'What happens if I don’t use PromoCash before expiry?',
          answer: 'Unused PromoCash will be automatically removed from your wallet after it expires.',
          open: false
        }
      ];
    }

    // Open the first FAQ item by default
    if (this.faqs.length > 0) {
      this.faqs[0].open = true;
    }
  }

  // toggleRoomDetails(index: number) {
  //   this.faqs[index].open = !this.faqs[index].open;
  // }

  toggleRoomDetails(index: number) {
    this.faqs.forEach((faq, i) => {
      faq.open = i === index ? !faq.open : false;
    });
  }

}
