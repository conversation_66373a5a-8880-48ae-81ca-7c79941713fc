<ion-content class="customer-dashboard-page package-detail-page">
  <app-customer-header [innerPage]="true" [headingText]="'Package Detail'" (rightActionCallback)="openNotifications()"
    [backUrl]="backUrl"></app-customer-header>
  <div class="customer-body-section has-header-section-package-detail no-padding">
    @if (loading==='LOADING') {
    <div class="my-booking-card-container">
      <div class="my-booking-card margin-top-10">
        <ion-grid>
          <ion-row>
            <ion-col>
              <ion-row class="check-in-out-con my-bookings-check">
                <ion-col size="8" class="my-booking-code ion-no-padding">
                  <div class="my-booking-text">
                    <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                  </div>
                </ion-col>
                <ion-col size="4" class="my-booking-status">
                  <div class="my-booking-text">
                    <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                  </div>
                </ion-col>
              </ion-row>

              <div>
                <div class="hotel-name my-bookings-hotel-name">
                  <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                </div>
                <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                    style="width: 200px"></ion-skeleton-text></div>
              </div>
            </ion-col>
          </ion-row>

          <ion-row class="check-in-out-con">
            <ion-col size="5">
              <div class="check-in-con">
                <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text></div>
                <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text></div>
              </div>
            </ion-col>
            <ion-col size="2">
              <div class="time-duration-con">
                <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                <div class="time-duration">
                  <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                </div>
              </div>
            </ion-col>
            <ion-col size="5">
              <div class="check-out-con">
                <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                </div>
                <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text></div>
              </div>
            </ion-col>
          </ion-row>

          <ion-row>
            <ion-col>
              <div class="guest-and-rooms-con">
                <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                </div>
                <div class="rooms">
                  <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                </div>
              </div>
            </ion-col>
          </ion-row>

        </ion-grid>
      </div>
    </div>
    }
    @else if (loading==='LOADED') {
    <div class="package-detail-container" *ngIf="responseReceived && package">
      <div class="package-selection-container margin-top-20">
        <div class="package-detail-selection-card">
          <span class="package-title margin-top-10">{{ package.packageName }}</span>
          <!-- <span class="package-rewards">Flat 60% OFF</span> -->
          <span class="package-desc">{{ package.packageDetails.description }}</span>
        </div>

        <div class="package-detail-container">
          <div class="overall-allowed-night-detail">
            <div class="overall-night-text">Overall Allowed Nights</div>
          </div>
          <div class="overall-allowed-nights-balance-container margin-top-10">
            <div class="overall-allowed-night-balance-item">
              <div class="balance-count">{{package.packageDetails.totalNights}}</div>
              <div class="balance-title">Total</div>
            </div>
            <div class="overall-allowed-night-balance-item">
              <div class="balance-count">{{package.packageDetails.indianNights}}</div>
              <div class="balance-title">Indian</div>
            </div>
            <div class="overall-allowed-night-balance-item">
              <div class="balance-count">{{package.packageDetails.asianNights}}</div>
              <div class="balance-title">Asian</div>
            </div>
            <div class="overall-allowed-night-balance-item">
              <div class="balance-count">{{package.packageDetails.internationalNights}}</div>
              <div class="balance-title">WorldWide</div>
            </div>
          </div>

          <hr class="horizontal-separator-line" />

          <div class="package-card-body">
            <div class="package-details">
              <div class="package-detail-item">
                <span class="detail-label">Package Nights</span>
                <span class="detail-value">{{package.packageDetails.totalNights}} Nights</span>
              </div>
              <div class="package-detail-item">
                <span class="detail-label">Package Price {{ package.packageDetails.isGstInclude ? '(inclu. GST)' :
                  '(exclu. GST)' }}</span>
                <span class="detail-value">{{ package.packagePrice | currency :'INR':'symbol':'1.0-0' }}</span>
              </div>
              <div class="package-detail-item">
                <span class="detail-label">Tenure</span>
                <span class="detail-value">{{ package.packageDetails?.tenure }} Years</span>
              </div>

              <div class="additional-info" *ngIf="package?.offerDetails">
                <img src="assets/images/svg/offer-blue.svg" alt="Offer" class="offer-image" />
                <span class="info-text">
                  You will get an <strong>{{commonService.getDescriptionUppercase(package.offerDetails?.description) ||
                    'OFFER'
                    }}</strong> worth
                  <strong>{{package.offerAmount| currency :'INR':'symbol':'1.0-0'}}</strong> by just adding
                  <strong>{{package.baseAmount |
                    currency :'INR':'symbol':'1.0-0' }}</strong> to your
                  package
                </span>
              </div>
            </div>
          </div>

        </div>
      </div>

      <div class="compaign-list-button-container">
        <ion-button class="site-full-rounded-button add-money-button" expand="full" shape="round" type="submit"
          (click)="purchaseNow()">
          Purchase Now
        </ion-button>
      </div>

    </div>
    }
  </div>
</ion-content>