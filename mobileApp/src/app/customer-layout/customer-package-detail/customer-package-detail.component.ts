import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { PackageDetail } from 'src/modals/profileDetail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';

@Component({
  selector: 'app-customer-package-detail',
  templateUrl: './customer-package-detail.component.html',
  styleUrls: ['./customer-package-detail.component.scss'],
})
export class CustomerPackageDetailComponent implements OnInit {

  packageId: string | null = null;
  fphId: string | null = null;
  offerId: string | null = null;
  backUrl: string | null = null;
  fromScreen: string | null = null;
  package!: PackageDetail;

  loading: string = "NOT_STARTED";
  responseReceived: boolean = false;
  packageDetailScreen: string = 'package-detail';

  constructor(private readonly dataService: DataService,
    private readonly navController: NavController,
    public readonly commonService: CommonService
  ) {

  }

  ngOnInit() { }

  ionViewWillEnter() {
    this.packageId = history.state.packageId || null;
    this.fphId = history.state.fphId || null;
    this.offerId = history.state.offerId || null;
    this.fromScreen = history.state.fromScreen || null;

    this.fetchPackageCustomPushNotification();

    if (
      this.fromScreen !== null
    ) {
      if (this.fromScreen === 'notification-screen') {
        this.backUrl = `/portal/notification/list`;
        return;
      }
    }
    this.backUrl = `/dashboard`;
  }

  fetchPackageCustomPushNotification() {
    this.responseReceived = false;

    const input = {} as any;
    input.packageId = this.packageId;
    input.fullyPaidHolidayId = this.fphId;
    input.offerId = this.offerId;

    this.loading = "LOADING";
    this.dataService.packageCustomPushNotification(input).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.responseReceived = true;
        this.package = response.data;
      },
      error: (error: any) => {
        this.loading = "LOADED";
      },
    });
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

  purchaseNow() {
    this.navController.navigateForward(`/account/register/package/${this.package.packageId}/${this.packageDetailScreen}/pay`, { state: this.package, animated: true });
  }

}
