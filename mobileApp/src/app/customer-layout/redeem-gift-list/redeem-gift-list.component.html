<ion-content class="customer-dashboard-page customer-payment-page booking-page">
  <app-customer-header [innerPage]="true" [headingText]="'Redeem Gift Listing'"
    [rightAction]="true"></app-customer-header>

  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">

    <div class="booking-page-container max-height">

      <div class="customer-body-section has-no-padding">
        @if (loading==='LOADING') {
        <div class="payment-items no-padding">
          <div class="payment-item box-shadow warring">
            <div class="payment-item-container status-class status-warring">
              <div class="support-card-container">
                <div class="support-status">
                  <span class="ticket-id"><ion-skeleton-text [animated]="true"
                      style="width: 150px"></ion-skeleton-text></span>
                </div>
                <div class="support-subject-title">
                  <span><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></span>
                  <p class="support-description">
                    <ion-skeleton-text [animated]="true" style="width: 90%;height: 50px;"></ion-skeleton-text>
                  </p>
                </div>
                <div class="support-bottom-container">

                  <div class="support-category">
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="payment-item box-shadow warring">
            <div class="payment-item-container status-class status-warring">
              <div class="support-card-container">
                <div class="support-status">
                  <span class="ticket-id"><ion-skeleton-text [animated]="true"
                      style="width: 150px"></ion-skeleton-text></span>
                </div>
                <div class="support-subject-title">
                  <span><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></span>
                  <p class="support-description">
                    <ion-skeleton-text [animated]="true" style="width: 90%;height: 50px;"></ion-skeleton-text>
                  </p>
                </div>
                <div class="support-bottom-container">

                  <div class="support-category">
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="payment-item box-shadow warring">
            <div class="payment-item-container status-class status-warring">
              <div class="support-card-container">
                <div class="support-status">
                  <span class="ticket-id"><ion-skeleton-text [animated]="true"
                      style="width: 150px"></ion-skeleton-text></span>
                </div>
                <div class="support-subject-title">
                  <span><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></span>
                  <p class="support-description">
                    <ion-skeleton-text [animated]="true" style="width: 90%;height: 50px;"></ion-skeleton-text>
                  </p>
                </div>
                <div class="support-bottom-container">

                  <div class="support-category">
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        }@else if (loading==='LOADED') {
        <div class="payment-items no-padding">
          <div class="payment-item box-shadow" *ngFor="let request of myRedeemedRequests" [ngClass]="{'success':(request.status==='RESOLVED'),'warring':(request.status==='OPEN'),
                'in-progress':(request.status==='IN_PROGRESS'),'highlighted-payment': request.id === redeemNotificationId
                }">
            <div class="payment-item-container status-class" [ngClass]="{'status-success':(request.status==='RESOLVED'),'status-warring':(request.status==='OPEN'),
                'status-in-progress':(request.status==='IN_PROGRESS')
              }">

              <div class="support-card-container">
                <span class="ticket-status date">{{ formatDate(request.createdOn)
                  }}</span>

                <span class="ticket-status font-progress" *ngIf="request.status" [ngClass]="{'ticket-success':(request.status==='RESOLVED'),'ticket-warring':(request.status==='OPEN'),
                        'ticket-in-progress ':(request.status==='IN_PROGRESS')
                      }">
                  {{ commonService.formatText(request.status) }}</span>


                <div class="support-status margin-top-10">
                  <span class="ticket-id">{{request.subject}}</span>


                </div>

                <div class="support-subject-title">
                  <!-- <span *ngIf="request.subject">{{ commonService.formatText(request.subject) }}</span> -->
                  <p class="support-description" *ngIf="request.description">{{ request.description }}</p>
                  <img *ngIf="request?.attachments?.length > 0" [src]="request?.attachments[0]?.path" alt="Pan Card"
                    class="">
                </div>

              </div>

            </div>
          </div>

          <div class="customer-body-section no-height" *ngIf="myRedeemedRequests?.length <= 0">
            <div class="no-bookings-container">
              <div class="no-bookings-icon">
                <ion-icon name="alert-circle-outline"></ion-icon>
              </div>
              <h2 class="no-bookings-title">No Data Available</h2>
            </div>
          </div>

        </div>
        }
      </div>

    </div>
  </form>

</ion-content>