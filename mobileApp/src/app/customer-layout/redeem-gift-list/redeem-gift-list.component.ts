import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-redeem-gift-list',
  templateUrl: './redeem-gift-list.component.html',
  styleUrls: ['./redeem-gift-list.component.scss'],
})
export class RedeemGiftListComponent implements OnInit {

  loading: string = "NOT_STARTED";
  offerId: any;
  redeemNotificationId: string = '';
  myRedeemedRequests: any;

  constructor(
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    private readonly toastService: ToastService,
    public readonly commonService: CommonService,

  ) { }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.route.queryParams.subscribe(params => {
      this.offerId = params['offerId'];
      this.redeemNotificationId = params['redeemGiftId'];
    });
    this.myRedeemedRequestListing();
  }

  myRedeemedRequestListing() {
    this.loading = "LOADING";
    this.dataService.myRedeemedRequest().subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.myRedeemedRequests = response.data;
      },
      error: (error: any) => {
        this.loading = "LOADED";
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  formatDate(bookingDate: string): string {
    if (!bookingDate) return ''; // Handle if bookingDate is null or undefined

    const date = new Date(bookingDate);
    if (isNaN(date.getTime())) return ''; // Return an empty string if date is invalid

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`; // Format as yyyy-mm-dd
  }

}
