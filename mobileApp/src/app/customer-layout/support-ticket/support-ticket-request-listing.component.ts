import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { ModalController, NavController } from '@ionic/angular';
import { DatePicker } from '@pantrist/capacitor-date-picker';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { FileCropperComponent } from 'src/shared/file-cropper/file-cropper.component';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-support-ticket-request-listing',
  templateUrl: './support-ticket-request-listing.component.html',
  styleUrls: ['./support-ticket-request-listing.component.scss'],
})
export class SupportTicketRequestListingComponent implements OnInit {

  isFilterPopupOpen: boolean = false;
  appliedFilter: any;
  filter: any;
  pageSize: number = 10;
  loading: string = "NOT_STARTED";
  showInfiniteScroll: boolean = false;
  searchQuery: string = '';  // Variable to hold the search query

  isRequestModalOpen: boolean = false;
  onClickValidation: boolean = false;
  showValidationErrors = false;
  requestImage: string | null = null;
  supportRequestData: any;
  isProcessing: boolean = false;

  categoryTypes: Array<{ display: string, value: string }> = [
    { display: 'Booking Issue', value: 'BOOKING_ISSUE' },
    { display: 'Account Issue', value: 'ACCOUNT_ISSUE' },
    { display: 'Payment Issue', value: 'PAYMENT_ISSUE' },
    { display: 'Technical Problem', value: 'TECHNICAL_ISSUE' },
    { display: 'Subscription Issue', value: 'SUBSCRIPTION_ISSUE' },
    { display: 'Others', value: 'OTHERS' },
  ];
  priorityTypes: string[] = ['LOW', 'HIGH'];
  statusTypes: string[] = ['Open', 'In Progress', 'Resolved'];

  subject: string | null = null;
  description: string | null = null;
  category: string | null = null;
  status: string | null = null;

  supportTickets: Array<any> = new Array<any>();
  totalCount: number = 0;
  supportRequestnotificationId: string | null = null;
  fromScreen: string | null = null;
  backUrl: string | null = null;

  isSuccessfulResponse: boolean = false;
  successPaymentDetails: any;

  constructor(private readonly navController: NavController,
    public readonly commonService: CommonService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private route: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    private modalCtrl: ModalController
  ) {

  }

  ngOnInit() { }

  ionViewWillEnter() {
    this.supportTickets = [];
    this.showInfiniteScroll = true;
    this.filter = {} as any;
    this.filter.offset = 1;

    const params = this.route.snapshot.queryParams;
    this.supportRequestnotificationId = params['supportRequestId'] || null;
    this.fromScreen = params['fromScreen'] ? params['fromScreen'].replace(/['"]/g, '') : null;

    this.backUrl = this.fromScreen === 'supportRequest' ? `/portal/notification/list` : `/account1`;

    this.fetchSupportListing();
  }

  fetchSupportListing($event?: any) {
    const payload = {
      fromDate: this.filter.startDate || null,
      toDate: this.filter.endDate || null,
      category: this.filter.category || null,
      status: this.filter.status || null,
      tabType: "SUPPORT_REQUEST",
      pagination: {
        next: this.pageSize,
        offset: this.filter.offset,
      },
    };

    this.loading = "LOADING";
    this.dataService.supportRequestList(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.handleSupportListResponse(response, $event);
      },
      error: (error: any) => {
        this.loading = "LOADED";
        this.toastService.show(error.message || 'An error occurred');

        // Complete the scroll event in case of an error
        if ($event) {
          $event.target.complete();
        }
      },
    });
  }

  handleSupportListResponse(response: any, $event: any) {
    // Check if data exists in the response
    if (response.data && response.data.length > 0) {
      // Append new data to the existing list
      this.supportTickets = [...this.supportTickets, ...response.data];
      this.totalCount = response.data[0]?.totalCount || 0; // Extract totalCount from the response
    }

    if ($event) {
      $event.target.complete(); // Signal the completion of the scroll event

      // Check if all data is loaded
      if (this.supportTickets.length >= this.totalCount) {
        this.showInfiniteScroll = false; // Disable infinite scroll
        $event.target.disabled = true;
      } else {
        this.showInfiniteScroll = true; // Keep infinite scroll enabled
        $event.target.disabled = false;
      }
    }
  }

  onPageChanged($event: any) {
    // Check if totalCount is greater than 10 before enabling scroll
    if (this.supportTickets.length >= this.totalCount || this.totalCount <= 10) {
      $event.target.complete();
      $event.target.disabled = true;
      this.showInfiniteScroll = false;
      return;
    }

    // Increment offset for the next page
    this.filter.offset += 1;

    // Prepare the payload for pagination
    const payload = {
      fromDate: this.filter.startDate || null,
      toDate: this.filter.endDate || null,
      category: this.filter.category || null,
      status: this.filter.status || null,
      tabType: "SUPPORT_REQUEST",
      pagination: {
        next: this.pageSize,
        offset: this.filter.offset,
      },
    };

    // Fetch the next page of data
    this.dataService.supportRequestList(payload).subscribe({
      next: (response: any) => {
        this.handleSupportListResponse(response, $event);
      },
      error: (error: any) => {
        this.toastService.show(error.message || 'An error occurred');
        $event.target.complete(); // Complete the scroll event in case of error
      },
    });
  }

  supportRequestDetail(requestListId: any) {
    //console.log("requestListId",requestListId);
    //const requestItem = this.supportList.find(item => item.id === requestListId);
    //this.localStorageService.setObject("requestList", requestItem);
    // this.isSupportDetailOpen = true;
    //  this.supportRequestData = requestItem;
    this.navController.navigateForward(`/portal/support/request/${requestListId}/detail`);
  }

  back() {
    this.navController.navigateBack("/account");
  }

  openRequestModal() {
    this.isRequestModalOpen = true;
  }

  closeRequestModal() {
    this.isRequestModalOpen = false;
    this.onClickValidation = false;
    this.clearForm();
  }

  openFilterPopup() {
    this.isFilterPopupOpen = true;
    this.appliedFilter = JSON.parse(JSON.stringify(this.filter));

    // Format startDate and endDate before showing in fields
    if (this.appliedFilter.startDate) {
      const date = new Date(this.appliedFilter.startDate);
      this.appliedFilter.startDate = `${this.commonService.getDisplayValue(date.getFullYear())}-${this.commonService.getDisplayValue(date.getMonth() + 1)}-${date.getDate()}`;
    }

    if (this.appliedFilter.endDate) {
      const date = new Date(this.appliedFilter.endDate);
      this.appliedFilter.endDate = `${this.commonService.getDisplayValue(date.getFullYear())}-${this.commonService.getDisplayValue(date.getMonth() + 1)}-${date.getDate()}`;
    }
  }

  closeFilterPopup() {
    this.isFilterPopupOpen = false;
  }

  clearFilter() {
    // Reset appliedFilter and filter models
    this.appliedFilter.startDate = undefined;
    this.appliedFilter.endDate = undefined;
    this.appliedFilter.category = undefined;
    this.appliedFilter.status = undefined;
    this.filter.startDate = undefined;
    this.filter.endDate = undefined;
    this.filter.category = undefined;
    this.filter.status = undefined;

    this.closeFilterPopup();  // Close the filter popup

    // Reset and reload payments
    setTimeout(() => {
      this.supportTickets = [];
      this.showInfiniteScroll = true; // Enable infinite scroll
      this.filter.offset = 1;
      this.fetchSupportListing();
    });
  }

  applyFilter() {
    const { startDate, endDate, category, status } = this.appliedFilter;

    // Ensure at least one filter is provided
    if (!this.commonService.hasValue(startDate) && !this.commonService.hasValue(endDate) && !this.commonService.hasValue(category) && !this.commonService.hasValue(status)) {
      this.toastService.show("Please provide at least one value to filter");
      return;
    }
    if (this.commonService.hasValue(startDate) && !this.commonService.hasValue(endDate)) {
      this.toastService.show("Please select a valid To Date.");
      return;
    }
    if (!this.commonService.hasValue(startDate) && this.commonService.hasValue(endDate)) {
      this.toastService.show("Please select a valid From Date.");
      return;
    }

    // Reapply the date format for startDate and endDate in the correct format
    if (this.appliedFilter.startDate) {
      const startDate = new Date(this.appliedFilter.startDate);
      this.filter.startDate = `${startDate.getFullYear()}-${(startDate.getMonth() + 1).toString().padStart(2, '0')}-${startDate.getDate().toString().padStart(2, '0')}`;
    }

    if (this.appliedFilter.endDate) {
      const endDate = new Date(this.appliedFilter.endDate);
      this.filter.endDate = `${endDate.getFullYear()}-${(endDate.getMonth() + 1).toString().padStart(2, '0')}-${endDate.getDate().toString().padStart(2, '0')}`;
    }

    this.filter.category = this.appliedFilter.category;
    this.filter.status = this.appliedFilter.status;
    this.closeFilterPopup();  // Close the filter popup

    // Reset and reload payments with the new filter
    setTimeout(() => {
      this.supportTickets = new Array<any>();  // Clear existing data
      this.filter.offset = 1;
      this.fetchSupportListing();
    });
  }

  openDatePicker(type: 'fromDate' | 'toDate') {
    const today = new Date();
    let dateToUse: Date = today;
    let minDate: string | undefined;
    let maxDate: string | undefined;

    if (type === 'fromDate') {
      dateToUse = this.appliedFilter.startDate ? new Date(this.appliedFilter.startDate) : today;
    } else {
      if (!this.appliedFilter.startDate) {
        this.toastService.show("Please select 'From Date' first.");
        return;
      }
      dateToUse = this.appliedFilter.endDate ? new Date(this.appliedFilter.endDate) : today;
      minDate = `${new Date(this.appliedFilter.startDate).getDate().toString().padStart(2, '0')}/${(new Date(this.appliedFilter.startDate).getMonth() + 1).toString().padStart(2, '0')}/${new Date(this.appliedFilter.startDate).getFullYear()}`;
    }

    const formattedDate = `${dateToUse.getDate().toString().padStart(2, '0')}/${(dateToUse.getMonth() + 1).toString().padStart(2, '0')}/${dateToUse.getFullYear()}`;

    DatePicker.present({
      mode: 'date',
      format: 'dd/MM/yyyy',
      min: minDate,
      date: formattedDate,
    }).then(date => {
      if (date && date.value) {
        const [day, month, year] = date.value.split('/');
        if (type === 'fromDate') {
          this.appliedFilter.startDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        } else {
          this.appliedFilter.endDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
      }
    }).catch(error => {
      console.error('Date Picker Error:', error);
    });
  }

  async upload() {
    if (this.requestImage) {
      this.toastService.show('Please remove the existing image before uploading a new one.');
      return;
    }
    try {
      const response = await Camera.getPhoto({
        quality: 50,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: CameraSource.Prompt, // Prompt user to select from camera or files
      });
      if (response.base64String) {
        await this.processCropper(response.base64String);
      }
    } catch (error) {
      //  this.toastService.show("Something went wrong while uploading profile picture.");
    }
  }

  async processCropper(base64Image: string) {
    const modal = await this.modalCtrl.create({
      component: FileCropperComponent,
      componentProps: {
        file: { base64String: base64Image }
      },
      cssClass: "cropper-modal"
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role !== 'confirm') {
      this.toastService.show("Sorry, profile picture uploading has been cancelled.");
      return;
    }
    if (data && data.croppedFile) {
      await this.uploadImage(data.croppedFile);
    } else {
      this.toastService.show("No file selected after cropping.");
    }
  }

  async uploadImage(blob: Blob): Promise<void> {
    if (!blob) {
      this.toastService.show('No file selected. Please choose an image to upload.');
      return;
    }

    const file = new File([blob], 'cropped-image.png', { type: 'image/png' }); // Convert Blob to File

    const formData = new FormData();
    formData.append('file', file, file.name);

    this.loadingService.show(); // Show loading indicator

    this.dataService.uploadFile(formData).subscribe({
      next: (response: any) => {
        this.loadingService.hide();
        this.handleUploadResponse(response);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred while uploading the file');
      }
    });
  }

  private handleUploadResponse(response: any): void {
    if (Array.isArray(response) && response.length > 0) {
      response.forEach((attachment: any) => {
        const fileUrl = attachment.path || attachment.fileName;

        // Update the Pan Card image URL
        this.requestImage = fileUrl;
      });
      this.changeDetectorRef.detectChanges();
      this.toastService.show('File uploaded successfully!');
    } else {
      this.toastService.show('Failed to get file URL');
    }
  }

  removeImage() {
    this.requestImage = null;
  }

  async submitForm(form: any): Promise<any> {
    this.onClickValidation = !form.valid;

    if (!form.valid || !this.isValidBasicRequest(form)) {
      return;
    }

    const payload = {
      customer: null,
      subject: this.subject || null,
      description: this.description || null,
      category: this.category || null,
      attachments: this.generateAttachments()
    };
    //  this.loadingService.show();
    this.isProcessing = true;
    this.dataService.createSupportRequest(payload)
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          const data = response.data;
          this.handleSupportRequestsResponse(data);

        }, error: (error: any) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message || 'An error occurred');
        }
      })
  }

  handleSupportRequestsResponse(data: any) {
    this.clearForm();
    this.closeRequestModal();

    this.successPaymentDetails = data;
    this.openSuccessPopup();
  }

  openSuccessPopup() {
    this.isSuccessfulResponse = true;
  }

  closeSuccessPopup() {
    this.isSuccessfulResponse = false;
  }

  closeDetails() {
    this.closeSuccessPopup();

    // Reset offset and reload requests
    this.filter.offset = 1; // Reset offset to 1
    this.supportTickets = new Array<any>(); // Clear current change requests to avoid duplicates
    this.fetchSupportListing(); // Reload the requests
  }

  openDetails(requestId: string) {
    this.closeSuccessPopup();
    setTimeout(() => {
      this.navController.navigateForward(`/portal/support/request/${requestId}/detail`);
    }, 200);
  }

  clearForm() {
    this.subject = null;
    this.description = null;
    this.category = null;
    this.requestImage = null;
  }

  private generateAttachments() {
    if (!this.requestImage) return [];

    const images = Array.isArray(this.requestImage) ? this.requestImage : [this.requestImage];

    return images.map((url, index) => ({
      fileName: `image-${index + 1}`, // Keep the filename simple
      mimeType: 'image/*', // Use a wildcard to accept any image type
      path: url,
      originalName: `image-${index + 1}`
    }));
  }

  isValidBasicRequest(form: any) {
    if (!this.subject || this.subject.trim() === '') {
      form.controls.subject.setErrors({ invalid: true });
      return false;
    }
    if (!this.description || this.description.trim() === '') {
      form.controls.description.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

}
