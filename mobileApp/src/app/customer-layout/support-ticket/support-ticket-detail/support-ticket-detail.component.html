<ion-content class="customer-dashboard-page customer-payment-page">
  <app-customer-header [innerPage]="true" [headingText]="'Support Request Detail'" [rightAction]="true"
    [backUrl]="backUrl">
  </app-customer-header>
  <div class="payment-status-items">
    <div class="payment-status-item" [ngClass]="{'active': filter?.tabType === 'BASICDETAIL'}"
      (click)="onChangeStatusTab('BASICDETAIL')">
      Basic Detail
    </div>
    <div class="payment-status-item" [ngClass]="{'active': filter?.tabType === 'COMMENTS'}"
      (click)="onChangeStatusTab('COMMENTS')">
      Comments
    </div>
  </div>
  <div class="customer-body-section has-header-section no-padding" #chatMessagesContainer>
    <div>
      <div class="payment-items no-padding" *ngIf="filter?.tabType === 'BASICDETAIL'">
        <div class="payment-item box-shadow suport-detail-no-border" [ngClass]="{
            'success': (supportRequestData?.status === 'RESOLVED'),
            'warring': (supportRequestData?.status === 'OPEN'),
            'in-progress': (supportRequestData?.status === 'IN_PROGRESS')
          }">
          <div class="payment-item-container status-class no-bg-color">
            <div class="support-card-container">
              <div class="support-status">
                <span class="ticket-id" *ngIf="supportRequestData?.ticketId">
                  {{ supportRequestData?.ticketId }}
                </span>
                <span class="ticket-status" *ngIf="supportRequestData?.status" [ngClass]="{
                    'ticket-success': (supportRequestData?.status === 'RESOLVED'),
                    'ticket-warring': (supportRequestData?.status === 'OPEN'),
                    'ticket-in-progress': (supportRequestData?.status === 'IN_PROGRESS')
                  }">
                  {{ commonService.formatText(supportRequestData?.status) }}
                </span>
                <span class="ticket-status ticket-category">
                  {{ commonService.formatText(supportRequestData?.category) }}
                </span>
              </div>

              <div class="support-subject-title">
                <span *ngIf="supportRequestData?.subject">{{ commonService.formatText(supportRequestData?.subject)
                  }}</span>
                <p class="support-description" *ngIf="supportRequestData?.description">
                  {{supportRequestData?.description.length > 100
                  ?
                  (supportRequestData?.description | slice:0:100) + '...' : supportRequestData?.description }}</p>

              </div>
              <div class="resolution-con">
                <div class="title margin-top-10"
                  *ngIf="supportRequestData?.resolution || (supportRequestData?.resolution && supportRequestData?.attachments && supportRequestData?.attachments.length > 0)">
                  Resolution</div>
                <div class="title"
                  *ngIf="!supportRequestData?.resolution && supportRequestData?.attachments && supportRequestData?.attachments.length > 0">
                  Attachments
                </div>
                <div class="desc" *ngIf="supportRequestData?.resolution">{{supportRequestData?.resolution}}</div>

                <div class="image-grid"
                  *ngIf="supportRequestData?.attachments && supportRequestData?.attachments.length > 0">
                  <div class="image-item" *ngFor="let image of supportRequestData?.attachments"
                    (click)="commonService.viewImage(image.path)">
                    <img [src]="image.path" alt="Image" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chat Section -->
      <div class="chat-container margin-bottom-45" *ngIf="filter?.tabType === 'COMMENTS'">
        <div *ngFor="let comment of supportTicketComments"
          [ngClass]="comment.isMe ? (comment.attachments?.length > 0 ? 'chat-messages message sender' : 'chat-messages message sender-text') : (comment.attachments?.length > 0 ? 'chat-messages-receiver message receiver' : 'chat-messages-receiver message receiver-text')">
          <div>
            <div>{{comment.comment}}</div>
            <div class="image-grid" *ngIf="comment.attachments && comment.attachments.length > 0">
              <div class="image-item" *ngFor="let image of comment.attachments"
                (click)="commonService.viewImage(image.path)">
                <img [src]="image.path" alt="Image" />
              </div>
            </div>
          </div>
        </div>

        <div class="chat-input-container">
          <ion-icon name="attach-outline" class="upload-icon" (click)="upload()"></ion-icon>
          <div class="full-width margin-right-7 margin-left-7">
            <ion-item class="site-form-control" lines="none">
              <ion-textarea label="Type your message..." labelPlacement="floating" [(ngModel)]="userMessage"
                required="required">
              </ion-textarea>
            </ion-item>
          </div>
          <!-- <input type="text" placeholder="Type your message..." class="chat-input" [(ngModel)]="userMessage" /> -->
          <ion-icon name="send-outline" class="send-icon" (click)="sendMessage()"></ion-icon>
        </div>
      </div>
    </div>

  </div>
</ion-content>