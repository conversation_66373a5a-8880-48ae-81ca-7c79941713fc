import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Modal<PERSON>ontroller, NavController } from '@ionic/angular';
import { take } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { FileCropperComponent } from 'src/shared/file-cropper/file-cropper.component';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-support-ticket-detail',
  templateUrl: './support-ticket-detail.component.html',
  styleUrls: ['./support-ticket-detail.component.scss'],
})
export class SupportTicketDetailComponent implements OnInit, OnDestroy {
  // @ViewChild(IonContent, { static: false }) content!: IonContent;
  @ViewChild('chatMessagesContainer', { static: false }) chatMessagesContainer: any;
  user: any;
  filter: any;
  loading: string = "NOT_STARTED";

  supportTicketComments: Array<any> = new Array<any>();
  requestImage: string | null = null;

  supportRequestData: any;
  userMessage: string = '';
  supportNotificationId: string = '';
  supportTicketId: string = "";
  queryParams: any;
  private commentsInterval: any;
  public scrollInterval: any;

  fromScreen: string | null = null;
  backUrl: string | null = null;

  constructor(private authService: AuthService,
    private readonly localStorageService: LocalStorageService,
    private route: ActivatedRoute,
    private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly dataService: DataService,
    public readonly commonService: CommonService,
    private readonly cdr: ChangeDetectorRef,
    private readonly loadingService: LoadingService,
    private modalCtrl: ModalController
  ) {

  }

  ngOnInit() {
    this.supportTicketId = this.route.snapshot.paramMap.get('id') || "";
    this.user = this.authService.getUser();
    // Start polling fetchMyComments every 30 seconds
    this.commentsInterval = setInterval(() => {
      if (this.filter?.tabType === 'COMMENTS') {
        this.fetchMyComments();
      }
    }, 30000); // 30 seconds interval
  }

  ionViewWillEnter() {
    this.supportTicketId = this.route.snapshot.paramMap.get('id') || "";
    this.user = this.authService.getUser();
    if (this.supportTicketId === "") {
      //TO DO redirect to 404
      return;
    }
    this.fetchSupportTicketDetailById();
    this.supportTicketComments = new Array<any>();

    this.filter = {} as any;
    this.filter.tabType = "BASICDETAIL";

    this.queryParams = this.route.snapshot.queryParams;

    this.filter.tabType = this.queryParams['tab'] || "BASICDETAIL";

    if (this.filter.tabType == "COMMENTS") {
      this.fetchMyComments();
      this.scrollInterval = setTimeout(() => {
        this.scrollToBottom();
      }, 1000);
    }

    this.fromScreen = this.queryParams['fromScreen'] ? this.queryParams['fromScreen'].replace(/['"]/g, '') : null;
    this.backUrl = this.fromScreen === 'supportRequestComment' ? `/portal/notification/list` : `/portal/support/request/listing`;

    // }
    this.cdr.detectChanges();
    // });
  }

  fetchSupportTicketDetailById() {
    this.dataService.getSupportRequestById(this.supportTicketId)
      .subscribe({
        next: (response: RestResponse) => {
          this.loading = "LOADED";
          this.supportRequestData = response.data;
        }, error: (error: any) => {
          this.loading = "LOADED";
          this.toastService.show(error.message || 'An error occurred');
        }
      });
  }

  async scrollToBottom() {
    if (!this.chatMessagesContainer) return;
    const container = this.chatMessagesContainer.nativeElement;
    container.scrollTop = container.scrollHeight;  // Scroll to the bottom
  }

  onChangeStatusTab(status: string) {
    this.filter.tabType = status;

    if (this.filter.tabType === 'COMMENTS') {
      this.fetchMyComments();
      setTimeout(() => {
        this.scrollToBottom();
      }, 1000);
    }
  }

  sendMessage() {
    if (!this.userMessage.trim() && !this.requestImage) {
      this.toastService.show("Please enter text or upload an image.");
      return;
    }
    const payload = {
      ticket: this.supportRequestData.id,
      comment: this.userMessage || null,
      attachments: this.generateAttachments()
    };
    this.dataService.sendComment(payload)
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          setTimeout(() => {
            this.scrollToBottom();
          }, 1000);
          this.fetchMyComments();
        }, error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'An error occurred');
        }
      })

    this.userMessage = '';
    this.requestImage = null;
  }

  private generateAttachments() {
    if (!this.requestImage) return []; // If no image is uploaded, return an empty array
    return [
      {
        fileName: 'uploaded-image',
        mimeType: 'image/*',
        path: this.requestImage,
        originalName: 'uploaded-image',
      },
    ];
  }

  fetchMyComments() {
    const payload = {
      supportTicketId: this.supportTicketId
    };
    this.dataService.mySupportComments(payload)
      .subscribe({
        next: (response: RestResponse) => {

          this.supportTicketComments = response.data;
        }, error: (error: any) => {
          this.toastService.show(error.message || 'An error occurred');
        }
      })
  }

  async upload() {
    try {
      const response = await Camera.getPhoto({
        quality: 50,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: CameraSource.Prompt, // Prompt user to select from camera or files
      });
      if (response.base64String) {
        await this.processCropper(response.base64String);
      }
    } catch (error) {
      //  this.toastService.show("Something went wrong while uploading profile picture.");
    }
  }

  async processCropper(base64Image: string) {
    const modal = await this.modalCtrl.create({
      component: FileCropperComponent,
      componentProps: {
        file: { base64String: base64Image }
      },
      cssClass: "cropper-modal"
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role !== 'confirm') {
      this.toastService.show("Sorry, profile picture uploading has been cancelled.");
      return;
    }
    if (data && data.croppedFile) {
      await this.uploadImage(data.croppedFile);
    } else {
      this.toastService.show("No file selected after cropping.");
    }
  }

  async uploadImage(blob: Blob): Promise<void> {
    if (!blob) {
      this.toastService.show('No file selected. Please choose an image to upload.');
      return;
    }

    const file = new File([blob], 'cropped-image.png', { type: 'image/png' }); // Convert Blob to File

    const formData = new FormData();
    formData.append('file', file, file.name);

    this.loadingService.show(); // Show loading indicator

    this.dataService.uploadFile(formData).subscribe({
      next: (response: any) => {
        this.loadingService.hide();
        this.handleUploadResponse(response);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred while uploading the file');
      }
    });
  }

  private handleUploadResponse(response: any): void {
    if (response && response[0]?.path) {
      this.requestImage = response[0].path; // Set single image path
      this.sendMessage();
      this.toastService.show('Image uploaded successfully.');
    } else {
      this.toastService.show('Failed to upload image.');
    }
  }

  ngOnDestroy() {
    // Clear the interval when the component is destroyed
    if (this.commentsInterval) {
      clearInterval(this.commentsInterval);
    }
    if (this.scrollInterval) {
      clearInterval(this.scrollInterval);
    }
    // if (this.queryParamsSubscribe) {
    //   this.queryParamsSubscribe.unsubscribe();
    // }
  }
}
