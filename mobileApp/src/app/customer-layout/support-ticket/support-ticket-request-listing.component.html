<ion-content class="customer-dashboard-page customer-payment-page booking-page">
  <app-customer-header [innerPage]="true" [headingText]="'Support Requests'" [rightAction]="true"
    [rightActionIcon]="'Plus'" (rightActionCallback)="openRequestModal()" [backUrl]="backUrl"></app-customer-header>

  <!-- <form class="custom-form height-for-ion-scroll" #recordForm="ngForm" novalidate="novalidate"> -->

  <!-- <div class="booking-page-container supportList-padding"> -->
  <div class="customer-body-section has-header-section-change-profile has-no-padding">

    <div class="payment-body-section less-height">
      <ion-content class="payment-body-ion-content scroll">
        @if (loading==='LOADING') {
        <div class="payment-items no-margin-bottom no-padding">
          <div class="payment-item box-shadow warring">
            <div class="payment-item-container status-class status-warring">
              <div class="support-card-container">
                <div class="support-status">
                  <span class="ticket-id"><ion-skeleton-text [animated]="true"
                      style="width: 150px"></ion-skeleton-text></span>
                </div>
                <div class="support-subject-title">
                  <span><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></span>
                  <p class="support-description">
                    <ion-skeleton-text [animated]="true" style="width: 90%;height: 50px;"></ion-skeleton-text>
                  </p>
                </div>
                <div class="support-bottom-container">

                  <div class="support-category">
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="payment-item box-shadow warring">
            <div class="payment-item-container status-class status-warring">
              <div class="support-card-container">
                <div class="support-status">
                  <span class="ticket-id"><ion-skeleton-text [animated]="true"
                      style="width: 150px"></ion-skeleton-text></span>
                </div>
                <div class="support-subject-title">
                  <span><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></span>
                  <p class="support-description">
                    <ion-skeleton-text [animated]="true" style="width: 90%;height: 50px;"></ion-skeleton-text>
                  </p>
                </div>
                <div class="support-bottom-container">

                  <div class="support-category">
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="payment-item box-shadow warring">
            <div class="payment-item-container status-class status-warring">
              <div class="support-card-container">
                <div class="support-status">
                  <span class="ticket-id"><ion-skeleton-text [animated]="true"
                      style="width: 150px"></ion-skeleton-text></span>
                </div>
                <div class="support-subject-title">
                  <span><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></span>
                  <p class="support-description">
                    <ion-skeleton-text [animated]="true" style="width: 90%;height: 50px;"></ion-skeleton-text>
                  </p>
                </div>
                <div class="support-bottom-container">

                  <div class="support-category">
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        }@else if (loading==='LOADED') {
        <div class="payment-items no-margin-bottom no-padding">
          <div class="payment-item box-shadow" *ngFor="let requestList of supportTickets" [ngClass]="{'success':(requestList.status==='RESOLVED'),'warring':(requestList.status==='OPEN'),
                'in-progress':(requestList.status==='IN_PROGRESS'),'highlighted-payment': requestList.id === supportRequestnotificationId
              }">
            <div class="payment-item-container status-class" (click)="supportRequestDetail(requestList.id)" [ngClass]="{'status-success':(requestList.status==='RESOLVED'),'status-warring':(requestList.status==='OPEN'),
                'status-in-progress':(requestList.status==='IN_PROGRESS')
              }">

              <div class="support-card-container">
                <div class="support-status">
                  <span class="ticket-id" *ngIf="requestList.ticketId">{{requestList.ticketId}}</span>
                  <span class="ticket-status" *ngIf="requestList.status" [ngClass]="{'ticket-success':(requestList.status==='RESOLVED'),'ticket-warring':(requestList.status==='OPEN'),
                        'ticket-in-progress':(requestList.status==='IN_PROGRESS')
                      }">
                    {{ commonService.formatText(requestList.status) }}</span>
                  <span class="ticket-status ticket-category">{{ commonService.formatText(requestList.category)
                    }}</span>
                </div>
                <div class="support-subject-title">
                  <span *ngIf="requestList.subject">{{ commonService.formatText(requestList.subject) }}</span>
                  <p class="support-description" *ngIf="requestList.description">{{requestList.description.length > 100
                    ?
                    (requestList.description | slice:0:100) + '...' : requestList.description }}</p>
                  <img *ngIf="requestList?.attachments?.length > 0" [src]="requestList?.attachments[0]?.path"
                    alt="Pan Card" class="">
                </div>

              </div>

            </div>
          </div>

          <!-- No Data Available Message -->
          <div class="customer-body-section no-height" *ngIf="supportTickets.length <= 0">
            <div class="no-bookings-container">
              <div class="no-bookings-icon">
                <ion-icon name="alert-circle-outline"></ion-icon>
              </div>
              <h2 class="no-bookings-title">No Data Available</h2>
            </div>
          </div>
        </div>

        <ion-infinite-scroll threshold="100px" (ionInfinite)="onPageChanged($event)" *ngIf="showInfiniteScroll">
          <ion-infinite-scroll-content loadingSpinner="bubbles"
            loadingText="Loading more data..."></ion-infinite-scroll-content>
        </ion-infinite-scroll>
        }
      </ion-content>
    </div>

  </div>

  <!-- </div> -->
  <!-- </form> -->
  <!-- Floating Action Button -->
  <ion-fab class="less-margin" vertical="bottom" horizontal="end" slot="fixed" (click)="openFilterPopup()">
    <ion-fab-button>
      <ion-icon src="assets/images/svg/filter.svg" class="fab-filter-icon"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #filterModal [isOpen]="isFilterPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeFilterPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">Apply Filter</div>
          <div class="job-info margin-top-10">
            <div class="margin-bottom-15">
              <ion-item class="site-form-control" lines="none">
                <ion-input readonly="readonly" (click)="openDatePicker('fromDate')" label="From Date"
                  labelPlacement="floating" required="required" name="checkInDate" #checkInDate="ngModel"
                  [(ngModel)]="appliedFilter.startDate" [value]="appliedFilter.startDate | date:'yyyy-MM-dd'">
                </ion-input>
                <ion-icon slot="end" class="toggle-password-icon" (click)="openDatePicker('fromDate')"
                  [src]="'/assets/images/svg/calendar.svg'"></ion-icon>
              </ion-item>
            </div>

            <div class="margin-bottom-15">
              <ion-item class="site-form-control" lines="none">
                <ion-input readonly="readonly" (click)="openDatePicker('toDate')" label="To Date"
                  labelPlacement="floating" required="required" name="checkOutDate" #checkOutDate="ngModel"
                  [(ngModel)]="appliedFilter.endDate" [value]="appliedFilter.endDate | date:'yyyy-MM-dd'">
                </ion-input>
                <ion-icon slot="end" class="toggle-password-icon" (click)="openDatePicker('toDate')"
                  [src]="'/assets/images/svg/calendar.svg'"></ion-icon>
              </ion-item>
            </div>

            <div class="address-fields-container">
              <div class="field-container large-field margin-bottom-15">
                <ion-item class="site-form-control" lines="none">
                  <ion-select class="no-padding" label="Category" labelPlacement="floating" interface="action-sheet"
                    required name="cat" #cat="ngModel" [(ngModel)]="appliedFilter.category">
                    <ion-select-option *ngFor="let category of categoryTypes" [value]="category.value">
                      {{ category.display }}
                    </ion-select-option>
                  </ion-select>
                  <ion-icon slot="end" class="select-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
                </ion-item>
              </div>

              <div class="field-container small-field margin-bottom-15">
                <ion-item class="site-form-control" lines="none">
                  <ion-select class="no-padding" label="Select Status" labelPlacement="floating"
                    interface="action-sheet" required name="statusOption" #statusOption="ngModel"
                    [(ngModel)]="appliedFilter.status">
                    <ion-select-option *ngFor="let status of statusTypes" [value]="status">
                      {{ status }}
                    </ion-select-option>
                  </ion-select>
                  <ion-icon slot="end" class="select-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
                </ion-item>
              </div>
            </div>

          </div>

          <ion-button class="site-full-rounded-button" shape="round" type="button" (click)="applyFilter()">
            Apply</ion-button>
          <ion-button class="site-full-rounded-button" shape="round" type="button" (click)="clearFilter()">
            Clear</ion-button>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<!-- create change request modal -->
<ion-modal class="site-custom-popup job-invitation-popup" #isChangeRequestPopup [isOpen]="isRequestModalOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeRequestModal()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">

        <form class="custom-form" #firstForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">Create Request</div>
          <div class="job-info margin-top-10">

            <div class="">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': cat.invalid && onClickValidation}">
                <ion-select class="no-padding" label="Category" labelPlacement="floating" interface="action-sheet"
                  required name="cat" #cat="ngModel" [(ngModel)]="category">
                  <ion-select-option *ngFor="let category of categoryTypes" [value]="category.value">
                    {{ category.display }}
                  </ion-select-option>
                </ion-select>
              </ion-item>
              <app-validation-message [field]="cat" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Please select a category.'">
              </app-validation-message>
            </div>

            <div class="margin-top-10">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': !sub.valid && onClickValidation}">
                <ion-input label="Title" labelPlacement="floating" name="sub" #sub="ngModel" [(ngModel)]="subject"
                  required="required" maxlength="50">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="sub" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
            </div>

            <div class="margin-top-10">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': !desc.valid && onClickValidation}">
                <ion-textarea label="Description" labelPlacement="floating" name="desc" #desc="ngModel"
                  [(ngModel)]="description" required="required">
                </ion-textarea>
              </ion-item>
              <app-validation-message [field]="desc" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
            </div>

            <!-- customer request Image Section -->
            <div class="document-items attachment-con">
              <div class="document-upload-container" (click)="upload()"
                [ngClass]="{'missing-image': showValidationErrors && !requestImage}">
                <img *ngIf="!requestImage" src="/assets/images/svg/attach.svg" alt="Upload Icon" class="upload-icon">
                <img *ngIf="requestImage" [src]="requestImage" alt="Pan Card" class="uploaded-image">
                <div class="upload-text" *ngIf="!requestImage"
                  [ngClass]="{'missing-image': showValidationErrors && !requestImage}">
                  <span class="upload-adhar">Attachments</span>
                </div>
              </div>
              <!-- Delete icon outside the container -->
              <div class="delete-icon" *ngIf="requestImage" (click)="removeImage()">
                <img src="/assets/images/svg/delete.svg" alt="Delete Icon">
              </div>
            </div>

            <div class="privacy-container">
              <ion-button class="site-full-rounded-button primary-button text-capitalize" expand="full" shape="round"
                [disabled]="isProcessing" (click)="submitForm(firstForm.form)">
                Create Request
              </ion-button>
            </div>

          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #isSuccessPopup [isOpen]="isSuccessfulResponse"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-body no-padding-top">
        <div class="campaign-card">
          <div class="campaign-card-header success-modal-card-header">
            <div class="campaign-image-slide success-modal-card-image-slide">
              <i-feather name="X" class="close-icon white-icon" (click)="closeDetails()"></i-feather>
            </div>
          </div>
          <div class="coin-container request-coin-container">
            <ion-icon class="balance-icon request-bal-coin"
              [src]="'/assets/images/svg/create-request-icon.svg'"></ion-icon>
          </div>
          <div class="campaign-detail-container">
            <span class="campaign-detail-title success-text margin-top-10">Request Created</span>

            <div class="success-container margin-top-10" *ngIf="successPaymentDetails">
              <span>Support ticket <strong> {{successPaymentDetails?.ticketId}} </strong> raised successfully!</span>

              <span class="earned-my-cash margin-top-5">
                We will get back to you shortly.
              </span>
            </div>
          </div>
          <div class="detail-button margin-top-10" (click)="openDetails(successPaymentDetails?.id)">
            <span class="detail-text">View Detail</span>
          </div>
          <!-- <div class="privacy-container margin-top-20">
            <ion-button class="site-full-rounded-button less-border-radius margin-left-20 margin-right-20" expand="full"
              shape="round" type="submit" (click)="openDetails(successPaymentDetails?.id)">
              View Detail
            </ion-button>
          </div> -->

        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>