<ion-content class="onboarding-page">
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="continue(recordForm.form)">
    <div class="ion-cutomer-container onboarding-page-container">
      <div class="ion-text-left">
        <div class="header-container">
          <div class="back-button-container">
            <i-feather name="arrow-left" (click)="back()"></i-feather>
          </div>
          <div class="icon-step-container">
            <img src="assets/images/svg/informations.svg" class="icon-right" alt="information" />
            <div class="step-indicator-container">
              <div class="step-indicator">
                <span class="current-step">Step {{ 2 }}</span>
                <span class="total-steps">/ 6</span>
              </div>
            </div>
          </div>
        </div>
        <div class="page-heading">Your Email</div>
        <div class="onboarding-progress">
          <div class="onboarding-progress-completed" [ngStyle]="{'width': getProgressWidth()}"></div>
        </div>
        <p class="page-sub-heading">Link email with your profile.</p>
        <div class="margin-top-20">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':!user?.email.trim() && !userEmail.valid && onClickValidation}">
            <ion-input mode="md" label="Email Address" labelPlacement="floating" required="required" name="userEmail"
              #userEmail="ngModel" pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$" [(ngModel)]="profile.email"
              [ngClass]="{'is-invalid': !user?.email && userEmail.invalid && onClickValidation}"
              [disabled]="isEmailPresent()">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="userEmail" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>
        <div class="register-action-button-container">
          <div class="dont-have-text ion-text-center" (click)="skip()" *ngIf="!user?.email">
            Don't have an email address?
          </div>
          <div class="ion-text-right">
            <ion-button class="register-contiune-button" size="large" type="submit" [disabled]="isProcessing">
              <ion-icon slot="icon-only" name="chevron-forward-outline"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  </form>
</ion-content>