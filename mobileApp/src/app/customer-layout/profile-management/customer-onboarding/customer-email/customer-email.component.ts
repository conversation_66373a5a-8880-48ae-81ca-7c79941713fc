import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { ProfileDetail } from 'src/modals/profileDetail';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-customer-email',
  templateUrl: './customer-email.component.html',
  styleUrls: ['./customer-email.component.scss'],
})
export class CustomerEmailComponent implements OnInit {

  onClickValidation: boolean = false;
  profile: ProfileDetail = new ProfileDetail();
  subscription: Subscription = new Subscription();
  isProcessing: boolean = false;
  user: any;

  constructor(private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private readonly authService: AuthService
  ) {
  }

  ngOnInit() {
    this.init();
  }

  ionViewDidEnter() {
    this.init();
  }

  init() {
    this.user = this.authService.getUser();

    this.onClickValidation = false;
    this.profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING") || new ProfileDetail();

    if (!this.profile.email && this.user?.email) {
      this.profile.email = this.user.email;
    }
  }

  isEmailPresent(): boolean {
    return !!(this.user?.email && this.user.email.trim().length > 0);
  }

  getProgressWidth() {
    return "20%";
  }

  back() {
    this.navController.navigateBack("/account/register/basic", { animated: true });
  }

  async continue(form: any): Promise<any> {
    if (this.user?.email.trim()) {
      this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
      this.navController.navigateForward(`/account/register/picture`, { animated: true });
      return;
    }

    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }

    this.validateEmailAvailable();
  }

  validateEmailAvailable() {
    const input = {} as any;
    input.email = this.profile.email;
    //  this.loadingService.show();
    this.isProcessing = true;

    this.subscription = this.dataService.emailAvailable(input)
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          if (response.data === true) {
            this.toastService.show("Email already associated with another account.");
            return;
          }
          this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
          this.navController.navigateForward(`/account/register/picture`, { animated: true });
        },
        error: (error: any) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message);
        }
      })
  }

  skip() {
    this.profile.email = null;
    this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
    this.navController.navigateForward(`/account/register/picture`, { animated: true });
  }

  ngOnDestroy(): void {
    // Unsubscribe from any subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
