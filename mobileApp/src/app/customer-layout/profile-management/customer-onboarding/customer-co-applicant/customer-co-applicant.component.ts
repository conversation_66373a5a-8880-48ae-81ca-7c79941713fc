import { Component, OnDestroy, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { ProfileDetail } from 'src/modals/profileDetail';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-customer-co-applicant',
  templateUrl: './customer-co-applicant.component.html',
  styleUrls: ['./customer-co-applicant.component.scss'],
})
export class CustomerCoApplicantComponent implements OnInit, OnDestroy {

  onClickValidation!: boolean;
  coApplicantFirstName: string | null = null;
  coApplicantLastName: string | null = null;
  subscription: Subscription = new Subscription();
  profile: ProfileDetail = new ProfileDetail();
  salutationTypes: string[] = ['Mr', 'Mrs', 'Ms'];
  relationTypes: Array<{ display: string, value: string }> = [
    { display: 'Father', value: 'FATHER' },
    { display: 'Mother', value: 'MOTHER' },
    { display: 'Spouse', value: 'SPOUSE' },
    { display: 'Son', value: 'SON' },
    { display: 'Daughter', value: 'DAUGHTER' },
    { display: 'Brother', value: 'BROTHER' },
    { display: 'Sister', value: 'SISTER' },
    { display: 'Grandfather', value: 'GRANDFATHER' },
    { display: 'Grandmother', value: 'GRANDMOTHER' },
    { display: 'Uncle', value: 'UNCLE' },
    { display: 'Aunt', value: 'AUNT' },
    { display: 'Nephew', value: 'NEPHEW' },
    { display: 'Niece', value: 'NIECE' },
    { display: 'Cousin', value: 'COUSIN' },
    { display: 'Father-in-law', value: 'FATHER IN LAW' },
    { display: 'Mother-in-law', value: 'MOTHER IN LAW' },
    { display: 'Son-in-law', value: 'SON IN LAW' },
    { display: 'Daughter-in-law', value: 'DAUGHTER IN LAW' },
    { display: 'Brother-in-law', value: 'BROTHER IN LAW' },
    { display: 'Sister-in-law', value: 'SISTER IN LAW' },
    { display: 'Other', value: 'OTHER' }
  ];

  constructor(
    private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService
  ) {
  }

  ngOnInit() {
    this.onClickValidation = false;
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  ionViewDidEnter() {
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !this.profile.isValidBasicRequest(form)) {
      this.onClickValidation = true;
      return;
    }

    // Serialize profile data
    this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
    this.navController.navigateForward(`/account/register/referral`, { animated: true });
  }

  skip() {
    this.onClickValidation = false;
    this.navController.navigateForward(`/account/register/referral`, { animated: true });
  }

  back() {
    this.navController.navigateBack(`/account/register/location`, { animated: true });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
