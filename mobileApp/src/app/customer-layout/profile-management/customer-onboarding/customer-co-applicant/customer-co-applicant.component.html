<ion-content class="onboarding-page">
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="continue(recordForm.form)">
    <div class="ion-cutomer-container onboarding-page-container">
      <div class="ion-text-left">
        <div class="header-container">
          <div class="back-button-container align-back-skip-btn">
            <i-feather name="arrow-left" (click)="back()"></i-feather>
            <span class="skip-text co-applicant-skip" (click)="skip()">Skip</span>
          </div>
          <div class="icon-step-container">
            <img src="assets/images/svg/informations.svg" class="icon-right" alt="information" />
            <div class="step-indicator-container">
              <div class="step-indicator">
                <span class="current-step">Step {{ 5 }}</span>
                <span class="total-steps">/ 6</span>
              </div>
            </div>
          </div>
        </div>
        <div class="page-heading">Register Co-Applicant</div>
        <div class="onboarding-progress">
          <div class="onboarding-progress-completed" [ngStyle]="{'width': '51%'}"></div>
        </div>
        <div class="margin-top-20">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': salu.invalid && onClickValidation}">
            <ion-select class="no-padding" label="Salutation" labelPlacement="floating" interface="action-sheet"
              required name="salu" #salu="ngModel" [(ngModel)]="profile.userProfileDetail.coApplicantDetail.salutation">
              <ion-select-option *ngFor="let salutation of salutationTypes" [value]="salutation">
                {{ salutation }}
              </ion-select-option>
            </ion-select>
          </ion-item>
          <app-validation-message [field]="salu" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please select a salutation'">
          </app-validation-message>
        </div>
        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': firstName.invalid && onClickValidation}">
            <ion-input name="firstName" #firstName="ngModel"
              [(ngModel)]="profile.userProfileDetail.coApplicantDetail.firstName" required="required" maxlength="100"
              pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="First Name" labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="firstName" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div>
        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': lastName.invalid && onClickValidation}">
            <ion-input name="lastName" #lastName="ngModel"
              [(ngModel)]="profile.userProfileDetail.coApplicantDetail.lastName" required="required" maxlength="100"
              pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Last Name" labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="lastName" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div>
        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none" [ngClass]="{'is-invalid': rel.invalid && onClickValidation}">
            <ion-select class="no-padding" label="Relation" labelPlacement="floating" interface="action-sheet" required
              name="rel" #rel="ngModel" [(ngModel)]="profile.userProfileDetail.coApplicantDetail.relation">
              <ion-select-option *ngFor="let relation of relationTypes" [value]="relation.value">
                {{ relation.display }}
              </ion-select-option>
            </ion-select>
          </ion-item>
          <app-validation-message [field]="rel" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please select a relation.'">
          </app-validation-message>
        </div>
        <p class="page-sub-heading small-font-size margin-top-20"><strong>Note:-</strong> Co-applicant is required.
          While doing membership booking, either you or your
          co-applicant should be part of the booking. Please ensure that a co-applicant is added during registration to
          enjoy seamless booking benefits. You can also request to add a co-applicant later from your profile.</p>
        <div class="register-action-button-container">
          <div class="ion-text-right">
            <ion-button class="register-contiune-button" size="large" type="submit">
              <ion-icon slot="icon-only" name="chevron-forward-outline"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  </form>
</ion-content>