.success-page-container,
.failure-page-container {
  height: 100vh;

  .success-container {
    height: 70vh;
    //background: #f2651e;
    background: url("/assets/images/payment-success-background.svg");
    background-size: auto 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .logo {
      margin-bottom: 40px;

      ion-img {
        max-width: 200px;
      }
    }

    .text-container {
      margin-bottom: 20px;

      .text-tag {
        background-color: white;
        width: 150px;
        padding: 10px;
        border-radius: 25px;
        text-align: center;
        color: #f2651e;
        font-weight: 500;
        font-size: 18px;
        margin: 0 auto;
      }

      .text-section {
        margin-top: 15px;
        text-align: center;
        color: white;
        font-size: 18px;

        div {
          margin-bottom: 5px;
        }

        .message {
          font-size: 31px;
          font-family: "Oswald", sans-serif;
          margin-bottom: 5px;
        }
      }
    }
  }

  .failure-container {
    height: 70vh;
    //background: #fe5858;
    background: url("/assets/images/payment-failure-background.svg");
    background-size: auto 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .logo {
      margin-bottom: 40px;

      ion-img {
        max-width: 200px;
      }
    }

    .text-container {
      margin-bottom: 20px;

      .text-tag {
        background-color: white;
        width: 150px;
        padding: 10px;
        border-radius: 25px;
        text-align: center;
        color: #e41414;
        font-weight: 500;
        font-size: 18px;
        margin: 0 auto;
      }

      .text-section {
        margin-top: 15px;
        text-align: center;
        color: white;
        font-size: 18px;

        div {
          margin-bottom: 5px;
        }

        .message {
          font-size: 31px;
          font-family: "Oswald", sans-serif;
          margin-bottom: 5px;
        }
      }
    }
  }

  .info-section {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 30vh;

    .package-name {
      font-size: 31px;
      margin-bottom: 10px;
      font-family: "Oswald", sans-serif;
    }

    .package-amount {
      font-size: 18px;
      font-weight: 500;

      div {
        margin-bottom: 5px;
      }
    }

    .action-container {
      max-width: 80%;
      width: 100%;
    }
  }
}

ion-button {
  &.success-button {
    --background: #f2651e;
    text-transform: uppercase;
  }

  &.failure-button {
    --background: #fe5858;
    text-transform: uppercase;
  }
}