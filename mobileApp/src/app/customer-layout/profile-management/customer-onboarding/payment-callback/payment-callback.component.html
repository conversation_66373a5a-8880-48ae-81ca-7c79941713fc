<ion-content [ngClass]="{'success-page':state==='SUCCESS','failure-page':state!=='SUCCESS'}">
  @if(state==="SUCCESS"){
  <div class="success-page-container">
    <div class="success-container">
      <div class="logo">
        <ion-img src="/assets/images/payment-success-background-icon.svg"></ion-img>
      </div>
      <div class="text-container">
        <div class="text-tag">
          Awesome!
        </div>
        <div class="text-section">
          <div class="message">Congratulations.</div>
          <div>Your Membership plan is active</div>
        </div>
      </div>
    </div>
    <div class="info-section">
      <div class="package-name">{{purchasedPackageDetail?.selectedPackage?.packageName}}</div>
      <div class="package-amount">{{purchasedPackageDetail?.selectedPackage?.payableAmount | currency
        :'INR':'symbol':'1.0-0'}}</div>
      <div class="action-container">
        <ion-button class="margin-top-20 success-button" expand="full" shape="round" (click)="loadHomePage()">
          Book Your Holidays
        </ion-button>
      </div>
    </div>
  </div>
  }@else{
  <div class="failure-page-container">
    <div class="failure-container">
      <div class="logo">
        <ion-img src="/assets/images/payment-failure-background-icon.svg"></ion-img>
      </div>
      <div class="text-container">
        <div class="text-tag">
          Failed
        </div>
        <div class="text-section">
          <div>Opps! Something went</div>
          <div>terribly wrong here</div>
        </div>
      </div>
    </div>
    <div class="info-section">
      <div class="package-amount">
        <div>Your payment wasn't</div>
        <div>completed.</div>
      </div>
      <div class="action-container">
        <ion-button class="margin-top-20 failure-button" expand="full" shape="round" (click)="tryAgain()">
          Please try again
        </ion-button>
      </div>
    </div>
  </div>
  }
</ion-content>