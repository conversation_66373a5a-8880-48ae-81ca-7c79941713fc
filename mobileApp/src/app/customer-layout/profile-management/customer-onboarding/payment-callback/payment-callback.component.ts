import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NavController } from '@ionic/angular';

@Component({
  selector: 'app-payment-callback',
  templateUrl: './payment-callback.component.html',
  styleUrls: ['./payment-callback.component.scss'],
})
export class PaymentCallbackComponent implements OnInit {

  state: string = "SUCCESS";
  purchasedPackageDetail: any = {} as any;
  constructor(private router: Router, private route: ActivatedRoute, private navController: NavController) { }

  ngOnInit() {
    this.state = this.router.url === "/account/register/package/failure" ? "FAILURE" : "SUCCESS";
    this.route.queryParams.subscribe((params) => {
      const currentNavigation = this.router.getCurrentNavigation();
      if (currentNavigation?.extras?.state) {
        this.purchasedPackageDetail = currentNavigation.extras.state as any;
      }
    });
  }

  loadHomePage() {
    const navigationState = {
      activePackageGoToHome: true,
    };

    this.navController.navigateRoot("/dashboard", {
      state: navigationState,
      animated: true,
    });
  }

  tryAgain() {
    this.navController.back({ animated: true });
  }
}

