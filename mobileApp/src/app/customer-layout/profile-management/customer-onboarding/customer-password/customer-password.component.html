<ion-content class="onboarding-page">
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="continue(recordForm.form)">
    <div class="ion-cutomer-container onboarding-page-container">
      <div class="ion-text-left">
        <div class="header-container">
          <div class="back-button-container">
            <i-feather name="arrow-left" (click)="back()"></i-feather>
          </div>
          <!-- <div class="icon-step-container">
            <img src="assets/images/svg/informations.svg" class="icon-right" alt="information" />
            <div class="step-indicator-container">
              <div class="step-indicator">
                <span class="current-step">Step {{ 6 }}</span>
                <span class="total-steps">/ 7</span>
              </div>
            </div>
          </div> -->
        </div>
        <div class="page-heading">Create a Password</div>
        <!-- <div class="onboarding-progress">
          <div class="onboarding-progress-completed" [ngStyle]="{'width': getProgressWidth()}"></div>
        </div> -->
        <p class="page-sub-heading">Let's create your password for profile</p>
        <div class="margin-top-20">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':!userPassword.valid && onClickValidation}">
            <ion-input mode="md" label="Password" labelPlacement="floating" [type]="passwordFieldType"
              name="userPassword" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
              #userPassword="ngModel" [(ngModel)]="profile.password" required="required"></ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="profile.password && profile.password.length>0 && passwordFieldType==='password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="profile.password && profile.password.length>0 && passwordFieldType!=='password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="userPassword" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & be greater than 8 characters'">
          </app-validation-message>
        </div>

        <div class="register-action-button-container">
          <div class="ion-text-right">
            <ion-button class="register-contiune-button" size="large" type="submit" [disabled]="isProcessing">
              <ion-icon slot="icon-only" name="chevron-forward-outline"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  </form>
</ion-content>