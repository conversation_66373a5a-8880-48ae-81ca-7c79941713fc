import { Component, OnDestroy, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { ProfileDetail } from 'src/modals/profileDetail';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-customer-password',
  templateUrl: './customer-password.component.html',
  styleUrls: ['./customer-password.component.scss'],
})
export class CustomerPasswordComponent implements OnInit, OnDestroy {

  onClickValidation: boolean = false;
  profile: ProfileDetail = new ProfileDetail();
  passwordFieldType!: string;
  subscription: Subscription = new Subscription();
  isProcessing: boolean = false;

  constructor(private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly authService: AuthService
  ) {
  }

  ngOnInit() {
    this.init();
  }

  ionViewDidEnter() {
    this.init();
  }

  init() {
    this.onClickValidation = false;
    this.passwordFieldType = "password";
    this.profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
  }

  getProgressWidth() {
    return "68%";
  }

  back() {
    this.navController.navigateBack("/account/register", { animated: true });
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  // async continue(form: any): Promise<any> {
  //   this.onClickValidation = !form.valid;
  //   if (!form.valid) {
  //     return;
  //   }
  //   this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
  // this.navController.navigateForward(`/account/register/verification`, { animated: true });
  // }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    if (this.profile.phoneNumber) {
      this.profile.countryCode = this.profile.phoneNumber.split(" ")[0].trim();
      this.profile.phoneNumber = this.formatPhoneNumber(this.profile.phoneNumber);
    }
    //  this.loadingService.show();
    this.isProcessing = true;
    this.subscription = this.dataService.createAccount(this.profile)
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          const data = response.data;
          this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
          this.navController.navigateForward(`/account/register/verification`, { queryParams: { "customerId": response.data }, animated: true });
        },
        error: (error: any) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message);
        }
      })
  }

  formatPhoneNumber(phoneNumber: string): string {
    if (!phoneNumber) {
      return '';
    }
    // Remove country code (if it exists in the number)
    const cleanedNumber = phoneNumber.replace(this.profile.countryCode || '', '');
    return cleanedNumber.replace(/[^\d]/g, '').trim();
  }

  ngOnDestroy(): void {
    // Unsubscribe from any subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
