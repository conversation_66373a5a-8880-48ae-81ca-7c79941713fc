<ion-content class="onboarding-page">
  <div class="onboarding-page-container">
    <div class="form-container">
      <div class="register-container">
        <ion-img src="/assets/images/svg/logo.svg"></ion-img>
        <h2><span>ZForb</span></h2>
        <div class="margin-top-40">
          <div class="page-heading">Start Your Journey</div>
          <p class="page-heading-title margin-bottom-30">Provide your contact number to begin creating your account.</p>
        </div>
      </div>
      <form class="create-account-form" #recordForm="ngForm" novalidate="novalidate"
        (ngSubmit)="continue(recordForm.form)">

        <div class="margin-bottom-15">
          <ion-item class="site-form-control mobile-form-control" lines="none"
            [ngClass]="{'is-invalid':userPhone.invalid && onClickValidation}">
            <ion-icon name="call-outline" slot="start" class="start-icon"></ion-icon>
            <input class="custom-mobile-input" inputmode="tel" [attr.pattern]="pattern" [maskito]="mask"
              required="required" name="userPhone" #userPhone="ngModel" [(ngModel)]="profile.phoneNumber"
              placeholder="Enter Contact Number" />
            <img width="30" height="30" [attr.alt]="countryIsoCode" [src]="countryIsoCode" slot="end"
              [style.border-radius.%]="50" *ngIf="countryIsoCode!==''" />
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div>
        <ion-button class="margin-top-20 site-button" expand="full" shape="round" type="submit">
          <span *ngIf="!request.emailAvailableLoading">Continue</span>
          <ion-spinner class="dots" name="dots" *ngIf="request.emailAvailableLoading"></ion-spinner>
        </ion-button>
      </form>
    </div>
    <div class="privacy-container">
      <p>
        By continuing you are agree to our
        <a href="https://www.forbcorp.com/Terms%20and%20Conditions.pdf" target="_blank" rel="noopener noreferrer">
          Terms of Service
        </a>
        and
        <a href="https://www.forbcorp.com/privacy.php" target="_blank" rel="noopener noreferrer">
          Privacy Policy
        </a>
      </p>
    </div>
  </div>
</ion-content>
