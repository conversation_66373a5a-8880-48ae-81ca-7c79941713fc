import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import { isValidPhoneNumber } from 'libphonenumber-js/core';
import metadata from 'libphonenumber-js/min/metadata';
import { Subscription } from 'rxjs';
import { ProfileDetail } from 'src/modals/profileDetail';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import mask from './../../../../../shared/phone-number.mask';

@Component({
  selector: 'app-customer-registration',
  templateUrl: './customer-registration.component.html',
  styleUrls: ['./customer-registration.component.scss'],
})
export class CustomerRegistrationComponent implements OnInit {

  twoFactorEnabled: boolean = false;
  onClickValidation!: boolean;
  profile: ProfileDetail = new ProfileDetail();
  isApple: boolean = false;
  code: string = "";
  subscription: Subscription = new Subscription();
  request: any = {} as any;
  protected readonly mask = mask;

  constructor(
    private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService
  ) {
  }

  ngOnInit() {
    this.onClickValidation = false;
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  ionViewDidEnter() {
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
    this.profile.phoneNumber = '+91';
  }

  protected get countryIsoCode(): string {
    this.code = maskitoGetCountryFromNumber(this.profile.phoneNumber, metadata) ?? '';
    if (this.code === '') {
      return "";
    }
    return `/assets/images/icons/flags/${this.code.toLocaleLowerCase()}.png`;
  }

  protected get pattern(): string {
    return this.isApple ? '+[0-9-]{1,20}' : '';
  }

  back() {
    this.navController.navigateBack("/account/login", { animated: true });
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !isValidPhoneNumber(this.profile.phoneNumber, metadata)) {
      form.controls.userPhone.setErrors({ pattern: true });
      this.onClickValidation = true;
      return;
    }
    this.validateEmailAvailable();
  }

  validateEmailAvailable() {
    if (this.request.emailAvailableLoading) {
      return;// Prevent multiple requests
    }
    this.request.emailAvailableLoading = true;
    const input = {} as any;
    if (this.profile.phoneNumber) {
      input.countryCode = this.profile.phoneNumber.split(" ")[0].trim();
      input.phoneNumber = this.formatPhoneNumber(this.profile.phoneNumber);
    }
    this.subscription = this.dataService.mobileAvailable(input)
      .subscribe({
        next: (response: RestResponse) => {
          this.request.emailAvailableLoading = false;
          if (response.data === true) {
            this.toastService.show("Contact number already associated with another account.");
            return;
          }
          this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
          this.navController.navigateForward(`/account/register/password`, { animated: true });
        },
        error: (error: any) => {
          this.request.emailAvailableLoading = false;
          this.toastService.show(error.message);
        }
      })
  }

  formatPhoneNumber(phoneNumber: string): string {
    if (!phoneNumber) {
      return '';
    }
    // Remove country code (if it exists in the number)
    const cleanedNumber = phoneNumber.replace(this.profile.countryCode || '', '');
    return cleanedNumber.replace(/[^\d]/g, '').trim();
  }

  ngOnDestroy(): void {
    // Unsubscribe from any subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
