<ion-content class="onboarding-page">
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="continue()">
    <div class="ion-cutomer-container onboarding-page-container">
      <div class="ion-text-left">
        <div class="header-container">
          <div class="back-button-container">
            <i-feather name="arrow-left" (click)="back()"></i-feather>
          </div>
          <div class="icon-step-container">
            <img src="assets/images/svg/informations.svg" class="icon-right" alt="information" />
            <div class="step-indicator-container">
              <div class="step-indicator">
                <span class="current-step">Step {{ 3 }}</span>
                <span class="total-steps">/ 6</span>
              </div>
            </div>
          </div>
        </div>
        <div class="page-heading">Your profile picture</div>
        <div class="onboarding-progress">
          <div class="onboarding-progress-completed" [ngStyle]="{'width': getProgressWidth()}"></div>
        </div>
        <p class="page-sub-heading">Upload a photo so other can recognise you!</p>
        <div class="margin-top-20">
          <div class="profile-picture-wrapper">
            <div class="profile-picture-container margin-bottom-30">
              <img src="/assets/images/icons/user.png" *ngIf="!profile.profileImageUrl" alt="" />
              <img [src]="profile.profileImageUrl" class="profile-pic" *ngIf="profile.profileImageUrl"
                alt="Customer Profile" />
            </div>
            <img class="action-icon" src="/assets/images/svg/add.svg" alt="Edit" (click)="upload()" />
          </div>
        </div>
        <div class="register-action-button-container">
          <div class="ion-text-right">
            <ion-button class="register-contiune-button" size="large" type="submit">
              <ion-icon slot="icon-only" name="chevron-forward-outline"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  </form>
</ion-content>