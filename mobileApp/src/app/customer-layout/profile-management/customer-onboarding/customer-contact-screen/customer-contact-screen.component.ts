import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { ProfileDetail } from 'src/modals/profileDetail';
import { CommonService } from 'src/services/common.service';

@Component({
  selector: 'app-customer-contact-screen',
  templateUrl: './customer-contact-screen.component.html',
  styleUrls: ['./customer-contact-screen.component.scss'],
})
export class CustomerContactScreenComponent implements OnInit {

  profile: ProfileDetail = new ProfileDetail();
  onClickValidation!: boolean;

  phoneMask = this.commonService.phoneMask;
  maskPredicate = this.commonService.maskPredicate;

  constructor(private activatedRoute: ActivatedRoute,
    private navController: NavController,
    private commonService: CommonService
  ) {
    this.onClickValidation = false;
  }

  ngOnInit() {
    this.onClickValidation = false;
    this.activatedRoute.queryParams.subscribe(params => {
      const profileData = params['profile'];
      if (profileData) {
        this.profile = JSON.parse(decodeURIComponent(profileData));
      }
    });
  }

  getProgressWidth() {
    return "40%";
  }

  back() {
    this.navController.navigateBack("/portal/profile/pic", { animated: true });
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }

    const profileData = JSON.stringify(this.profile);

    // Navigate and pass profile data as a query parameter
    await this.navController.navigateForward(`/portal/customer/verification?profile=${encodeURIComponent(profileData)}`);
  }

}
