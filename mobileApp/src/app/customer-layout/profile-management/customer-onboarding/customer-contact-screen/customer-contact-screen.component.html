<ion-content class="onboarding-page">
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="continue(recordForm.form)">
    <div class="ion-cutomer-container onboarding-page-container">
      <div class="form-container text-left">
        <div class="back-button-container">
          <i-feather name="arrow-left" (click)="back()"></i-feather>
        </div>
        <div class="page-heading">Contact Number</div>
        <div class="onboarding-progress">
          <div class="onboarding-progress-completed" [ngStyle]="{'width':getProgressWidth()}"></div>
        </div>
        <div class="margin-top-20">
          <ion-item class="site-form-floating-control" lines="none"
            [ngClass]="{'is-invalid':!userPhone.valid && onClickValidation}">
            <ion-label position="floating">Phone Number</ion-label>
            <ion-input type="text" required name="userPhone" [(ngModel)]="profile.phoneNumber" #userPhone="ngModel"
              [ngClass]="{'is-invalid': userPhone.invalid && onClickValidation}"
              [maskito]="phoneMask" [maskitoElement]="maskPredicate" minlength="15">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>
      </div>
      <div class="privacy-container">
        <ion-button class="site-full-rounded-button primary-button" expand="full" shape="round" type="submit">
          Continue</ion-button>
      </div>
    </div>
  </form>
</ion-content>