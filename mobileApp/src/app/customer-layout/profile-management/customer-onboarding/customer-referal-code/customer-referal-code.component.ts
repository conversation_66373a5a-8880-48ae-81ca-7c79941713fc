import { Component, OnDestroy, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { ProfileDetail } from 'src/modals/profileDetail';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-customer-referal-code',
  templateUrl: './customer-referal-code.component.html',
  styleUrls: ['./customer-referal-code.component.scss'],
})
export class CustomerReferalCodeComponent implements OnInit, OnDestroy {

  onClickValidation: boolean = false;
  profile: ProfileDetail = new ProfileDetail();
  subscription: Subscription = new Subscription();
  isProcessing: boolean = false;

  constructor(private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly authService: AuthService
  ) {
  }

  ngOnInit() {
    this.init();
  }

  ionViewDidEnter() {
    this.init();
  }

  init() {
    this.onClickValidation = false;
    this.profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
  }

  getProgressWidth() {
    return "85%";
  }

  back() {
    this.navController.navigateBack("/account/register/co/applicant", { animated: true });
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    this.profile.countryCode = null;
    this.profile.phoneNumber = '';
    this.profile.password = null;

    //  this.loadingService.show();
    this.isProcessing = true;

    this.localStorageService.setObject('firstTimePackagePurchasing', true);
    this.subscription = this.dataService.customerOnboarding(this.profile)
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          const data = response.data;
          this.localStorageService.remove("CUSTOMER_ONBOARDING");
          this.processAndRedirectToDashboard(data);
        },
        error: (error: any) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message);
        }
      })
  }

  processAndRedirectToDashboard(data: any) {
    // Store token and user data in local storage
    data.token.expires_at = new Date(data.token.expires).getTime();
    this.localStorageService.setObject('temp-token', data.token);
    this.localStorageService.setObject('temp-user', data.user);

    // Navigate to the appropriate dashboard based on user role
    if (this.authService.isCustomer()) {
      this.localStorageService.remove('isLoginOpened');
     this.localStorageService.set('isLoginOpened',true);
      this.navController.navigateForward("/account1", { animated: true });
      return;
    }
    this.navController.navigateRoot("/sale-portal/dashboard");
  }

  ngOnDestroy(): void {
    // Unsubscribe from any subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
