<ion-content class="onboarding-page">
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="continue(recordForm.form)">
    <div class="ion-cutomer-container onboarding-page-container">
      <div class="ion-text-left">
        <div class="header-container">
          <div class="back-button-container">
            <i-feather name="arrow-left" (click)="back()"></i-feather>
          </div>
          <div class="icon-step-container">
            <img src="assets/images/svg/informations.svg" class="icon-right" alt="information" />
            <div class="step-indicator-container">
              <div class="step-indicator">
                <span class="current-step">Step {{ 6 }}</span>
                <span class="total-steps">/ 6</span>
              </div>
            </div>
          </div>
        </div>
        <div class="page-heading">Got a Referral Code?</div>
        <div class="onboarding-progress">
          <div class="onboarding-progress-completed" [ngStyle]="{'width': getProgressWidth()}"></div>
        </div>
        <p class="page-sub-heading">Enter it here to claim your rewards. You can skip this step if you don't have a
          referral code.</p>
        <div class="margin-top-20">
          <ion-item class="site-form-control" lines="none">
            <ion-input mode="md" label="Referral Code" labelPlacement="floating" type="text" name="userReferral"
              [(ngModel)]="profile.userProfileDetail.referredBy"></ion-input>
          </ion-item>
        </div>

        <div class="register-action-button-container text-capitalize">
          <div class="ion-text-right">
            <ion-button class="margin-top-20" expand="full" shape="round" type="submit"
              [disabled]="isProcessing">
              Complete Account Setup
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  </form>
</ion-content>