<ion-content class="login-page">
  <div class="login-page-container">
    <ion-buttons slot="start" class="back-button">
      <ion-button (click)="goToLoginPage()" fill="clear">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <div class="login-container">
      <ion-img src="/assets/images/svg/two-factor-authentication.svg"></ion-img>
      <h2>Mobile Number Verification</h2>
      <p class="page-heading-title no-margin-top">Please enter the verification code to proceed</p>
    </div>
    <!-- Container for the 2FA form -->
    <div class="form-container">
      <form #twoFactorOtpForm="ngForm" novalidate="novalidate" class="custom-form" autocomplete="off"
        (ngSubmit)="continue(twoFactorOtpForm.form)">
        <div class="margin-bottom-100">
          <div class="otp-validate-container">
            <ion-input required="required" minlength="1" maxlength="1" inputmode="numeric" type="text" placeholder="*"
              [debounce]="100" name="otpInput1" [ngClass]="{ 'is-invalid': onClickValidation && otpInput1.invalid }"
              [(ngModel)]="data.otpInput1" #otpInput1="ngModel" (ionInput)="commonService.changeInputFocus($event)"
              (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" [debounce]="100"
              name="otpInput2" #otpInput2="ngModel" [(ngModel)]="data.otpInput2"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput2.invalid }" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput3"
              [debounce]="100" #otpInput3="ngModel" [(ngModel)]="data.otpInput3"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput3.invalid}" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput4"
              [debounce]="100" #otpInput4="ngModel" [(ngModel)]="data.otpInput4"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput4.invalid }" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput5"
              [debounce]="100" #otpInput5="ngModel" [(ngModel)]="data.otpInput5"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput5.invalid}" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput6"
              [debounce]="100" #otpInput6="ngModel" [(ngModel)]="data.otpInput6"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput6.invalid}" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
          </div>
        </div>
        <!-- Submit button for the OTP form -->
        <ion-button class="margin-top-20" expand="full" shape="round" type="submit" [disabled]="isProcessing">
          Continue
        </ion-button>
      </form>
      <div class="ion-text-center margin-bottom-10 margin-top-20 resend-otp-text">
        <div *ngIf="counter>0">Resend enable in {{counter}} seconds</div>
        <a (click)="resendOtp()" *ngIf="counter<=0">Resend</a>
      </div>
    </div>
    <!-- Container for privacy and terms links -->
    <div class="privacy-container">
      <p>
        By continuing you are agree to our
        <a href="https://www.forbcorp.com/Terms%20and%20Conditions.pdf" target="_blank" rel="noopener noreferrer">
          Terms of Service
        </a>
        and
        <a href="https://www.forbcorp.com/privacy.php" target="_blank" rel="noopener noreferrer">
          Privacy Policy
        </a>
      </p>
    </div>
  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #noPackageAvailablePopup
  [isOpen]="isNoPackageAvailablePopupOpen" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeNoPackageAvailablePopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <div *ngIf="!activePackage">
          <div class="popup-large-heading">Oops! No Membership Found</div>
          <div class="popup-normal-heading margin-top-10 secondary-text">It looks like you don't have an active
            membership plan yet. Only members can enjoy exclusive holiday benefits! 🎉</div>
          <div class="popup-normal-heading margin-top-15 secondary-text">
            Unlock special offers, enjoy hassle-free holiday breaks, and more with our membership plans. Purchase a plan
            today to start enjoying your benefits!
          </div>
          <div class="popup-normal-heading margin-top-15 secondary-text">Don’t miss out on our special holiday deals!
          </div>
          <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
            (click)="explorePlan();noPackageAvailablePopup.dismiss()">
            Explore Membership Plans
          </ion-button>
        </div>
        <div *ngIf="activePackage && (!activePackage.isBookingAllowed || activePackage.status !== 'ACTIVE')">
          <div class="popup-large-heading">Booking Unavailable for Your Membership</div>
          <div class="popup-normal-heading margin-top-10 secondary-text">It seems your current membership plan doesn’t
            allow holiday bookings at this time.</div>
          <div class="popup-normal-heading margin-top-15 secondary-text">
            <b>Reason: </b>{{activePackage?.bookingNotAllowedNote}}
          </div>
          <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
            (click)="closeNoPackageAvailablePopup()">
            Contact
          </ion-button>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #noOnboardingPopup [isOpen]="isNoOnboarding"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeNoOnboardingPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <div>
          <div class="popup-large-heading">Incomplete Information</div>
          <div class="popup-normal-heading margin-top-10 secondary-text">Your profile needs to be updated before process
            this request. Please click 'Continue' to update your details.</div>
          <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
            (click)="continueToOnboarding()">
            Continue
          </ion-button>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>
