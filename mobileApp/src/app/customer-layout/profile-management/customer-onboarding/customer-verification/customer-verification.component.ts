import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ModalController, NavController } from '@ionic/angular';
import { interval, Subscription } from 'rxjs';
import { Login } from 'src/modals/login';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-customer-verification',
  templateUrl: './customer-verification.component.html',
  styleUrls: ['./customer-verification.component.scss'],
})
export class CustomerVerificationComponent implements OnInit {
  user: any;
  data: any; // data object
  subscription: Subscription = new Subscription();  // Subscription for verify request
  resendSubscription: Subscription = new Subscription();  // Subscription for resend request
  onClickValidation!: boolean; // Flag to control validation state
  counter!: number;
  path: any;
  isProcessing: boolean = false;
  hotels: any[] = []; // Initialize as an empty array
  hotelDetailsString: any;
  roomDetailsString: any;
  hotelDetails: any;
  roomDetailsDetails: any;
  selectedHotel: any;
  bookingType: string | null = null;
  hotelDetail: any;
  campaignId: string | null = null;
  fromScreen: string | null = 'home-screen';
  activePackage: any;
  isNoPackageAvailablePopupOpen: boolean = false;
  isNoOnboarding: boolean = false;

  constructor(private readonly navController: NavController,
    public commonService: CommonService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly toastService: ToastService,
    private readonly route: ActivatedRoute,
    private readonly authService: AuthService,
    private router: Router,
    private modalCtrl: ModalController
  ) {
    this.onClickValidation = false;
  }

  ngOnInit() {
    this.onClickValidation = false;

    this.path = this.router.url;

    this.route.queryParams.subscribe(params => {
      this.data = {
        id: params['customerId'] || null,
        sendotp: params['sendotp'] || false
      };
      if (!this.data.id) {
        this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
        this.navController.navigateRoot("/account/login", { animated: true });
        return;
      }
      if (this.data.sendotp) {
        this.resendOtp();
      }
    });
  }

  ionViewDidEnter() {
    this.disableResendOtp();
  }

  continue(form: any): void {
    if (!this.data.id) {
      this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
      this.navController.navigateRoot("/dashboard", { animated: true });
      return;
    }
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    //  this.loadingService.show();
    this.isProcessing = true;

    const otpString = this.commonService.getOTPFromInputs(
      this.data.otpInput1,
      this.data.otpInput2,
      this.data.otpInput3,
      this.data.otpInput4,
      this.data.otpInput5,
      this.data.otpInput6
    );
    const otpPayload = {
      id: this.data.id,
      otpCode: otpString
    };

    const method = this.path.includes('account/dashboard/otp/verification')
      ? 'verifyMobileForLoginWithOtp'
      : 'verifyMobile';

    // Make HTTP request to verify OTP
    this.subscription = this.dataService[method](otpPayload).subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        this.isProcessing = false;

        const data = response.data;

        if (data.user && !data.user.isOnBoardingComplete) {
          this.redirectToDashboard(data);
          return;
        }
        // Check if two-factor authentication is enable
        if (data?.twoFactorEnabled) {
          this.redirectToVerificationFlow(data);
          this.localStorageService.setObject('twoFactorLoginData', data);
          return;
        }
        this.localStorageService.set('isLoginOpened', 'true');
        this.redirectToDashboard(data);

      },
      error: (error: any) => {
        //  this.loadingService.hide();
        this.isProcessing = false;
        this.toastService.show(error.data?.message || error.message);
      }
    });
  }

  redirectToVerificationFlow(data: any) {
    this.navController.navigateForward("/account/verification", {
      queryParams: {
        verificationToken: data.verificationToken,
      }, animated: true
    });
  }

  redirectToOnboardingFlow(data: any) {
    data.token.expires_at = new Date(data.token.expires).getTime();
    if (this.path.includes('account/login/otp/verification')) {
      this.localStorageService.setObject('token', data.token);
      this.localStorageService.setObject('user', data.user);
    }
    else {
      this.localStorageService.setObject('temp-token', data.token);
      this.localStorageService.setObject('temp-user', data.user);
    }
    this.navController.navigateRoot(`/account/register/basic`, { animated: true });
  }

  redirectToDashboard(data: any) {
    // Store token and user data in local storage
    data.token.expires_at = new Date(data.token.expires).getTime();
    this.localStorageService.setObject('token', data.token);
    this.localStorageService.setObject('user', data.user);
    const redirectUrl = localStorage.getItem('redirectAfterLogin');
    this.localStorageService.remove('redirectAfterLogin');
    // Navigate to the appropriate dashboard based on user role
    this.localStorageService.remove('isLoginOpened');
    this.localStorageService.set('isLoginOpened', true);
    if (redirectUrl) {
      this.navController.navigateRoot(redirectUrl);
      return;
    }
    // Navigate to the appropriate dashboard based on user role

    if (this.authService.isCustomer()) {
      this.user = this.authService.getUser();

      //handle all the steps of booking without booking
      // isLoginOpened
      let bookingWithoutLogin = this.localStorageService.get('bookingWithoutLogin');
      let compaignsWithoutLogin = this.localStorageService.get('compaignsWithoutLogin');
      if (bookingWithoutLogin === 'OTHERS' && !compaignsWithoutLogin) {
        this.bookingType = bookingWithoutLogin;
        this.hotelDetailsString = this.localStorageService.getObject("hotelDetails");
        this.roomDetailsString = this.localStorageService.getObject("roomDetails");
        if (this.roomDetailsString) {
          this.roomDetailsDetails = JSON.parse(this.roomDetailsString);
        }
        if (this.hotelDetailsString) {
          this.hotelDetails = JSON.parse(this.hotelDetailsString);
          if (this.hotelDetails) {
            if (Array.isArray(this.hotelDetails)) {
              this.hotels = this.hotelDetails;
            } else if (this.hotelDetails.hotel) {
              this.hotels = this.hotelDetails.hotel;
              this.bookingType = this.hotelDetails.bookingType;
            } else {
              this.hotels = [this.hotelDetails];
            }
          } else {
            this.hotels = [];
          }
        }
        this.preBooking(this.roomDetailsDetails);
        return;
      }
      let bookingType = this.localStorageService.get('bookingTypeNoLogin');
      if (bookingType === 'MEMBERSHIP') {
        this.fetchMyActiveSubscriptionAndHandleMembership(bookingType);
        return;
        // if (!this.activePackage || !this.activePackage.isBookingAllowed || this.activePackage.status !== "ACTIVE") {
        //   this.isNoPackageAvailablePopupOpen = true;
        //   return;
        // }
        // if (this.activePackage && !this.user?.isOnBoardingComplete) {
        //   this.openNoOnboardingPopup();
        //   return;
        // }
        // this.navController.navigateForward("/portal/search/hotels", { animated: true, queryParams: { bookingType: bookingType } });
        // return;
      }
      let subscribeWithoutLogin = this.localStorageService.get('subscribeNowWithoutLogin');
      if (subscribeWithoutLogin) {
        let packageDetail = this.localStorageService.getObject('packageDetail');
        this.navController.navigateForward(`/account/register/package/${packageDetail.packageId}/${this.fromScreen}/pay`, { state: packageDetail, animated: true });
        return;
      }
      let activeCampaignWithoutLogin = this.localStorageService.get('activeCampaignWithoutLogin');
      let user = this.localStorageService.getObject('user')
      if (activeCampaignWithoutLogin) {
        this.navController.navigateForward(`/portal/see/more/${user.walletId}/compaigns`, {
          state: {
            backScreen: this.fromScreen
          },
          animated: true
        });
        return;
      }

      if (compaignsWithoutLogin) {
        let campaignId = this.localStorageService.get('campaignId');
        this.navController.navigateForward('/portal/my/campaign/details', {
          state: {
            campaignId: campaignId,
            walletId: user.walletId,
            fromScreen: this.fromScreen
          },
          animated: true
        });
        this.localStorageService.remove('campaignId');
        this.localStorageService.remove('compaignsWithoutLogin');
        return;
      }

      this.navController.navigateRoot("/dashboard");
      return;
    }
    if (this.authService.isSale()) {
      this.navController.navigateRoot("/sale-portal/dashboard");
      return;
    }
    this.toastService.show("Sorry, Something went wrong while processing your request. Please contact to administrator");
    this.authService.logout();
    this.data = new Login();
  }

  fetchMyActiveSubscriptionAndHandleMembership(bookingType: string) {
    this.dataService.fetchMyActiveSubscription().subscribe({
      next: (response: RestResponse) => {
        this.activePackage = response.data;
        this.localStorageService.setObject("activeSubscriptionData", response.data);
        this.localStorageService.setObject("coApplicantDetail", response.data.coApplicantDetail);

        this.handleMembershipFlow(bookingType);
      },
      error: (error: any) => {
        const message = error?.data?.message || error?.message || "";
        if (message.includes("purchased package")) {
          this.activePackage = null;  // Simulate no package
        }
        this.handleMembershipFlow(bookingType);
      }
    });
  }

  handleMembershipFlow(bookingType: string) {
    if (!this.activePackage || !this.activePackage.isBookingAllowed || this.activePackage.status !== "ACTIVE") {
      this.isNoPackageAvailablePopupOpen = true;
      return;
    }

    if (!this.user?.isOnBoardingComplete) {
      this.openNoOnboardingPopup();
      return;
    }

    this.navController.navigateForward("/portal/search/hotels", {
      animated: true,
      queryParams: { bookingType: bookingType }
    });
  }

  redirectToBookingPage(bookingType: string) {
    this.navController.navigateForward('/portal/search/hotels', { animated: true, queryParams: { bookingType: bookingType } });
  }

  closeNoPackageAvailablePopup() {
    this.isNoPackageAvailablePopupOpen = false;
    setTimeout(() => {
      this.navController.navigateRoot('/dashboard', { animated: true });
    }, 200);
  }

  openNoOnboardingPopup() {
    this.isNoOnboarding = true;
  }

  closeNoOnboardingPopup() {
    this.isNoOnboarding = false;
    setTimeout(() => {
      this.navController.navigateRoot('/dashboard', { animated: true });
    }, 200);
  }

  continueToOnboarding() {
    this.closeNoOnboardingPopup();
    setTimeout(() => {
      this.navController.navigateForward(`/account/register/basic`, {
        state: {
          fromScreen: 'dashboard'
        }, animated: true
      });
    }, 200);
  }

  explorePlan() {
    this.isNoPackageAvailablePopupOpen = false;
    this.navController.navigateForward("account/register/package/selection", { animated: true });
  }

  preBooking(room: any) {
    const payload = {
      searchSessionId: this.hotelDetails.searchSessionId,
      bookingType: this.bookingType,
      arrivalDate: this.hotelDetails.arrivalDate,
      departureDate: this.hotelDetails.departureDate,
      currency: this.hotelDetails.currency,
      guestNationality: this.hotelDetails.guestNationality,
      countryCode: this.hotelDetails.countryCode,
      city: this.hotelDetails.city,
      hotelId: this.hotelDetails.hotelCode || this.hotelDetails.hotel[0].hotelCode,
      name: this.hotelDetails.name || this.hotelDetails.hotel[0].name,
      roomDetails: [
        {
          type: room.type,
          bookingKey: room.bookingKey,
          adults: room.adults,
          children: room.children,
          ChildrenAges: room.childAges,
          totalRooms: room.totalRooms,
          totalRate: room.totalRate,
          additionalNights: room.additionalNights || 0,
          additionalAmount: room.additionalAmount || 0
        }
      ],
      hotelDetailResponse: JSON.stringify(this.hotelDetail)
    };

    //  this.loadingService.show();
    if (this.user) {
      this.isProcessing = true;
      this.dataService.preBooking(payload).subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          const data = response.data;
          this.handlePreBookingResponse(data, room);
        },
        error: (error) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message || 'An error occurred');
        }
      });
    }
    else {
      this.commonService.openLoginModal();
      return;
    }
  }

  handlePreBookingResponse(data: any, room: any) {
    this.localStorageService.setObject("preBookingResponse", data);
    this.localStorageService.setObject("roomDetails", JSON.stringify(room));
    this.navController.navigateForward("/portal/add/guest/details", {
      animated: true,
      queryParams: {
        bookingType: this.bookingType,
        campaignId: this.campaignId
      }
    });
  }

  async goToLoginPage() {
    this.navController.navigateRoot("/dashboard", { animated: true });
  }

  resendOtp() {
    if (!this.data.id) {
      this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
      this.navController.navigateRoot("/account/login", { animated: true });
      return;
    }
    const input = {} as any;
    input.id = this.data.id;
    this.loadingService.show();
    this.resendSubscription = this.dataService.resendMobileVerificationCode(input)
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          this.disableResendOtp();
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      });
  }

  disableResendOtp() {
    this.counter = 60;
    this.subscription = interval(1000)
      .subscribe(x => {
        --this.counter;
        if (this.counter <= 0) {
          this.subscription.unsubscribe();
        }
      });
  }


  ngOnDestroy() {
    // Unsubscribe from any subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    if (this.resendSubscription) {
      this.resendSubscription.unsubscribe();
    }
  }
}
