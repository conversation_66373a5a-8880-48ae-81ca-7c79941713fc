<ion-content class="onboarding-page">
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="continue(recordForm.form)">
    <div class="ion-cutomer-container onboarding-page-container">
      <div class="ion-text-left">
        <div class="header-container">
          <div class="back-button-container">
            <i-feather name="arrow-left" (click)="back()"></i-feather>
          </div>
          <div class="icon-step-container">
            <img src="assets/images/svg/informations.svg" class="icon-right" alt="information" />
            <div class="step-indicator-container">
              <div class="step-indicator">
                <span class="current-step">Step {{ 4 }}</span>
                <span class="total-steps">/ 6</span>
              </div>
            </div>
          </div>
        </div>
        <div class="page-heading">Location</div>
        <div class="onboarding-progress">
          <div class="onboarding-progress-completed" [ngStyle]="{'width': '34%'}"></div>
        </div>
        <p class="page-sub-heading">Give Address Details</p>
        <!-- Country -->
        <div *ngIf="hasAddressDetail">

          <div class="margin-top-20">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': count.invalid && onClickValidation}">
              <ion-input readonly required name="count" #count="ngModel"
                [(ngModel)]="profile.userProfileDetail.addressDetail[0].country" (click)="openCountrySelectionPopup()"
                placeholder="Select Country">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="count" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Please select a country'">
            </app-validation-message>
          </div>

          <div class="margin-top-10">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': citys.invalid && onClickValidation}">
              <ion-input readonly required name="citys" #citys="ngModel"
                [(ngModel)]="profile.userProfileDetail.addressDetail[0].city" (click)="openCitySelectionPopup()"
                placeholder="Select City">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="citys" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Please select a city'">
            </app-validation-message>
          </div>
        </div>

        <div class="register-action-button-container">
          <div class="ion-text-right">
            <ion-button class="register-contiune-button" size="large" type="submit">
              <ion-icon slot="icon-only" name="chevron-forward-outline"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  </form>
</ion-content>

<!-- Country Selection Modal -->
<ion-modal class="location-popup" #locationSelectionPopup [isOpen]="commonService.isLocationSelectionPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>Select Country</ion-title>
        <ion-buttons slot="start">
          <i-feather name="x" (click)="closeCountrySelectionPopup()"></i-feather>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <div class="search-container fixed-search country-city-margin">
        <div class="ion-padding no-padding-bottom padding-top">
          <ion-item class="site-form-control" lines="none">
            <i-feather class="map-pin-icon start-icon" name="Search" slot="start"></i-feather>
            <ion-input placeholder="Search country" [(ngModel)]="searchCountry" (ionInput)="onSearchCountry()"
              [debounce]="300">
            </ion-input>
          </ion-item>
        </div>
      </div>

      <div class="google-places-container margin-top-70">
        <ion-list class="google-places">
          <ion-item class="google-place" *ngFor="let country of filteredCountryTypes" lines="full"
            (click)="onSelectCountry(country)">
            <i-feather class="map-pin-icon" name="map-pin" slot="start"></i-feather>
            <ion-label>
              {{ country.name }}
            </ion-label>
          </ion-item>
        </ion-list>
      </div>
    </ion-content>
  </ng-template>
</ion-modal>

<ion-modal class="location-popup" [isOpen]="commonService.isCitySelectionPopupOpen" [backdropDismiss]="false">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>Select City</ion-title>
        <ion-buttons slot="start">
          <i-feather name="x" (click)="closeCitySelectionPopup()"></i-feather>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <div class="search-container fixed-search country-city-margin">
        <div class="ion-padding no-padding-bottom padding-top">
          <ion-item class="site-form-control" lines="none">
            <i-feather class="map-pin-icon start-icon" name="Search" slot="start"></i-feather>
            <ion-input placeholder="Search city" [(ngModel)]="searchCity" (ionInput)="onSearchCity()" [debounce]="300">
            </ion-input>
          </ion-item>
        </div>
      </div>

      <!-- <div class="ion-padding no-padding-bottom">
        <ion-item class="site-form-control" lines="none">
          <i-feather class="map-pin-icon start-icon" name="map-pin" slot="start"></i-feather>
          <ion-input placeholder="Search city" [(ngModel)]="searchCity" (ionInput)="onSearchCity()" [debounce]="300">
          </ion-input>
        </ion-item>
      </div> -->

      <div class="google-places-container margin-top-70">
        <ion-list class="google-places">
          <ion-item class="google-place" *ngFor="let city of displayedCityTypes" lines="full"
            (click)="onSelectCity(city)">
            <i-feather class="map-pin-icon" name="map-pin" slot="start"></i-feather>
            <ion-label>
              {{ city.name }}
            </ion-label>
          </ion-item>

          <!-- Load More Cities -->
          <!-- <ion-item *ngIf="displayedCityTypes.length < filteredCityTypes.length" (click)="loadMoreCities()">
            <ion-label class="ion-text-center">
              <strong>Load more cities...</strong>
            </ion-label>
          </ion-item> -->

        </ion-list>
      </div>
    </ion-content>
  </ng-template>
</ion-modal>