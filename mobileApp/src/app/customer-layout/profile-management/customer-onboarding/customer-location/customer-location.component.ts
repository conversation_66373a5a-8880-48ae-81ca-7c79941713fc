import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { AddressDetail, ProfileDetail } from 'src/modals/profileDetail';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { Country, City, State } from 'country-state-city';
import { ToastService } from 'src/shared/toast.service';
import { CommonService } from 'src/services/common.service';

@Component({
  selector: 'app-customer-location',
  templateUrl: './customer-location.component.html',
  styleUrls: ['./customer-location.component.scss'],
})
export class CustomerLocationComponent implements OnInit {

  onClickValidation!: boolean;
  subscription: Subscription = new Subscription();
  profile: ProfileDetail = new ProfileDetail();
  countryTypes: any[] = [];
  cityTypes: any[] = [];
  selectedCountryCode: string = '';
  searchCountry: string = '';
  filteredCountryTypes: any[] = [];
  searchCity: string = '';
  filteredCityTypes: any[] = [];
  displayedCityTypes: any[] = [];
  cityDisplayLimit = 100;

  isLoadingMoreCities: boolean = false;
  cityAutoLoadTimer: any;

  constructor(
    private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly changeDetectorRef: ChangeDetectorRef,
    private readonly toastService: ToastService,
    public readonly commonService: CommonService
  ) { }

  ngOnInit() {
    this.onClickValidation = false;
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  ionViewDidEnter() {
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();

    if (!this.profile.userProfileDetail.addressDetail || this.profile.userProfileDetail.addressDetail.length === 0) {
      this.profile.userProfileDetail.addressDetail = [new AddressDetail()];
    }

    const allCountries = Country.getAllCountries();
    const india = allCountries.find(c => c.name.toLowerCase() === 'india');
    const otherCountries = allCountries
      .filter(c => c.name.toLowerCase() !== 'india')
      .sort((a, b) => a.name.localeCompare(b.name));

    this.countryTypes = india ? [india, ...otherCountries] : otherCountries;
    this.filteredCountryTypes = [...this.countryTypes];

    //  Restore cityTypes based on saved country, if any
    const savedCountryName = this.profile.userProfileDetail.addressDetail[0].country;
    const savedCountry = this.countryTypes.find(c => c.name === savedCountryName);

    if (savedCountry) {
      this.selectedCountryCode = savedCountry.isoCode;
      this.cityTypes = City.getCitiesOfCountry(this.selectedCountryCode) || [];
    } else {
      this.cityTypes = [];
    }
  }

  get hasAddressDetail(): boolean {
    return !!this.profile.userProfileDetail?.addressDetail?.length;
  }

  openCountrySelectionPopup() {
    this.commonService.isLocationSelectionPopupOpen = false;
    this.changeDetectorRef.markForCheck();

    setTimeout(() => {
      this.filteredCountryTypes = [...this.countryTypes];
      this.searchCountry = '';
      this.commonService.isLocationSelectionPopupOpen = true;
      this.changeDetectorRef.detectChanges();
    }, 100);
  }

  closeCountrySelectionPopup() {
    this.commonService.isLocationSelectionPopupOpen = false;
  }

  onSearchCountry() {
    const query = this.searchCountry.toLowerCase();
    this.filteredCountryTypes = this.countryTypes.filter(c =>
      c.name.toLowerCase().includes(query)
    );
  }

  onSelectCountry(country: any) {
    this.profile.userProfileDetail.addressDetail[0].country = country.name;
    this.selectedCountryCode = country.isoCode;

    this.cityTypes = City.getCitiesOfCountry(this.selectedCountryCode) || [];

    if (this.cityTypes.length === 0) {
      this.profile.userProfileDetail.addressDetail[0].city = country.name;
      this.filteredCityTypes = [];
      this.displayedCityTypes = [];
    } else {
      this.profile.userProfileDetail.addressDetail[0].city = '';
      this.filteredCityTypes = [...this.cityTypes];
      this.cityDisplayLimit = 100;
      this.displayedCityTypes = this.filteredCityTypes.slice(0, this.cityDisplayLimit);
    }

    this.searchCountry = '';
    this.filteredCountryTypes = [...this.countryTypes];
    this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
    this.closeCountrySelectionPopup();
  }

  openCitySelectionPopup() {
    if (!this.profile.userProfileDetail.addressDetail[0].country) {
      this.toastService.show("Country is required before city.");
      return;
    }

    if (!this.cityTypes || this.cityTypes.length === 0) {
      this.toastService.show("No cities available for the selected country.");
      return;
    }

    this.commonService.isCitySelectionPopupOpen = false;
    this.changeDetectorRef.markForCheck();

    setTimeout(() => {
      this.commonService.isCitySelectionPopupOpen = true;
      this.searchCity = '';
      this.filteredCityTypes = [...this.cityTypes];
      this.cityDisplayLimit = 100;
      this.displayedCityTypes = this.filteredCityTypes.slice(0, this.cityDisplayLimit);
      this.changeDetectorRef.detectChanges();

      this.startAutoLoadCities();
    }, 100);
  }

  startAutoLoadCities() {
    if (this.cityAutoLoadTimer) {
      clearInterval(this.cityAutoLoadTimer);
    }

    this.cityAutoLoadTimer = setInterval(() => {
      if (this.displayedCityTypes.length < this.filteredCityTypes.length) {
        this.isLoadingMoreCities = true;

        // Load next 100
        this.cityDisplayLimit += 100;
        this.displayedCityTypes = this.filteredCityTypes.slice(0, this.cityDisplayLimit);
        this.changeDetectorRef.detectChanges();

      } else {
        this.isLoadingMoreCities = false;
        clearInterval(this.cityAutoLoadTimer);
      }
    }, 100); // Load every 100ms (you can increase to 500ms or 1000ms for smoother experience)
  }

  closeCitySelectionPopup() {
    this.commonService.isCitySelectionPopupOpen = false;
    if (this.cityAutoLoadTimer) {
      clearInterval(this.cityAutoLoadTimer);
    }
  }

  closeCitySelectionPopupp() {
    this.commonService.isCitySelectionPopupOpen = false;
  }

  onSearchCity() {
    const query = this.searchCity.toLowerCase();
    this.filteredCityTypes = this.cityTypes.filter(city =>
      city.name.toLowerCase().includes(query)
    );
    this.cityDisplayLimit = 100;
    this.displayedCityTypes = this.filteredCityTypes.slice(0, this.cityDisplayLimit);
  }

  loadMoreCities() {
    this.cityDisplayLimit += 100;
    this.displayedCityTypes = this.filteredCityTypes.slice(0, this.cityDisplayLimit);
  }

  onSelectCity(city: any) {
    this.profile.userProfileDetail.addressDetail[0].city = city.name;

    // Fetch the state name using stateCode and countryCode
    const state = State.getStateByCodeAndCountry(city.stateCode, this.selectedCountryCode);
    this.profile.userProfileDetail.addressDetail[0].state = state?.name || '';

    this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
    this.closeCitySelectionPopup();
  }

  back() {
    this.navController.navigateBack(`/account/register/picture`, { animated: true });
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
    this.navController.navigateForward("/account/register/co/applicant", { animated: true });
  }

}
