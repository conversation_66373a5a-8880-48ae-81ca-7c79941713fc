<ion-content class="onboarding-page">
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="continue(recordForm.form)">
    <div class="ion-cutomer-container onboarding-page-container">
      <div class="ion-text-left">
        <div class="header-container">
          <div class="back-button-container">
            <i-feather name="arrow-left" (click)="back()"></i-feather>
          </div>
          <div class="icon-step-container">
            <img src="assets/images/svg/informations.svg" class="icon-right" alt="information" />
            <div class="step-indicator-container">
              <div class="step-indicator">
                <span class="current-step">Step {{ 1 }}</span>
                <span class="total-steps">/ 6</span>
              </div>
            </div>
          </div>
        </div>
        <div class="page-heading">Let's get to know you</div>
        <div class="onboarding-progress">
          <div class="onboarding-progress-completed" [ngStyle]="{'width': '10%'}"></div>
        </div>
        <p class="page-sub-heading">What should we call you?</p>
        <div class="margin-top-20">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': salu.invalid && onClickValidation}">
            <ion-select class="no-padding" label="Salutation" labelPlacement="floating" interface="action-sheet"
              required name="salu" #salu="ngModel" [(ngModel)]="profile.salutation">
              <ion-select-option *ngFor="let salutation of salutationTypes" [value]="salutation">
                {{ salutation }}
              </ion-select-option>
            </ion-select>
          </ion-item>
          <app-validation-message [field]="salu" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please select a salutation'">
          </app-validation-message>
        </div>
        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': firstName.invalid && onClickValidation}">
            <ion-input name="firstName" #firstName="ngModel" [(ngModel)]="profile.firstName" required="required"
              maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="First Name" labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="firstName" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div>
        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': lastName.invalid && onClickValidation}">
            <ion-input name="lastName" #lastName="ngModel" [(ngModel)]="profile.lastName" required="required"
              maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Last Name" labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="lastName" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div>
        <div class="register-action-button-container">
          <div class="ion-text-right">
            <ion-button class="register-contiune-button" size="large" type="submit">
              <ion-icon slot="icon-only" name="chevron-forward-outline"></ion-icon>
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  </form>
</ion-content>