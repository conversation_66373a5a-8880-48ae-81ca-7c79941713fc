import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { ProfileDetail } from 'src/modals/profileDetail';
import { LocalStorageService } from 'src/shared/local-storage.service';

@Component({
  selector: 'app-customer-basic',
  templateUrl: './customer-basic.component.html',
  styleUrls: ['./customer-basic.component.scss'],
})
export class CustomerBasicComponent implements OnInit, OnDestroy {

  onClickValidation!: boolean;
  subscription: Subscription = new Subscription();
  profile: ProfileDetail = new ProfileDetail();
  fromScreen: string | null = null;
  salutationTypes: string[] = ['Mr', 'Mrs', 'Ms'];

  constructor(
    private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly router: Router,
    private readonly route: ActivatedRoute
  ) {
  }

  ngOnInit() {
    this.onClickValidation = false;
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  ionViewDidEnter() {
    this.onClickValidation = false;
    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
    this.fromScreen = history.state.fromScreen || null;
  }

  getProgressWidth() {
    return "17%";
  }

  back() {
    if (this.fromScreen === 'dashboard') {
      this.navController.navigateBack("/dashboard", { animated: true });
      return;
    }
    if (this.fromScreen === 'subscription') {
      this.navController.navigateBack("/membership", { animated: true });
      return;
    }
    this.navController.navigateBack("/portal/basic/profile/details", { animated: true });
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !this.profile.isValidBasicRequest(form)) {
      this.onClickValidation = true;
      return;
    }
    // Serialize profile data
    this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
    this.navController.navigateForward("/account/register/email", { animated: true });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
