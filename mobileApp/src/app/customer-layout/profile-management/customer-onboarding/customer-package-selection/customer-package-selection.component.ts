import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { PackageDetail } from 'src/modals/profileDetail';
import { CommaSeparatorPipe } from 'src/services/comma.separator.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-customer-package-selection',
  templateUrl: './customer-package-selection.component.html',
  styleUrls: ['./customer-package-selection.component.scss'],
})
export class CustomerPackageSelectionComponent implements OnInit {

  availablePackages: Array<PackageDetail> = new Array<PackageDetail>();
  selectedPackage: any;
  fromMembershipPage: boolean = false;
  packageDetailScreen: string = 'package-selection';
  fromScreen: string | null = null;
  showBackButton: boolean = false;
  loading: string = "NOT_STARTED";

  constructor(
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private readonly localStorageService: LocalStorageService
  ) { }

  ngOnInit() {
    this.fromMembershipPage = this.localStorageService.getObject("fromMembershipPage");
    this.fetchAvailablePackageWithOffers();
  }

  ionViewDidEnter() {
    setTimeout(() => {
      this.showBackButton = true;
    }, 0);
    this.fromScreen = history.state.fromScreen || null;
    this.fetchAvailablePackageWithOffers();
  }

  back() {
    if (this.localStorageService.getObject("temp-token")) {
      this.localStorageService.setObject('token', this.localStorageService.getObject("temp-token"));
    }
    if (this.localStorageService.getObject("temp-user")) {
      this.localStorageService.setObject('user', this.localStorageService.getObject("temp-user"));
    }
    this.localStorageService.remove('temp-token');
    this.localStorageService.remove('temp-user');
    this.navController.navigateRoot("/dashboard", { animated: true });
  }

  fetchAvailablePackageWithOffers() {
    this.loading = "LOADING";
    this.dataService.fetchAvailablePackageWithOffers()
      .subscribe({
        next: (response: RestResponse) => {
          this.loading = "LOADED";

          let packages = response.data;

          if (this.fromScreen === 'home-screen') {
            const tenureOnePackages = packages.filter(
              (pkg: { packageDetails: { tenure: number } }) => pkg?.packageDetails?.tenure === 1
            );
            this.availablePackages = tenureOnePackages.length > 0 ? tenureOnePackages : packages;
          } else {
            this.availablePackages = packages;
          }
        },
        error: (error) => {
          this.loading = "LOADED";
          this.toastService.show(error.message || 'Unexpected error occurred');
        }
      });
  }

  onPackageSelection(input: PackageDetail) {
    this.availablePackages.forEach((x: PackageDetail) => x.isSelected = false);
    this.selectedPackage = input;
    input.isSelected = true;
  }

  getDescriptionUppercase(description: string): string {
    if (description) {
      return description.toUpperCase();
    }
    return '';
  }

  formatNumber(value: number | null): string {
    if (value == null) {
      return '';
    }
    return new CommaSeparatorPipe().transform(value);
  }

  async continue(): Promise<any> {
    if (!this.selectedPackage) {
      this.toastService.show("Please select a package.");
      return;
    }
    this.navController.navigateForward(`/account/register/package/${this.selectedPackage.packageId}/${this.fromScreen}/pay`, { state: this.selectedPackage, animated: true });
  }
}
