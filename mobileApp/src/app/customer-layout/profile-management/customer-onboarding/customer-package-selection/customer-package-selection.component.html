<ion-content class="onboarding-page">
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="continue()">
    <div class="ion-cutomer-container onboarding-page-container no-padding-bottom">
      <div class="ion-text-left padding-bottom-20">
        <div class="header-container package-selection-header">
          <div class="back-button-container" *ngIf="showBackButton">
            <i-feather name="X" *ngIf="fromMembershipPage || fromScreen === 'home-screen'" (click)="back()"></i-feather>
            <span class="skip-text" *ngIf="!(fromMembershipPage || fromScreen === 'home-screen')"
              (click)="back()">Skip</span>
            <!-- <ion-icon name="close-outline" (click)="back()"></ion-icon> -->
          </div>
        </div>
        <div class="page-heading">Subscribe now!</div>
        <p class="page-sub-heading">Select a plan and <b>Travel with Pleasure</b></p>
        @if (loading==='LOADING') {
        <div class="package-card-container margin-top-20">
          <div class="package-card skeleton" *ngFor="let i of [1,2,3]">
            <div class="card-header">
              <div class="card-header-content">
                <ion-skeleton-text animated style="width: 120px; height: 20px;"></ion-skeleton-text>
              </div>
            </div>
            <div class="card-body">
              <div class="package-details">
                <div class="package-detail-item" *ngFor="let j of [1]">
                  <ion-skeleton-text animated
                    style="width: 100px; height: 14px; margin-bottom: 6px;"></ion-skeleton-text>
                </div>
              </div>
              <hr class="separator-line" />
              <div class="additional-info">
                <ion-skeleton-text animated style="width: 100%; height: 30px;"></ion-skeleton-text>
              </div>
            </div>
          </div>
        </div>
        }
        @else if (loading==='LOADED') {
        <div class="package-card-container margin-top-20">
          <!-- Package Details Card -->
          <div class="package-card" *ngFor="let package of availablePackages"
            [ngStyle]="{'--package-color': package.packageDetails.color}" [ngClass]="{'selected':package.isSelected}"
            (click)="onPackageSelection(package)">
            <div class="card-header">
              <div class="card-header-content">
                <span class="card-title">{{ package.packageName }}</span>
              </div>
            </div>
            <div class="card-body">
              <div class="package-details">
                <div class="package-detail-item">
                  <span class="detail-label">Tenure</span>
                  <span class="detail-value">{{ package.packageDetails.tenure }} Years</span>
                </div>
                <div class="package-detail-item" *ngIf="package.packageDetails.indianNights>0">
                  <span class="detail-label">India Nights</span>
                  <span class="detail-value">{{ package.packageDetails.indianNights }} Nights</span>
                </div>
                <div class="package-detail-item" *ngIf="package.packageDetails.asianNights>0">
                  <span class="detail-label">Asian Nights</span>
                  <span class="detail-value">{{ package.packageDetails.asianNights }} Nights</span>
                </div>
                <div class="package-detail-item" *ngIf="package.packageDetails.internationalNights>0">
                  <span class="detail-label">Worldwide Nights</span>
                  <span class="detail-value">{{ package.packageDetails.internationalNights }} Nights</span>
                </div>
                <!-- <div class="package-detail-item">
                  <span class="detail-label">Package Price</span>
                  <span class="detail-value">{{ package.packagePrice | currency :'INR':'symbol':'1.0-0' }}</span>
                </div> -->
                <!-- <div class="package-detail-item">
                  <span class="detail-label">GST Amount</span>
                  <span class="detail-value">{{ package.gstAmount | currency :'INR':'symbol':'1.0-0' }}</span>
                </div>
                <div class="package-detail-item" *ngIf="package.offerDetails">
                  <span class="detail-label">Offer Amount</span>
                  <span class="detail-value">{{package.offerAmount | currency :'INR':'symbol':'1.0-0' }}</span>
                </div> -->
                <div class="package-detail-item">
                  <span class="detail-label">Payable Amount</span>
                  <span class="detail-value">{{ package.payableAmount | currency :'INR':'symbol':'1.0-0' }}</span>
                </div>
              </div>
              <hr class="separator-line" *ngIf="package.offerDetails" />
              <div class="additional-info" *ngIf="package.offerDetails">
                <img src="assets/images/svg/offer.svg" alt="Offer" class="offer-image" />
                <span class="info-text">
                  You will get an <strong>{{ getDescriptionUppercase(package.offerDetails?.description) || 'OFFER'
                    }}</strong> worth
                  {{package.offerAmount| currency :'INR':'symbol':'1.0-0'}} by just adding {{package.baseAmount |
                  currency :'INR':'symbol':'1.0-0' }} to your
                  package
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="privacy-container ">
          <ion-button class="site-full-rounded-button primary-button text-capitalize" expand="full" shape="round"
            type="submit" [disabled]="!selectedPackage">
            Continue
          </ion-button>
        </div>
        }
      </div>
    </div>
  </form>
</ion-content>