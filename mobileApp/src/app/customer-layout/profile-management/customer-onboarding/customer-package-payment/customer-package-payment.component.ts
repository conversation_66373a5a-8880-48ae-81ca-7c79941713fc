import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NavController } from '@ionic/angular';
import { maskitoParseNumber } from '@maskito/kit';
import { Checkout } from 'capacitor-razorpay';
import { NgxMaskPipe } from 'ngx-mask';
import { environment } from 'src/environments/environment';
import { PackageDetail } from 'src/modals/profileDetail';
import { CommaSeparatorPipe } from 'src/services/comma.separator.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import mask from '../../../../../shared/amount.mask';

@Component({
  selector: 'app-customer-package-payment',
  templateUrl: './customer-package-payment.component.html',
  styleUrls: ['./customer-package-payment.component.scss'],
})
export class CustomerPackagePaymentComponent implements OnInit {

  isEmiPopupOpen: boolean = false;
  selectedPackage!: PackageDetail;
  tab: string = "FULL_PAYMENT";
  emiDetail: any;
  selectedInstallment: any;
  downpaymentAmount: string = "";
  downPaymentWithConvenienceCharges: number = 0;
  onClickValidation: boolean = false;
  protected readonly amountMask = mask;
  emiSummaryLoading: boolean = true;
  convenienceFeeAmount: number = 0;
  convenienceFeeAmountForFullPayment: number = 0;
  convenienceFeeGstAmountForEmi: number = 0;
  convenienceFeeGstAmountForFullPayment: number = 0;
  convenienceAmountForEmi: number = 0;
  totalConvenienceAmountEmi: number = 0;
  totalAmount: number = 0;
  totalConvenienceAmount: number = 0;
  user: any;
  isPaymentProcessing: boolean = false;
  fromScreen: string | null = null;

  constructor(private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private readonly maskPipe: NgxMaskPipe,
    private readonly localStorageService: LocalStorageService,
    private readonly authService: AuthService
  ) {

  }

  ngOnInit() {
    this.init();
    this.handleQueryParams();
  }

  ionViewDidEnter() {
    this.init();
  }

  init() {
    this.user = this.authService.getUser();
    this.fromScreen = this.route.snapshot.paramMap.get('fromScreen');
    this.isPaymentProcessing = false;
  }

  handleQueryParams() {
    this.route.queryParams.subscribe(params => {
      const currentNavigation = this.router.getCurrentNavigation();
      if (currentNavigation?.extras?.state) {
        this.selectedPackage = currentNavigation.extras.state as PackageDetail;
        if ((this.selectedPackage.convenienceFee ?? 0) > 0) {
          this.calculateConvenienceFee();
        }
        this.setDownPayment();
        this.calculateEmiSummary();
      }
    });
  }

  calculateConvenienceFee() {
    this.convenienceFeeAmountForFullPayment = ((this.selectedPackage.payableAmount ?? 0) * ((this.selectedPackage.convenienceFee ?? 0) / 100));
    this.convenienceFeeGstAmountForFullPayment = ((this.convenienceFeeAmountForFullPayment ?? 0) * ((this.selectedPackage.convenienceFeeGst ?? 0) / 100));
    this.totalAmount = this.selectedPackage.payableAmount ?? 0;
    this.selectedPackage.payableAmount = (this.selectedPackage.payableAmount ?? 0) + this.convenienceFeeAmountForFullPayment + this.convenienceFeeGstAmountForFullPayment;
    this.totalConvenienceAmount = this.convenienceFeeAmountForFullPayment + this.convenienceFeeGstAmountForFullPayment;
  }

  setDownPayment() {
    this.selectedPackage.downPayment = this.selectedPackage.minDownPayment;
    if (this.selectedPackage?.convenienceFee > 0) {
      this.convenienceAmountForEmi =
        (this.selectedPackage.downPayment ?? 0) * ((this.selectedPackage.convenienceFee ?? 0) / 100);
      this.convenienceFeeGstAmountForEmi = ((this.convenienceAmountForEmi ?? 0) * ((this.selectedPackage.convenienceFeeGst ?? 0) / 100));
      this.totalConvenienceAmountEmi = this.convenienceAmountForEmi + this.convenienceFeeGstAmountForEmi
    }
  }

  back() {
    if (this.localStorageService.getObject("temp-token")) {
      this.localStorageService.setObject('token', this.localStorageService.getObject("temp-token"));
    }
    if (this.localStorageService.getObject("temp-user")) {
      this.localStorageService.setObject('user', this.localStorageService.getObject("temp-user"));
    }
    this.localStorageService.setObject('FirstTimePackagePurchased', false);
    this.localStorageService.remove('temp-token');
    this.localStorageService.remove('temp-user');
    if (this.fromScreen === 'package-detail') {
      this.navController.navigateBack('/portal/package/detail', { animated: true });
      return;
    }
    if (this.fromScreen === 'home-screen') {
      this.navController.navigateBack('/dashboard', {
        state: {
          fromScreen: this.fromScreen
        },
        animated: true
      });
      return;
    }
    this.navController.navigateBack(`/account/register/package/selection`, { animated: true });
  }

  onTabSelection(tab: string) {
    this.tab = tab;
  }

  formatNumber(value: number | null): string {
    if (value == null) {
      return '';
    }
    return new CommaSeparatorPipe().transform(value);
  }

  openEmiPopup() {
    const amount = this.selectedPackage.downPayment || 0;
    this.downpaymentAmount = this.maskPipe.transform(amount?.toString(), "separator.2", { thousandSeparator: "," });
    this.isEmiPopupOpen = true;
  }

  closeEmiPopup() {
    this.isEmiPopupOpen = false;
  }

  async calculateEmiSummary() {
    const payload = {
      totalAmount: this.selectedPackage.payableAmount,
      downpayment: this.selectedPackage.downPayment
    };
    this.emiSummaryLoading = true;
    this.emiDetail = {} as any;
    this.dataService.calculateEmiSummary(payload)
      .subscribe({
        next: (response: RestResponse) => {
          this.emiSummaryLoading = false;
          this.emiDetail.records = response.data;
          if (this.emiDetail.records.length > 0) {
            this.selectedInstallment = this.emiDetail.records[0];
            if (this.selectedPackage.convenienceFee > 0) {
              this.convenienceAmountForEmi = ((this.selectedPackage.downPayment ?? 0) * ((this.selectedPackage.convenienceFee ?? 0) / 100));
              this.convenienceFeeGstAmountForEmi = ((this.convenienceAmountForEmi ?? 0) * ((this.selectedPackage.convenienceFeeGst ?? 0) / 100));
              this.downPaymentWithConvenienceCharges = (this.selectedPackage.downPayment ?? 0) + this.convenienceAmountForEmi + this.convenienceFeeGstAmountForEmi;
              this.totalConvenienceAmountEmi = this.convenienceAmountForEmi + this.convenienceFeeGstAmountForEmi;
            }
          }
        },
        error: (error) => {
          this.emiSummaryLoading = false;
          this.toastService.show(error.message);
        }
      });
  }

  updateDownPayment(form: any) {
    const amount = maskitoParseNumber(this.downpaymentAmount.toString());
    if (amount < (this.selectedPackage.minDownPayment || 0)) {
      form.controls.packageDownpayment.setErrors({ customMinAmount: true, minAmount: `₹${this.formatNumber(this.selectedPackage.minDownPayment)}` });
      this.onClickValidation = true;
      return;
    }
    if (amount > (this.selectedPackage.payableAmount || 0)) {
      form.controls.packageDownpayment.setErrors({ customMaxAmount: true, maxAmount: `₹${this.formatNumber(this.selectedPackage.payableAmount)}` });
      this.onClickValidation = true;
      return;
    }
    this.selectedPackage.downPayment = amount;
    this.calculateEmiSummary();
    this.closeEmiPopup();
  }

  pay() {
    let amount: number = 0;
    if (this.tab === 'FULL_PAYMENT') {
      amount = this.selectedPackage.payableAmount || 0;
    }
    if (this.tab === 'EMI_PAYMENT') {
      amount = (this.selectedPackage.convenienceFee ?? 0) > 0 ? this.downPaymentWithConvenienceCharges : (this.selectedPackage.downPayment || 0);
    }
    if (amount <= 0) {
      this.toastService.show("Somethings went wrong while processing your request. Please try after sometime");
      return;
    }
    this.processPayment(amount);
  }

  processPayment(amount: number) {
    this.isPaymentProcessing = true;
    const input = {} as any;
    //input.customerId = this.user.id;
    input.packageId = this.selectedPackage.packageId;
    input.offerId = this.selectedPackage.offerId;
    input.fphId = this.selectedPackage.fphId;

    input.amount = this.tab === 'FULL_PAYMENT' ? amount - ((this.convenienceFeeGstAmountForFullPayment || 0) + (this.convenienceFeeAmountForFullPayment || 0)) : amount - ((this.convenienceFeeGstAmountForEmi || 0) + (this.convenienceAmountForEmi || 0));
    input.convenienceFeeGst = this.tab === 'FULL_PAYMENT' ? this.convenienceFeeGstAmountForFullPayment : this.convenienceFeeGstAmountForEmi;

    input.paymentType = this.tab === 'FULL_PAYMENT' ? "FULL_PAYMENT" : "DOWN_PAYMENT";
    this.convenienceFeeAmount = this.tab === 'FULL_PAYMENT' ? this.convenienceFeeAmountForFullPayment : this.convenienceAmountForEmi;
    input.convenienceFee = this.convenienceFeeAmount;
    if (this.tab === 'EMI_PAYMENT') {
      input.emiTenure = this.selectedInstallment.months;
    }

    if (this.localStorageService.getObject("firstTimePackagePurchasing") === false) {
      setTimeout(() => {
        this.navController.navigateForward("/portal/select/payment/method", {
          animated: true,
          queryParams: {
            package: input,
            fromScreen: 'package-payment',
            paymentType: 'PACKAGE',
            packageTab: this.tab,
            selectedInstallment: this.selectedInstallment,
            selectedPackage: this.selectedPackage
          }
        });
      }, 300);
    }
    else {
      this.dataService.createOrder(input)
        .subscribe({
          next: (response: RestResponse) => {
            this.openPaymentPopup(response.data);
          },
          error: (error) => {
            this.isPaymentProcessing = false;
            this.toastService.show(error.message);
          }
        });
    }
  }

  async openPaymentPopup(data: any) {
    const options: any = {
      key: environment.PaymentGateway.Key,
      description: `${this.selectedPackage.packageName} Purchase`,
      image: 'https://forbcorp.iotasolstaging.com/assets/images/svg/logo-icon.svg',
      order_id: data.orderId,
      currency: 'INR',
      name: 'ZFORB Private Limited',
      prefill: {
        email: this.user.email,
        contact: `${this.user.countryCode}${this.user.phoneNumber}`
      },
      theme: {
        color: '#29385B'
      }
    }
    try {
      let paymentResponse = (await Checkout.open(options));
      this.sendPaymentResponse(data, paymentResponse.response);
    } catch (error: any) {
      this.isPaymentProcessing = false;
      if (!error) {
        return;
      }
      let errorObj = JSON.parse(error)
      if (!errorObj || !errorObj.description) {
        return;
      }
      this.toastService.show(errorObj.description);
    }
  }

  sendPaymentResponse(data: any, paymentResponse: any) {
    const input = {} as any;
    input.orderId = paymentResponse.razorpay_order_id;
    input.paymentId = paymentResponse.razorpay_payment_id;
    input.signature = paymentResponse.razorpay_signature;
    this.dataService.purchasePackage(input)
      .subscribe({
        next: (response: RestResponse) => {
          this.isPaymentProcessing = false;
          this.redirectToSuccessPage();
        },
        error: (error) => {
          this.isPaymentProcessing = false;
          this.redirectToFailurePage();
          this.toastService.show(error.message);
        }
      });
  }

  redirectToSuccessPage() {
    const finalData = {} as any;
    finalData.tab = this.tab;
    finalData.selectedPackage = this.selectedPackage;
    finalData.selectedInstallment = this.selectedInstallment;
    if (this.localStorageService.getObject("temp-token")) {
      this.localStorageService.setObject('token', this.localStorageService.getObject("temp-token"));
    }
    if (this.localStorageService.getObject("temp-user")) {
      this.localStorageService.setObject('user', this.localStorageService.getObject("temp-user"));
    }
    this.localStorageService.remove('temp-token');
    this.localStorageService.remove('temp-user');
    if (this.localStorageService.getObject("firstTimePackagePurchasing")) {
      this.localStorageService.setObject('firstTimePackagePurchasing', false);
    }
    setTimeout(() => {
      this.navController.navigateForward(`/account/register/package/success`, { state: finalData, animated: true });
    });
  }

  redirectToFailurePage() {
    const finalData = {} as any;
    finalData.tab = this.tab;
    finalData.selectedPackage = this.selectedPackage;
    finalData.selectedInstallment = this.selectedInstallment;
    if (this.localStorageService.getObject("temp-token")) {
      this.localStorageService.setObject('token', this.localStorageService.getObject("temp-token"));
    }
    if (this.localStorageService.getObject("temp-user")) {
      this.localStorageService.setObject('user', this.localStorageService.getObject("temp-user"));
    }
    this.localStorageService.remove('temp-token');
    this.localStorageService.remove('temp-user');
    setTimeout(() => {
      this.navController.navigateForward(`/account/register/package/failure`, { state: finalData, animated: true });
    });
  }
}
