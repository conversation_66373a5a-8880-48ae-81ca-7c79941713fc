<ion-content class="onboarding-page">
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
    <div class="ion-cutomer-container onboarding-page-container no-padding">
      <div class="ion-text-left payment-header-container">
        <div class="header-container">
          <div class="back-button-container">
            <ion-icon name="close-outline" (click)="back()"></ion-icon>
          </div>
        </div>
        <div class="page-heading">Finalize Your Payment</div>
        <p class="page-sub-heading">Choose Full Payment or EMI to complete your purchase.</p>
      </div>
      <div class="payment-divider"></div>
      <div class="payment-detail-container">
        <div class="payment-option-tab-container">
          <div class="payment-option-tab" [ngClass]="{'selected':tab==='FULL_PAYMENT'}"
            (click)="onTabSelection('FULL_PAYMENT')">
            Full Payment
          </div>
          <div class="payment-option-tab" [ngClass]="{'selected':tab==='EMI_PAYMENT'}"
            (click)="onTabSelection('EMI_PAYMENT')">
            EMI
          </div>
        </div>
        <div class="payment-body-container">
          <div class="package-card" [ngStyle]="{'--package-color': selectedPackage.packageDetails.color}">
            <div class="card-header">
              <div class="card-header-content">
                <span class="card-title">{{ selectedPackage.packageName }}</span>
              </div>
            </div>
          </div>
          <div class="payment-body-section package-tab-section" *ngIf="tab==='FULL_PAYMENT'">
            <div class="price-detail-section">
              <div class="price-detail-rows">
                <div class="price-detail-row">
                  <span class="price-detail-label">Package Price</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.packagePrice"></ion-skeleton-text>
                    <span *ngIf="selectedPackage.packagePrice">{{selectedPackage.packagePrice | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row" *ngIf="selectedPackage.offerDetails">
                  <span class="price-detail-label">Offer Amount</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.baseAmount"></ion-skeleton-text>
                    <span *ngIf="(selectedPackage.offerDetails?.forbcorpPrice || 0) > (selectedPackage.baseAmount || 0)"
                      class="line-through margin-right-5">
                      {{selectedPackage.offerDetails?.forbcorpPrice || 0 | currency :'INR':'symbol':'1.0-0'}}
                    </span>
                    <span *ngIf="selectedPackage.baseAmount">{{selectedPackage.baseAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row" *ngIf="selectedPackage?.fullyPaidHolidayDetail">
                  <span class="price-detail-label">FPH Amount</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.baseAmount"></ion-skeleton-text>
                    <span class="line-through margin-right-5"
                      *ngIf="(selectedPackage.fullyPaidHolidayDetail?.forbcorpPrice || 0) > (selectedPackage.baseAmount || 0)">
                      {{ selectedPackage.fullyPaidHolidayDetail.forbcorpPrice || 0 | currency :'INR':'symbol':'1.0-0'
                      }}
                    </span>
                    <span *ngIf="selectedPackage.baseAmount">{{selectedPackage.baseAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row" *ngIf="selectedPackage.offerDetails">
                  <span class="price-detail-label">Sub Total</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.subTotal"></ion-skeleton-text>
                    <span *ngIf="selectedPackage.subTotal">{{selectedPackage.subTotal | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row" *ngIf="selectedPackage.packageDetails .gstRate > 0">
                  <span class="price-detail-label">Package GST ({{selectedPackage.packageDetails.gstRate}}%)</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.packageGstAmount"></ion-skeleton-text>
                    <span *ngIf="selectedPackage.packageGstAmount">{{selectedPackage.packageGstAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row"
                  *ngIf="selectedPackage?.fullyPaidHolidayDetail && selectedPackage?.fphGstAmount != 0">
                  <span class="price-detail-label">FPH GST({{selectedPackage.fullyPaidHolidayDetail.gstRate}}%)</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.fphGstAmount"></ion-skeleton-text>
                    <span *ngIf="selectedPackage.fphGstAmount">{{selectedPackage.fphGstAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row" *ngIf="selectedPackage.offerDetails?.gstRate > 0">
                  <span class="price-detail-label">Offer GST ({{selectedPackage.offerDetails.gstRate}}%)</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.offerGstAmount"></ion-skeleton-text>
                    <span *ngIf="selectedPackage.offerGstAmount">{{selectedPackage.offerGstAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>


                <div class="price-detail-row" *ngIf="(selectedPackage?.convenienceFee ?? 0) > 0">
                  <span class="price-detail-label">Total Amount</span>
                  <span class="price-detail-value">{{totalAmount| currency
                    :'INR':'symbol':'1.0-0'}}</span>
                </div>
                <!-- <div class="price-detail-row" *ngIf="selectedPackage?.convenienceFee ?? 0 > 0">
                  <span class="price-detail-label">Convenience fee ({{selectedPackage.convenienceFee}}%)</span>
                  <span class="price-detail-value">
                  
                    {{convenienceFeeAmountForFullPayment | currency
                      :'INR'}}
                  </span>
                </div>
                <div class="price-detail-row" *ngIf="selectedPackage?.convenienceFee ?? 0 > 0">
                  <span class="price-detail-label">GST On Convenience fee ({{selectedPackage.convenienceFeeGst}}%)</span>
                  <span class="price-detail-value">
                    {{convenienceFeeGstAmountForFullPayment | currency
                      :'INR'}}
                  </span>
                </div> -->

                <div class="price-detail-row" *ngIf="(selectedPackage?.convenienceFee ?? 0) > 0">
                  <span class="price-detail-label">Convenience fee (incl. GST)</span>
                  <span class="price-detail-value">

                    {{totalConvenienceAmount | currency
                    :'INR' :'symbol':'1.0-2'}}
                  </span>
                </div>


                <div class="price-detail-row">
                  <span class="price-detail-label">Payable Amount</span>
                  <span class="price-detail-value">
                    <!-- <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      ></ion-skeleton-text> -->
                    {{selectedPackage.payableAmount | currency
                    :'INR':'symbol':'1.0-2'}}
                    <!-- {{selectedPackage.offerGstAmount | currency
                      :'INR':'symbol':'1.0-0'}} -->
                  </span>
                </div>

              </div>
              <div class="asf-amount-detail" *ngIf="selectedPackage.packageDetails.isAsfAllowed">
                <span>Note:</span> An additional annual service charge will be applied each year.
              </div>
            </div>
          </div>
          <div class="payment-body-section package-tab-section" *ngIf="tab==='EMI_PAYMENT'">
            <div class="price-detail-section">
              <div class="emi-items" *ngIf="!emiSummaryLoading">
                <div class="emi-item" *ngFor="let emi of emiDetail.records" (click)="selectedInstallment=emi">
                  <div class="emi-selection-checkbox">
                    <ion-checkbox [checked]="emi.months===selectedInstallment?.months"></ion-checkbox>
                  </div>
                  <div class="emi-selection-label">
                    <span class="emi-text">{{emi.emiAmount | currency :'INR':'symbol':'1.0-0'}} for {{emi.months}}
                      months</span> -
                    <span class="emi-downpayment-text">{{selectedPackage.downPayment | currency
                      :'INR':'symbol':'1.0-0'}} down
                      payment</span>
                  </div>
                </div>
              </div>
              <div class="emi-items" *ngIf="emiSummaryLoading">
                <div class="emi-item">
                  <div class="emi-selection-checkbox">
                    <ion-skeleton-text [animated]="true"
                      style="width: 22px;height: 22px;border-radius: 50%;"></ion-skeleton-text>
                  </div>
                  <div class="emi-selection-label">
                    <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
                  </div>
                </div>
                <div class="emi-item">
                  <div class="emi-selection-checkbox">
                    <ion-skeleton-text [animated]="true"
                      style="width: 22px;height: 22px;border-radius: 50%;"></ion-skeleton-text>
                  </div>
                  <div class="emi-selection-label">
                    <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
                  </div>
                </div>
                <div class="emi-item">
                  <div class="emi-selection-checkbox">
                    <ion-skeleton-text [animated]="true"
                      style="width: 22px;height: 22px;border-radius: 50%;"></ion-skeleton-text>
                  </div>
                  <div class="emi-selection-label">
                    <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
                  </div>
                </div>
                <div class="emi-item">
                  <div class="emi-selection-checkbox">
                    <ion-skeleton-text [animated]="true"
                      style="width: 22px;height: 22px;border-radius: 50%;"></ion-skeleton-text>
                  </div>
                  <div class="emi-selection-label">
                    <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
                  </div>
                </div>
              </div>
              <div class="price-detail-rows">
                <div class="price-detail-row">
                  <span class="price-detail-label">Package Price</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.packagePrice"></ion-skeleton-text>
                    <span *ngIf="selectedPackage.packagePrice">{{selectedPackage.packagePrice | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row" *ngIf="selectedPackage.offerDetails">
                  <span class="price-detail-label">Offer Amount</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.baseAmount"></ion-skeleton-text>
                    <span *ngIf="(selectedPackage?.offerDetails.forbcorpPrice || 0) > (selectedPackage.baseAmount||0)"
                      class="line-through margin-right-5">
                      {{selectedPackage.offerDetails.forbcorpPrice || 0 | currency :'INR':'symbol':'1.0-0'}}
                    </span>
                    <span *ngIf="selectedPackage.baseAmount">{{selectedPackage.baseAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row" *ngIf="selectedPackage?.fullyPaidHolidayDetail">
                  <span class="price-detail-label">FPH Amount</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.baseAmount"></ion-skeleton-text>
                    <span class="line-through margin-right-5"
                      *ngIf="(selectedPackage?.fullyPaidHolidayDetail?.forbcorpPrice || 0) > (selectedPackage.baseAmount||0)">
                      {{selectedPackage.fullyPaidHolidayDetail.forbcorpPrice || 0 | currency :'INR':'symbol':'1.0-0'}}
                    </span>
                    <span *ngIf="selectedPackage.baseAmount">{{selectedPackage.baseAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row" *ngIf="selectedPackage.offerDetails">
                  <span class="price-detail-label">Sub Total</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.subTotal"></ion-skeleton-text>
                    <span *ngIf="selectedPackage.subTotal">{{selectedPackage.subTotal | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>

                <div class="price-detail-row" *ngIf="selectedPackage.packageDetails.gstRate > 0">
                  <span class="price-detail-label">Package GST ({{selectedPackage.packageDetails.gstRate}}%)</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.packageGstAmount"></ion-skeleton-text>
                    <span *ngIf="selectedPackage.packageGstAmount">{{selectedPackage.packageGstAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row"
                  *ngIf="selectedPackage?.fullyPaidHolidayDetail && selectedPackage?.fphGstAmount != 0">
                  <span class="price-detail-label">FPH GST({{selectedPackage.fullyPaidHolidayDetail.gstRate}}%)</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.fphGstAmount"></ion-skeleton-text>
                    <span *ngIf="selectedPackage.fphGstAmount">{{selectedPackage.fphGstAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row" *ngIf="selectedPackage.offerDetails?.gstRate > 0">
                  <span class="price-detail-label">Offer GST ({{selectedPackage.offerDetails.gstRate}}%)</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage.offerGstAmount"></ion-skeleton-text>
                    <span *ngIf="selectedPackage.offerGstAmount">{{selectedPackage.offerGstAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row">
                  <span class="price-detail-label">Interest ({{selectedInstallment?.interestRate}}%)</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedInstallment"></ion-skeleton-text>
                    <span *ngIf="selectedInstallment?.totalInterest">{{selectedInstallment?.totalInterest | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
                <div class="price-detail-row">
                  <span class="price-detail-label">Down Payment</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage"></ion-skeleton-text>
                    <span *ngIf="selectedPackage">- {{selectedPackage.downPayment | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                    <i-feather name="edit" class="edit-downpayment-icon" (click)="openEmiPopup()"
                      *ngIf="selectedPackage"></i-feather>
                  </span>
                </div>
                <div class="price-detail-row" *ngIf="(selectedPackage?.convenienceFee ?? 0) > 0">
                  <span class="price-detail-label">Convenience Fee (incl. GST)</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedInstallment"></ion-skeleton-text>
                    <span *ngIf="selectedInstallment?.totalInterest">{{totalConvenienceAmountEmi | currency
                      :'INR' :'symbol':'1.0-2'}}</span>
                  </span>
                </div>

                <!-- <div class="price-detail-row" *ngIf="selectedPackage?.convenienceFee ?? 0 > 0">
                  <span class="price-detail-label">GST On Convenience Fee ({{selectedPackage?.convenienceFeeGst}}%)</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedInstallment"></ion-skeleton-text>
                    <span *ngIf="selectedInstallment?.totalInterest">{{convenienceFeeGstAmountForEmi | currency
                      :'INR'}}</span>
                  </span>
                </div> -->

                <div class="price-detail-row" *ngIf="downPaymentWithConvenienceCharges">
                  <span class="price-detail-label">Payable Amount</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedPackage"></ion-skeleton-text>
                    <span *ngIf="selectedPackage">- {{downPaymentWithConvenienceCharges | currency
                      :'INR':'symbol':'1.0-2'}}</span>

                  </span>
                </div>
                <div class="price-detail-row">
                  <span class="price-detail-label">Total EMI for {{selectedInstallment?.months}} months</span>
                  <span class="price-detail-value">
                    <ion-skeleton-text [animated]="true" style="width: 30%;float: right;"
                      *ngIf="!selectedInstallment"></ion-skeleton-text>
                    <span *ngIf="selectedInstallment?.totalAmount">{{selectedInstallment?.totalAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </span>
                </div>
              </div>
              <div class="asf-amount-detail" *ngIf="selectedPackage.packageDetails.isAsfAllowed">
                <span>Note:</span> An additional annual service charge amount will be charged per year.
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="privacy-container pay-button-margin">
        <ion-button class="site-full-rounded-button primary-button text-capitalize no-margin-top" expand="full"
          shape="round" type="submit" (click)="pay()" [disabled]="!selectedPackage || isPaymentProcessing">
          <span class="padding-right-5">Pay</span> <span *ngIf="tab==='FULL_PAYMENT'">{{selectedPackage.payableAmount|
            currency :'INR':'symbol':'1.0-2'}}</span> <span *ngIf="tab==='EMI_PAYMENT'">
            {{ (selectedPackage.convenienceFee) > 0? (downPaymentWithConvenienceCharges |
            currency:'INR':'symbol':'1.0-2') : (selectedPackage.downPayment | currency:'INR':'symbol':'1.0-2') }}
          </span>
        </ion-button>
      </div>
    </div>
  </form>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #emiPopup [isOpen]="isEmiPopupOpen" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeEmiPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top" *ngIf="selectedPackage">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate"
          (ngSubmit)="updateDownPayment(recordForm.form)">
          <div class="popup-large-heading">Adjust Your Down Payment</div>
          <div class="popup-normal-heading margin-top-5 secondary-text">Adjust the down payment amount (Minimum
            required:
            {{ selectedPackage.minDownPayment| currency :'INR':'symbol':'1.0-0' }}). </div>
          <div class="job-info margin-top-10">
            <div class="margin-bottom-15">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid':packageDownpayment.invalid && onClickValidation}">
                <ion-icon src="/assets/images/svg/envelope-plus.svg" slot="start" class="start-icon"></ion-icon>
                <ion-input label="Down Payment" labelPlacement="floating" required="required" name="packageDownpayment"
                  #packageDownpayment="ngModel" [(ngModel)]="downpaymentAmount" mask="separator.2"
                  thousandSeparator=","></ion-input>
              </ion-item>
              <app-validation-message [field]="packageDownpayment" [onClickValidation]="onClickValidation">
              </app-validation-message>
            </div>
          </div>
          <ion-button class="site-full-rounded-button primary-button text-capitalize margin-top-20" expand="full"
            shape="round" type="submit">
            Update Down Payment
          </ion-button>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>