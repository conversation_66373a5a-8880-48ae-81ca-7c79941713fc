import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { NavigationDataService } from 'src/services/navigation.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss'],
})
export class ChangePasswordComponent implements OnInit {

  onClickValidation: boolean = false;
  passwordFieldType: string = 'password';
  data: any = {};
  confirmPassword!: string | null;
  isProcessing: boolean = false;

  constructor(
    private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly navigationService: NavigationDataService,
    private readonly cdr: ChangeDetectorRef
  ) { }

  ngOnInit() {
    this.resetForm();
  }

  resetForm() {
    this.data = {} as any;
    this.confirmPassword = null;
    this.onClickValidation = false;

    // Trigger change detection
    this.cdr.detectChanges();
  }

  validatePasswords(): boolean {
    if (this.data.newPassword !== this.confirmPassword) {
      this.toastService.show('New Password and Confirm Password do not match');
      return false;
    }
    return true;
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  back() {
    this.resetForm();
    this.navController.navigateBack('/account1', { animated: true });
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

  async changePassword(form: any): Promise<void> {
    this.onClickValidation = true;
    if (!form.valid || !this.validatePasswords()) {
      return;
    }
    // Show loading indicator
    //  this.loadingService.show();
    this.isProcessing = true;

    this.dataService.changePassword(this.data).subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        this.isProcessing = false;

        this.navigateToScreens(response);
      },
      error: (error) => {
        //  this.loadingService.hide();
        this.isProcessing = false;
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  navigateToScreens(response: any) {
    this.toastService.show(response.message);
    this.resetForm();
    this.navController.navigateForward('/account1');
  }
}
