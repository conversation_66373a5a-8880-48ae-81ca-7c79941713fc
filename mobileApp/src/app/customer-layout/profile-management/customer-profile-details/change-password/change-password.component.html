<ion-content class="customer-dashboard-page customer-payment-page booking-page">
  <app-customer-header [innerPage]="true" [headingText]="'Update Your Password'"
    [rightAction]="true"></app-customer-header>
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="changePassword(recordForm.form)">
    <div class="booking-page-container">
      <div class="form-container text-left">
        <!-- <div class="header-container">
          <div class="back-button-container">
            <i-feather name="arrow-left" (click)="back()"></i-feather>
          </div>
        </div>
        <div class="page-heading">Update Your Password</div> -->

        <div class="image-container">
          <img src="assets/images/svg/updatePassword.svg" class="custom-image" />
        </div>

        <div class="margin-top-20">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':!password.valid && onClickValidation}">
            <ion-input label="Current Password*" labelPlacement="floating" mode="md" [type]="passwordFieldType"
              name="password" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
              [ngClass]="{'is-invalid':password.invalid && onClickValidation}" #password="ngModel"
              [(ngModel)]="data.oldPassword" required="required"></ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="data.oldPassword && data.oldPassword.length>0 && passwordFieldType==='password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="data.oldPassword && data.oldPassword.length>0 && passwordFieldType!=='password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="password" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & be greater than 8 characters'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':!newPasswords.valid && onClickValidation}">
            <ion-input label="New Password*" labelPlacement="floating" mode="md" [type]="passwordFieldType"
              name="newPasswords" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
              [ngClass]="{'is-invalid':newPasswords.invalid && onClickValidation}" #newPasswords="ngModel"
              [(ngModel)]="data.newPassword" required="required"></ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="data.newPassword && data.newPassword.length>0 && passwordFieldType==='password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="data.newPassword && data.newPassword.length>0 && passwordFieldType!=='password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="newPasswords" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & be greater than 8 characters'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':!confirmNewPassword.valid && onClickValidation}">
            <ion-input label="Confirm New Password*" labelPlacement="floating" mode="md" [type]="passwordFieldType"
              name="confirmNewPassword" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
              [ngClass]="{'is-invalid':confirmNewPassword.invalid && onClickValidation}" #confirmNewPassword="ngModel"
              [(ngModel)]="confirmPassword" required="required"></ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="confirmPassword && confirmPassword.length>0 && passwordFieldType==='password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="confirmPassword && confirmPassword.length>0 && passwordFieldType!=='password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="confirmNewPassword" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & be greater than 8 characters'">
          </app-validation-message>
        </div>

        <div class="privacy-container">
          <ion-button class="site-full-rounded-button primary-button text-capitalize" expand="full" shape="round"
            type="submit" [disabled]="isProcessing">
            Submit
          </ion-button>
        </div>

      </div>
    </div>
  </form>
</ion-content>