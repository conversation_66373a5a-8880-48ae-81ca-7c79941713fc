<ion-content class="customer-dashboard-page my-basic-detail-page">
  <app-customer-header [innerPage]="true" [headingText]="'Basic Information'" [rightAction]="true"
    [backUrl]="'/account1'"></app-customer-header>

  <div class="customer-body-section has-header-section-change-profile no-padding">
    <div class="profile-details-container">

      <div class="profile-picture-wrapper">
        <div class="profile-picture-container margin-bottom-30">
          <img src="/assets/images/icons/user.png" *ngIf="!profile.profileImageUrl" alt="Profile Picture" />
          <img [src]="profile.profileImageUrl" class="profile-pic" *ngIf="profile.profileImageUrl"
            alt="Profile Picture" />
        </div>
      </div>

      <div class="basic-detail-heading">Personal Details</div>
      <div class="profile-card">
        <div class="profile-fields">
          <div class="form-field" *ngIf="profile.firstName">
            <label>First Name</label>
            <div class="field-value">{{ commonService.formatText(profile.firstName) }}</div>
          </div>
          <div class="form-field" *ngIf="profile.lastName">
            <label>Last Name</label>
            <div class="field-value">{{ commonService.formatText(profile.lastName) }}</div>
          </div>
          <div class="form-field" *ngIf="profile.email">
            <label>Email Address</label>
            <div class="field-value">{{ profile.email }}</div>
          </div>
          <div class="form-field" *ngIf="profile.phoneNumber">
            <label>Phone Number</label>
            <div class="field-value">{{ profile.countryCode }} {{ commonService.formatPhoneNumber(profile.phoneNumber)
              }}</div>
          </div>
          <div class="form-field">
            <label>D.O.B</label>
            <div class="field-value" *ngIf="profile.userProfileDetail.dob">{{
              formatDateOfBirth(profile.userProfileDetail.dob) }}</div>
            <div class="field-value small-field-value" *ngIf="!profile.userProfileDetail.dob">No D.O.B Available.
            </div>
          </div>
          <div class="form-field">
            <label>PAN Card</label>
            <div class="field-value" *ngIf="profile.userProfileDetail.panCard">{{
              commonService.formatPanCard(profile.userProfileDetail.panCard) }}</div>
            <div class="field-value small-field-value" *ngIf="!profile.userProfileDetail.panCard">
              No PAN Card Available.
            </div>
          </div>
        </div>
      </div>
      <div class="basic-detail-heading margin-top-20"
        *ngIf="profile.userProfileDetail?.coApplicantDetail?.firstName || profile.userProfileDetail?.coApplicantDetail?.lastName || profile.userProfileDetail?.coApplicantDetail?.relation">
        Co-Applicant
      </div>
      <div class="profile-card"
        *ngIf="profile.userProfileDetail.coApplicantDetail?.firstName || profile.userProfileDetail.coApplicantDetail?.lastName || profile.userProfileDetail.coApplicantDetail?.relation">
        <div class="profile-fields co-applicant-container">
          <div class="form-field"
            *ngIf="profile.userProfileDetail.coApplicantDetail?.firstName && profile.userProfileDetail.coApplicantDetail?.lastName">
            <div class="field-value bold-value">{{profile.userProfileDetail.coApplicantDetail.firstName}}
              {{profile.userProfileDetail.coApplicantDetail.lastName}}</div>
          </div>
          <div class="form-field" *ngIf="profile.userProfileDetail.coApplicantDetail?.relation">
            <div class="field-value small-field-value">({{
              commonService.formatText(profile.userProfileDetail.coApplicantDetail.relation) }})</div>
          </div>
        </div>
      </div>
      <div class="basic-detail-heading margin-top-20"
        *ngIf="profile.userProfileDetail?.passportNumber || profile.userProfileDetail?.passportExpiryDate">Passport
        Details</div>
      <div class="profile-card"
        *ngIf="profile.userProfileDetail?.passportNumber || profile.userProfileDetail?.passportExpiryDate">
        <div class="profile-fields">
          <div class="form-field" *ngIf="profile.userProfileDetail?.passportNumber">
            <label>Passport Number</label>
            <div class="field-value">{{
              profile.userProfileDetail.passportNumber }}</div>
          </div>
          <div class="form-field" *ngIf="profile.userProfileDetail?.passportExpiryDate">
            <label>Passport Expiry Date</label>
            <div class="field-value">{{
              formatDateOfBirth(profile.userProfileDetail.passportExpiryDate) }}</div>
          </div>
        </div>
      </div>
      <div class="basic-detail-heading margin-top-20">Address Details
      </div>
      <div class="profile-card">
        <div class="profile-fields">
          <div class="form-field">
            <div class="field-value" *ngIf="profile.userProfileDetail?.addressDetail">
              {{ formattedAddress }}
            </div>
            <div class="field-value small-field-value margin-top-5" *ngIf="!profile.userProfileDetail?.addressDetail">
              No Address Available.
            </div>
          </div>
        </div>
      </div>

      <div class="basic-detail-heading margin-top-20">
        Family Details
      </div>
      <div *ngIf="profile?.userProfileDetail?.familyDetail?.length">
        <div *ngFor="let familyMember of profile.userProfileDetail.familyDetail; let i = index" class="margin-top-15">

          <div class="profile-card">
            <div class="profile-fields">
              <div class="form-field family-form-field" *ngIf="familyMember?.firstName">
                <div class="field-value bold-value">{{ commonService.formatText(familyMember.firstName) }} {{
                  commonService.formatText(familyMember.lastName) }}</div>
              </div>
              <div class="form-field family-form-field" *ngIf="familyMember?.email">
                <div class="field-value small-field-value">{{ familyMember.email }}</div>
              </div>
              <div class="form-field family-form-field" *ngIf="familyMember?.phoneNumber">
                <div class="field-value small-field-value">
                  {{ profile.countryCode }} {{ commonService.formatPhoneNumber(familyMember.phoneNumber) }}
                </div>
              </div>
              <div class="form-field family-form-field gender-relation-dob-container"
                *ngIf="familyMember?.relation || familyMember?.gender || familyMember?.dob">
                <div class="gender-relation-container">
                  <div class="field-value small-field-value" *ngIf="familyMember?.relation">{{
                    commonService.formatText(familyMember.relation) }}
                  </div>
                  <div class="field-value small-field-value" *ngIf="familyMember?.gender">({{
                    commonService.formatText(familyMember.gender) }})</div>
                </div>
                <div class="form-field family-form-field" *ngIf="familyMember?.dob">
                  <div class="field-value small-field-value">{{ formatDateOfBirth(familyMember.dob) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="profile-card" *ngIf="!profile.userProfileDetail?.familyDetail">
        <div class="profile-fields">
          <div class="form-field">
            <div class="field-value small-field-value margin-top-5">
              No Family Available.
            </div>
          </div>
        </div>
      </div>

      <div class="basic-detail-heading other-detail-heading margin-top-20">
        Identity Documents
      </div>
      <div class="document-card" *ngIf="kycAadharFront?.fileDetail?.path || kycAadharBack?.fileDetail?.path">
        <h3 class="card-title">Aadhaar Card</h3>
        <div class="image-container">
          <img *ngIf="kycAadharFront?.fileDetail?.path" [src]="kycAadharFront.fileDetail.path" alt="Aadhaar Front"
            class="card-image" (click)="commonService.viewImage(kycAadharFront.fileDetail.path)" />
          <img *ngIf="kycAadharBack?.fileDetail?.path" [src]="kycAadharBack.fileDetail.path" alt="Aadhaar Back"
            class="card-image" (click)="commonService.viewImage(kycAadharBack.fileDetail.path)" />
        </div>
      </div>
      <div class="document-card no-document"
        *ngIf="!kycAadharFront?.fileDetail?.path && !kycAadharBack?.fileDetail?.path">
        <h3 class="card-title no-document-title">No Aadhaar Card Available.</h3>
      </div>

      <div class="document-card margin-top-10" *ngIf="panCard?.fileDetail?.path">
        <h3 class="card-title">PAN Card</h3>
        <div class="image-container">
          <img [src]="panCard.fileDetail.path" alt="Aadhaar Front" class="card-image"
            (click)="commonService.viewImage(panCard.fileDetail.path)" />
        </div>
      </div>
      <div class="document-card no-document margin-top-10" *ngIf="!panCard?.fileDetail?.path">
        <h3 class="card-title no-document-title">No PAN Card Available.</h3>
      </div>

      <div class="document-card margin-top-10" *ngIf="otherDocuments?.length > 0">
        <h3 class="card-title">Other Documents</h3>
        <div class="other-document-grid">
          <div *ngFor="let doc of otherDocuments" (click)="commonService.viewImage(doc.fileDetail.path)"
            class="other-document-image-container">
            <img [src]="doc.fileDetail.path" alt="Others" class="card-image" />
          </div>
        </div>
      </div>
      <div class="document-card no-document margin-top-10" *ngIf="otherDocuments?.length <= 0">
        <h3 class="card-title no-document-title">No Other Document Available.</h3>
      </div>

      <div class="basic-detail-heading margin-top-10">Emergency Details</div>
      <div class="profile-card" *ngIf="profile.userProfileDetail?.emergencyContactName ||
        profile.userProfileDetail?.emergencyEmail || 
        profile.userProfileDetail?.emergencyContactNumber">
        <div class="profile-fields">
          <div class="form-field family-form-field" *ngIf="profile.userProfileDetail?.emergencyContactName">
            <div class="field-value bold-value">{{
              commonService.formatText(profile.userProfileDetail.emergencyContactName) }}
            </div>
          </div>
          <div class="form-field family-form-field" *ngIf="profile.userProfileDetail?.emergencyEmail">
            <div class="field-value small-field-value">{{ profile.userProfileDetail.emergencyEmail }}</div>
          </div>
          <div class="form-field family-form-field" *ngIf="profile.userProfileDetail?.emergencyContactNumber">
            <div class="field-value small-field-value">
              {{ profile.countryCode }} {{
              commonService.formatPhoneNumber(profile.userProfileDetail.emergencyContactNumber) }}
            </div>
          </div>
        </div>
      </div>
      <div class="profile-card no-emergency-card" *ngIf="!profile.userProfileDetail?.emergencyContactName && 
        !profile.userProfileDetail?.emergencyEmail && 
        !profile.userProfileDetail?.emergencyContactNumber">
        <div class="profile-fields">
          <div class="form-field family-form-field">
            <div class="field-value small-field-value">No Emergency Details Available.</div>
          </div>
        </div>
      </div>

      <div class="add-new-button-container margin-top-15" (click)="openChangeCreateRequest()"
        *ngIf="profile.isOnBoardingComplete">
        <ion-button class="add-new-button change-profile text-capitalize">
          Profile Change Request
        </ion-button>
      </div>
      <div class="add-new-button-container margin-top-15" (click)="completeOnboarding()"
        *ngIf="!profile.isOnBoardingComplete">
        <ion-button class="add-new-button change-profile text-capitalize">
          Update Profile
        </ion-button>
      </div>

    </div>
  </div>
</ion-content>

<!-- create change request modal -->
<ion-modal class="site-custom-popup job-invitation-popup" #isChangeRequestPopup [isOpen]="isChangeProfileRequest"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeChangeRequest()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">

        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">Create Request</div>
          <div class="job-info margin-top-10">

            <div class="">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': !sub.valid && onClickValidation}">
                <ion-input label="Title" labelPlacement="floating" name="sub" #sub="ngModel" [(ngModel)]="title"
                  required="required" maxlength="50">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="sub" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
            </div>

            <div class="margin-top-10">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid':!desc.valid && onClickValidation}">
                <ion-textarea label="Description" labelPlacement="floating" name="desc" #desc="ngModel"
                  [(ngModel)]="description" required="required">
                </ion-textarea>
              </ion-item>
              <app-validation-message [field]="desc" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
            </div>

            <!-- Customer Request Image Section -->
            <div class="document-items">
              <!-- Upload Button -->
              <div class="document-upload-container" (click)="upload()"
                [ngClass]="{'missing-image': showValidationErrors && uploadedImages.length === 0}">
                <img src="/assets/images/svg/attach.svg" alt="Upload Icon" class="upload-icon">
                <div class="upload-text"
                  [ngClass]="{'missing-image': showValidationErrors && uploadedImages.length === 0}">
                  <span class="upload-adhar">Attachments</span>
                </div>
              </div>

              <!-- Uploaded Images -->
              <div class="document-upload-container has-image" *ngFor="let image of uploadedImages"
                (click)="commonService.viewImage(image.url)">
                <img [src]="image.url" alt="Uploaded Document" class="uploaded-image">
                <div class="delete-icon" *ngIf="image" (click)="removeImage(image)">
                  <img src="/assets/images/svg/delete.svg" alt="Delete Icon">
                </div>
              </div>
            </div>

            <ion-button class="site-full-rounded-button primary-button text-capitalize margin-top-20" expand="full"
              shape="round" type="submit" [disabled]="isProcessing" (click)="createChangeRequest(recordForm.form)">
              Create Request
            </ion-button>

          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #isSuccessPopup [isOpen]="isSuccessfulResponse"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-body no-padding-top">
        <div class="campaign-card">
          <div class="campaign-card-header success-modal-card-header">
            <div class="campaign-image-slide success-modal-card-image-slide">
              <i-feather name="X" class="close-icon white-icon" (click)="closeDetails()"></i-feather>
            </div>
          </div>
          <div class="coin-container request-coin-container">
            <ion-icon class="balance-icon request-bal-coin"
              [src]="'/assets/images/svg/create-request-icon.svg'"></ion-icon>
          </div>
          <div class="campaign-detail-container">
            <span class="campaign-detail-title success-text margin-top-10">Request Created</span>
            <div class="success-container margin-top-10" *ngIf="successPaymentDetails">
              <span>Profile update request<strong> {{successPaymentDetails?.requestId}} </strong> submitted!</span>
              <span class="earned-my-cash margin-top-5">
                We will notify you once it is <strong>approved!</strong>
              </span>
            </div>
          </div>
          <div class="detail-button margin-top-10" (click)="openDetails(successPaymentDetails?.id)">
            <span class="detail-text">View Detail</span>
          </div>
          <!-- <div class="privacy-container margin-top-20">
            <ion-button class="site-full-rounded-button less-border-radius margin-left-20 margin-right-20" expand="full"
              shape="round" type="submit" (click)="openDetails(successPaymentDetails?.id)">
              View Detail
            </ion-button>
          </div> -->

        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>