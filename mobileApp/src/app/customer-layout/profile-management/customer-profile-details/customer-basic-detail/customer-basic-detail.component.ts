import { DatePipe } from '@angular/common'; // Import DatePipe
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { ModalController, NavController } from '@ionic/angular';
import { ProfileDetail } from 'src/modals/profileDetail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { FileCropperComponent } from 'src/shared/file-cropper/file-cropper.component';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-customer-basic-detail',
  templateUrl: './customer-basic-detail.component.html',
  styleUrls: ['./customer-basic-detail.component.scss'],
  providers: [DatePipe] // Provide DatePipe
})
export class CustomerBasicDetailComponent implements OnInit {

  profile: ProfileDetail = new ProfileDetail();
  isChangeProfileRequest: boolean = false;
  onClickValidation: boolean = false;

  title: string | null = null;
  description: string | null = null;
  showValidationErrors = false;
  requestImage: string | null = null;
  uploadedImages: { url: string }[] = []; // Array to store uploaded images

  kycAadharFront: any;
  kycAadharBack: any;
  panCard: any;
  otherDocuments: any;
  formattedAddress: string = '';

  isSuccessfulResponse: boolean = false;
  successPaymentDetails: any;
  isProcessing: boolean = false;
  user: any;
  isOnBoardingComplete: boolean = false

  constructor(
    private readonly navController: NavController,
    private readonly activatedRoute: ActivatedRoute,
    public readonly commonService: CommonService,
    private readonly datePipe: DatePipe, // Inject DatePipe
    private modalCtrl: ModalController,
    private readonly toastService: ToastService,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly cdr: ChangeDetectorRef,
    private readonly localStorageService: LocalStorageService,
  ) {

  }

  ngOnInit() {
    this.user = this.localStorageService.getObject('user');
    if (this.user) {
      this.isOnBoardingComplete = this.user.isOnBoardingComplete;
    }
    this.activatedRoute.queryParams.subscribe(params => {
      const profileData = params['profile'];
      if (profileData) {
        this.profile = JSON.parse(decodeURIComponent(profileData));
        // Extract documents from the profile's userProfileDetail
        this.kycAadharFront = this.getDocumentBySubType('AADHARCARD_FRONT');
        this.kycAadharBack = this.getDocumentBySubType('AADHARCARD_BACK');
        this.panCard = this.getDocumentBySubType('PANCARD');
        this.otherDocuments = this.getDocumentsBySubType('OTHERS');

        // Format the address details
        this.formattedAddress = this.getFormattedAddress();
      }
    });
  }

  ionViewWillEnter() {
    this.user = this.localStorageService.getObject('user');
    if (this.user) {
      this.isOnBoardingComplete = this.user.isOnBoardingComplete;
    }
  }

  getDocumentBySubType(subType: string) {
    return this.profile?.userProfileDetail?.documentDetail?.find(
      (doc: any) => doc.subType === subType
    );
  }

  // Helper method to get all documents of a specific subType
  getDocumentsBySubType(subType: string) {
    return this.profile?.userProfileDetail?.documentDetail?.filter(
      (doc: any) => doc.subType === subType && doc.fileDetail?.path
    );
  }

  // Method to format the address
  getFormattedAddress(): string {
    const addressFields = [
      this.profile?.userProfileDetail?.addressDetail?.[0]?.addressLine1,
      this.profile?.userProfileDetail?.addressDetail?.[0]?.addressLine2,
      this.profile?.userProfileDetail?.addressDetail?.[0]?.city,
      this.profile?.userProfileDetail?.addressDetail?.[0]?.landMark,
      this.profile?.userProfileDetail?.addressDetail?.[0]?.state,
      this.profile?.userProfileDetail?.addressDetail?.[0]?.country,
      this.profile?.userProfileDetail?.addressDetail?.[0]?.pinCode
    ];

    // Filter out null, undefined, or empty strings and join with a comma
    return addressFields
      .filter(field => field && field.trim() !== '') // Filter valid fields
      .join(', '); // Concatenate with commas
  }

  formatDateOfBirth(date: string | null): string {
    return this.datePipe.transform(date, 'yyyy-MM-dd') || '';
  }

  openChangeRequestList() {
    this.navController.navigateForward("/portal/change/profile/request");
  }

  openChangeCreateRequest() {
    if (this.profile.isOpenProfileChangeRequest) {
      this.toastService.show('A change request is already in progress.');
      return;
    }
    this.isChangeProfileRequest = true;
  }

  closeChangeRequest() {
    this.isChangeProfileRequest = false;
    this.onClickValidation = false;
    this.clearForm();
  }

  openSuccessPopup() {
    this.isSuccessfulResponse = true;
  }

  closeSuccessPopup() {
    this.isSuccessfulResponse = false;
  }

  async upload() {
    if (this.requestImage) {
      this.toastService.show('Please remove the existing image before uploading a new one.');
      return;
    }
    try {
      const response = await Camera.getPhoto({
        quality: 50,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: CameraSource.Prompt, // Prompt user to select from camera or files
      });
      if (response.base64String) {
        await this.processCropper(response.base64String);
      }
    } catch (error) {
      //  this.toastService.show("Something went wrong while uploading profile picture.");
    }
  }

  async processCropper(base64Image: string) {
    const modal = await this.modalCtrl.create({
      component: FileCropperComponent,
      componentProps: {
        file: { base64String: base64Image }
      },
      cssClass: "cropper-modal"
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role !== 'confirm') {
      this.toastService.show("Sorry, profile picture uploading has been cancelled.");
      return;
    }
    if (data && data.croppedFile) {
      await this.uploadImage(data.croppedFile);
    } else {
      this.toastService.show("No file selected after cropping.");
    }
  }

  async uploadImage(blob: Blob): Promise<void> {
    if (!blob) {
      this.toastService.show('No file selected. Please choose an image to upload.');
      return;
    }

    const file = new File([blob], 'cropped-image.png', { type: 'image/png' }); // Convert Blob to File

    const formData = new FormData();
    formData.append('file', file, file.name);

    this.loadingService.show(); // Show loading indicator

    this.dataService.uploadFile(formData).subscribe({
      next: (response: any) => {
        this.loadingService.hide();
        this.handleUploadResponse(response);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred while uploading the file');
      }
    });
  }

  private handleUploadResponse(response: any): void {
    if (Array.isArray(response) && response.length > 0) {
      response.forEach((attachment: any) => {
        const fileUrl = attachment.path || attachment.fileName;
        this.uploadedImages.push({ url: fileUrl });
      });
      this.cdr.detectChanges();
      this.toastService.show('File uploaded successfully.');
    } else {
      this.toastService.show('Failed to upload file.');
    }
  }

  removeImage(image: { url: string }) {
    this.uploadedImages = this.uploadedImages.filter(img => img.url !== image.url);
  }

  async createChangeRequest(form: any): Promise<any> {
    this.onClickValidation = !form.valid;

    if (!form.valid) {
      return;
    }
    const payload = {
      title: this.title || null,
      description: this.description || null,
      attachments: this.generateAttachments()
    };
    //  this.loadingService.show();
    this.isProcessing = true;

    this.dataService.createChangeProfileRequest(payload)
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          const data = response.data;
          this.handleChangeRequestsResponse(data);
        }, error: (error: any) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message || 'An error occurred');
        }
      })
  }

  handleChangeRequestsResponse(data: any) {
    this.clearForm();
    this.closeChangeRequest();

    this.successPaymentDetails = data;
    this.openSuccessPopup();
  }

  closeDetails() {
    this.closeSuccessPopup();
  }

  openDetails(requestId: string) {
    this.closeSuccessPopup();

    setTimeout(() => {
      this.navController.navigateForward("/portal/change/profile/request", {
        state: {
          requestId: requestId
        },
        animated: true
      });
    }, 200);
  }

  private generateAttachments() {
    if (!this.uploadedImages || this.uploadedImages.length === 0) return [];

    return this.uploadedImages.map((image, index) => ({
      fileName: `image-${index + 1}`, // Generate a unique file name for each image
      mimeType: 'image/*', // Use a wildcard to accept any image type
      path: image.url, // Use the URL from the uploadedImages array
      originalName: `image-${index + 1}` // Optionally, set an original name
    }));
  }

  clearForm() {
    this.title = null;
    this.description = null;
    //  this.category = null;
    this.requestImage = null;
    this.uploadedImages = [];
  }

  completeOnboarding() {
    this.navController.navigateRoot(`/account/register/basic`, { animated: true });
  }

}
