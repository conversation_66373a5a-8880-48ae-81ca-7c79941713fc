<ion-content class="customer-dashboard-page customer-payment-page booking-page">
  <app-customer-header [innerPage]="true" [headingText]="'2FA Authentication'"
    [rightAction]="true"></app-customer-header>
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate"
    (ngSubmit)="verifyTwoFactorOtp(recordForm.form)">
    <div class="booking-page-container">
      <div class="form-container text-left">

        <div class="image-container no-margin">
          <img src="assets/images/svg/customer-otp.svg" class="otp-image" />
        </div>

        <p class="page-sub-heading customer-otp-text">
          Enter the code from the <strong class="download-strong-code"> Two-Factor Authenticator </strong>
          app on your phone.
        </p>

        <!-- OTP Input Fields -->
        <div class="otp-validate-container margin-top-30">
          <!-- Individual OTP input fields with validation and focus handling -->
          <ion-input required="required" minlength="1" maxlength="1" inputmode="numeric" type="text" placeholder="*"
            [debounce]="100" name="otpInput1" [ngClass]="{ 'is-invalid': onClickValidation && otpInput1.invalid }"
            [(ngModel)]="data.otpInput1" #otpInput1="ngModel" (ionInput)="commonService.changeInputFocus($event)"
            (keydown)="commonService.handleBackspace($event)">
          </ion-input>
          <ion-input required="required" inputmode="numeric" type="text" placeholder="*" [debounce]="100"
            name="otpInput2" #otpInput2="ngModel" [(ngModel)]="data.otpInput2"
            [ngClass]="{ 'is-invalid': onClickValidation && otpInput2.invalid }" minlength="1" maxlength="1"
            (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
          </ion-input>
          <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput3"
            [debounce]="100" #otpInput3="ngModel" [(ngModel)]="data.otpInput3"
            [ngClass]="{ 'is-invalid': onClickValidation && otpInput3.invalid}" minlength="1" maxlength="1"
            (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
          </ion-input>
          <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput4"
            [debounce]="100" #otpInput4="ngModel" [(ngModel)]="data.otpInput4"
            [ngClass]="{ 'is-invalid': onClickValidation && otpInput4.invalid }" minlength="1" maxlength="1"
            (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
          </ion-input>
          <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput5"
            [debounce]="100" #otpInput5="ngModel" [(ngModel)]="data.otpInput5"
            [ngClass]="{ 'is-invalid': onClickValidation && otpInput5.invalid}" minlength="1" maxlength="1"
            (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
          </ion-input>
          <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput6"
            [debounce]="100" #otpInput6="ngModel" [(ngModel)]="data.otpInput6"
            [ngClass]="{ 'is-invalid': onClickValidation && otpInput6.invalid}" minlength="1" maxlength="1"
            (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
          </ion-input>
        </div>

        <!-- Error message displayed if the form is invalid after submission -->
        <div *ngIf="recordForm.invalid && onClickValidation" class="error-message mt-1 text-left">
          Please provide a valid OTP
        </div>

        <!-- Continue button for the OTP form -->
        <div class="privacy-container">
          <ion-button class="site-full-rounded-button primary-button text-capitalize" expand="full" shape="round"
            type="submit" [disabled]="isProcessing">
            Continue
          </ion-button>
        </div>

      </div>
    </div>
  </form>
</ion-content>