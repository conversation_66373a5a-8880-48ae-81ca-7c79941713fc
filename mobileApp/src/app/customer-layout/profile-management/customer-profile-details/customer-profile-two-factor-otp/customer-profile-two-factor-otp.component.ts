import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { AuthService } from 'src/shared/authservice';

@Component({
  selector: 'app-customer-profile-two-factor-otp',
  templateUrl: './customer-profile-two-factor-otp.component.html',
  styleUrls: ['./customer-profile-two-factor-otp.component.scss'],
})
export class CustomerProfileTwoFactorOtpComponent implements OnInit {

  data: any; // Data object
  onClickValidation!: boolean; // Flag to control validation state
  twoFactorEnabled: boolean = false; // Holds the two-factor status
  isProcessing: boolean = false;

  constructor(
    private readonly navController: NavController,
    public readonly commonService: CommonService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private readonly route: ActivatedRoute,
    private readonly localStorageService: LocalStorageService, // Inject LocalStorageService
    private readonly authService: AuthService
  ) {
    // Initialize validation flag
    this.onClickValidation = false;
  }

  ngOnInit() {
    this.onClickValidation = false;

    // Retrieve the twoFactorEnabled status from local storage
    const user = this.authService.getUser();
    this.twoFactorEnabled = user.twoFactorEnabled || false; // Default to false if not set
    // Subscribe to query parameters from the route to get the verification token
    this.route.queryParams.subscribe(params => {
      this.data = {
        verificationToken: params['verificationToken'] || null,
      };
      if (!this.data.verificationToken) {
        this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
        this.navController.navigateRoot("/portal/verification/two/factor", { animated: true });
      }
    });
  }

  async verifyTwoFactorOtp(form: any): Promise<any> {
    if (!this.data.verificationToken) {
      this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
      this.navController.navigateRoot("/portal/verification/two/factor", { animated: true });
      return;
    }

    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return; // Exit if form is invalid
    }

    //  this.loadingService.show();
    this.isProcessing = true;

    const otpString = this.commonService.getOTPFromInputs(
      this.data.otpInput1,
      this.data.otpInput2,
      this.data.otpInput3,
      this.data.otpInput4,
      this.data.otpInput5,
      this.data.otpInput6
    );

    const twoFactorData = {
      verificationToken: this.data.verificationToken,
      code: otpString
    };

    this.dataService.verifyOtpForProfileTwoFactor(twoFactorData)
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide(); // Hide loading indicator
          this.isProcessing = false;

          const data = response.data;

          // Update twoFactorEnabled status based on the OTP verification result
          if (response.message.includes("enable")) {
            this.twoFactorEnabled = true; // Set to true on successful enabling
          } else if (response.message.includes("disabled")) {
            this.twoFactorEnabled = false; // Set to false on successful disabling
          }

          this.saveToLocalStorage(); // Save updated status

          this.navController.navigateForward("/account1");
          this.toastService.show(response.message);
        },
        error: (error: any) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message || 'Unexpected error occurred');
        }
      });
  }

  saveToLocalStorage() {
    const user = this.authService.getUser();
    user.twoFactorEnabled = this.twoFactorEnabled;
    this.localStorageService.setObject('user', user);
  }

  back() {
    this.navController.navigateBack("/portal/verification/two/factor");
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

}
