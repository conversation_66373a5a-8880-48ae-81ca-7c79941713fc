import { Routes } from "@angular/router";
import { AddGuestDetailsComponent } from "./customer-booking-management/add-guest-details/add-guest-details.component";
import { BookingCancelComponent } from "./customer-booking-management/booking-cancel/booking-cancel.component";
import { BookingConfirmationDetailsComponent } from "./customer-booking-management/booking-confirmation-details/booking-confirmation-details.component";
import { BookingConfirmationFailComponent } from "./customer-booking-management/booking-confirmation-fail/booking-confirmation-fail.component";
import { BookingConfirmationComponent } from "./customer-booking-management/booking-confirmation/booking-confirmation.component";
import { GuestRoomPreviewDetailsComponent } from "./customer-booking-management/guest-room-preview-details/guest-room-preview-details.component";
import { HotelDetailsComponent } from "./customer-booking-management/hotel-details/hotel-details.component";
import { HotelsListingComponent } from "./customer-booking-management/hotels-listing/hotels-listing.component";
import { CustomerLayoutComponent } from "./customer-layout.component";
import { HomeComponent } from "./home/<USER>";
import { MySubscriptionComponent } from "./my-subscription/my-subscription.component";
import { CustomerContactScreenComponent } from "./profile-management/customer-onboarding/customer-contact-screen/customer-contact-screen.component";
import { ChangePasswordComponent } from "./profile-management/customer-profile-details/change-password/change-password.component";
import { CustomerBasicDetailComponent } from "./profile-management/customer-profile-details/customer-basic-detail/customer-basic-detail.component";
import { CustomerProfileTwoFactorOtpComponent } from "./profile-management/customer-profile-details/customer-profile-two-factor-otp/customer-profile-two-factor-otp.component";
import { SupportTicketRequestListingComponent } from "./support-ticket/support-ticket-request-listing.component";
import { MyBookingsComponent } from "./my-bookings/my-bookings.component";
import { MyBookingsListDetailsComponent } from "./my-bookings/my-bookings-list-details/my-bookings-list-details.component";
import { CancelMyBookingComponent } from "./my-bookings/cancel-my-booking/cancel-my-booking.component";
import { SearchHotelComponent } from "./customer-booking-management/search-hotel/search-hotel.component";
import { MyPaymentsComponent } from "./my-payments/my-payments.component";
import { TwoFactorAuthenticationComponent } from "../authentication/mfa/two-factor-authentication/two-factor-authentication.component";
import { PaymentConfirmComponent } from "./my-payments/payment-confirm/payment-confirm.component";
import { NotificationListComponent } from "./notification-list/notification-list.component";
import { ChangeProfileRequestComponent } from "./change-profile-request/change-profile-request.component";
import { RedeemGiftListComponent } from "./redeem-gift-list/redeem-gift-list.component";
import { EmiDetailComponent } from "./emi-detail/emi-detail.component";
import { SupportTicketDetailComponent } from "./support-ticket/support-ticket-detail/support-ticket-detail.component";
import { CustomerMyWalletComponent } from "./customer-my-wallet/customer-my-wallet.component";
import { CustomerHowItWorksComponent } from "./customer-how-it-works/customer-how-it-works.component";
import { CustomerFaqComponent } from "./customer-faq/customer-faq.component";
import { CustomerSeeMoreTransactionsComponent } from "./customer-see-more-transactions/customer-see-more-transactions.component";
import { MyPackagesComponent } from "./my-subscription/my-packages/my-packages.component";
import { MyCompaignsComponent } from "./my-compaigns/my-compaigns.component";
import { CustomerSeeMoreCompaignsComponent } from "./customer-see-more-compaigns/customer-see-more-compaigns.component";
import { CampaignDetailComponent } from "./my-compaigns/campaign-detail/campaign-detail.component";
import { SelectPaymentMethodComponent } from "./my-payments/select-payment-method/select-payment-method.component";
import { CustomerReferralRewardComponent } from "./customer-referral-reward/customer-referral-reward.component";
import { CustomerPackageDetailComponent } from "./customer-package-detail/customer-package-detail.component";
import { GuestDetailCouponsComponent } from "./customer-booking-management/guest-detail-coupons/guest-detail-coupons.component";

export const CUSTOMERLAYOUTROUTING: Routes = [
  {
    path: "",
    component: CustomerLayoutComponent,
    children: [
      // {
      //   path: "dashboard",
      //   component: HomeComponent
      // },
      // {
      //   path: "booking",
      //   component: MyBookingsComponent
      // },
      // {
      //   path: "payment",
      //   component: MyPaymentsComponent
      // },
      {
        path: "select/payment/method",
        component: SelectPaymentMethodComponent
      },
      {
        path: "notification/list",
        component: NotificationListComponent
      },
      {
        path: "confirm/payment",
        component: PaymentConfirmComponent
      },
      // {
      //   path: "membership",
      //   component: MySubscriptionComponent
      // },
      // {
      //   path: "account",
      //   component: CustomerAccountComponent
      // },
      {
        path: "my/wallet",
        component: CustomerMyWalletComponent
      },
      {
        path: "how/it/works/:tabType",
        component: CustomerHowItWorksComponent
      },
      {
        path: "faq/:tabType",
        component: CustomerFaqComponent
      },
      {
        path: "see/more/:id/:type/:tabType/transactions",
        component: CustomerSeeMoreTransactionsComponent
      },
      {
        path: "see/more/:walletId/compaigns",
        component: CustomerSeeMoreCompaignsComponent
      },
      {
        path: "customer/contact",
        component: CustomerContactScreenComponent
      },
      {
        path: "change/password",
        component: ChangePasswordComponent
      },
      {
        path: 'support/request/listing',
        component: SupportTicketRequestListingComponent,
      },
      {
        path: 'support/request/:id/detail',
        component: SupportTicketDetailComponent,
      },
      {
        path: 'change/profile/request',
        component: ChangeProfileRequestComponent,
      },
      {
        path: "search/hotels",
        component: SearchHotelComponent
      },
      {
        path: "available/hotels",
        component: HotelsListingComponent
      },
      {
        path: "hotel/:id/details",
        component: HotelDetailsComponent
      },
      {
        path: "add/guest/details",
        component: AddGuestDetailsComponent
      },
      {
        path: "guest/preview/details",
        component: GuestRoomPreviewDetailsComponent
      },
      {
        path: "confirmation",
        component: BookingConfirmationComponent
      },
      {
        path: "confirmation/details",
        component: BookingConfirmationDetailsComponent
      },
      {
        path: "confirmation/status",
        component: BookingConfirmationFailComponent
      },
      {
        path: "cancel/booking",
        component: BookingCancelComponent
      },
      {
        path: "my/booking/:id/detail",
        component: MyBookingsListDetailsComponent
      },
      {
        path: "cancel/my/booking",
        component: CancelMyBookingComponent
      },
      {
        path: 'basic/profile/details',
        component: CustomerBasicDetailComponent,
      },
      {
        path: 'verification/two/factor',
        component: TwoFactorAuthenticationComponent
      },
      {
        path: 'verification/two/factor/otp',
        component: CustomerProfileTwoFactorOtpComponent,
      },
      {
        path: 'redeem/gift/list',
        component: RedeemGiftListComponent,
      },
      {
        path: 'emi/details',
        component: EmiDetailComponent,
      },
      {
        path: 'my/packages',
        component: MyPackagesComponent,
      },
      {
        path: 'my/:walletId/compaigns',
        component: MyCompaignsComponent,
      },
      {
        path: 'referral/reward',
        component: CustomerReferralRewardComponent,
      },
      {
        path: "my/campaign/details/:campaignId",
        component: CampaignDetailComponent
      },
      {
        path: "my/campaign/details",
        component: CampaignDetailComponent
      },
      {
        path: "package/detail",
        component: CustomerPackageDetailComponent
      },
      {
        path: "guest/details/coupons",
        component: GuestDetailCouponsComponent
      },
    ],
  },
];
