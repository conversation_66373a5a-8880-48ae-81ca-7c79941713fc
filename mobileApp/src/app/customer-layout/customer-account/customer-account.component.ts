import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NavController } from '@ionic/angular';
import { ProfileDetail } from 'src/modals/profileDetail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { NavigationDataService } from 'src/services/navigation.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-customer-account',
  templateUrl: './customer-account.component.html',
  styleUrls: ['./customer-account.component.scss'],
})
export class CustomerAccountComponent implements OnInit {

  profile: ProfileDetail = new ProfileDetail();
  user: any;
  walletId: string = '';
  formattedPhoneNumber: string = '';
  fullName: string = '';

  constructor(private readonly navController: NavController,
    private readonly authService: AuthService,
    private readonly navigationService: NavigationDataService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private readonly localStorageService: LocalStorageService,
    private readonly commonService: CommonService,
    private readonly router: Router,
  ) {

  }

  ngOnInit() {
    this.user = this.authService.getUser();
    // if (!this.user) {
    //   this.commonService.openLoginModal();
    //   return;
    // }
    if (this.user) {
      this.walletId = this.user.walletId;
      this.getProfileDetails();
    }
  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();
    if (!this.user) {
      this.commonService.openLoginModal();
      let currentUrl = this.router.url;
      this.localStorageService.set('redirectAfterLogin', currentUrl);
      return;
    }
    if (this.user) {
      this.walletId = this.user.walletId;
      this.getProfileDetails();
    }

  }

  getProfileDetails() {
    //  this.loadingService.show();
    this.dataService.getProfileDetails()
      .subscribe({
        next: (response: RestResponse) => {
          //   this.loadingService.hide();
          const data = response.data;
          this.handleResponse(data);
        },
        error: (error) => {
          //  this.loadingService.hide();
          this.toastService.show(error.message || 'Unexpected error occurred');
        }
      });
  }

  handleResponse(data: any) {
    this.localStorageService.setObject('coApplicantDetail', data?.userProfileDetail?.coApplicantDetail);
    this.profile = data;
    this.formattedPhoneNumber = this.formatPhoneNumber(this.profile?.phoneNumber || '');
    const first = this.profile?.firstName?.trim() || '';
    const last = this.profile?.lastName?.trim() || '';
    this.fullName = (first + ' ' + last).trim();

    if (!this.fullName) {
      this.fullName = 'Traveller';
    }
  }

  formatPhoneNumber(phoneNumber: string): string {
    if (!phoneNumber) {
      return '';
    }
    const countryCode = '+91 ';
    const formatted = phoneNumber.replace(/(\d{5})(\d{5})/, '$1 $2');
    return countryCode + formatted;
  }

  profileDetails() {
    const profileData = JSON.stringify(this.profile);
    this.navController.navigateForward(`/portal/basic/profile/details?profile=${encodeURIComponent(profileData)}`);
  }

  changePassword() {
    this.navController.navigateForward("/portal/change/password", { animated: true });
  }

  openTwoFactorAuthentication() {
    this.navController.navigateForward("/portal/verification/two/factor", { animated: true });
  }

  openSupportRequest() {
    this.navController.navigateForward("/portal/support/request/listing", { animated: true });
  }

  openChangeProfileRequest() {
    this.navController.navigateForward("/portal/change/profile/request", { animated: true });
  }

  openMyWallet() {
    this.navController.navigateForward("/portal/my/wallet", { animated: true });
  }

  openActiveCompaigns() {
    this.navController.navigateForward(`/portal/see/more/${this.walletId}/compaigns`, { animated: true });
  }

  openMyCompaigns() {
    this.navController.navigateForward(`/portal/my/${this.walletId}/compaigns`, { animated: true });
  }

  openRefer() {
    this.navController.navigateForward("/portal/referral/reward", { animated: true });
  }

  logout() {
    this.authService.logout();
    this.localStorageService.set('isLoginOpened', 'false')
    this.navController.navigateRoot("/dashboard");
  }

}
