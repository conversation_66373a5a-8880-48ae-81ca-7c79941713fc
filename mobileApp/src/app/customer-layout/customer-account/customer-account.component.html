<ion-content class="customer-dashboard-page">
  <ion-content class="scroll">
    <div class="customer-dashboard-container account-page-height">
      <div class="settings-heading-container">
        <!-- <div class="settings-heading">Settings</div> -->
      </div>

      <!-- Profile Section -->
      <div class="profile-section">
        <div class="profile-picture-wrapper">
          <div class="profile-picture-container margin-bottom-30">
            <img src="/assets/images/icons/user.png" *ngIf="!profile?.profileImageUrl" alt="Profile Picture" />
            <img [src]="profile.profileImageUrl" class="profile-pic" *ngIf="profile?.profileImageUrl"
              alt="Profile Picture" />
          </div>
        </div>
        <h2 class="user-name">{{fullName}}</h2>
        <p class="phone-number">{{formattedPhoneNumber}}</p>
      </div>

      <!-- Cards -->
      <ion-card class="profile-actions-card">
        <ion-list lines="none">
          <ion-item detail="false" (click)="profileDetails()">
            <ion-icon slot="start" [src]="'/assets/images/svg/profile-detail.svg'"></ion-icon>
            Profile Details
            <ion-icon class="arrow-right" slot="end" name="chevron-forward-outline"></ion-icon>
          </ion-item>
          <div class="separator-line"></div>

          <ion-item detail="false" (click)="changePassword()">
            <ion-icon slot="start" [src]="'/assets/images/svg/change-password.svg'"></ion-icon>
            Change Password
            <ion-icon class="arrow-right" slot="end" name="chevron-forward-outline"></ion-icon>
          </ion-item>
        </ion-list>
      </ion-card>

      <ion-card class="other-options-card">
        <ion-list lines="none">

          <ion-item detail="false" (click)="openTwoFactorAuthentication()">
            <ion-icon slot="start" [src]="'/assets/images/svg/two-factor-auth.svg'"></ion-icon>
            2FA Authentication
            <ion-icon class="arrow-right" slot="end" name="chevron-forward-outline"></ion-icon>
          </ion-item>

          <div class="separator-line"></div>

          <ion-item detail="false" (click)="openSupportRequest()">
            <ion-icon slot="start" [src]="'/assets/images/svg/support-icon.svg'"></ion-icon>
            Support Requests
            <ion-icon class="arrow-right" slot="end" name="chevron-forward-outline"></ion-icon>
          </ion-item>

          <div class="separator-line"></div>

          <ion-item detail="false" (click)="openChangeProfileRequest()">
            <ion-icon slot="start" [src]="'/assets/images/svg/change-request-icon.svg'"></ion-icon>
            Profile Change Requests
            <ion-icon class="arrow-right" slot="end" name="chevron-forward-outline"></ion-icon>
          </ion-item>

          <div class="separator-line"></div>

          <ion-item detail="false" (click)="openMyWallet()">
            <ion-icon slot="start" [src]="'/assets/images/svg/wallet-icon.svg'"></ion-icon>
            My Wallet
            <ion-icon class="arrow-right" slot="end" name="chevron-forward-outline"></ion-icon>
          </ion-item>

          <div class="separator-line"></div>

          <ion-item detail="false" (click)="openActiveCompaigns()">
            <ion-icon slot="start" [src]="'/assets/images/svg/campaign-icon.svg'"></ion-icon>
            Active Campaigns
            <ion-icon class="arrow-right" slot="end" name="chevron-forward-outline"></ion-icon>
          </ion-item>

          <div class="separator-line"></div>

          <ion-item detail="false" (click)="openMyCompaigns()">
            <ion-icon class="my-campaign-icon" slot="start"
              [src]="'/assets/images/svg/my-campaign-icon.svg'"></ion-icon>
            My Campaigns
            <ion-icon class="arrow-right" slot="end" name="chevron-forward-outline"></ion-icon>
          </ion-item>

          <div class="separator-line"></div>

          <ion-item detail="false" (click)="openRefer()">
            <ion-icon slot="start" [src]="'/assets/images/svg/referral.svg'"></ion-icon>
            Refer and Win
            <ion-icon class="arrow-right" slot="end" name="chevron-forward-outline"></ion-icon>
          </ion-item>
        </ion-list>
      </ion-card>

      <div class="privacy-container">
        <ion-button class="site-full-rounded-button primary-button text-capitalize" expand="full" shape="round"
          type="submit" (click)="logout()">
          Logout
        </ion-button>
      </div>

    </div>
  </ion-content>
</ion-content>