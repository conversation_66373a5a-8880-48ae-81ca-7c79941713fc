import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-customer-how-it-works',
  templateUrl: './customer-how-it-works.component.html',
  styleUrls: ['./customer-how-it-works.component.scss'],
})
export class CustomerHowItWorksComponent implements OnInit {

  howItWorksText: string = '';
  tabType!: string;
  backUrl: string = '';

  constructor(private readonly route: ActivatedRoute

  ) {

  }

  ngOnInit() { }

  ionViewWillEnter() {
    this.tabType = this.route.snapshot.paramMap.get('tabType') || "";

    this.backUrl = `/portal/my/wallet?tab=${this.tabType}`;

    if (this.tabType === 'MYCASH') {
      this.howItWorksText = `
      <p><strong>MyCash</strong> is your personal wallet that lets you make faster and smarter payments on zforb. Here's everything you need to know:</p>

      <p><strong>🏦 Adding Money</strong></p>
      <ul>
        <li>You can <strong>add money to your MyCash</strong> wallet anytime using our secure payment gateway.</li>
        <li>You can recharge your MyCash wallet using our secure payment gateway via debit/credit cards, UPI, net banking, and other supported methods.</li>
      </ul>

      <p><strong>🎁 Earning Cashbacks</strong></p>
      <ul>
        <li>Cashbacks from special offers or eligible transactions are automatically credited to your MyCash wallet. </li>
        <li>Stay tuned for exciting cashback promotions and maximize your savings!</li>
      </ul>

      <p><strong>💳 Using MyCash</strong></p>
      <ul>
        <li>MyCash can be used <strong>for all transactions on zforb,</strong> including bookings, EMI/ASF payments, and subscription renewals.</li>
        <p>There are no limits or restrictions—if your MyCash balance covers the full amount, you can use it to complete the entire payment.</p>
      </ul>

      <p><strong>✅ Quick & Easy Payments</strong></p>
      <ul>
        <li>During checkout, simply select <strong>MyCash</strong> as your payment option.</li>
        <li>If your MyCash balance covers the full amount, you're good to go. Otherwise, you can pay the remaining via other methods.</li>
      </ul>
    `;
    } else {
      this.howItWorksText = `
    <p><strong>PromoCash</strong> refers to promotional wallet credits granted as part of special offers or campaigns.</p>

      <p><strong>❌ Not Rechargeable</strong></p>
      <ul>
        <li>PromoCash cannot be <strong>manually </strong>recharged or topped up by the user.</li>
      </ul>

      <p><strong>🎯 Credited Through Promotions</strong></p>
      <ul>
        <li>Earn PromoCash exclusively via <strong>special deals, cashback offers, or contests.</strong></li>
      </ul>

      <p><strong>⏳ Comes with Expiry Dates</strong></p>
      <ul>
        <li>Each PromoCash credit has a <strong>defined expiry</strong>—make sure to use it before it runs out.</li>
      </ul>

      <p><strong>⚠️ Usage Restrictions Apply</strong></p>
      <ul>
        <li>PromoCash can only be used on select transactions and may have a cap on the amount applicable per order.</li>
      </ul>
    `;
    }

  }

}
