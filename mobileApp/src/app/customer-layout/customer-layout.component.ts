import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { NavController, Platform } from '@ionic/angular';
import { filter, Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { EventService } from 'src/shared/event.service';
import { FcmService } from 'src/shared/fcm.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-layout',
  templateUrl: './customer-layout.component.html',
  styleUrls: ['./customer-layout.component.scss'],
})
export class CustomerLayoutComponent implements OnInit, OnDestroy {

  showTabs = true;
  private routeSub!: Subscription;
  user: any;
  walletId: string = '';
  packageId: any;
  balanceNights: any;
  endDate: any;
  isPackageReminderPopupOpen: boolean = false;
  isCurrentYearHolidayExpirePopupOpen: boolean = false;
  isgrabDealPopupOpen: boolean = false;
  isRandomCampaignModalOpen: boolean = false;
  loading: string = "NOT_STARTED";
  customPushNotificationDetail: any;
  randomCampaignDetail: any;
  responseReceived: boolean = false;

  constructor(private readonly navController: NavController,
    private readonly router: Router,
    private readonly platform: Platform,
    private readonly fcmService: FcmService,
    private readonly eventService: EventService,
    private readonly changeDetectorRef: ChangeDetectorRef,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private readonly localStorageService: LocalStorageService,
    public readonly commonService: CommonService,
    private readonly authService: AuthService
  ) {

  }

  ngOnInit() {
    this.platform.ready().then(() => {
      if (this.platform.is('cordova')) {
        this.fcmService.initializeFirebaseToken();
      }
    });
    // this.routeSub = this.router.events
    //   .pipe(filter(event => event instanceof NavigationEnd))
    //   .subscribe(() => {
    //     this.checkRoute();
    //   });
    this.eventService.event.subscribe((data) => {
      if (!data) {
        return;
      }
      if (data.key === "fcm:notification") {
        this.processNotifications(JSON.parse(JSON.stringify(data.value)))
      }
      data = undefined;
    });

    this.user = this.authService.getUser();
    if (this.user) {
      this.walletId = this.user.walletId;
      this.fetchMyActiveSubscription();
      // setTimeout(() => {
      //   this.fetchRandomCampaigns();
      // }, 3000);
    }

  }

  ionViewWillEnter() {
  }

  fetchMyActiveSubscription() {
    //  this.loadingService.show();
    this.dataService.fetchMyActiveSubscription().subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        const packageData = response.data;
        if (!packageData.package) {
          this.openPackageReminderPopup();
          return;
        }
        this.packageId = packageData.id;
        this.localStorageService.setObject("activeSubscriptionData", packageData);
        this.handleSubscriptionResponse(packageData)
      },
      error: (error: any) => {
        //  this.loadingService.hide();
      }
    })
  }

  handleSubscriptionResponse(packageData: any) {
    this.balanceNights = packageData.currentYearDetails.nightsAllowed - packageData.currentYearDetails.nightsUsed;
    this.endDate = new Date(packageData.currentYearDetails.endDate).toISOString().split('T')[0];

    const shouldOpenPopup = this.commonService.expireHolidays(packageData, this.balanceNights);

    if (shouldOpenPopup) {
      this.openCurrentYearHolidayExpirePopup();
    } else {
      this.closeCurrentYearHolidayExpirePopup();
    }
  }

  private checkRoute() {
    const currentRoute = this.router.url;

    // Define the routes where tabs should be shown
    const hideTabRoutes = ['/dashboard',
      '/account1',
      '/booking',
      '/membership',
      '/payment'
    ];

    this.showTabs = hideTabRoutes.some(route => currentRoute.startsWith(route));
  }

  onTabChange(event: any) {
    const selectedTab = event.tab;
    if (selectedTab === 'booking' || selectedTab === 'payment') {
      this.router.navigate(['/portal/', selectedTab], {
        queryParams: {}, // Reset query params
        skipLocationChange: true, // Optional, resets the query params
      });
      this.checkRoute();
    }
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }

  processNotifications(data: any) {
    // JSON.parse(data.body)
    if (data.type === "LOW_AGENT_BALANCE" && data.targetType === "BOOKING") {
      this.resumeBooking(data.targetId);
    } else if (data.targetType === "PAYMENT") {
      this.navController.navigateForward(`/payment?paymentId=${data.targetId}&fcmPaymentType=${data.type}&message=${data.message}&status=${data.status}`, { animated: true });
    } else if ((data.type === "BOOKING_CONFIRMATION" || data.type === "HOTEL_BOOKING_CANCELLATION") && data.targetType === "BOOKING") {
      this.navController.navigateForward(`/booking?bookingId=${data.targetId}&bookingType=${data.type}&message=${data.message}`, { animated: true });
    } else if (
      (data.type === "START_REDEEM_REQUEST" || data.type === "CANCEL_REDEEM_REQUEST" || data.type === "REDEEM_OFFER") &&
      (data.targetType === "PACKAGE" || data.targetType === "REDEEM_REQUEST")
    ) {
      this.navController.navigateForward(`/membership?membershipId=${data.targetId}&membershipType=${data.type}&message=${data.message}`, { animated: true });
    } else if (data.type === "START_REDEEM_REQUEST" && data.targetType === "REDEEM_REQUEST_LIST") {
      this.navController.navigateForward(`/portal/redeem/gift/list?redeemGiftId=${data.targetId}&redeemGiftType=${data.type}&message=${data.message}`, { animated: true });
    } else if (data.type === "SUPPORT_TICKET" && data.targetType === "SUPPORT_REQUEST") {
      this.navController.navigateForward(`/portal/support/request/listing?supportRequestId=${data.targetId}&supportRequestType=${data.type}&message=${data.message}&fromScreen='supportRequest'`, { animated: true });
    } else if (data.type === "SUPPORT_REQUEST" && data.targetType === "SUPPORT_TICKET_COMMENT") {
      this.navController.navigateForward(`/portal/support/request/${data.targetId}/detail?supportRequestType=${data.type}&tab=COMMENTS&message=${data.message}&fromScreen='supportRequestComment'`, { animated: true });
    } else if (data.type === "PROFILE_CHANGE_REQUEST" && data.targetType === "CUSTOMER_PROFILE_CHANGE_REQUEST") {
      this.navController.navigateForward(`/portal/change/profile/request?changeProfileRequestId=${data.targetId}&changeProfileRequestType=${data.type}&message=${data.message}&fromScreen='changeProfileRequest'`, { animated: true });
    } else if (data.type === "CURRENT_YEAR_EXPIRATION" && data.targetType === "PACKAGE") {
      this.openCurrentYearHolidayExpirePopup();
    } else if (data.type === "PACKAGE_EXPIRATION" && data.targetType === "PACKAGE") {
      this.openPackageReminderPopup();
    } else if (data.type === "CAMPAIGN_COMPLETE" && data.targetType === "CAMPAIGN") {
      this.navController.navigateForward('/portal/my/campaign/details', {
        state: {
          campaignId: data.targetId,
          campaignType: data.type,
          message: data.message,
          walletId: this.walletId,
          fromScreen: 'notification-screen'
        },
        animated: true
      });
    } else if (data.type === "CUSTOM_PUSH_NOTIFICATION" && data.targetType === "CUSTOM_PUSH_NOTIFICATION") {
      this.openGrabDealPopup(data.targetId);
    }

    setTimeout(() => this.changeDetectorRef.detectChanges(), 200);
  }

  resumeBooking(bookingId: string) {
    this.loadingService.show();
    this.dataService.resumeBooking(bookingId)
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          this.handleResumeBookingResponse(response.data);
        },
        error: (error) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'An error occurred');
        }
      });
  }

  handleResumeBookingResponse(data: any) {
    const hotelDetails: any = {
      searchSessionId: data.searchSessionId,
      arrivalDate: data.arrivalDate,
      departureDate: data.departureDate,
      currency: data.currency,
      guestNationality: data.guestNationality,
      countryCode: data.countryCode,
      city: data.city,
      bookingType: data.bookingType,
      hotel: data.hotels
    };
    this.localStorageService.setObject("hotelDetails", JSON.stringify(hotelDetails));
    this.navController.navigateForward(`/portal/hotel/${hotelDetails.hotel.hotelCode}/details`, { animated: true });
  }

  carryForwardHolidays() {
    this.dataService.carryForwardHolidays(this.packageId).subscribe({
      next: (response: RestResponse) => {
        this.fetchMyActiveSubscription();
      },
      error: (error: any) => {
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  lapseHolidays() {
    this.dataService.lapseHolidays(this.packageId).subscribe({
      next: (response: RestResponse) => {
        this.fetchMyActiveSubscription();
      },
      error: (error: any) => {
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  openPackageReminderPopup() {
    this.isPackageReminderPopupOpen = true;
  }

  closePackageReminderPopup() {
    this.isPackageReminderPopupOpen = false;
  }

  openCurrentYearHolidayExpirePopup() {
    this.isCurrentYearHolidayExpirePopupOpen = true;
  }

  closeCurrentYearHolidayExpirePopup() {
    this.isCurrentYearHolidayExpirePopupOpen = false;
  }

  openGrabDealPopup(targetId: any) {
    this.isgrabDealPopupOpen = true;
    this.fetchCustomPushNotificationDetails(targetId);
  }

  closeGrabDealPopup() {
    this.isgrabDealPopupOpen = false;
  }

  openRandomCampaignModal() {
    this.isRandomCampaignModalOpen = true;
  }

  closeRandomCampaignModal() {
    this.isRandomCampaignModalOpen = false;
  }

  fetchRandomCampaigns() {
    this.responseReceived = false;
    this.loading = "LOADING";
    this.dataService.fetchRandomCampaigns().subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.responseReceived = true;
        this.openRandomCampaignModal();
        this.randomCampaignDetail = response.data;
      },
      error: (error: any) => {
        this.loading = "LOADED";
      }
    })
  }

  randomCampaignDetailButton() {
    this.closeRandomCampaignModal();
    if (!this.user) {
      this.localStorageService.set('compaignsWithoutLogin', true);
      this.localStorageService.set('campaignId', this.randomCampaignDetail?.id);
      this.commonService.openLoginModal();
      return;
    }
    setTimeout(() => {
      this.navController.navigateForward('/portal/my/campaign/details', {
        state: {
          campaignId: this.randomCampaignDetail?.id,
          walletId: this.walletId,
        },
        animated: true
      });
    }, 200);
  }

  fetchCustomPushNotificationDetails(id: string) {
    this.responseReceived = false;
    this.loading = "LOADING";
    this.dataService.pushNotificationDetailById(id).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.responseReceived = true;
        this.customPushNotificationDetail = response.data;
      },
      error: (error: any) => {
        this.loading = "LOADED";
      },
    });
  }

  seeCampaignDetail(pushNotificationDetail: any) {
    this.closeGrabDealPopup();
    setTimeout(() => {
      if (pushNotificationDetail?.type === 'CAMPAIGN') {
        this.navController.navigateForward('/portal/my/campaign/details', {
          state: {
            campaignId: pushNotificationDetail?.typeId,
            walletId: this.walletId,
            fromScreen: 'notification-screen'
          },
          animated: true
        });
      } else if (pushNotificationDetail?.type === 'PACKAGE') {
        this.navController.navigateForward('/portal/package/detail', {
          state: {
            packageId: pushNotificationDetail?.typeId,
            fphId: pushNotificationDetail?.fphId,
            offerId: pushNotificationDetail?.offerId,
            fromScreen: 'notification-screen'
          },
          animated: true
        });
      } else if (pushNotificationDetail?.type === 'WALLET_RECHARGE') {
        this.navController.navigateForward("/portal/my/wallet", { animated: true });
      }
    }, 200);
  }

  explorePlan() {
    this.navController.navigateForward("account/register/package/selection", { animated: true });
  }
}
