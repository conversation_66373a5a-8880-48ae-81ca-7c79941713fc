import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Modal<PERSON>ontroller, NavController } from '@ionic/angular';
import { take } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { FileCropperComponent } from 'src/shared/file-cropper/file-cropper.component';
import { FullImageComponent } from 'src/shared/full-image/full-image.component';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-change-profile-request',
  templateUrl: './change-profile-request.component.html',
  styleUrls: ['./change-profile-request.component.scss'],
})
export class ChangeProfileRequestComponent implements OnInit, OnDestroy {

  user: any;
  filter: any;
  pageSize: number = 10;
  loading: string = "NOT_STARTED";
  showInfiniteScroll: boolean = false;
  changeRequests: Array<any> = new Array<any>();
  totalCount: number = 0;
  changeRequestDetail: any;
  appliedFilter: any;

  isChangeProfileRequest: boolean = false;
  isChangeRequestDetailOpen: boolean = false;
  onClickValidation: boolean = false;

  title: string | null = null;
  description: string | null = null;

  showValidationErrors = false;
  requestImage: string | null = null;

  uploadedImages: { url: string }[] = []; // Array to store uploaded images

  eventTarget: any;
  changeProfileRequestId: string = '';
  fromScreen: string = '';
  backUrl: string = '';
  queryParamsSubscribe: any;

  isSuccessfulResponse: boolean = false;
  successPaymentDetails: any;
  profileDetailId!: string;
  isProcessing: boolean = false;

  constructor(private authService: AuthService,
    private route: ActivatedRoute,
    private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly dataService: DataService,
    public readonly commonService: CommonService,
    private readonly cdr: ChangeDetectorRef,
    private readonly loadingService: LoadingService,
    private modalCtrl: ModalController
  ) {

  }

  ngOnInit() {
    this.user = this.authService.getUser();
  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();

    this.showInfiniteScroll = true;
    this.changeRequests = new Array<any>();
    this.filter = {} as any;
    this.filter.offset = 1;
    this.filter.tabType = "OPEN";

    this.queryParamsSubscribe = this.route.queryParams.pipe(take(1)).subscribe(params => {
      if (this.commonService.isObjectEmpty(params)) {
        this.fetchMyChangeRequests(null, true);
        return;
      }
      this.filter.tabType = params['tab'] || "OPEN";
      this.changeProfileRequestId = params['changeProfileRequestId'] || null;
      this.fromScreen = params['fromScreen'] ? params['fromScreen'].replace(/['"]/g, '') : null;

      if (this.changeProfileRequestId) {
        this.filter.tabType = "CLOSED";
      }

      this.backUrl = this.fromScreen === 'changeProfileRequest' ? `/portal/notification/list` : `/account`;

      this.cdr.detectChanges();
      this.fetchMyChangeRequests(null, true);
    });

    // below requestId come from basic detail screen for show change request detail //
    this.profileDetailId = history.state.requestId || null;

    if (this.profileDetailId) {
      this.showChangeRequestDetail(this.profileDetailId);
    }
  }

  fetchMyChangeRequests($event: any, handleLoading: boolean) {
    const payload = {
      tabType: this.filter.tabType || 'OPEN',
      searchText: null,
      pagination: {
        next: this.pageSize,
        offset: this.filter.offset
      }
    };
    if (handleLoading) { this.loading = "LOADING"; }
    this.dataService.myProfileChangeRequests(payload)
      .subscribe({
        next: (response: RestResponse) => {
          this.loading = "LOADED";

          if (response.data.length > 0) {
            this.changeRequests.push(...response.data);
          }
          if ($event) {
            $event.target.complete();
            if (this.changeRequests.length > 0 && this.changeRequests.length >= this.changeRequests[0].totalCount) {
              $event.target.disabled = true;
            }
          }
          this.cdr.detectChanges();
        }, error: (error: any) => {
          this.loading = "LOADED";
          this.toastService.show(error.message || 'An error occurred');
        }
      })
  }

  openChangeRequest() {
    // Check if any request in the list has status 'REQUESTED'
    const hasPendingRequest = this.changeRequests.some(request => request.status === 'REQUESTED');
    if (hasPendingRequest) {
      this.toastService.show('A change request is already in progress.');
      return;
    }
    this.isChangeProfileRequest = true;
  }

  closeChangeRequest() {
    this.isChangeProfileRequest = false;
    this.onClickValidation = false;
    this.clearForm();
  }

  showChangeRequestDetail(id: string) {
    //  this.loadingService.show();
    this.isProcessing = true;
    this.dataService.getProfileChangeRequestDetail(id)
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          this.changeRequestDetail = response.data;
          this.isChangeRequestDetailOpen = true;
          this.cdr.detectChanges();
        },
        error: (error) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message);
        }
      });
  }

  closeChangeRequestDetailPopup() {
    this.isChangeRequestDetailOpen = false;
  }

  onPageChanged($event: any) {
    if (this.changeRequests.length > 0 && this.changeRequests.length >= this.changeRequests[0].totalCount) {
      $event.target.complete();
      $event.target.disabled = true;
      return;
    }
    this.filter.offset = this.filter.offset + 1;
    this.fetchMyChangeRequests($event, false);
  }

  onChangeStatusTab(status: string) {
    this.filter.tabType = status;

    this.changeRequests = new Array<any>();
    this.filter.offset = 1; // Reset offset
    this.fetchMyChangeRequests(null, true);
  }

  async upload() {
    if (this.requestImage) {
      this.toastService.show('Please remove the existing image before uploading a new one.');
      return;
    }
    try {
      const response = await Camera.getPhoto({
        quality: 50,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: CameraSource.Prompt, // Prompt user to select from camera or files
      });
      if (response.base64String) {
        await this.processCropper(response.base64String);
      }
    } catch (error) {
      //  this.toastService.show("Something went wrong while uploading profile picture.");
    }
  }

  async processCropper(base64Image: string) {
    const modal = await this.modalCtrl.create({
      component: FileCropperComponent,
      componentProps: {
        file: { base64String: base64Image }
      },
      cssClass: "cropper-modal"
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role !== 'confirm') {
      this.toastService.show("Sorry, profile picture uploading has been cancelled.");
      return;
    }
    if (data && data.croppedFile) {
      await this.uploadImage(data.croppedFile);
    } else {
      this.toastService.show("No file selected after cropping.");
    }
  }

  async uploadImage(blob: Blob): Promise<void> {
    if (!blob) {
      this.toastService.show('No file selected. Please choose an image to upload.');
      return;
    }

    const file = new File([blob], 'cropped-image.png', { type: 'image/png' }); // Convert Blob to File

    const formData = new FormData();
    formData.append('file', file, file.name);

    this.loadingService.show(); // Show loading indicator

    this.dataService.uploadFile(formData).subscribe({
      next: (response: any) => {
        this.loadingService.hide();
        this.handleUploadResponse(response);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred while uploading the file');
      }
    });
  }

  private handleUploadResponse(response: any): void {
    if (Array.isArray(response) && response.length > 0) {
      response.forEach((attachment: any) => {
        const fileUrl = attachment.path || attachment.fileName;
        this.uploadedImages.push({ url: fileUrl });
      });
      this.cdr.detectChanges();
      this.toastService.show('File uploaded successfully.');
    } else {
      this.toastService.show('Failed to upload file.');
    }
  }

  removeImage(image: { url: string }) {
    this.uploadedImages = this.uploadedImages.filter(img => img.url !== image.url);
  }

  async createChangeRequest(form: any): Promise<any> {
    this.onClickValidation = !form.valid;

    if (!form.valid) {
      return;
    }
    const payload = {
      title: this.title || null,
      description: this.description || null,
      attachments: this.generateAttachments()
    };
    //  this.loadingService.show();
    this.isProcessing = true;
    this.dataService.createChangeProfileRequest(payload)
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          const data = response.data;
          this.handleChangeRequestsResponse(data);
        }, error: (error: any) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message || 'An error occurred');
        }
      })
  }

  private generateAttachments() {
    if (!this.uploadedImages || this.uploadedImages.length === 0) return [];

    return this.uploadedImages.map((image, index) => ({
      fileName: `image-${index + 1}`, // Generate a unique file name for each image
      mimeType: 'image/*', // Use a wildcard to accept any image type
      path: image.url, // Use the URL from the uploadedImages array
      originalName: `image-${index + 1}` // Optionally, set an original name
    }));
  }

  handleChangeRequestsResponse(data: any) {
    this.clearForm();
    this.closeChangeRequest();

    this.successPaymentDetails = data;
    this.openSuccessPopup();
  }

  openSuccessPopup() {
    this.isSuccessfulResponse = true;
  }

  closeSuccessPopup() {
    this.isSuccessfulResponse = false;
  }

  closeDetails() {
    this.closeSuccessPopup();

    // Reset offset and reload requests
    this.filter.offset = 1; // Reset offset to 1
    this.changeRequests = []; // Clear current change requests to avoid duplicates
    this.fetchMyChangeRequests(null, true); // Reload the requests    
  }

  openDetails(requestId: string) {
    this.closeSuccessPopup();

    // Reset offset and reload requests
    this.filter.offset = 1; // Reset offset to 1
    this.changeRequests = []; // Clear current change requests to avoid duplicates
    this.fetchMyChangeRequests(null, true); // Reload the requests  

    this.showChangeRequestDetail(requestId);
  }

  clearForm() {
    this.title = null;
    this.description = null;
    //  this.category = null;
    this.requestImage = null;
    this.uploadedImages = [];
  }

  ngOnDestroy(): void {
    if (this.queryParamsSubscribe) {
      this.queryParamsSubscribe.unsubscribe();
    }
  }

}
