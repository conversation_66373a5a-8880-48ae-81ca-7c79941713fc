<ion-content class="customer-dashboard-page customer-payment-page">
  <app-customer-header [innerPage]="true" [headingText]="'Profile Change Requests'" [rightAction]="true"
    [rightActionIcon]="'Plus'" (rightActionCallback)="openChangeRequest()" [backUrl]="backUrl"></app-customer-header>
  <!-- Tabs Section -->
  <div class="customer-body-section has-header-section-change-profile no-padding"
    [ngClass]="{'full-height': filter?.tabType === 'CLOSED'}">
    <div class="payment-status-items">
      <div class="payment-status-item" [ngClass]="{'active': filter?.tabType === 'OPEN'}"
        (click)="onChangeStatusTab('OPEN')">Open</div>
      <div class="payment-status-item" [ngClass]="{'active': filter?.tabType === 'CLOSED'}"
        (click)="onChangeStatusTab('CLOSED')">Closed</div>
    </div>
    <div class="payment-body-section">
      <ion-content class="payment-body-ion-content scroll">
        @if (loading==='LOADING') {
        <div class="payment-items no-padding">
          <div class="payment-item box-shadow warring">
            <div class="payment-item-container status-class status-warring">
              <div class="support-card-container">
                <div class="support-status">
                  <span class="ticket-id"><ion-skeleton-text [animated]="true"
                      style="width: 150px"></ion-skeleton-text></span>
                </div>
                <div class="support-subject-title">
                  <span><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></span>
                  <p class="support-description">
                    <ion-skeleton-text [animated]="true" style="width: 90%;height: 50px;"></ion-skeleton-text>
                  </p>
                </div>
                <div class="support-bottom-container">

                  <div class="support-category">
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="payment-item box-shadow warring">
            <div class="payment-item-container status-class status-warring">
              <div class="support-card-container">
                <div class="support-status">
                  <span class="ticket-id"><ion-skeleton-text [animated]="true"
                      style="width: 150px"></ion-skeleton-text></span>
                </div>
                <div class="support-subject-title">
                  <span><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></span>
                  <p class="support-description">
                    <ion-skeleton-text [animated]="true" style="width: 90%;height: 50px;"></ion-skeleton-text>
                  </p>
                </div>
                <div class="support-bottom-container">

                  <div class="support-category">
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="payment-item box-shadow warring">
            <div class="payment-item-container status-class status-warring">
              <div class="support-card-container">
                <div class="support-status">
                  <span class="ticket-id"><ion-skeleton-text [animated]="true"
                      style="width: 150px"></ion-skeleton-text></span>
                </div>
                <div class="support-subject-title">
                  <span><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></span>
                  <p class="support-description">
                    <ion-skeleton-text [animated]="true" style="width: 90%;height: 50px;"></ion-skeleton-text>
                  </p>
                </div>
                <div class="support-bottom-container">

                  <div class="support-category">
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        }@else if (loading==='LOADED') {
        <div class="payment-items no-padding">
          <div class="payment-item box-shadow" *ngFor="let requestList of changeRequests" [ngClass]="{'success':(requestList.status==='APPROVED') || (requestList.status==='COMPLETED'),'rejected':(requestList.status==='REJECTED'),
                'in-progress':(requestList.status==='REQUESTED'),'highlighted-payment': requestList.id === changeProfileRequestId
              }">
            <div class="payment-item-container status-class" (click)="showChangeRequestDetail(requestList.id)"
              [ngClass]="{'status-success':(requestList.status==='APPROVED') || (requestList.status==='COMPLETED'),'status-reject':(requestList.status==='REJECTED'),
                'status-in-progress':(requestList.status==='REQUESTED'),'disabled-state': isProcessing
              }">

              <div class="support-card-container">
                <div class="support-status">
                  <span class="ticket-id" *ngIf="requestList.requestId">{{requestList.requestId}}</span>
                  <span class="ticket-status" *ngIf="requestList.status" [ngClass]="{'ticket-success success-margin-left':(requestList.status==='APPROVED') || (requestList.status==='COMPLETED'),'ticket-reject':(requestList.status==='REJECTED'),
                        'ticket-in-progress progress-margin-left':(requestList.status==='REQUESTED')
                      }">
                    {{ commonService.formatText(requestList.status) }}</span>
                </div>
                <div class="support-subject-title">
                  <span *ngIf="requestList.title">{{ commonService.formatText(requestList.title) }}</span>
                  <p class="support-description" *ngIf="requestList.description">{{requestList.description.length > 100
                    ?
                    (requestList.description | slice:0:100) + '...' : requestList.description }}</p>
                  <img *ngIf="requestList?.attachments?.length > 0" [src]="requestList?.attachments[0]?.path"
                    alt="Pan Card" class="">
                </div>
              </div>
            </div>
          </div>

          <!-- <div class="payment-item margin-top-10" *ngIf="changeRequests.length<=0">
            <div class="no-record" *ngIf="filter.tabType==='OPEN' || filter.tabType==='CLOSED'">No Data Available.</div>
          </div> -->
          <div class="customer-body-section no-height" *ngIf="changeRequests.length <= 0">
            <div class="no-bookings-container" *ngIf="filter.tabType==='OPEN' || filter.tabType==='CLOSED'">
              <div class="no-bookings-icon">
                <ion-icon name="alert-circle-outline"></ion-icon>
              </div>
              <h2 class="no-bookings-title">No Data Available</h2>
            </div>
          </div>
        </div>

        <ion-infinite-scroll threshold="50px" *ngIf="changeRequests.length > 0" (ionInfinite)="onPageChanged($event)">
          <ion-infinite-scroll-content loadingSpinner="bubbles"
            loadingText="Loading more data..."></ion-infinite-scroll-content>
        </ion-infinite-scroll>
        }
      </ion-content>
    </div>
  </div>
</ion-content>

<!-- create change request modal -->
<ion-modal class="site-custom-popup job-invitation-popup" #isChangeRequestPopup [isOpen]="isChangeProfileRequest"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeChangeRequest()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">

        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">Create Request</div>
          <div class="job-info margin-top-10">

            <div class="">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': !sub.valid && onClickValidation}">
                <ion-input label="Title" labelPlacement="floating" name="sub" #sub="ngModel" [(ngModel)]="title"
                  required="required" maxlength="50">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="sub" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
            </div>

            <div class="margin-top-10">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid':!desc.valid && onClickValidation}">
                <ion-textarea label="Description" labelPlacement="floating" name="desc" #desc="ngModel"
                  [(ngModel)]="description" required="required">
                </ion-textarea>
              </ion-item>
              <app-validation-message [field]="desc" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
            </div>

            <!-- Customer Request Image Section -->
            <div class="document-items">
              <!-- Upload Button -->
              <div class="document-upload-container" (click)="upload()"
                [ngClass]="{'missing-image': showValidationErrors && uploadedImages.length === 0}">
                <img src="/assets/images/svg/attach.svg" alt="Upload Icon" class="upload-icon">
                <div class="upload-text"
                  [ngClass]="{'missing-image': showValidationErrors && uploadedImages.length === 0}">
                  <span class="upload-adhar">Attachments</span>
                </div>
              </div>

              <!-- Uploaded Images -->
              <div class="document-upload-container has-image" *ngFor="let image of uploadedImages"
                (click)="commonService.viewImage(image.url)">
                <img [src]="image.url" alt="Uploaded Document" class="uploaded-image">
                <div class="delete-icon" *ngIf="image" (click)="removeImage(image)">
                  <img src="/assets/images/svg/delete.svg" alt="Delete Icon">
                </div>
              </div>
            </div>

            <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
              type="submit" [disabled]="isProcessing" (click)="createChangeRequest(recordForm.form)">
              Create Request
            </ion-button>

          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<!-- change request details modal -->
<ion-modal class="site-custom-popup job-invitation-popup" #changeRequestPopup [isOpen]="isChangeRequestDetailOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container min-height">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeChangeRequestDetailPopup()"></i-feather>
        <div class="header-text">Profile Change Request Detail</div>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">{{ changeRequestDetail.title }}</div>
          <div class="payment-items no-padding">
            <div class="payment-item box-shadow" [ngClass]="{'success':(changeRequestDetail.status==='APPROVED') || (changeRequestDetail.status==='COMPLETED'),'rejected':(changeRequestDetail.status==='REJECTED'),
              'in-progress':(changeRequestDetail.status==='REQUESTED')
            }">
              <div class="payment-item-container status-class no-bg-color">
                <div class="support-card-container">
                  <div class="support-status">
                    <span class="ticket-id"
                      *ngIf="changeRequestDetail.requestId">{{changeRequestDetail.requestId}}</span>
                    <span class="ticket-status" *ngIf="changeRequestDetail.status" [ngClass]="{'ticket-success success-margin-left':(changeRequestDetail.status==='APPROVED') || (changeRequestDetail.status==='COMPLETED'),'ticket-reject':(changeRequestDetail.status==='REJECTED'),
                      'ticket-in-progress progress-margin-left':(changeRequestDetail.status==='REQUESTED')
                    }">
                      {{ commonService.formatText(changeRequestDetail.status) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
        <div class="popup-normal-heading secondary-text">{{ changeRequestDetail.description }}</div>
        <div class="resolution-con margin-top-20">
          <div class="title"
            *ngIf="changeRequestDetail.remark || (changeRequestDetail.remark && changeRequestDetail.attachments && changeRequestDetail.attachments.length > 0)">
            Remark
          </div>
          <div class="title"
            *ngIf="!changeRequestDetail.remark && changeRequestDetail.attachments && changeRequestDetail.attachments.length > 0">
            Attachments
          </div>
          <div class="desc" *ngIf="changeRequestDetail.remark">{{ changeRequestDetail.remark }}</div>
          <div class="image-grid" *ngIf="changeRequestDetail.attachments && changeRequestDetail.attachments.length > 0">
            <div class="image-item" *ngFor="let image of changeRequestDetail.attachments"
              (click)="commonService.viewImage(image.path)">
              <img [src]="image.path" alt="Image" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #isSuccessPopup [isOpen]="isSuccessfulResponse"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-body no-padding-top">
        <div class="campaign-card">
          <div class="campaign-card-header success-modal-card-header">
            <div class="campaign-image-slide success-modal-card-image-slide">
              <i-feather name="X" class="close-icon white-icon" (click)="closeDetails()"></i-feather>
            </div>
          </div>
          <div class="coin-container request-coin-container">
            <ion-icon class="balance-icon request-bal-coin"
              [src]="'/assets/images/svg/create-request-icon.svg'"></ion-icon>
          </div>
          <div class="campaign-detail-container">
            <span class="campaign-detail-title success-text margin-top-10">Request Created</span>

            <div class="success-container margin-top-10" *ngIf="successPaymentDetails">
              <span>Profile update request<strong> {{successPaymentDetails?.requestId}} </strong> submitted!</span>

              <span class="earned-my-cash margin-top-5">
                We will notify you once it is <strong>approved!</strong>
              </span>
            </div>
          </div>

          <div class="detail-button margin-top-10" (click)="openDetails(successPaymentDetails?.id)">
            <span class="detail-text">View Detail</span>
          </div>

          <!-- <div class="privacy-container margin-top-20">
            <ion-button class="site-full-rounded-button less-border-radius margin-left-20 margin-right-20" expand="full"
              shape="round" type="submit" (click)="openDetails(successPaymentDetails?.id)">
              View Detail
            </ion-button>
          </div> -->
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>