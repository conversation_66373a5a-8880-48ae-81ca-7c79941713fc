import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NavController } from '@ionic/angular';
import { DatePicker } from '@pantrist/capacitor-date-picker';
import { PackageData } from 'src/modals/customerShowPackageDetails';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-my-subscription',
  templateUrl: './my-subscription.component.html',
  styleUrls: ['./my-subscription.component.scss'],
  providers: [DatePipe]
})
export class MySubscriptionComponent implements OnInit {

  packageDetail: PackageData | null = null; // Make sure to use PackageData type
  userId: string = '';
  user: any;
  loading: string = "NOT_STARTED";
  isOfferDetailPopupOpen: boolean = false;
  isCancelOfferPopupOpen: boolean = false;
  isNoOnboarding: boolean = false;
  locationList: any;
  selectedLocation: any;
  title: any;
  packageTitle: any;
  packageTitleData: any;
  selectedMonth: any;
  monthList: any;
  onClickValidation: boolean = false;
  passportDetail: any;
  customerNotesData: string | null = null;
  // Month names array
  monthNames: string[] = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  searchLocation: string | undefined;
  availablePlaces: Array<any> = new Array<any>();
  selectedInput: any;
  // isLocationSelectionPopupOpen: boolean = false;

  packageId: any;
  balanceNights: any;
  endDate: any;
  showHolidayExpireButton: boolean = false;
  isCurrentYearHolidayExpirePopupOpen: boolean = false;
  isProcessing: boolean = false;

  passportNumberMask = this.commonService.passportMask;
  maskPredicate = this.commonService.maskPredicate;

  constructor(
    private readonly dataService: DataService,
    private readonly navController: NavController,
    private authService: AuthService,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    public readonly commonService: CommonService,
    private readonly datePipe: DatePipe,
    private readonly localStorageService: LocalStorageService,
    private router: Router
  ) {

  }

  ngOnInit() {
    this.user = this.authService.getUser();
    // if (!this.user) {
    //   return;
    // }
  };

  ionViewWillEnter() {
    this.user = this.authService.getUser();
    if (this.user) {
      this.fetchMyActiveSubscription();
      return;
    }
    else {
      let currentUrl = this.router.url;
      this.localStorageService.set('redirectAfterLogin', currentUrl);
      this.commonService.openLoginModal();
      return;
    }

  }

  fetchAvailableMonthsLocation($event?: any) {
    let locationId = this.packageDetail!.soldFullyPaidHolidayOfferDetail!.location;
    this.loading = "LOADING";
    //  this.loadingService.show();
    this.dataService.fetchAvailableMonthsLocation(locationId).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        //  this.loadingService.hide();
        this.monthList = response.data;
        // Process JSON data and map months
        this.monthList = response.data.map((item: any) => ({
          ...item,
          monthName: this.monthNames[item.month - 1] // Convert month number to name
        }));
        // this.handleSupportListResponse(response, $event);
      },
      error: (error: any) => {
        this.loading = "LOADED";
        //  this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');

        // Complete the scroll event in case of an error
        if ($event) {
          $event.target.complete();
        }
      },
    });
  }

  async submitForm(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !this.isValidBasicRequest(form)) {
      return;
    }

    const payload: any = {};

    if (this.packageDetail && this.packageDetail.offerType === 'OFFER') {
      payload.offerId = this.packageDetail.offersDetail.id;
      payload.description = this.customerNotesData;
    } else {
      Object.assign(payload, {
        locationMonth: this.selectedMonth,
        soldFullyPaidHoliday: this.packageDetail!.soldFullyPaidHolidayOfferDetail!.id,
        customerDetail: {
          userProfileDetail: {
            passportNumber: this.passportDetail.passportNumber || null,
            passportExpiryDate: this.passportDetail.passportExpiryDate || null,
          }
        },
        description: this.customerNotesData,
      });
    }

    //  this.loadingService.show();
    this.isProcessing = true;
    if (this.packageDetail && this.packageDetail.offerType === 'FPH') {
      this.dataService.fphRedeemRequest(payload)
        .subscribe({
          next: (response: RestResponse) => {
            //  this.loadingService.hide();
            this.isProcessing = false;

            this.customerNotesData = "";
            this.isOfferDetailPopupOpen = false;
            this.selectedMonth = "";
            this.passportDetail = {
              passportNumber: "",
              passportExpiryDate: null,
            };
            this.packageDetail!.soldFullyPaidHolidayOfferDetail.supportTicketId = response.data.supportTicketId;
            this.packageDetail!.isOfferAvailable = response.data.isOfferAvailable;
            this.packageDetail!.soldFullyPaidHolidayOfferDetail.supportTicketStatus = response.data.supportTicketStatus;
            this.packageDetail!.isRedeemProcessStart = response.data.isRedeemProcessStart;
            this.toastService.show('FPH redeem request has been submitted');

            this.fetchMyActiveSubscription();
          }, error: (error: any) => {
            //  this.loadingService.hide();
            this.isProcessing = false;
            this.toastService.show(error.message || 'An error occurred');
          }
        })
    }

    if (this.packageDetail && this.packageDetail.offerType === 'OFFER') {
      this.dataService.offerRedeemRequest(payload)
        .subscribe({
          next: (response: RestResponse) => {
            this.loadingService.hide();
            this.customerNotesData = "";
            this.isOfferDetailPopupOpen = false;
            this.passportDetail = {
              passportNumber: "",
              passportExpiryDate: null,
            };
            this.packageDetail!.offersDetail.supportTicketId = response.data.supportTicketId;
            this.packageDetail!.isOfferAvailable = response.data.isOfferAvailable;
            this.packageDetail!.offersDetail.supportTicketStatus = response.data.supportTicketStatus;
            this.packageDetail!.isRedeemProcessStart = response.data.isRedeemProcessStart;
            this.toastService.show('OFFER redeem request has been submitted');

            this.fetchMyActiveSubscription();
          }, error: (error: any) => {
            this.loadingService.hide();
            this.toastService.show(error.message || 'An error occurred');
          }
        })
    }
  }

  isValidBasicRequest(form: any) {
    if (!this.packageTitleData || this.packageTitleData.trim() === '') {
      form.controls.packageTitleData.setErrors({ invalid: true });
      return false;
    }

    if (this.packageDetail && this.packageDetail.offerType == 'FPH') {
      if (!this.selectedMonth || this.selectedMonth.trim() === '') {
        form.controls.selectedMonth.setErrors({ invalid: true });
        return false;
      }
    }

    if (!this.customerNotesData || this.customerNotesData.trim() === '') {
      form.controls.customerNotesData.setErrors({ invalid: true });
      return false;
    }

    return true;
  }

  fetchMyActiveSubscription() {
    this.loading = "LOADING";
    this.dataService.fetchMyActiveSubscription()
      .subscribe({
        next: (response: RestResponse) => {
          this.loading = "LOADED";
          this.packageDetail = response.data;
          this.localStorageService.setObject("activeSubscriptionData", response.data);
          if (this.packageDetail) {
            this.localStorageService.setObject('firstTimePackagePurchasing', false);
          }
          this.packageTitleData = (this.packageDetail && this.packageDetail.offerType == 'FPH') ? this.packageDetail?.soldFullyPaidHolidayOfferDetail!.title : this.packageDetail?.offersDetail?.title;
          this.passportDetail = {
            passportNumber: this.packageDetail?.userDetail?.userProfileDetail?.passportNumber,
            passportCountry: this.packageDetail?.userDetail?.userProfileDetail?.passportCountry,
            passportIssueDate: this.packageDetail?.userDetail?.userProfileDetail?.passportIssueDate,
            passportExpiryDate: this.packageDetail?.userDetail?.userProfileDetail?.passportExpiryDate,
          };
          this.packageId = this.packageDetail?.id;
          this.handleholidayExpireData();
          // if (this.packageDetail && this.packageDetail.offerType === 'FPH') {
          //   this.fetchAvailableMonthsLocation();
          // }
        },
        error: (error: any) => {
          this.loading = "LOADED";
        }
      })
  }

  handleholidayExpireData() {
    this.balanceNights = this.packageDetail?.currentYearDetails.nightsAllowed - this.packageDetail?.currentYearDetails.nightsUsed;
    this.endDate = new Date(this.packageDetail?.currentYearDetails.endDate).toISOString().split('T')[0];

    const shouldOpenPopup = this.commonService.expireHolidays(this.packageDetail, this.balanceNights);

    if (shouldOpenPopup) {
      this.showHolidayExpireButton = true;
    } else {
      this.showHolidayExpireButton = false;
    }
  }

  openCurrentYearHolidayExpirePopup() {
    this.isCurrentYearHolidayExpirePopupOpen = true;
  }

  closeCurrentYearHolidayExpirePopup() {
    this.isCurrentYearHolidayExpirePopupOpen = false;
  }

  carryForwardHolidays() {
    this.dataService.carryForwardHolidays(this.packageId).subscribe({
      next: (response: RestResponse) => {
        this.fetchMyActiveSubscription();
      },
      error: (error: any) => {
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  lapseHolidays() {
    this.dataService.lapseHolidays(this.packageId).subscribe({
      next: (response: RestResponse) => {
        this.fetchMyActiveSubscription();
      },
      error: (error: any) => {
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

  openPackages() {
    this.navController.navigateForward("/portal/my/packages", { animated: true });
  }

  back() {
    this.navController.navigateBack("/dashboard", { animated: true });
  }

  backToSettings() {
    this.navController.navigateForward("/account1", { animated: true });
  }

  explorePlan() {
    this.localStorageService.setObject('fromMembershipPage', true);
    this.navController.navigateForward("account/register/package/selection", { animated: true });
  }

  openOfferDetailPopup() {
    this.isOfferDetailPopupOpen = true;
    this.onClickValidation = false;
    this.selectedMonth = null;
    this.customerNotesData = null;

    // Ensure passportDetail exists before attempting to set its properties
    if (!this.passportDetail) {
      this.passportDetail = {}; // Initialize if undefined
    }

    // Clear passportNumber and passportExpiryDate if not provided in the package details
    this.passportDetail.passportNumber = this.packageDetail?.userDetail?.userProfileDetail?.passportNumber || null;
    this.passportDetail.passportExpiryDate = this.formatDateOfBirth(this.packageDetail?.userDetail?.userProfileDetail?.passportExpiryDate || null);
  }

  formatDateOfBirth(date: string | null): string {
    return this.datePipe.transform(date, 'yyyy-MM-dd') || '';
  }

  closeOfferDetailPopup() {
    this.isOfferDetailPopupOpen = false;
    this.onClickValidation = false; // Reset validation state
  }

  openCancelOfferPopup() {
    this.isCancelOfferPopupOpen = true;
  }

  closeCancelOfferPopup() {
    this.isCancelOfferPopupOpen = false;
  }

  openDatePickerForExpiryDate() {
    const today = new Date();
    const formattedToday = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1)
      .toString()
      .padStart(2, '0')}/${today.getFullYear()}`;

    // Use the previously selected date if available, otherwise default to today
    const defaultDate = this.passportDetail?.passportExpiryDate
      ? new Date(this.passportDetail.passportExpiryDate)
      : today;

    const formattedDefaultDate = `${defaultDate.getDate().toString().padStart(2, '0')}/${(defaultDate.getMonth() + 1)
      .toString()
      .padStart(2, '0')}/${defaultDate.getFullYear()}`;

    DatePicker.present({
      mode: 'date',
      format: 'dd/MM/yyyy',
      min: formattedToday, // Restrict to today and future dates
      date: formattedDefaultDate, // Default to the previously selected date or today
    })
      .then((date) => {
        if (date && date.value) {
          const [day, month, year] = date.value.split('/');
          this.passportDetail.passportExpiryDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
      })
      .catch((error) => {
        console.error('Date Picker Error:', error);
      });
  }

  async cancelRedeemRequest(): Promise<any> {
    const payload: any = {};

    if (this.packageDetail && this.packageDetail.offerType === 'OFFER') {
      payload.id = this.packageDetail?.offersDetail.supportTicketId
    } else {
      // payload.id = this.packageDetail?.soldFullyPaidHolidayOfferDetail.supportTicketId 
      Object.assign(payload, {
        id: this.packageDetail?.soldFullyPaidHolidayOfferDetail.supportTicketId,
      });
    }

    //  this.loadingService.show();
    this.isProcessing = true;

    if (this.packageDetail && this.packageDetail.offerType === 'FPH') {
      this.dataService.cancelRedeemRequest(payload)
        .subscribe({
          next: (response: RestResponse) => {
            //  this.loadingService.hide();
            this.isProcessing = false;

            this.isCancelOfferPopupOpen = false;
            this.packageDetail!.soldFullyPaidHolidayOfferDetail.supportTicketStatus = 'OPEN';
            this.packageDetail!.isRedeemProcessStart = response.data.isRedeemProcessStart;
            this.packageDetail!.isOfferAvailable = response.data.isOfferAvailable;
            this.toastService.show('FPH redeem cancel request has been submitted');

            this.fetchMyActiveSubscription();
          }, error: (error: any) => {
            //  this.loadingService.hide();
            this.isProcessing = false;
            this.toastService.show(error.message || 'An error occurred');
          }
        })
    }

    if (this.packageDetail && this.packageDetail.offerType === 'OFFER') {
      this.dataService.cancelRedeemRequest(payload)
        .subscribe({
          next: (response: RestResponse) => {
            this.loadingService.hide();
            this.isCancelOfferPopupOpen = false;
            this.packageDetail!.offersDetail.supportTicketStatus == 'OPEN';
            this.packageDetail!.isRedeemProcessStart = response.data.isRedeemProcessStart;
            this.packageDetail!.isOfferAvailable = response.data.isOfferAvailable;
            this.toastService.show('OFFER redeem cancel request has been submitted');

            this.fetchMyActiveSubscription();
          }, error: (error: any) => {
            this.loadingService.hide();
            this.toastService.show(error.message || 'An error occurred');
          }
        })
    }
  }

  openRedeemRequstList() {
    if (this.packageDetail && this.packageDetail.offerType === 'FPH') {
      this.navController.navigateForward("/portal/redeem/gift/list", {
        animated: true, queryParams: {
          offerId: this.packageDetail!.packageDetail.id
        }
      });
    } else {
      this.navController.navigateForward("/portal/redeem/gift/list", {
        animated: true, queryParams: {
          offerId: this.packageDetail!.offersDetail.id
        }
      });
    }
  }


  openEMIDetail() {
    this.navController.navigateForward("/portal/emi/details", { animated: true });
  }

  purchasePackage() {

  }
  GoToMembership(bookingType: string) {
    if (!this.user?.isOnBoardingComplete) {
      this.openNoOnboardingPopup();
      return;
    }
    this.navController.navigateForward('/portal/search/hotels', { animated: true, queryParams: { bookingType: bookingType } });
  }

  openNoOnboardingPopup() {
    this.isNoOnboarding = true;
  }

  closeNoOnboardingPopup() {
    this.isNoOnboarding = false;
  }

  continueToOnboarding() {
    this.closeNoOnboardingPopup();
    setTimeout(() => {
      this.navController.navigateForward(`/account/register/basic`, {
        state: {
          fromScreen: 'subscription'
        }, animated: true
      });
    }, 200);
  }

}
