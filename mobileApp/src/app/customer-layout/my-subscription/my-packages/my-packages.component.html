<ion-content class="customer-dashboard-page booking-hotel-detail-page ">
  <app-customer-header [innerPage]="true" [membershipPage]="true" [rightAction]="true"
    [headingText]="'My Packages'"></app-customer-header>
  <div class="ion-cutomer-container onboarding-page-container no-padding-bottom">
    <div class="ion-text-left">
      @if (loading==='LOADING') {
      <div class="my-booking-card-container">
        <div class="my-booking-card margin-top-10">
          <ion-grid>
            <ion-row>
              <ion-col>
                <ion-row class="check-in-out-con my-bookings-check">
                  <ion-col size="8" class="my-booking-code ion-no-padding">
                    <div class="my-booking-text">
                      <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                    </div>
                  </ion-col>
                  <ion-col size="4" class="my-booking-status">
                    <div class="my-booking-text">
                      <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                    </div>
                  </ion-col>
                </ion-row>

                <div>
                  <div class="hotel-name my-bookings-hotel-name">
                    <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                  </div>
                  <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                      style="width: 200px"></ion-skeleton-text></div>
                </div>
              </ion-col>
            </ion-row>

            <ion-row class="check-in-out-con">
              <ion-col size="5">
                <div class="check-in-con">
                  <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text></div>
                  <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text></div>
                </div>
              </ion-col>
              <ion-col size="2">
                <div class="time-duration-con">
                  <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                  <!-- <ion-skeleton-text [animated]="true"
                          style="width: 20px;height: 20px"></ion-skeleton-text> -->
                  <div class="time-duration">
                    <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                  </div>
                </div>
              </ion-col>
              <ion-col size="5">
                <div class="check-out-con">
                  <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                  </div>
                  <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text></div>
                </div>
              </ion-col>
            </ion-row>

            <!-- Show total adults per booking -->
            <ion-row>
              <ion-col>
                <div class="guest-and-rooms-con">
                  <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                  </div>
                  <div class="rooms">
                    <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                  </div>
                </div>
              </ion-col>
            </ion-row>

          </ion-grid>
        </div>
      </div>
      }@else if (loading==='LOADED') {
      <div class="package-card-container max-height-package margin-top-10">
        <div class="no-data-available" *ngIf="packages.length === 0">
          No Package Available
        </div>
        <!-- Package Details Card  -->
        <!-- <ion-radio-group [(ngModel)]="selectedPackageId" name="packageSelection" (ionChange)="onPackageChange($event)"> -->
        <div class="package-card-without-gradient" [ngClass]="{'highlighted-payment': selectedPackageId ===package.id}"
          *ngFor="let package of packages" (click)="onPackageChange(package.id)">
          <div class="card-header package-item">
            <div class="card-header-content package-selection-label">
              <span
                [ngClass]="selectedPackageId ===package.id ? 'card-title': 'card-title-color'">{{package?.packageDetail.title}}</span>
            </div>
            <!-- <div class="package-selection-checkbox" >
                <ion-radio [value]="package?.id"></ion-radio>
                <span class="custom-checkbox" *ngIf="selectedPackageId === package.id">&#10003;</span>
              </div> -->
          </div>
          <div class="card-body">
            <div class="package-details">
              <div class="package-detail-item">
                <span class="detail-label">Tenure</span>
                <span class="detail-value">{{package?.packageDetail.tenure}} Years</span>
              </div>

              <div class="package-detail-item">
                <span class="detail-label">Package Nights</span>
                <span class="detail-value">{{package?.packageDetail.totalNights}}</span>
              </div>

              <div class="package-detail-item">
                <span class="detail-label">Package Price</span>
                <span class="detail-value">{{ package.totalAmount | currency :'INR':'symbol':'1.0-0' }}</span>
              </div>
            </div>
            <!-- <hr class="separator-line" *ngIf="package.offersDetail" />
            <div class="additional-info" *ngIf="package.offersDetail">
              <img
                [src]="selectedPackageId ===package.id ? 'assets/images/svg/offer.svg' : 'assets/images/svg/offer-blue.svg'"
                alt="Offer" class="offer-image" />
              <span class="info-common" [ngClass]="selectedPackageId ===package.id ? 'info-text':'info-color'">
                You will get an <strong>{{ package.offerDetails?.description ? (package.offerDetails?.description |
                  uppercase) : 'OFFER' }}</strong>
                worth
                {{package.offerAmount| currency :'INR':'symbol':'1.0-0'}} by just adding {{package.baseAmount |
                currency :'INR':'symbol':'1.0-0' }} to your
                package
              </span>
            </div> -->
          </div>
        </div>
        <!-- </ion-radio-group> -->
      </div>
      <div class="compaign-list-button-container margin-bottom-10">
        <ion-button class="site-full-rounded-button add-money-button margin-top-8" expand="full" shape="round"
          type="submit" (click)="setAsDefaultPackage(previousPackageId)">
          Select Package
        </ion-button>
      </div>
      }
    </div>
  </div>

</ion-content>