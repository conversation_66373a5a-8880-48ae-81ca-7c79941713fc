import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-my-packages',
  templateUrl: './my-packages.component.html',
  styleUrls: ['./my-packages.component.scss'],
})
export class MyPackagesComponent implements OnInit {
  packages: Array<any> = new Array<any>();
  packageId: string = "";
  setAsDefaultModalOpen: boolean = false;
  loading: string = "NOT_STARTED";
  previousPackageId: string = "";
  selectedPackageId: string = "";
  isSelected : boolean=false
  constructor(
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private readonly navController: NavController
  ) { }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.fetchMyPackages();
  }

  fetchMyPackages() {
    this.loading = "LOADING";
    this.dataService.myPackages().subscribe({
      next: (response: RestResponse) => {
        this.packages = response.data;
        const defaultPackage = this.packages.find(pkg => pkg.isDefault);
        if (defaultPackage) {
          this.selectedPackageId = defaultPackage.id;
          this.previousPackageId = this.selectedPackageId;
        }
        this.loading = "LOADED";
      },
      error: (error: any) => {
        this.toastService.show(error.message);
        this.loading = "LOADED";
      },
    });
  }

  setAsDefaultPackage(packageId: string) {
    this.previousPackageId = packageId;
    this.loading = "LOADING";
    this.dataService.setAsDefaultPackage(this.previousPackageId).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.selectedPackageId = this.previousPackageId;
        this.navController.navigateForward("/membership", { animated: true });
      },
      error: (error: any) => {
        this.toastService.show(error.message);
      },
    });
  }

  onPackageChange(id: any) {
    this.selectedPackageId = id;
    this.previousPackageId = this.selectedPackageId;
    this.isSelected =true;
  }

  // closeModal() {
  //   this.selectedPackageId = this.previousPackageId;
  //   this.setAsDefaultModalOpen = false;
  // }
}
