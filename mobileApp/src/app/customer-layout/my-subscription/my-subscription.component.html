<ion-content class="customer-dashboard-page my-membership-page">
  <app-customer-header [rightAction]="false" (rightActionCallback)="openNotifications()" [membershipPage]="true"
    [packageDetail]="packageDetail" (packageActionCallback)="openPackages()"></app-customer-header>
  <div class="customer-body-section has-header-section no-padding" [ngClass]="{'background-white':packageDetail}">
    <div class="my-membership-container" *ngIf="loading==='LOADING'">
      <div class="my-booking-card-container">
        <div class="my-booking-card margin-top-10">
          <ion-grid>
            <ion-row>
              <ion-col>
                <ion-row class="check-in-out-con my-bookings-check">
                  <ion-col size="8" class="my-booking-code ion-no-padding">
                    <div class="my-booking-text">
                      <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                    </div>
                  </ion-col>
                  <ion-col size="4" class="my-booking-status">
                    <div class="my-booking-text">
                      <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                    </div>
                  </ion-col>
                </ion-row>

                <div>
                  <div class="hotel-name my-bookings-hotel-name">
                    <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                  </div>
                  <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                      style="width: 200px"></ion-skeleton-text></div>
                </div>
              </ion-col>
            </ion-row>

            <ion-row class="check-in-out-con">
              <ion-col size="5">
                <div class="check-in-con">
                  <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text></div>
                  <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text></div>
                </div>
              </ion-col>
              <ion-col size="2">
                <div class="time-duration-con">
                  <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                  <!-- <ion-skeleton-text [animated]="true"
                        style="width: 20px;height: 20px"></ion-skeleton-text> -->
                  <div class="time-duration">
                    <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                  </div>
                </div>
              </ion-col>
              <ion-col size="5">
                <div class="check-out-con">
                  <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                  </div>
                  <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text></div>
                </div>
              </ion-col>
            </ion-row>

            <!-- Show total adults per booking -->
            <ion-row>
              <ion-col>
                <div class="guest-and-rooms-con">
                  <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                  </div>
                  <div class="rooms">
                    <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                  </div>
                </div>
              </ion-col>
            </ion-row>

          </ion-grid>
        </div>
      </div>
    </div>
    <div class="my-membership-container" *ngIf="!packageDetail && loading==='LOADED'"
      [ngStyle]="{'--package-color': '#B48429'}">
      <div class="new-holiday-selection-container margin-top-20">
        <div class="membership-holiday-selection-card">
          <div class="package-heading-container no-membership-container">
            <div class="membership-text">
              <div class="membership-package-name no-membership-text">Oops! No Membership Found</div>
            </div>
          </div>
          <div class="ion-padding">
            <ion-button class="site-full-rounded-button no-membership-button primary-button margin-top-20" expand="full"
              shape="round" (click)="explorePlan()">
              Explore Membership Plans
            </ion-button>
          </div>
        </div>
      </div>
    </div>
    <div class="my-membership-container" *ngIf="packageDetail && loading==='LOADED'"
      [ngStyle]="{'--package-color': packageDetail.packageDetail.color}">

      <div class="new-holiday-selection-container margin-top-10">
        <div class="membership-holiday-selection-card">
          <div class="package-heading-container">
            <div class="membership-text">
              <div class="membership-package-name">{{ packageDetail.packageDetail.title }}</div>
              <!-- <div class="membership-puch-line-text">{{ packageDetail.packageDetail
                .description!.length > 30 ? packageDetail.packageDetail
                .description?.slice(0, 30) + '...' : packageDetail.packageDetail
                .description }}</div> -->
            </div>
            <div class="package-icon-container">
              <ion-icon class="package-icon" src="assets/images/svg/membership-icon.svg"></ion-icon>
            </div>
          </div>

          <div class="offer-container" *ngIf="packageDetail.offerType == 'FPH'">
            <div>
              <span
                [ngClass]="{'redeem-btn-not-underline':packageDetail.isOfferAvailable,'redeem-btn':!packageDetail.isOfferAvailable}"
                class="redeem-btn"
                *ngIf="packageDetail.isOfferAvailable && packageDetail.soldFullyPaidHolidayOfferDetail.supportTicketStatus!='OPEN'"
                (click)="openOfferDetailPopup()">
                Redeem <span>{{packageDetail.offerType}}</span>
              </span>
              <span>
                <span class="redeem-sent-btn" *ngIf="packageDetail.offerClaimed">
                  {{packageDetail.offerType}} is Claimed
                </span>

                <!-- <div style="width: 1px; height: 12px; background-color: #ddd;
                  margin: 0 9px;"></div> -->

                <span class="redeem-sent-btn">
                  <span
                    *ngIf="(packageDetail.isRedeemProcessStart  || packageDetail.soldFullyPaidHolidayOfferDetail.supportTicketStatus==='OPEN') &&  !packageDetail.offerClaimed">FPH
                    redeem request is
                    in
                    progress</span>
                  <span class="no-offer"
                    *ngIf="!packageDetail.isOfferAvailable && !packageDetail.isRedeemProcessStart && !packageDetail.offerClaimed">
                    Currently,the FPH is not available.
                  </span>
                  <div class="cancel-view-redeem-container margin-top-8">
                    <div
                      *ngIf="(!packageDetail.offerClaimed && !(packageDetail?.soldFullyPaidHolidayOfferDetail?.isAmountAdded)) && (packageDetail.isRedeemProcessStart  || packageDetail.soldFullyPaidHolidayOfferDetail.supportTicketStatus==='OPEN')"
                      (click)="openCancelOfferPopup()" class="cancel-request-btn">Cancel your request</div>

                    <!-- View Redeem Requests (When Cancel Request is shown) -->
                    <span
                      *ngIf="packageDetail.isSupportTicketAvailable && (!packageDetail.offerClaimed && !(packageDetail?.soldFullyPaidHolidayOfferDetail?.isAmountAdded)) && (packageDetail.isRedeemProcessStart  || packageDetail.soldFullyPaidHolidayOfferDetail.supportTicketStatus==='OPEN')"
                      class="btn-body" (click)="openRedeemRequstList()">
                      <span class="view-detail-font-style">View Redeem Requests</span>
                    </span>

                    <!-- View Redeem Requests (When Cancel Request is NOT shown) -->
                    <span
                      *ngIf="packageDetail.isSupportTicketAvailable && !((!packageDetail.offerClaimed && !(packageDetail?.soldFullyPaidHolidayOfferDetail?.isAmountAdded)) && (packageDetail.isRedeemProcessStart  || packageDetail.soldFullyPaidHolidayOfferDetail.supportTicketStatus==='OPEN'))"
                      class="btn-body-alt margin-top-8" (click)="openRedeemRequstList()">
                      <span class="view-detail-font-style">View Redeem Requests</span>
                    </span>
                  </div>
                </span>
              </span>

            </div>
          </div>

          <div class="offer-container" *ngIf="packageDetail.offerType == 'OFFER'">
            <div>
              <span
                [ngClass]="{'redeem-btn-not-underline':packageDetail.isOfferAvailable,'redeem-btn':!packageDetail.isOfferAvailable}"
                *ngIf="packageDetail.isOfferAvailable && packageDetail.offersDetail?.supportTicketStatus!='OPEN'"
                (click)="openOfferDetailPopup()">
                Redeem <span>{{packageDetail.offerType}}</span>
              </span>

              <span>
                <span class="redeem-sent-btn" *ngIf="packageDetail.offerClaimed">
                  {{packageDetail.offerType}} is Claimed
                </span>

                <span class="redeem-sent-btn">
                  <span *ngIf="packageDetail.isRedeemProcessStart">OFFER redeem request is in progress</span>
                  <span class="no-offer"
                    *ngIf="!packageDetail.isOfferAvailable && !packageDetail.isRedeemProcessStart && !packageDetail.offerClaimed">
                    Currently, the offer is not available.
                  </span>
                  <div class="cancel-view-redeem-container margin-top-5">
                    <div
                      *ngIf="!packageDetail.offerClaimed && (packageDetail.isRedeemProcessStart || packageDetail?.offersDetail?.supportTicketStatus==='OPEN')"
                      (click)="openCancelOfferPopup()" class="cancel-request-btn">Cancel your request</div>

                    <!-- View Redeem Requests (When Cancel Request is shown) -->
                    <span
                      *ngIf="packageDetail.isSupportTicketAvailable && (!packageDetail.offerClaimed && (packageDetail.isRedeemProcessStart || packageDetail?.offersDetail?.supportTicketStatus==='OPEN'))"
                      class="btn-body" (click)="openRedeemRequstList()">
                      <span class="view-detail-font-style">View Redeem Requests</span>
                    </span>

                    <!-- View Redeem Requests (When Cancel Request is NOT shown) -->
                    <span
                      *ngIf="packageDetail.isSupportTicketAvailable && !( !packageDetail.offerClaimed && (packageDetail.isRedeemProcessStart || packageDetail?.offersDetail?.supportTicketStatus==='OPEN') )"
                      class="btn-body-alt margin-top-8" (click)="openRedeemRequstList()">
                      <span class="view-detail-font-style">View Redeem Requests</span>
                    </span>
                  </div>
                </span>
              </span>
            </div>
          </div>

          <div class="new-overall-night-detail margin-top-10">
            <div class="overall-night-text">Overall Allowed Nights</div>
          </div>
          <div class="new-membership-holiday-balance-container">
            <div class="membership-holiday-balance-item">
              <div class="balance-count">{{packageDetail.packageDetail.totalNights}}</div>
              <div class="balance-title">Total</div>
            </div>
            <div class="membership-holiday-balance-item">
              <div class="balance-count">{{packageDetail.packageDetail.indianNights}}</div>
              <div class="balance-title">Indian</div>
            </div>
            <div class="membership-holiday-balance-item">
              <div class="balance-count">{{packageDetail.packageDetail.asianNights}}</div>
              <div class="balance-title">Asian</div>
            </div>
            <div class="membership-holiday-balance-item">
              <div class="balance-count">{{packageDetail.packageDetail.internationalNights}}</div>
              <div class="balance-title">WorldWide</div>
            </div>
          </div>

          <div class="new-overall-night-detail">
            <div class="overall-night-text">Overall Balance Nights</div>
          </div>
          <div class="new-membership-holiday-balance-container no-maring-top">
            <div class="membership-holiday-balance-item bal-current-year">
              <div class="balance-count">{{packageDetail.currentYearDetails.indianNightsUsed}}</div>
              <div class="balance-title">Indian</div>
            </div>
            <div class="membership-holiday-balance-item bal-current-year">
              <div class="balance-count">{{packageDetail.currentYearDetails.asianNightsUsed}}</div>
              <div class="balance-title">Asian</div>
            </div>
            <div class="membership-holiday-balance-item bal-current-year">
              <div class="balance-count">{{packageDetail.currentYearDetails.internationalNightsUsed}}</div>
              <div class="balance-title">WorldWide</div>
            </div>
            <div class="membership-holiday-balance-item bal-current-year">
              <div class="balance-count">{{packageDetail.balanceNights}}</div>
              <div class="balance-title">Balance</div>
            </div>
          </div>
          <div class="new-overall-night-detail">
            <div class="overall-night-text">Current Year Nights</div>
          </div>
          <div class="new-membership-holiday-balance-container no-maring-top">
            <div class="membership-holiday-balance-item bal-current-year current-year">
              <div class="balance-count">{{packageDetail.currentYearDetails?.nightsAllowed}}</div>
              <div class="balance-title">Total</div>
            </div>
            <div class="membership-holiday-balance-item bal-current-year current-year">
              <div class="balance-count">{{packageDetail.currentYearDetails?.nightsUsed}}</div>
              <div class="balance-title">Used</div>
            </div>
            <div class="membership-holiday-balance-item bal-current-year current-year">
              <div class="balance-count">{{packageDetail.currentYearDetails?.nightsAllowed -
                packageDetail.currentYearDetails?.nightsUsed}}</div>
              <div class="balance-title">Balance</div>
            </div>
          </div>

          <div class="new-membership-button-container">
            <ion-button *ngIf="packageDetail.isEmiAvailed"
              class="site-full-rounded-button primary-button membership-button text-capitalize margin-top-20"
              expand="full" shape="round" type="submit" (click)="openEMIDetail()">View Emi Details</ion-button>
            <ion-button class="site-full-rounded-button primary-button membership-button expire-holiday margin-top-20"
              expand="full" shape="round" type="submit" *ngIf="showHolidayExpireButton"
              (click)="openCurrentYearHolidayExpirePopup()">Expire
              Holidays</ion-button>
          </div>

          <div class="ion-padding">
            <!--Member Ship End Detail Section-->
            <div class="new-membership-package-expiry"
              [ngClass]="{'active':packageDetail.status==='ACTIVE','expired':packageDetail.status==='EXPIRED'}">
              <div class="membership-package-text" *ngIf="packageDetail.status==='ACTIVE'">
                Membership end on {{packageDetail.expiryDate|date:"dd MMM"}}'{{packageDetail.expiryDate|date:"yy"}}
              </div>
              <div class="membership-package-text" *ngIf="packageDetail.status==='EXPIRED'">
                Membership ended on {{packageDetail.expiryDate|date:"dd MMM"}}'{{packageDetail.expiryDate|date:"yy"}}
              </div>
            </div>
          </div>

        </div>
        <div class="new-membership-button-container add-package-btn-padding">
          <ion-button
            class="site-full-rounded-button primary-button text-capitalize membership-button add-package margin-bottom-10"
            expand="full" shape="round" type="submit" (click)="explorePlan()"><ion-icon
              src="/assets/images/svg/plus-circle-dotted.svg" class="service-icon margin-right-5"></ion-icon>Add New
            Package</ion-button>
        </div>
        <div class="new-membership-button-container add-package-btn-padding">
          <ion-button
            class="site-full-rounded-button primary-button text-capitalize membership-button membership margin-bottom-10"
            expand="full" shape="round" type="submit" (click)="GoToMembership('MEMBERSHIP')"><ion-icon
              src="/assets/images/svg/plus-circle-dotted.svg" class="service-icon margin-right-5"></ion-icon>Book your
            stay here</ion-button>
        </div>
      </div>

    </div>
  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #offerDetailModal [isOpen]="isOfferDetailPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeOfferDetailPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">Redeem {{ packageDetail?.offerType }}</div>
          <div class="job-info margin-top-10">
            <div class="margin-bottom-15">
              <ion-item class="site-form-control" lines="none">
                <ion-input disabled="true" label="Title" required="required" labelPlacement="floating"
                  name="packageTitle" #packageTitle="ngModel" [(ngModel)]="packageTitleData">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="packageTitle" [onClickValidation]="onClickValidation">
              </app-validation-message>
            </div>
            <!-- <ion-item class="site-form-control" lines="none">
              <ion-input disabled="true" readonly="readonly" label="Location" labelPlacement="floating"
                required="required" name="location"
                [(ngModel)]="packageDetail!.soldFullyPaidHolidayOfferDetail!.locationTitle">
              </ion-input>
            </ion-item> -->
            <div class="margin-bottom-15" *ngIf="packageDetail && packageDetail.offerType == 'FPH'">
              <ion-item class="site-form-control" lines="none">
                <ion-input disabled="true" readonly="readonly" label="Location" labelPlacement="floating"
                  required="required" name="location"
                  [(ngModel)]="packageDetail!.soldFullyPaidHolidayOfferDetail!.locationTitle">
                </ion-input>
              </ion-item>
            </div>
            <div class="field-container large-field margin-bottom-15"
              *ngIf="packageDetail && packageDetail.offerType == 'FPH'" (click)="fetchAvailableMonthsLocation()">
              <ion-item class="site-form-control" lines="none">
                <ion-select class="no-padding" label="Month" labelPlacement="floating" interface="action-sheet" required
                  name="month" #month="ngModel" [(ngModel)]="selectedMonth" #monthSelect>
                  <ion-select-option *ngFor="let month of monthList" [value]="month.id">
                    {{ month.monthName }} / {{ month.year }}
                  </ion-select-option>
                </ion-select>
                <ion-icon slot="end" class="toggle-password-icon" [src]="'/assets/images/svg/down-arrow.svg'"
                  (click)="monthSelect.open()">
                </ion-icon>
              </ion-item>
              <app-validation-message [field]="month" [onClickValidation]="onClickValidation">
              </app-validation-message>
            </div>

            <div *ngIf="packageDetail && packageDetail.offerType == 'FPH'">
              <div class="popup-large-heading">Passport Information</div>
              <div class="field-container large-field margin-bottom-15">
                <ion-item class="site-form-control" lines="none"
                  [ngClass]="{'is-invalid': !passportNumber.valid && onClickValidation}">
                  <ion-input label="Passport Number" labelPlacement="floating" name="passportNumber"
                    [(ngModel)]="passportDetail.passportNumber" #passportNumber="ngModel" [maskito]="passportNumberMask"
                    [maskitoElement]="maskPredicate" (ionInput)="commonService.convertToUpperCase($event)"
                    pattern="^[A-Z]{1}[0-9]{7}$"
                    [ngClass]="{'is-invalid': passportNumber.invalid && onClickValidation}">
                  </ion-input>
                </ion-item>
                <app-validation-message [field]="passportNumber" [onClickValidation]="onClickValidation"
                  [panCardPatternMessage]="'Passport number must contain 1 letter (uppercase), followed by 7 digits.'">
                </app-validation-message>
              </div>
              <!-- <div class="field-container large-field margin-bottom-15">
               
                <ion-item class="site-form-control" lines="none" (click)="openLocationSelectionPopup()">
                  <ion-input disable="true" label="Passport Country" labelPlacement="floating" name="passportCountry"
                    [(ngModel)]="passportDetail.passportCountry" #passportCountry="ngModel">
                  </ion-input>
                </ion-item>
                <app-validation-message [field]="passportCountry" [onClickValidation]="onClickValidation">
                </app-validation-message>
              </div> -->
              <div class="margin-bottom-15">
                <ion-item class="site-form-control" lines="none">
                  <ion-input readonly="readonly" (click)="openDatePickerForExpiryDate()" label="Passport Expiry Date"
                    labelPlacement="floating" name="passportExpiryDate" #passportExpiryDate="ngModel"
                    [(ngModel)]="passportDetail.passportExpiryDate"
                    [value]="passportDetail.passportExpiryDate | date:'yyyy-MM-dd'">
                  </ion-input>
                  <ion-icon slot="end" class="toggle-password-icon" (click)="openDatePickerForExpiryDate()"
                    [src]="'/assets/images/svg/calendar.svg'"></ion-icon>
                </ion-item>
                <!-- <app-validation-message [field]="passportExpiryDate" [onClickValidation]="onClickValidation">
                </app-validation-message> -->
              </div>
            </div>
            <div class="margin-bottom-15">
              <ion-item class="site-form-control" lines="none">
                <ion-textarea label="Notes" labelPlacement="floating" name="customerNotes" #customerNotes="ngModel"
                  [(ngModel)]="customerNotesData" required="required" pattern="^(?=.*\S)[A-Za-z0-9\s.,'-]+$">
                </ion-textarea>
              </ion-item>
              <app-validation-message [field]="customerNotes" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
            </div>
          </div>
          <div class="privacy-container">
            <ion-button class="site-full-rounded-button primary-button text-capitalize" expand="full" shape="round"
              [disabled]="isProcessing" (click)="submitForm(recordForm.form)">
              Submit
            </ion-button>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #cancelOfferModal [isOpen]="isCancelOfferPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeCancelOfferPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">Confirm Cancellation</div>
          <div class="popup-normal-heading margin-top-5 secondary-text">Are you sure you want to cancel your {{
            packageDetail?.offerType }} redeem request</div>
          <div class="privacy-container">
            <ion-button class="site-full-rounded-button primary-button text-capitalize" (click)="cancelRedeemRequest()"
              expand="full" shape="round" [disabled]="isProcessing">
              Confirm
            </ion-button>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #currentYearHolidayExpirePopup
  [isOpen]="isCurrentYearHolidayExpirePopupOpen" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeCurrentYearHolidayExpirePopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading popup-expire-holiday-heading">Holiday Expiring!</div>
          <div class="popup-normal-heading bold-text margin-top-5 secondary-text">Your {{ balanceNights }} holiday
            nights are
            going to expire on
            {{ commonService.formatDateForHolidayExpire(endDate) }}. Do you want to carry forward or lapse?</div>

          <div class="main-modal-dismiss button-gap">
            <ion-button (click)="carryForwardHolidays();currentYearHolidayExpirePopup.dismiss()"
              class="site-full-rounded-button primary-button margin-top-20 notification-list-yes-button" expand="full"
              shape="round">
              Carry Forward
            </ion-button>
            <ion-button (click)="lapseHolidays();currentYearHolidayExpirePopup.dismiss()"
              class="site-full-rounded-button primary-button margin-top-20 notification-list-no-button" expand="full"
              shape="round">
              Lapse
            </ion-button>
          </div>

          <div class="remind-later-container"
            (click)="closeCurrentYearHolidayExpirePopup();currentYearHolidayExpirePopup.dismiss()">
            <p class="remind-later-text">Remind Me Later</p>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #noOnboardingPopup [isOpen]="isNoOnboarding"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeNoOnboardingPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <div>
          <div class="popup-large-heading">Incomplete Information</div>
          <div class="popup-normal-heading margin-top-10 secondary-text">Your profile needs to be updated before process
            this request. Please click 'Continue' to update your details.</div>
          <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
            (click)="continueToOnboarding()">
            Continue
          </ion-button>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>