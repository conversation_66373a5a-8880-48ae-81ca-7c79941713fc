<ion-router-outlet></ion-router-outlet>
<!-- <ion-tabs class="customer-forbcorp-navigation" [ngClass]="{'hide-tab-bar':!showTabs}"
  (ionTabsWillChange)="onTabChange($event)">
  <ion-tab-bar class="tabs" slot="bottom">
    <ion-tab-button tab="dashboard">
      <div class="icon-container-forbcorp">
        <ion-icon class="non-selected-icon" [src]="'/assets/images/svg/tab-home.svg'"></ion-icon>
        <ion-icon class="selected-icon" [src]="'/assets/images/svg/house.svg'"></ion-icon>
      </div>
      <ion-label>Home</ion-label>
    </ion-tab-button>
    <ion-tab-button tab="booking">
      <div class="icon-container-forbcorp">
        <ion-icon class="non-selected-icon" [src]="'/assets/images/svg/booking-icon.svg'"></ion-icon>
        <ion-icon class="selected-icon" [src]="'/assets/images/svg/booking-icon.svg'"></ion-icon>
      </div>
      <ion-label>Bookings</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="payment">
      <div class="icon-container-forbcorp">
        <ion-icon class="non-selected-icon" [src]="'/assets/images/svg/payment-icon.svg'"></ion-icon>
        <ion-icon class="selected-icon" [src]="'/assets/images/svg/payment-icon.svg'"></ion-icon>
      </div>
      <ion-label>Payments</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="membership">
      <div class="icon-container-forbcorp">
        <ion-icon class="non-selected-icon" [src]="'/assets/images/svg/tab-emi.svg'"></ion-icon>
        <ion-icon class="selected-icon" [src]="'/assets/images/svg/white-tab-membership.svg'"></ion-icon>
      </div>
      <ion-label>Membership</ion-label>
    </ion-tab-button>

    <ion-tab-button tab="account">
      <div class="icon-container-forbcorp">
        <ion-icon class="non-selected-icon" [src]="'/assets/images/svg/tab-setting-active.svg'"></ion-icon>
        <ion-icon class="selected-icon" [src]="'/assets/images/svg/setting-white.svg'"></ion-icon>
      </div>
      <ion-label>Account</ion-label>
    </ion-tab-button>
  </ion-tab-bar>
</ion-tabs> -->


<ion-modal class="site-custom-popup job-invitation-popup" #cancelOfferModal [isOpen]="isPackageReminderPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closePackageReminderPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading popup-medium-heading">🌟 Welcome to Your Holiday Experience!</div>
          <div class="popup-normal-heading margin-top-5 secondary-text">We noticed you haven’t purchased a holiday
            package yet. With our exclusive packages, you can enjoy:</div>
          <ul class="bullet-text popup-large-heading popup-medium-heading">
            <li>Free holiday nights every year</li>
            <li>Exclusive discounts on bookings</li>
            <li>Hassle-free travel planning</li>
          </ul>
          <div class="popup-large-heading popup-medium-heading">💼 Start Your Journey Today!</div>

          <div class="privacy-container margin-top-30">
            <ion-button (click)="explorePlan();cancelOfferModal.dismiss()"
              class="site-full-rounded-button primary-button" expand="full" shape="round">
              Explore Membership Plans!!
            </ion-button>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #currentYearHolidayExpirePopup
  [isOpen]="isCurrentYearHolidayExpirePopupOpen" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeCurrentYearHolidayExpirePopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading popup-expire-holiday-heading">Holiday Expiring!</div>
          <div class="popup-normal-heading bold-text margin-top-5 secondary-text">Your {{ balanceNights }} holiday
            nights are
            going to expire on
            {{ commonService.formatDateForHolidayExpire(endDate) }}. Do you want to carry forward or lapse?</div>

          <div class="main-modal-dismiss button-gap">
            <ion-button (click)="carryForwardHolidays();currentYearHolidayExpirePopup.dismiss()"
              class="site-full-rounded-button primary-button margin-top-20 notification-list-yes-button" expand="full"
              shape="round">
              Carry Forward
            </ion-button>
            <ion-button (click)="lapseHolidays();currentYearHolidayExpirePopup.dismiss()"
              class="site-full-rounded-button primary-button margin-top-20 notification-list-no-button" expand="full"
              shape="round">
              Lapse
            </ion-button>
          </div>

          <div class="remind-later-container"
            (click)="closeCurrentYearHolidayExpirePopup();currentYearHolidayExpirePopup.dismiss()">
            <p class="remind-later-text">Remind Me Later</p>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #grabDealPopup [isOpen]="isgrabDealPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <!-- <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="grabDealPopup.dismiss()"></i-feather>
      </div> -->
      <div class="site-custom-popup-body no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">

          @if (loading==='LOADING') {
          <div class="my-booking-card-container">
            <div class="my-booking-card margin-top-10">
              <ion-grid>
                <ion-row>
                  <ion-col>
                    <ion-row class="check-in-out-con my-bookings-check">
                      <ion-col size="8" class="my-booking-code ion-no-padding">
                        <div class="my-booking-text">
                          <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                        </div>
                      </ion-col>
                      <ion-col size="4" class="my-booking-status">
                        <div class="my-booking-text">
                          <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                        </div>
                      </ion-col>
                    </ion-row>

                    <div>
                      <div class="hotel-name my-bookings-hotel-name">
                        <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                      </div>
                      <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                          style="width: 200px"></ion-skeleton-text></div>
                    </div>
                  </ion-col>
                </ion-row>

                <ion-row class="check-in-out-con">
                  <ion-col size="5">
                    <div class="check-in-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                      </div>
                      <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                  <ion-col size="2">
                    <div class="time-duration-con">
                      <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                      <div class="time-duration">
                        <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                      </div>
                    </div>
                  </ion-col>
                  <ion-col size="5">
                    <div class="check-out-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                      </div>
                      <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                </ion-row>

                <ion-row>
                  <ion-col>
                    <div class="guest-and-rooms-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                      </div>
                      <div class="rooms">
                        <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                </ion-row>

              </ion-grid>
            </div>
          </div>
          }
          @else if (loading==='LOADED') {
          <div class="campaign-card" *ngIf="responseReceived && customPushNotificationDetail">
            <div class="campaign-card-header">
              <div class="campaign-image-slide" *ngFor="let attachment of customPushNotificationDetail?.attachments">
                <img *ngIf="attachment.path" [src]="attachment.path" alt="">
                <i-feather name="X" class="close-icon" (click)="closeGrabDealPopup()"></i-feather>
              </div>
              <div class="campaign-image-slide"
                *ngIf="!customPushNotificationDetail?.attachments || customPushNotificationDetail.attachments?.length === 0">
                <img
                  [src]="customPushNotificationDetail?.type === 'FREE_TRIP' ? '/assets/images/svg/free-trip-2.svg' : customPushNotificationDetail?.type === 'WALLET_TOPUP' ? '/assets/images/svg/wallet-2.svg' : '/assets/images/svg/booking-2.svg'"
                  alt="Default Campaign Image">
                <i-feather name="X" class="close-icon" (click)="closeGrabDealPopup()"></i-feather>
              </div>
            </div>

            <div class="campaign-detail-container">
              <span class="campaign-detail-title margin-top-12">{{customPushNotificationDetail?.title}}</span>
              <!-- <p #reward class="campaign-detail-rewards text margin-top-5">{{pushNotificationDetail?.remarks}}</p> -->
              <!-- <span
                *ngIf="campaignDetail?.rewards?.length > 100" class="see-more" (click)="toggleText(reward,$event)">
                See More
              </span>  -->

              <!-- <p #reward class="campaign-detail-desc text margin-top-5">{{campaignDetail?.rewards}}</p><span
                *ngIf="campaignDetail?.rewards?.length > 100" class="see-more" (click)="toggleText(reward,$event)">
                See More
              </span> -->
              <p #desc class="campaign-detail-desc text">{{customPushNotificationDetail?.description}}</p>
              <!-- <span *ngIf="campaignDetail?.description?.length > 100" class="see-more" (click)="toggleText(desc,$event)">
                See More
              </span> -->

            </div>

            <ion-button class="grab-deal-button" expand="full" shape="round" type="submit"
              *ngIf="customPushNotificationDetail?.type != 'CARD_MESSAGE' && customPushNotificationDetail?.type != 'MESSAGE'"
              (click)="seeCampaignDetail(customPushNotificationDetail)">
              <div class="grab-deal-container">
                <img src="/assets/images/svg/grab-deal.svg" alt="Pay Now" width="24px" />
                <span class="grab-deal-text">
                  {{ customPushNotificationDetail?.type === 'PACKAGE' ? 'Get It Now' : 'Grab the deal' }}
                </span>
              </div>
            </ion-button>

          </div>
          }

        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" [isOpen]="isRandomCampaignModalOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-body no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          @if (loading==='LOADING') {
          <div class="my-booking-card-container">
            <div class="my-booking-card margin-top-10">
              <ion-grid>
                <ion-row>
                  <ion-col>
                    <ion-row class="check-in-out-con my-bookings-check">
                      <ion-col size="8" class="my-booking-code ion-no-padding">
                        <div class="my-booking-text">
                          <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                        </div>
                      </ion-col>
                      <ion-col size="4" class="my-booking-status">
                        <div class="my-booking-text">
                          <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                        </div>
                      </ion-col>
                    </ion-row>

                    <div>
                      <div class="hotel-name my-bookings-hotel-name">
                        <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                      </div>
                      <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                          style="width: 200px"></ion-skeleton-text></div>
                    </div>
                  </ion-col>
                </ion-row>

                <ion-row class="check-in-out-con">
                  <ion-col size="5">
                    <div class="check-in-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                      </div>
                      <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                  <ion-col size="2">
                    <div class="time-duration-con">
                      <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                      <div class="time-duration">
                        <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                      </div>
                    </div>
                  </ion-col>
                  <ion-col size="5">
                    <div class="check-out-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                      </div>
                      <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                </ion-row>

                <ion-row>
                  <ion-col>
                    <div class="guest-and-rooms-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                      </div>
                      <div class="rooms">
                        <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                </ion-row>

              </ion-grid>
            </div>
          </div>
          }
          @else if (loading==='LOADED') {
          <div class="campaign-card" *ngIf="responseReceived && randomCampaignDetail">
            <div class="campaign-card-header">
              <div class="campaign-image-slide" *ngFor="let attachment of randomCampaignDetail?.attachments">
                <img *ngIf="attachment.path" [src]="attachment.path" alt="" (click)="randomCampaignDetailButton()">
                <i-feather name="X" class="close-icon" (click)="closeRandomCampaignModal()"></i-feather>
              </div>
              <div class="campaign-image-slide"
                *ngIf="!randomCampaignDetail?.attachments || randomCampaignDetail.attachments?.length === 0">
                <img
                  [src]="randomCampaignDetail?.type === 'FREE_TRIP' ? '/assets/images/svg/free-trip-2.svg' : randomCampaignDetail?.type === 'WALLET_TOPUP' ? '/assets/images/svg/wallet-2.svg' : '/assets/images/svg/booking-2.svg'"
                  alt="Default Campaign Image" (click)="randomCampaignDetailButton()">
                <i-feather name="X" class="close-icon" (click)="closeRandomCampaignModal()"></i-feather>
              </div>
            </div>

            <div class="campaign-detail-container" (click)="randomCampaignDetailButton()">
              <span
                class="campaign-detail-title random-campaign-title margin-top-15">{{randomCampaignDetail?.title}}</span>

              <!-- Show rewards if available -->
              <ng-container *ngIf="randomCampaignDetail?.rewards">
                <p class="rewards">{{ randomCampaignDetail.rewards }}</p>
              </ng-container>

              <!-- Show description if type is FREE_TRIP and no rewards -->
              <ng-container *ngIf="!randomCampaignDetail?.rewards && randomCampaignDetail?.type === 'FREE_TRIP'">
                <p class="rewards">{{ randomCampaignDetail?.description }}</p>
              </ng-container>

              <!-- Show cashback section if no rewards and type is BOOKING_CASHBACK or WALLET_TOPUP -->
              <ng-container
                *ngIf="!randomCampaignDetail?.rewards && 
                    (randomCampaignDetail?.type === 'BOOKING_CASHBACK' || randomCampaignDetail?.type === 'WALLET_TOPUP')">
                <div class="padding-congratulations-container compaign-card-bg random-campaign-card-bg">
                  <div class="congratulations-content-money">
                    <div class="overlay-svg">
                      <img src="/assets/images/svg/money-bag.svg" width="38" height="48" alt="money-bag">
                    </div>
                    <span class="text-content-money random-campaign-text margin-left-12">
                      {{ randomCampaignDetail?.myCash + randomCampaignDetail?.promoCash }}% Cashback,
                      <span *ngIf="randomCampaignDetail?.myCash > 0">
                        {{ randomCampaignDetail?.myCash }}% in My Cash
                      </span>
                      <span *ngIf="randomCampaignDetail?.myCash > 0 && randomCampaignDetail?.promoCash > 0"> and </span>
                      <span *ngIf="randomCampaignDetail?.promoCash > 0">
                        {{ randomCampaignDetail?.promoCash }}% in Promo Cash.
                      </span>
                    </span>
                  </div>
                </div>
              </ng-container>

            </div>

            <!-- <ion-button class="grab-deal-button random-compaign-button" expand="full" shape="round" type="submit"
              (click)="randomCampaignDetailButton()">
              <div class="grab-deal-container">
                <img src="/assets/images/svg/grab-deal.svg" alt="Pay Now" width="24px" />
                <span class="grab-deal-text">
                  View Detail
                </span>
              </div>
            </ion-button> -->

          </div>
          }

        </form>
      </div>

    </div>
  </ng-template>
</ion-modal>