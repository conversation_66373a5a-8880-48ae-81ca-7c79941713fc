import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { MaskitoDirective } from '@maskito/angular';
import { ImageCropperModule } from 'ngx-image-cropper';
import { CommaSeparatorPipe } from 'src/services/comma.separator.service';
import { FileCropperComponent } from 'src/shared/file-cropper/file-cropper.component';
import { FullImageComponent } from 'src/shared/full-image/full-image.component';
import { HttpAuthInterceptor } from 'src/shared/http.interceptor';
import { IconsModule } from 'src/shared/icon.module';
import { SharedModuleModule } from 'src/shared/shared-module.module';
import { SwiperModule } from 'swiper/angular';
import { CustomerAccountComponent } from './customer-account/customer-account.component';
import { AddGuestDetailsComponent } from './customer-booking-management/add-guest-details/add-guest-details.component';
import { BookingCancelComponent } from './customer-booking-management/booking-cancel/booking-cancel.component';
import { BookingConfirmationDetailsComponent } from './customer-booking-management/booking-confirmation-details/booking-confirmation-details.component';
import { BookingConfirmationFailComponent } from './customer-booking-management/booking-confirmation-fail/booking-confirmation-fail.component';
import { BookingConfirmationComponent } from './customer-booking-management/booking-confirmation/booking-confirmation.component';
import { GuestRoomPreviewDetailsComponent } from './customer-booking-management/guest-room-preview-details/guest-room-preview-details.component';
import { HotelDetailsComponent } from './customer-booking-management/hotel-details/hotel-details.component';
import { HotelsListingComponent } from './customer-booking-management/hotels-listing/hotels-listing.component';
import { CustomerLayoutComponent } from './customer-layout.component';
import { CUSTOMERLAYOUTROUTING } from './customer-layout.routing';
import { HomeComponent } from './home/<USER>';
import { MySubscriptionComponent } from './my-subscription/my-subscription.component';
import { CustomerContactScreenComponent } from './profile-management/customer-onboarding/customer-contact-screen/customer-contact-screen.component';
import { ChangePasswordComponent } from './profile-management/customer-profile-details/change-password/change-password.component';
import { CustomerBasicDetailComponent } from './profile-management/customer-profile-details/customer-basic-detail/customer-basic-detail.component';
import { CustomerProfileTwoFactorOtpComponent } from './profile-management/customer-profile-details/customer-profile-two-factor-otp/customer-profile-two-factor-otp.component';
import { SupportTicketRequestListingComponent } from './support-ticket/support-ticket-request-listing.component';
import { MyBookingsComponent } from './my-bookings/my-bookings.component';
import { MyBookingsListDetailsComponent } from './my-bookings/my-bookings-list-details/my-bookings-list-details.component';
import { CancelMyBookingComponent } from './my-bookings/cancel-my-booking/cancel-my-booking.component';
import { SearchHotelComponent } from './customer-booking-management/search-hotel/search-hotel.component';
import { CustomerHeaderComponent } from '../common/customer-header/customer-header.component';
import { MyPaymentsComponent } from './my-payments/my-payments.component';
import { TwoFactorAuthenticationComponent } from '../authentication/mfa/two-factor-authentication/two-factor-authentication.component';
import { BookingFilterComponent } from '../common/booking-filter/booking-filter.component';
import { PaymentConfirmComponent } from './my-payments/payment-confirm/payment-confirm.component';
import { NotificationListComponent } from './notification-list/notification-list.component';
import { ChangeProfileRequestComponent } from './change-profile-request/change-profile-request.component';
import { RedeemGiftListComponent } from './redeem-gift-list/redeem-gift-list.component';
import { EmiDetailComponent } from './emi-detail/emi-detail.component';
import { SupportTicketDetailComponent } from './support-ticket/support-ticket-detail/support-ticket-detail.component';
import { CustomerMyWalletComponent } from './customer-my-wallet/customer-my-wallet.component';
import { CustomerHowItWorksComponent } from './customer-how-it-works/customer-how-it-works.component';
import { CustomerFaqComponent } from './customer-faq/customer-faq.component';
import { CustomerSeeMoreTransactionsComponent } from './customer-see-more-transactions/customer-see-more-transactions.component';
import { MyPackagesComponent } from './my-subscription/my-packages/my-packages.component';
import { MyCompaignsComponent } from './my-compaigns/my-compaigns.component';
import { CustomerSeeMoreCompaignsComponent } from './customer-see-more-compaigns/customer-see-more-compaigns.component';
import { CampaignDetailComponent } from './my-compaigns/campaign-detail/campaign-detail.component';
import { SelectPaymentMethodComponent } from './my-payments/select-payment-method/select-payment-method.component';
import { CustomerReferralRewardComponent } from './customer-referral-reward/customer-referral-reward.component';
import { CustomerPackageDetailComponent } from './customer-package-detail/customer-package-detail.component';
import { GuestDetailCouponsComponent } from './customer-booking-management/guest-detail-coupons/guest-detail-coupons.component';
import { LoginComponent } from '../authentication/login/login.component';

@NgModule({
  declarations: [
    LoginComponent,
    TwoFactorAuthenticationComponent,
    CustomerLayoutComponent,
    CustomerAccountComponent,
    CustomerMyWalletComponent,
    CustomerHowItWorksComponent,
    CustomerFaqComponent,
    CustomerSeeMoreTransactionsComponent,
    CustomerSeeMoreCompaignsComponent,
    HomeComponent,
    NotificationListComponent,
    MyBookingsComponent,
    MyPaymentsComponent,
    SelectPaymentMethodComponent,
    PaymentConfirmComponent,
    MyBookingsListDetailsComponent,
    CancelMyBookingComponent,
    HotelsListingComponent,
    HotelDetailsComponent,
    AddGuestDetailsComponent,
    GuestRoomPreviewDetailsComponent,
    BookingConfirmationComponent,
    BookingConfirmationDetailsComponent,
    BookingConfirmationFailComponent,
    BookingCancelComponent,
    ChangePasswordComponent,
    SupportTicketRequestListingComponent,
    SupportTicketDetailComponent,
    ChangeProfileRequestComponent,
    CustomerContactScreenComponent,
    FileCropperComponent,
    FullImageComponent,
    CommaSeparatorPipe,
    CustomerBasicDetailComponent,
    CustomerProfileTwoFactorOtpComponent,
    MySubscriptionComponent,
    SearchHotelComponent,
    CustomerHeaderComponent,
    BookingFilterComponent,
    RedeemGiftListComponent,
    EmiDetailComponent,
    MyPackagesComponent,
    MyCompaignsComponent,
    CampaignDetailComponent,
    CustomerReferralRewardComponent,
    CustomerPackageDetailComponent,
    GuestDetailCouponsComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    RouterModule.forChild(CUSTOMERLAYOUTROUTING),
    SharedModuleModule,
    ImageCropperModule,
    MaskitoDirective,
    IconsModule,
    SwiperModule
  ],
  providers: [
    provideHttpClient(withInterceptorsFromDi()),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpAuthInterceptor,
      multi: true
    }
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CustomerLayoutModule { }
