import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Checkout } from 'capacitor-razorpay';
import { environment } from 'src/environments/environment';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-select-payment-method',
  templateUrl: './select-payment-method.component.html',
  styleUrls: ['./select-payment-method.component.scss'],
})
export class SelectPaymentMethodComponent implements OnInit {

  paymentSelections: { WALLET_CASH: boolean; PROMO_CASH: boolean } = {
    WALLET_CASH: false,  // Default selected
    PROMO_CASH: false
  };
  detailShow = {
    open: false
  };
  user: any;
  isPaymentProcessing: boolean = false;
  loading: string = "NOT_STARTED";
  selectedPayment: any;
  amount: any;
  finalAmountAfterUsePayment: any;
  availableCashDetails: any;
  remainingAmount: number = 0;
  discount: number = 0;
  enableWalletCash: boolean = false;  //disable
  enablePromoCash: boolean = false; //disable
  convienceFee: number = 0;
  convenienceFeeGst: number = 0;
  covenienceFeeWithGst: number = 0;
  usablePromoCash: number = 0;
  paymentType!: string;
  fromScreen!: string;
  walletType!: string
  walletId: string | null = null;
  campaign: any;
  guestDetails: any;
  bookingId: string | null = null;
  campaignId: string | null = null;
  package: any;
  packageTab: string | null = null;
  selectedInstallment: any;
  selectedPackage: any;
  bookingType: string | null = null;
  amountUsedFromMyCash: number = 0;
  amountUsedFromPromoCash: number = 0;
  selectedBookingCampaignId: string | null = null;
  myCampaignBackScreen: string | null = null;

  isSuccessfulResponse: boolean = false;
  successPaymentDetails: any;
  userSelectPayment: string | null = null;

  constructor(private authService: AuthService,
    private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly dataService: DataService,
    public readonly commonService: CommonService,
    private readonly cdr: ChangeDetectorRef,
    private readonly loadingService: LoadingService,
    private readonly route: ActivatedRoute,
    private readonly localStorageService: LocalStorageService
  ) {

  }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();
    this.route.queryParams.subscribe(params => {
      this.selectedPayment = params['selectedPayment'] || null;
      this.paymentType = params['paymentType'] || null;
      this.fromScreen = params['fromScreen'] || null;
      this.walletType = params['walletType'] || null;
      this.walletId = params['walletId'] || null;
      this.campaign = params['campaigns'] || null;
      this.guestDetails = params['guestDetails'] || null;
      this.bookingId = params['bookingId'] || null;
      this.campaignId = params['campaignId'] || null;
      this.package = params['package'] || null;
      this.amount = params['amount'] || this.package?.amount;
      this.packageTab = params['packageTab'] || null;
      this.selectedInstallment = params['selectedInstallment'] || null;
      this.selectedPackage = params['selectedPackage'] || null;
      this.bookingType = params['bookingType'] || null;
      this.selectedBookingCampaignId = params['selectedBookingCampaignId'] || null;
      this.userSelectPayment = params['userSelectPayment'] || null;
      this.myCampaignBackScreen = params['myCampaignBackScreen'] || null;
      this.successPaymentDetails = params['successPaymentDetails'] || null
    });
    this.campaignId = this.campaignId === "null" ? null : this.campaignId;
    this.discount = 0;
    this.amountUsedFromPromoCash = 0;
    this.amountUsedFromMyCash = 0;
    this.paymentSelections = {
      WALLET_CASH: false,
      PROMO_CASH: false
    };
    this.enableWalletCash = false;
    this.enablePromoCash = false;
    this.isPaymentProcessing = false;
    this.fetchAvailableCashDetails();

  }

  fetchAvailableCashDetails() {
    const payload = {
      paymentType: this.selectedPayment?.paymentType || this.paymentType,
      expiryDays: 10
    };

    this.loading = "LOADING";

    this.dataService.availableCashDetails(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.availableCashDetails = response.data;
        this.calculatePromoCash();
        this.calculateConvenienceFeeWithGst();
        this.handleWalletAndPromoCash();
      },
      error: () => {
        this.loading = "LOADED";
        // this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  /** Calculates the usable promo cash */
  private calculatePromoCash() {
    if (!this.availableCashDetails) return;
    this.usablePromoCash = Math.round(Math.min(
      this.amount * (this.availableCashDetails.maxPromoPercentage / 100),
      this.availableCashDetails.maxPromoAmount,
      this.availableCashDetails.promoCash
    ));
  }

  /** Calculates the convenience fee including GST */
  private calculateConvenienceFeeWithGst() {
    this.covenienceFeeWithGst = this.paymentType
      ? this.calculateConvenienceFeeForOthers()
      : this.calculateConvenienceFee(this.selectedPayment);

    this.finalAmountAfterUsePayment = this.amount + this.covenienceFeeWithGst;
  }

  /** Handles wallet and promo cash enabling logic */
  private handleWalletAndPromoCash() {
    if (!this.fromScreen) return;

    const isWalletOrPromoScreen = [
      //'my-wallet',
      'guest-room-preview',
      'package-payment',
      'my-payments'
    ].includes(this.fromScreen);

    const isCampaignScreen = [
      'campaign-detail',
      'customer-view-campaign-detail'
    ].includes(this.fromScreen);

    if (isWalletOrPromoScreen) {
      if (this.campaignId) {
        this.campaignDetails();
      } else if (this.walletId) {
        this.enableWalletCash = false;
        this.enablePromoCash = false;
      } else {
        this.enableWalletCash = this.availableCashDetails?.walletEnable;
        this.enablePromoCash = this.availableCashDetails?.promoEnable;
      }
    } else if (isCampaignScreen) {
      this.enableWalletCash = this.campaign?.isWalletPaymentAllowed;
      this.enablePromoCash = false; // Keeping promoCash disabled
    }
  }


  campaignDetails() {
    this.dataService.campaignDetailId(this.campaignId || "").subscribe({
      next: (response: RestResponse) => {
        this.campaign = response.data;
        //this.enableWalletCash = this.campaign.isWalletPaymentAllowed;
        this.enableWalletCash =  this.availableCashDetails?.walletEnable;
        this.enablePromoCash = this.availableCashDetails?.promoEnable; //disabled
      },
      error(error) {
        console.log("error", error)
      }
    });
  }

  private calculateConvenienceFee(payment: any): number {
    const { paymentType, convenienceFeeConfigurationDetail } = payment;
    const feeConfig: Record<string, number> = {
      ASF: convenienceFeeConfigurationDetail.asf,
      EMI: convenienceFeeConfigurationDetail.emi,
      HOTEL_BOOKING: convenienceFeeConfigurationDetail.booking,
      OTHER: convenienceFeeConfigurationDetail.additional,
      BOOKING_ADDITIONAL_AMOUNT: convenienceFeeConfigurationDetail.additional,
      FPH_ADDITIONAL_AMOUNT: convenienceFeeConfigurationDetail.additional,
      CAMPAIGN: convenienceFeeConfigurationDetail.additional
    };
    if (paymentType in feeConfig) {
      const baseFee = this.amount * (feeConfig[paymentType as keyof typeof feeConfig] / 100);
      const gstFee = (convenienceFeeConfigurationDetail.gstRate / 100) * baseFee;
      return baseFee + gstFee;
    }
    return 0; // Default case if no matching payment type
  }


  private amountBaseCalculateConvenienceFee(amount: number): number {
    const feeConfig: Record<string, number> = {
      BOOKING: this.availableCashDetails?.convenienceFeeConfigurationDetail.booking,
      ADDITIONAL: this.availableCashDetails?.convenienceFeeConfigurationDetail.additional,
      PACKAGE: this.availableCashDetails?.convenienceFeeConfigurationDetail.package,

    };
    if (this.paymentType in feeConfig) {
      const baseFee = amount * (feeConfig[this.paymentType as keyof typeof feeConfig] / 100);
      const gstFee = (this.availableCashDetails?.convenienceFeeConfigurationDetail.gstRate / 100) * baseFee;
      return baseFee + gstFee;
    }
    return 0; // Default case if no matching payment type
  }

  private calculateConvenienceFeeForOthers(): number {
    return this.amountBaseCalculateConvenienceFee(this.amount);
  }

  private calculateConvenienceFeeForPackage(): number {
    return this.amountBaseCalculateConvenienceFee(this.remainingAmount);
  }


  toggleSelection(type: 'WALLET_CASH' | 'PROMO_CASH') {
    this.paymentSelections[type] = !this.paymentSelections[type];

    this.calculateFinalAmount();
  }

  calculateFinalAmount() {
    let discount = 0;
    this.amountUsedFromPromoCash = 0;
    this.amountUsedFromMyCash = 0;
    let amount = Number(this.amount); // Total amount including convenience fee
    this.calculatePromoCash();
    if (this.paymentSelections.PROMO_CASH && this.availableCashDetails?.promoCash > 0) {
      const promoCashToUse = Math.min(amount, Number(this.usablePromoCash));
      discount += promoCashToUse;
      this.amountUsedFromPromoCash = promoCashToUse;
      amount -= promoCashToUse; // Reduce remaining amount
    }

    // Step 2: Apply Wallet Cash (My Cash) Next
    if (this.paymentSelections.WALLET_CASH && this.availableCashDetails?.walletCash > 0 && amount > 0) {
      const myCashToUse = Math.min(amount, Number(this.availableCashDetails.walletCash));
      discount += myCashToUse;
      this.amountUsedFromMyCash = myCashToUse;
      amount -= myCashToUse; // Reduce remaining amount
    }

    // Step 3: Calculate the remaining amount after applying discounts
    // let remainingAmount = amount - discount;
    this.remainingAmount = amount;

    //console.log("this.remainingAmount",this.remainingAmount)
    //console.log("this.paymentType",this.paymentType);
    if (this.remainingAmount <= 0) {
      // ✅ Case: Fully Paid via My Cash / Promo Cash → No Fee
      this.convienceFee = 0;
      this.convenienceFeeGst = 0;
      this.finalAmountAfterUsePayment = 0;
      this.covenienceFeeWithGst = 0;
    }

    else if (this.paymentType) {
      this.covenienceFeeWithGst = this.calculateConvenienceFeeForPackage();
      this.finalAmountAfterUsePayment = this.remainingAmount + this.covenienceFeeWithGst;
    }
    else {
      // ✅ Case: Partial Payment → Fee applies on the remaining amount
      if (this.selectedPayment.paymentType === 'EMI') {
        this.convienceFee = this.remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.emi / 100);
      } else if (this.selectedPayment.paymentType === 'ASF') {
        this.convienceFee = this.remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.asf / 100);
      } else if (this.selectedPayment.paymentType === 'HOTEL_BOOKING') {
        this.convienceFee = this.remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.booking / 100);
      } else if (this.selectedPayment.paymentType === 'OTHER') {
        this.convienceFee = this.remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.additional / 100);
      }
      else if (this.selectedPayment.paymentType === 'BOOKING_ADDITIONAL_AMOUNT') {
        this.convienceFee = this.remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.additional / 100);
      }
      else if (this.selectedPayment.paymentType === 'CAMPAIGN') {
        this.convienceFee = this.remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.asf / 100);
      }
      else if (this.selectedPayment.paymentType === 'FPH_ADDITIONAL_AMOUNT') {
        this.convienceFee = this.remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.additional / 100);
      } else {
        this.convienceFee = 0;
      }

      // // Calculate GST on the reduced convenience fee
      this.convenienceFeeGst = this.convienceFee * (this.availableCashDetails?.convenienceFeeConfigurationDetail.gstRate / 100);
      this.covenienceFeeWithGst = this.convienceFee + this.convenienceFeeGst;
      // this.covenienceFeeWithGst = this.calculateConvenienceFee(this.selectedPayment);
      // Final Amount to be Paid
      this.finalAmountAfterUsePayment = this.remainingAmount + this.convienceFee + this.convenienceFeeGst;
      //console.log("remainigAmount", this.remainingAmount);
      this.discount = discount;
      //console.log("discount", this.discount);
    }
  }


  async createOrder() {
    this.isPaymentProcessing = true;
    const input = {} as any;
    input.paymentType = this.selectedPayment?.paymentType || this.walletType || this.package?.paymentType;
    if (this.walletType) {
      input.walletId = this.walletId
      if (this.campaign) {
        input.campaignId = this.campaign.id || null;
      }
      if (this.bookingId) {
        input.bookingId = this.bookingId;
        input.campaignId = this.campaignId || this.selectedBookingCampaignId;
      }

    }
    else if (this.paymentType === 'PACKAGE') {
      input.packageId = this.package?.packageId;
      if (this.packageTab === 'EMI_PAYMENT') {
        input.emiTenure = this.selectedInstallment?.months;
      }

    }
    else {
      input.paymentLogId = this.selectedPayment?.id;
    }

    let totalPayableAmount = this.amount; // ✅ Full amount before deductions
    // let walletCashUsed = 0;
    // let promoCashUsed = 0;
    let remainingAmount = (this.amountUsedFromMyCash > 0 || this.amountUsedFromPromoCash > 0) ? this.remainingAmount : totalPayableAmount; // ✅ Track amount left to pay

    // // ✅ Step 1: Use Wallet Cash First (if selected)
    // if (this.paymentSelections.WALLET_CASH && this.availableCashDetails?.walletCash > 0) {
    //   walletCashUsed = Math.min(remainingAmount, this.availableCashDetails.walletCash);
    //   console.log("walletCashUsed",walletCashUsed)
    //   remainingAmount -= walletCashUsed; // Reduce remaining amount
    // }
    // console.log("remainingAmount",remainingAmount);
    // // ✅ Step 2: Use Promo Cash Next (if selected)
    // console.log("this.availableCashDetails?.promoCash",this.availableCashDetails?.promoCash)
    // console.log("this.usablePromoCash",this.usablePromoCash)
    // if (this.paymentSelections.PROMO_CASH && this.availableCashDetails?.promoCash > 0 && remainingAmount > 0) {
    //   promoCashUsed = Math.min(remainingAmount, this.usablePromoCash);
    //   console.log("walletCashUsed",promoCashUsed)
    //   remainingAmount -= promoCashUsed; // Reduce remaining amount
    // }
    // console.log("remainingAmount",remainingAmount);
    // // ✅ Step 3: Apply Convenience Fee on Remaining Amount
    // console.log("walletCashUsed",walletCashUsed);
    // console.log("promoCashUsed",promoCashUsed);
    // console.log("remainingAmount",remainingAmount);
    let convenienceFee = 0;
    let convenienceFeeGst = 0;


    if (remainingAmount > 0) {
      if (this.fromScreen === 'package-payment') {
        convenienceFee = remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.package / 100);
      }
      // this.fromScreen === 'my-wallet' || 
      else if (this.fromScreen === 'customer-view-campaign-detail' || this.fromScreen === 'campaign-detail') {
        convenienceFee = remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.additional / 100);
      }
      else if (this.fromScreen === 'guest-room-preview') {
        convenienceFee = remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.booking / 100);
      }
      else {
        if (this.selectedPayment.paymentType === 'EMI') {
          convenienceFee = remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.emi / 100);
        } else if (this.selectedPayment.paymentType === 'ASF') {
          convenienceFee = remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.asf / 100);
        } else if (this.selectedPayment.paymentType === 'CAMPAIGN') {
          convenienceFee = remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.asf / 100);
        }
        else if (this.selectedPayment.paymentType === 'BOOKING_ADDITIONAL_AMOUNT') {
          convenienceFee = remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.additional / 100);
        }
        else if (this.selectedPayment.paymentType === 'FPH_ADDITIONAL_AMOUNT') {
          convenienceFee = remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.additional / 100);
        }
        else if (this.selectedPayment.paymentType === 'HOTEL_BOOKING') {
          convenienceFee = remainingAmount * (this.availableCashDetails?.convenienceFeeConfigurationDetail.booking / 100);
        }

      }
      convenienceFeeGst = convenienceFee * (this.availableCashDetails?.convenienceFeeConfigurationDetail.gstRate / 100);
    }
    // remainingAmount += convenienceFee + convenienceFeeGst;
    // ✅ Step 4: Assign correct values to API payload
    input.wallet = this.amountUsedFromMyCash || 0;
    input.promo = this.amountUsedFromPromoCash || 0;
    input.amount = remainingAmount; // ✅ Send remaining amount only
    input.convenienceFee = convenienceFee;
    input.convenienceFeeGst = convenienceFeeGst;
    input.offerId = this.package?.offerId;
    input.fphId = this.package?.fphId;
    console.log("input", input)

    //console.log("input",input);
    if (input.amount === 0) {
      input.bookingRequest = { ...this.guestDetails };
    }

    // ✅ Step 5: Call API
    this.dataService.createOrder(input)
      .subscribe({
        next: (response: RestResponse) => {
          if (remainingAmount > 0) {
            this.openPaymentPopup(response.data, this.selectedPayment?.paymentType || this.paymentType);
          }
          else if (remainingAmount <= 0 && this.fromScreen === 'guest-room-preview') {
            this.isPaymentProcessing = false;
            this.navController.navigateForward("/booking", { animated: true });
          }
          else if (remainingAmount <= 0 && this.fromScreen === 'my-payments') {
            this.navController.navigateForward(['/portal/confirm/payment'], {
              queryParams: {
                userSelectPayment: this.userSelectPayment,
                successPaymentType: this.selectedPayment?.paymentType
              },
              animated: true
            });
          } else if (remainingAmount <= 0 && (this.fromScreen === 'customer-view-campaign-detail' || this.fromScreen === 'campaign-detail')) {
            this.successPaymentDetails = response.data;
            this.navController.navigateForward('/portal/my/campaign/details', {
              state: {
                campaignId: this.campaign?.id,
                walletId: this.walletId,
                fromScreen: this.myCampaignBackScreen,
                successPaymentDetails: this.successPaymentDetails
              },
              animated: true
            });
            //  this.openSuccessPopup();
          }
        },
        error: (error) => {
          this.isPaymentProcessing = false;
          this.toastService.show(error.message);
        }
      });
  }

  async openPaymentPopup(data: any, paymentType: any) {
    const options: any = {
      key: environment.PaymentGateway.Key,
      description: paymentType,
      image: environment.PaymentGateway.Image,
      order_id: data.orderId,
      currency: environment.PaymentGateway.Currency,
      name: environment.PaymentGateway.Name,
      prefill: {
        email: this.user.email,
        contact: `${this.user.countryCode}${this.user.phoneNumber}`
      },
      theme: {
        color: '#29385B'
      }
    }
    try {
      let paymentResponse = (await Checkout.open(options));
      this.paymentProcessing(data, paymentResponse.response, paymentType);
    } catch (error: any) {
      this.isPaymentProcessing = false;
      if (!error) {
        return;
      }
      let errorObj = JSON.parse(error)
      if (!errorObj || !errorObj.description) {
        return;
      }
      this.toastService.show(errorObj.description);
    }
  }

  paymentProcessing(data: any, paymentResponse: any, paymentType: string) {

    const payload = {
      orderId: paymentResponse.razorpay_order_id,
      paymentId: paymentResponse.razorpay_payment_id,
      signature: paymentResponse.razorpay_signature,
      bookingRequest: {}
    }
    this.loadingService.show();
    let method: string | undefined;
    if (this.fromScreen === 'my-payments') {
      method = paymentType === 'FPH_ADDITIONAL_AMOUNT' ? 'fphPayments' : (paymentType === 'EMI' || paymentType === 'ASF') ? 'recurringPayments' : 'otherPayments';
    }
    else if (this.fromScreen === 'my-wallet' || this.fromScreen === 'customer-view-campaign-detail' || this.fromScreen === 'campaign-detail') {
      if (this.campaign?.type === "FREE_TRIP") {
        method = 'freeTrip';
      } else {
        method = 'walletRecharge';
      }
    }
    else if (this.fromScreen === 'guest-room-preview') {
      method = 'booking';
      payload.bookingRequest = { ...this.guestDetails };
    }
    else if (this.fromScreen === 'package-payment') {
      method = 'purchasePackage';
    }

    if (method && typeof this.dataService[method as keyof typeof this.dataService] === 'function') {
      (this.dataService[method as keyof typeof this.dataService] as any)(payload).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          this.isPaymentProcessing = false;

          this.successPaymentDetails = response.data;

          if (this.fromScreen === 'my-payments') {
            //  this.navController.navigateForward("/portal/confirm/payment", { animated: true });
            this.navController.navigateForward(['/portal/confirm/payment'], {
              queryParams: {
                userSelectPayment: this.userSelectPayment,
                successPaymentType: this.selectedPayment?.paymentType
              },
              animated: true
            });
            return;
          }
          // else if(this.fromScreen==='my-wallet'){
          //   this.navController.navigateForward("/portal/account", { animated: true });
          // }
          else if (this.fromScreen === 'guest-room-preview') {
            this.handleConfirmBookingResponse(response.data);
            return;
          }
          // else if(this.fromScreen==='campaign-detail'){
          //   //this.navController.navigateForward(`/portal/my/${this.walletId}/compaigns`, { animated: true });
          // }
          else if (this.fromScreen === 'package-payment') {
            this.redirectToSuccessPage();
            return;
          } else if (this.fromScreen === 'customer-view-campaign-detail' || this.fromScreen === 'campaign-detail') {
            this.navController.navigateForward('/portal/my/campaign/details', {
              state: {
                campaignId: this.campaign?.id,
                walletId: this.walletId,
                fromScreen: this.myCampaignBackScreen,
                successPaymentDetails: this.successPaymentDetails
              },
              animated: true
            });
            return;
          }

          //  this.openSuccessPopup();
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.isPaymentProcessing = false;
          if (this.fromScreen === 'package-payment') {
            this.redirectToFailurePage();
          }
          this.toastService.show(error.message || 'An error occurred');
        }
      });
    }
  }

  fetchWalletDetails() {
    this.loading = "LOADING";
    this.dataService.getWalletDetails().subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
      },
      error: (error: any) => {
        this.loading = "LOADED";
        //  this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  handleConfirmBookingResponse(data: any) {
    this.localStorageService.setObject("confirmBookingResponse", data);
    if (data.bookingDetails.bookingStatus === "Fail") {
      this.navController.navigateForward("/portal/confirmation/status", { animated: true, queryParams: { bookingType: this.guestDetails?.bookingType } });
      return;
    }
    this.localStorageService.setObject("resetGuestFormDetails", true);
    this.navController.navigateForward("/portal/confirmation", { animated: true, queryParams: { bookingType: this.guestDetails?.bookingType } });
  }

  redirectToSuccessPage() {
    const finalData = {} as any;
    finalData.tab = this.packageTab;
    finalData.selectedPackage = this.selectedPackage;
    finalData.selectedInstallment = this.selectedInstallment;
    if (this.localStorageService.getObject("temp-token")) {
      this.localStorageService.setObject('token', this.localStorageService.getObject("temp-token"));
    }
    if (this.localStorageService.getObject("temp-user")) {
      this.localStorageService.setObject('user', this.localStorageService.getObject("temp-user"));
    }
    this.localStorageService.remove('temp-token');
    this.localStorageService.remove('temp-user');
    setTimeout(() => {
      this.navController.navigateForward(`/account/register/package/success`, { state: finalData, animated: true });
    });
  }

  redirectToFailurePage() {
    const finalData = {} as any;
    finalData.tab = this.packageTab;
    finalData.selectedPackage = this.selectedPackage;
    finalData.selectedInstallment = this.selectedInstallment;
    if (this.localStorageService.getObject("temp-token")) {
      this.localStorageService.setObject('token', this.localStorageService.getObject("temp-token"));
    }
    if (this.localStorageService.getObject("temp-user")) {
      this.localStorageService.setObject('user', this.localStorageService.getObject("temp-user"));
    }
    this.localStorageService.remove('temp-token');
    this.localStorageService.remove('temp-user');
    setTimeout(() => {
      this.navController.navigateForward(`/account/register/package/failure`, { state: finalData, animated: true });
    });
  }

  openSuccessPopup() {
    this.isSuccessfulResponse = true;
  }

  closeSuccessPopup() {
    this.isSuccessfulResponse = false;
  }

  closeDetails() {
    this.closeSuccessPopup();

    setTimeout(() => {
      if (this.fromScreen === 'customer-view-campaign-detail' || this.fromScreen === 'campaign-detail') {
        // if (this.myCampaignBackScreen === 'my-campaigns') {
        //   this.navController.navigateForward(`/portal/my/${this.walletId}/compaigns`, { animated: true });
        //   return;
        // }
        // this.navController.navigateForward(`/portal/see/more/${this.walletId}/compaigns`, { animated: true });
        this.navController.navigateForward('/portal/my/campaign/details', {
          state: {
            campaignId: this.campaign?.id,
            walletId: this.walletId,
            fromScreen: this.myCampaignBackScreen
          },
          animated: true
        });
      }
    }, 200);
  }

  openDetails(successPaymentDetails: any) {
    this.cdr.detectChanges();
    this.closeSuccessPopup();

    setTimeout(() => {
      // if(this.noRechargeAmount){
      //   this.noRechargeAmount = false; 
      //   this.navController.navigateForward("/portal/payment", { animated: true });
      //   return;
      // }
      if (this.fromScreen === 'customer-view-campaign-detail' || this.fromScreen === 'campaign-detail') {
        if (
          this.successPaymentDetails?.promo === 0 &&
          this.successPaymentDetails?.myCash === 0
        ) {
          this.navController.navigateForward('/portal/my/campaign/details', {
            state: {
              campaignId: this.campaign?.id,
              walletId: this.walletId,
              fromScreen: this.myCampaignBackScreen
            },
            animated: true
          });
          return;
        }
        this.navController.navigateForward("/portal/my/wallet", {
          state: {
            successPaymentDetails: successPaymentDetails
          },
          animated: true
        });
      }
    }, 200);
  }

}
