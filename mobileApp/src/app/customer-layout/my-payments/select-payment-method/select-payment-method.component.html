<ion-content class="customer-dashboard-page customer-payment-page select-payment-method-page">
  <app-customer-header [innerPage]="true" [headingText]="'Select Payment Method'"
    [rightAction]="true"></app-customer-header>
  <!-- Tabs Section -->
  <div class="customer-body-section has-header-section-change-profile no-padding">

    <div class="payment-body-section">
      <ion-content class="payment-body-ion-content">
        <div class="select-payment-method-container">
          <div class="select-payment-method">
            <div class="wallet-detail-container">
            </div>
            <div class="wallet-cash-payment" [ngClass]="{'active': paymentSelections.PROMO_CASH ,
              'disabled': availableCashDetails?.promoCash <= 0 || !enablePromoCash || availableCashDetails?.maxPromoPercentage<=0 || usablePromoCash<=0
            }" (click)="toggleSelection('PROMO_CASH')">
              <div class="wallet-container">
                <div class="checkbox-text-container">
                  <div class="checkbox-container">
                    <div class="cash-balance-container">
                      <span class="cash-text">Promo Cash</span>
                      <div class="promo-cash-bal-container">
                        <span class="bal-heading margin-top-5">Balance:</span>
                        <!-- <ion-icon class="icon-size" [src]="'/assets/images/svg/promo-cash.svg'" slot="start"></ion-icon> -->
                        <span class="cash-balance margin-top-5" *ngIf="loading === 'LOADED'">
                          ₹{{ commonService.formatAmount(availableCashDetails?.promoCash || 0) }}/-
                        </span>
                        <ion-skeleton-text *ngIf="loading === 'LOADING'" [animated]="true"
                          style="width: 60px; height: 7px;"></ion-skeleton-text>
                      </div>
                    </div>
                  </div>

                  <div class="promo-expire-container">
                    <!-- <ion-icon class="margin-bottom-5" [src]="'/assets/images/svg/promo-cash.svg'"
                      slot="start"></ion-icon> -->
                    <span class="promo-expire-text">You are eligible to use {{ (enablePromoCash ?
                      usablePromoCash: 0) | currency: 'INR' : 'symbol':'1.0-0'}} of {{ availableCashDetails?.promoCash |
                      currency:
                      'INR' : 'symbol':'1.0-0'}}</span>
                  </div>
                </div>
                <!-- Right Section: Icon -->
                <div class="icon-container margin-bottom-15">
                  <ion-icon class="icon-size" [src]="'/assets/images/svg/promo-cash.svg'" slot="start"></ion-icon>
                </div>
              </div>
            </div>

            <div class="wallet-cash-payment margin-bottom-15" [ngClass]="{'active': paymentSelections.WALLET_CASH,
            'disabled': !availableCashDetails?.walletCash || availableCashDetails?.walletCash <= 0 || !enableWalletCash
          }" (click)="toggleSelection('WALLET_CASH')">
              <div class="wallet-container">
                <!-- Left Section: Checkbox and Text -->
                <div class="checkbox-text-container">
                  <div class="checkbox-container">
                    <!-- <div >
                      <ion-checkbox [checked]="paymentSelections.WALLET_CASH"></ion-checkbox>
                    </div> -->
                    <div class="cash-balance-container">
                      <span class="cash-text">My Cash</span>
                      <div class="promo-cash-bal-container margin-top-5">
                        <span class="bal-heading">Balance:</span>
                        <span class="cash-balance" *ngIf="loading === 'LOADED'">{{(availableCashDetails?.walletCash || 0) | currency :
                          'INR' : 'symbol' : '1.0-2'}}/-</span>
                          <ion-skeleton-text *ngIf="loading === 'LOADING'" [animated]="true"
                          style="width: 60px; height: 7px;"></ion-skeleton-text>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Right Section: Icon -->
                <div class="icon-container margin-bottom-15">
                  <ion-icon class="icon-size" [src]="'/assets/images/svg/money-bag.svg'" slot="start"></ion-icon>
                </div>
              </div>
            </div>
            <div class="wallet-detail-container">
              <hr class="horizontal-separator-line" />

              <div class="detail-container">

                <div class="detail-card">
                  <span class="status-heading">Total Amount:</span>
                  <div class="amount-container">
                    <span class="status-detail">{{amount | currency :
                      'INR' : 'symbol' : '1.0-1'}}/-</span>
                    <!-- <span class="gst-text">+₹1500/- GST</span> -->
                  </div>
                </div>

                <div class="detail-card margin-top-10" *ngIf="amountUsedFromPromoCash>0">
                  <span class="status-heading">Promo Cash:</span>
                  <span class="status-detail">
                    {{amountUsedFromPromoCash| currency: 'INR' :
                    'symbol' : '1.0-2'}}/-
                  </span>
                </div>

                <div class="detail-card margin-top-10" *ngIf="amountUsedFromMyCash>0">
                  <span class="status-heading">My Cash :</span>
                  <span class="status-detail">
                    {{amountUsedFromMyCash| currency: 'INR' :
                    'symbol' : '1.0-2'}}/-
                  </span>
                </div>

                <div class="detail-card  margin-top-10" *ngIf="remainingAmount>0 && discount>0">
                  <span class="status-heading">Balance Amount:</span>
                  <span class="status-detail">
                    {{ remainingAmount | currency: 'INR' : 'symbol' : '1.0-2'}}/-
                  </span>
                </div>

                <div class="detail-card margin-top-10" *ngIf="covenienceFeeWithGst>0">
                  <span class="status-heading">Convenience Fee(Incl. GST):</span>
                  <span class="status-detail">{{ (finalAmountAfterUsePayment >0 ? covenienceFeeWithGst:0)| currency :
                    'INR': 'symbol' : '1.0-2'}}/-</span>
                </div>
                <div class="detail-card margin-top-10" *ngIf="covenienceFeeWithGst>0">
                  <span class="status-heading">Total Payable Amount:</span>
                  <span class="status-detail">
                    {{ (finalAmountAfterUsePayment > 0 ? finalAmountAfterUsePayment : amount )| currency: 'INR' :
                    'symbol' : '1.0-2'}}/-
                  </span>
                </div>
              </div>
            </div>

            <div class="privacy-container margin-top-20" *ngIf="finalAmountAfterUsePayment>0">
              <ion-button class="site-full-rounded-button less-border-radius text-capitalize" expand="full"
                shape="round" type="submit" [disabled]="isPaymentProcessing" (click)="createOrder()">
                Pay {{finalAmountAfterUsePayment | currency :
                'INR' :'symbol':'1.0-2'}}/-
              </ion-button>
            </div>

            <div class="privacy-container margin-top-20" *ngIf="finalAmountAfterUsePayment<=0">
              <ion-button class="site-full-rounded-button less-border-radius text-capitalize" expand="full"
                shape="round" type="submit" [disabled]="isPaymentProcessing" (click)="createOrder()">
                Confirm Payment
              </ion-button>
            </div>

          </div>
        </div>
        <!-- } -->
      </ion-content>
    </div>
  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #isSuccessPopup [isOpen]="isSuccessfulResponse"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-body no-padding-top">
        <div class="campaign-card">
          <div class="campaign-card-header success-modal-card-header">
            <div class="campaign-image-slide success-modal-card-image-slide">
              <i-feather name="X" class="close-icon white-icon" (click)="closeDetails()"></i-feather>
            </div>
          </div>
          <div class="coin-container">
            <ion-icon class="balance-icon" [src]="'/assets/images/svg/success-icon.svg'"></ion-icon>
          </div>
          <div class="campaign-detail-container">
            <span class="campaign-detail-title success-text">Congratulations!</span>

            <div class="success-container">
              <!-- <span>Payment completed successfully.</span> -->

              <span class="earned-my-cash my-cash margin-top-10" *ngIf="successPaymentDetails?.myCash > 0">
                You've earned {{ successPaymentDetails?.myCash | currency : 'INR' : 'symbol' : '1.0-0' }} MyCash! 💰
              </span>

              <span class="earned-promo-cash margin-top-5" *ngIf="successPaymentDetails?.promo > 0">
                You've earned {{ successPaymentDetails?.promo | currency : 'INR' : 'symbol' : '1.0-0' }} PromoCash! 🎁
              </span>
            </div>
          </div>

          <div class="privacy-container margin-top-20">
            <ion-button class="site-full-rounded-button less-border-radius margin-left-20 margin-right-20" expand="full"
              shape="round" type="submit" (click)="openDetails(successPaymentDetails)">
              View Detail
            </ion-button>
          </div>

        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>