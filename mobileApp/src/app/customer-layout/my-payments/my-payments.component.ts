import { ChangeDetectorR<PERSON>, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IonInfiniteScroll, NavController } from '@ionic/angular';
import { maskitoParseNumber } from '@maskito/kit';
import { DatePicker } from '@pantrist/capacitor-date-picker';
import { Checkout } from 'capacitor-razorpay';
import { NgxMaskPipe } from 'ngx-mask';
import { take } from 'rxjs';
import { environment } from 'src/environments/environment';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { ToastService } from 'src/shared/toast.service';
import { Filesystem, Directory } from '@capacitor/filesystem';
import { LocalStorageService } from 'src/shared/local-storage.service';

@Component({
  selector: 'app-my-payments',
  templateUrl: './my-payments.component.html',
  styleUrls: ['./my-payments.component.scss'],
})
export class MyPaymentsComponent implements OnInit, OnDestroy {
  isDetailsOpen: boolean = false;
  isFullyPaidOrNot: boolean = false;
  isFilterPopupOpen: boolean = false;
  user: any;
  filter: any;
  pageSize: number = 10;
  loading: string = "NOT_STARTED";
  searchText: string | undefined;
  payments: Array<any> = new Array<any>();
  appliedFilter: any;
  convenienceFeeGst: number = 0;
  totalAmountWithConvenienceFee: number = 0;
  UpdatedbalanceAmount: number = 0;
  selectedClient: any;
  onClickValidation: boolean = false;
  paymentAmount: string = "";
  statusTypes: Array<{ display: string, value: string }> = [
    { display: 'Due', value: 'DUE' },
    { display: 'Overdue', value: 'OVERDUE' },
    { display: 'Partially Completed', value: 'PARTIALLY_COMPLETED' }
  ];
  view: string = "FULL_PAYMENT";
  isPaymentProcessing: boolean = false;
  isDetailsVisible: boolean = false;
  isOtherAmountSelected: boolean = false;
  paymentId: string = '';
  selectedPayment: any;
  queryParamsSubscribe: any;

  isRequestInProgress: boolean = false;
  profileImageUrl: string | null = null;

  selectedPaymentOption: string | null = null;
  successfulPaymentType: string | null = null;
  successfulPaymentUser: string | null = null;

  constructor(private authService: AuthService,
    private route: ActivatedRoute,
    private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    public readonly commonService: CommonService,
    private readonly maskPipe: NgxMaskPipe,
    private readonly cdr: ChangeDetectorRef,
    private localStorageService: LocalStorageService,
    private router: Router
  ) {

  }

  ngOnInit() {
    this.user = this.authService.getUser();
  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();


    this.profileImageUrl = this.localStorageService.get('profileImageUrl');

    this.closeFullyPaidPopup();
    this.payments = new Array<any>();
    this.filter = {} as any;
    this.filter.offset = 1;
    this.filter.tabType = "CURRENT";

    // this.queryParamsSubscribe = this.route.queryParams.pipe(take(1)).subscribe(params => {
    this.queryParamsSubscribe = this.route.snapshot.queryParams;
    if (!this.user) {
      let currentUrl = this.router.url;
      this.localStorageService.set('redirectAfterLogin', currentUrl);
      this.commonService.openLoginModal();
      return;
    }
    if (this.isObjectEmpty(this.queryParamsSubscribe)) {
      this.fetchMyPayments(null, true);
      this.paymentId = this.queryParamsSubscribe.paymentId;
      return;
    }
    this.filter.tabType = this.queryParamsSubscribe.tab || "CURRENT";
    this.paymentId = this.queryParamsSubscribe.paymentId;
    const paymentType = this.queryParamsSubscribe['fcmPaymentType'];
    const status = this.queryParamsSubscribe['status'];

    if (paymentType === "BOOKING_ADDITIONAL_AMOUNT_CANCELLED" || paymentType === "FPH_ADDITIONAL_AMOUNT_CANCELLED"
      || paymentType === "OTHER_ADDITIONAL_AMOUNT_CANCELLED" || paymentType === "HOTEL_BOOKING_ADDITIONAL_AMOUNT_CANCELLED"
      || status === "COMPLETED"
    ) {
      this.filter.tabType = "CLOSED";
    }
    // else {
    //   this.filter.tabType = this.queryParamsSubscribe.tab || "CURRENT";
    //   console.log("in else notification::",this.filter.tabType);
    // }

    this.successfulPaymentType = this.queryParamsSubscribe.successPaymentType || null;
    this.successfulPaymentUser = this.queryParamsSubscribe.userSelectPayment || null;
    if (
      this.successfulPaymentType !== null &&
      this.successfulPaymentUser !== null
    ) {
      if (
        (this.successfulPaymentType !== 'EMI' && this.successfulPaymentType !== 'ASF' &&
          this.successfulPaymentUser !== 'PartiallyPaid') ||
        ((this.successfulPaymentType === 'EMI' || this.successfulPaymentType === 'ASF') &&
          this.successfulPaymentUser === 'FullyPaid')
      ) {
        this.filter.tabType = "CLOSED";
        this.cdr.detectChanges();
        this.fetchMyPayments(null, true);
        return;
      }
    }

    this.cdr.detectChanges();
    this.fetchMyPayments(null, true);
    // });
  }

  isObjectEmpty(obj: object): boolean {
    return Object.keys(obj).length === 0;
  }

  fetchMyPayments($event: any, handleLoading: boolean) {

    const payload = {
      tabType: this.filter.tabType || 'CURRENT',
      fromDate: this.filter.fromDate || null,
      toDate: this.filter.toDate || null,
      status: this.filter.status || null,
      pagination: {
        next: this.pageSize,
        offset: this.filter.offset
      }
    };
    if (handleLoading) { this.loading = "LOADING"; }
    this.dataService.myPayments(payload)
      .subscribe({
        next: (response: RestResponse) => {
          this.loading = "LOADED";
          this.isRequestInProgress = false;
          if (response.data.length > 0) {
            if (this.filter.tabType === 'UPCOMING' || this.filter.tabType === 'CURRENT') {
              this.calculatePaymentsWithCovienceFee(response.data)
            }
            this.payments.push(...response.data);
          }
          if ($event) {
            $event.target.complete();
            if (this.payments.length > 0 && this.payments.length >= this.payments[0].totalCount) {
              $event.target.disabled = true;
            }
          }
          this.cdr.detectChanges();
        }, error: (error: any) => {
          this.loading = "LOADED";
          this.isRequestInProgress = false;
          this.toastService.show(error.message || 'An error occurred');
        }
      })
  }

  calculatePaymentsWithCovienceFee(payments: []) {
    payments.forEach(async (payment: any) => {
      let fee = this.calculateConvenienceFee(payment);
      payment.UpdatedbalanceAmount = payment.balanceAmount + fee;
    });
  }

  onPageChanged($event: any) {
    if (this.payments.length > 0 && this.payments.length >= this.payments[0].totalCount) {
      $event.target.complete();
      $event.target.disabled = true;
      return;
    }
    this.filter.offset = this.filter.offset + 1;
    this.fetchMyPayments($event, false);
  }

  onChangeStatusTab(status: string) {
    if (this.isRequestInProgress) {
      //  this.toastService.show("Please wait for the current operation to complete.");
      return;
    }
    this.isRequestInProgress = true;
    this.filter.tabType = status;
    this.payments = new Array<any>();
    this.filter.offset = 1;
    this.fetchMyPayments(null, true);
  }



  onSelectPartialPayment() {
    this.view = 'Partial Payment';
    this.isDetailsVisible = true;
    this.isOtherAmountSelected = true;
    this.cdr.detectChanges();
  }

  openPaymentDetailPopup(payment: any) {
    if (this.filter.tabType === 'CLOSED') {
      this.convenienceFeeGst = payment.convenienceFee + payment.convenienceFeeGst;
    } else if (['CURRENT', 'UPCOMING'].includes(this.filter.tabType)) {
      this.convenienceFeeGst = this.calculateConvenienceFee(payment);
    }
    this.selectedPayment = { ...payment, dueAmount: payment.dueAmount };
    this.isDetailsOpen = true;
  }

  private calculateConvenienceFee(payment: any): number {
    const { paymentType, dueAmount, balanceAmount, convenienceFeeConfigurationDetail } = payment;
    const feeConfig: Record<string, number> = {
      ASF: convenienceFeeConfigurationDetail.asf,
      EMI: convenienceFeeConfigurationDetail.emi,
      HOTEL_BOOKING: convenienceFeeConfigurationDetail.booking,
      OTHER: convenienceFeeConfigurationDetail.additional,
      BOOKING_ADDITIONAL_AMOUNT: convenienceFeeConfigurationDetail.additional,
      FPH_ADDITIONAL_AMOUNT: convenienceFeeConfigurationDetail.additional
    };
    if (paymentType in feeConfig) {
      if ((this.filter.tabType === 'CURRENT' || this.filter.tabType === 'UPCOMING') && (paymentType === 'EMI' || paymentType === 'ASF')) {
        const baseFee = balanceAmount * (feeConfig[paymentType as keyof typeof feeConfig] / 100);
        const gstFee = (convenienceFeeConfigurationDetail.gstRate / 100) * baseFee;
        return baseFee + gstFee;
      }
      const baseFee = dueAmount * (feeConfig[paymentType as keyof typeof feeConfig] / 100);
      const gstFee = (convenienceFeeConfigurationDetail.gstRate / 100) * baseFee;
      return baseFee + gstFee;
    }
    return 0; // Default case if no matching payment type
  }


  closeDetailsPopup() {
    this.isDetailsOpen = false;
    setTimeout(() => this.selectedPayment = undefined);
    this.cdr.detectChanges();
  }

  isPaymentDueExpired(paymentDueDate: string): boolean {
    const currentDate = new Date();
    const dueDate = new Date(paymentDueDate);
    // Return true if the current date is after the due date
    return currentDate > dueDate;
  }

  getIconForStatus(status: string): string {
    switch (status) {
      case 'PARTIALLY_COMPLETED':
        return '/assets/images/svg/partial-icon.svg';
      case 'COMPLETED':
        return '/assets/images/svg/complete-icon.svg';
      case 'CANCELLED':
        return '/assets/images/svg/cancel-icon.svg';
      case 'DUE':
        return '/assets/images/svg/due-icon.svg';
      case 'OVERDUE':
        return '/assets/images/svg/overdue-icon.svg';
      case 'REFUND_IN_PROGRESS':
        return '/assets/images/svg/refund-inprogress.svg';
      default:
        return '/assets/images/svg/due-icon.svg'; // Fallback for unknown statuses
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'PARTIALLY_COMPLETED':
        return 'partially-completed';
      case 'PAID':
        return 'status-complete';
      case 'CANCELLED':
        return 'status-close';
      case 'DUE':
        return 'status-due';
      case 'OVERDUE':
        return 'status-overDue';
      case 'REFUND_IN_PROGRESS':
        return 'status-refund';
      default:
        return 'status-default';
    }
  }

  openChooseOptionPopup(payment: any) {
    this.closeFullyPaidPopup();
    const amount = payment.balanceAmount || 0;
    if (payment.paymentType === 'EMI' || payment.paymentType === 'ASF') {
      this.isFullyPaidOrNot = true;
      this.selectedPayment = JSON.parse(JSON.stringify(payment));
      this.paymentAmount = this.maskPipe.transform(amount.toString(), "separator.2", { thousandSeparator: "," });
      return;
    }
    this.selectedPaymentOption = 'FullyPaid';
    this.selectedPayment = payment;

    this.navController.navigateForward("/portal/select/payment/method", {
      animated: true,
      queryParams: {
        selectedPayment: payment,
        amount: amount,
        fromScreen: 'my-payments',
        userSelectPayment: this.selectedPaymentOption
      }
    });
    //  this.createOrder(payment.id, payment.paymentType, amount);
    setTimeout(() => {
      this.cdr.detectChanges();
    }, 50);
  }

  closeFullyPaidPopup() {
    this.isFullyPaidOrNot = false;
    this.isOtherAmountSelected = false;
    this.isDetailsVisible = false;
    this.view = "FULL_PAYMENT";
  }

  paymentProcess(form: any) {
    this.onClickValidation = true;
    if (form.invalid) {
      return;
    }
    const amount = maskitoParseNumber(this.paymentAmount.toString());

    if (this.view !== 'Partial Payment') {
      this.createOrderAndProcessPayment(this.selectedPayment.balanceAmount);
      return;
    }
    if (amount > (this.selectedPayment.dueAmount || 0)) {
      form.controls.payment.setErrors({
        customMaxAmount: true,
        maxAmount: `₹ ${this.commonService.formatNumber(this.selectedPayment.dueAmount)}`
      });
      this.onClickValidation = true;
      return;
    }
    this.createOrderAndProcessPayment(amount);
  }

  createOrderAndProcessPayment(finalAmount: number) {
    this.closeFullyPaidPopup();
    setTimeout(() => {
      this.navController.navigateForward("/portal/select/payment/method", {
        animated: true,
        queryParams: {
          selectedPayment: this.selectedPayment,
          amount: finalAmount,
          fromScreen: 'my-payments',
          userSelectPayment: this.selectedPaymentOption
        }
      });
    }, 300);
    //  this.createOrder(this.selectedPayment.id, this.selectedPayment.paymentType, finalAmount)
  }



  async createOrder(id: any, paymentType: any, dueAmount: any) {

    this.isPaymentProcessing = true;
    const input: any = {
      paymentType,
      paymentLogId: id
    };

    // Calculate Convenience Fee & GST
    const feeConfig: Record<string, number> = {
      ASF: this.selectedPayment?.convenienceFeeConfigurationDetail.asf,
      EMI: this.selectedPayment?.convenienceFeeConfigurationDetail.emi,
      HOTEL_BOOKING: this.selectedPayment?.convenienceFeeConfigurationDetail.booking,
      OTHER: this.selectedPayment?.convenienceFeeConfigurationDetail.additional,
      BOOKING_ADDITIONAL_AMOUNT: this.selectedPayment.convenienceFeeConfigurationDetail.booking,
      FPH_ADDITIONAL_AMOUNT: this.selectedPayment.convenienceFeeConfigurationDetail.additional
    };

    if (this.selectedPayment?.paymentType in feeConfig) {
      input.convenienceFee = (dueAmount * (feeConfig[this.selectedPayment?.paymentType] / 100));
      input.convenienceFeeGst = (input.convenienceFee * this.selectedPayment?.convenienceFeeConfigurationDetail.gstRate / 100);
      input.amount = dueAmount;
    }
    // Call API
    this.dataService.createOrder(input).subscribe({
      next: (response: RestResponse) => {
        this.openPaymentPopup(response.data, paymentType);
        setTimeout(() => (this.isPaymentProcessing = false));
      },
      error: (error) => {
        setTimeout(() => (this.isPaymentProcessing = false));
        this.toastService.show(error.message);
      }
    });
  }


  async openPaymentPopup(data: any, paymentType: any) {
    const options: any = {
      key: environment.PaymentGateway.Key,
      description: paymentType,
      image: environment.PaymentGateway.Image,
      order_id: data.orderId,
      currency: environment.PaymentGateway.Currency,
      name: environment.PaymentGateway.Name,
      prefill: {
        email: this.user.email,
        contact: `${this.user.countryCode}${this.user.phoneNumber}`
      },
      theme: {
        color: '#29385B'
      }
    }
    try {
      let paymentResponse = (await Checkout.open(options));
      this.paymentProcessing(data, paymentResponse.response, paymentType);
    } catch (error: any) {
      this.isPaymentProcessing = false;
      if (!error) {
        return;
      }
      let errorObj = JSON.parse(error)
      if (!errorObj || !errorObj.description) {
        return;
      }
      this.toastService.show(errorObj.description);
    }
  }

  paymentProcessing(data: any, paymentResponse: any, paymentType: string) {
    const payload = {
      orderId: paymentResponse.razorpay_order_id,
      paymentId: paymentResponse.razorpay_payment_id,
      signature: paymentResponse.razorpay_signature
    }
    this.loadingService.show();
    const method = paymentType === 'FPH_ADDITIONAL_AMOUNT' ? 'fphPayments' : (paymentType === 'EMI' || paymentType === 'ASF') ? 'recurringPayments' : 'otherPayments';
    this.dataService[method](payload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.isPaymentProcessing = false;
        this.navController.navigateForward("/portal/confirm/payment", { animated: true });
      },
      error: (error) => {
        this.loadingService.hide();
        this.isPaymentProcessing = false;
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  downloadInvoice(payment: any) {
    //  this.loadingService.show();
    this.isPaymentProcessing = true;

    const method = (payment.paymentType === 'BOOKING_PAYMENT' || payment.paymentType === 'BOOKING ADDITIONAL AMOUNT') ? 'invoiceDownloadForBooking' : 'invoiceDownloadForOthers';
    this.dataService[method](payment.id).subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        this.isPaymentProcessing = false;

        if (response && response.data) {
          const fileUrl = response.data.filePath; // URL of the file
          const fileName = response.data.fileName;
          this.saveFileToDownloads(fileUrl, fileName);
        } else {
          this.toastService.show('Failed to retrieve the file URL.');
        }
      },
      error: (error) => {
        //  this.loadingService.hide();
        this.isPaymentProcessing = false;
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  async saveFileToDownloads(fileUrl: string, fileName: string) {
    try {
      // Fetch the file
      const response = await fetch(fileUrl);
      const invoiceName = fileName;
      // Save the file to the Documents directory using Capacitor Filesystem
      await Filesystem.writeFile({
        path: invoiceName,
        data: response?.url,
        directory: Directory.Documents, // Save to Documents folder
      });

      this.toastService.show('File saved to Documents folder.');
    } catch (error) {
      console.error('Error saving file:', error);
      this.toastService.show('Failed to save the file.');
    }
  }

  openFilterPopup() {
    this.isFilterPopupOpen = true;
    this.appliedFilter = JSON.parse(JSON.stringify(this.filter));

    this.cdr.detectChanges();

    // Format fromDate and toDate before showing in fields
    if (this.appliedFilter.fromDate) {
      const date = new Date(this.appliedFilter.fromDate);
      this.appliedFilter.fromDate = `${this.commonService.getDisplayValue(date.getFullYear())}-${this.commonService.getDisplayValue(date.getMonth() + 1)}-${date.getDate()}`;
    }
    if (this.appliedFilter.toDate) {
      const date = new Date(this.appliedFilter.toDate);
      this.appliedFilter.toDate = `${this.commonService.getDisplayValue(date.getFullYear())}-${this.commonService.getDisplayValue(date.getMonth() + 1)}-${date.getDate()}`;
    }

  }

  closeFilterPopup() {
    this.isFilterPopupOpen = false;
  }

  clearFilter() {
    // Reset appliedFilter and filter models
    this.appliedFilter.fromDate = undefined;
    this.appliedFilter.toDate = undefined;
    this.appliedFilter.status = undefined;
    this.filter.fromDate = undefined;
    this.filter.toDate = undefined;
    this.filter.status = undefined;

    this.closeFilterPopup();  // Close the filter popup

    // Reset and reload payments
    setTimeout(() => {
      this.payments = new Array<any>();
      this.filter.offset = 1;
      this.fetchMyPayments(null, true);
    });
  }

  applyFilter() {
    if (!this.hasValue(this.appliedFilter.fromDate)
      && !this.hasValue(this.appliedFilter.toDate)
      && !this.hasValue(this.appliedFilter.status)) {
      this.toastService.show("Please provide at least one value to filter");
      return;
    }

    if (this.hasValue(this.appliedFilter.fromDate) && !this.hasValue(this.appliedFilter.toDate)) {
      this.toastService.show("Please select a valid To Date.");
      return;
    }
    if (!this.hasValue(this.appliedFilter.fromDate) && this.hasValue(this.appliedFilter.toDate)) {
      this.toastService.show("Please select a valid From Date.");
      return;
    }

    // Reapply the date format for fromDate and toDate in the correct format
    if (this.appliedFilter.fromDate) {
      const fromDate = new Date(this.appliedFilter.fromDate);
      this.filter.fromDate = `${fromDate.getFullYear()}-${(fromDate.getMonth() + 1).toString().padStart(2, '0')}-${fromDate.getDate().toString().padStart(2, '0')}`;
    }

    if (this.appliedFilter.toDate) {
      const toDate = new Date(this.appliedFilter.toDate);
      this.filter.toDate = `${toDate.getFullYear()}-${(toDate.getMonth() + 1).toString().padStart(2, '0')}-${toDate.getDate().toString().padStart(2, '0')}`;
    }

    this.filter.status = this.appliedFilter.status;
    this.closeFilterPopup();  // Close the filter popup

    // Reset and reload payments with the new filter
    setTimeout(() => {
      this.payments = new Array<any>();  // Clear existing data
      this.filter.offset = 1;
      this.fetchMyPayments(null, true);
    });
  }

  hasValue(value: string) {
    return value && value.trim() !== "";
  }

  openFromDatePicker() {
    const today = new Date();
    let minDate: string | undefined;
    let maxDate: string | undefined;

    // Set min/max dates based on tab type
    if (this.filter.tabType === 'UPCOMING') {
      minDate = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;
      maxDate = undefined;
    } else if (this.filter.tabType === 'CLOSED') {
      minDate = undefined;
      maxDate = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;
    }

    // If 'fromDate' exists, use it as the initial date in the date picker
    const fromDate = this.appliedFilter.fromDate ? new Date(this.appliedFilter.fromDate) : today;
    const formattedFromDate = `${fromDate.getDate().toString().padStart(2, '0')}/${(fromDate.getMonth() + 1).toString().padStart(2, '0')}/${fromDate.getFullYear()}`;

    DatePicker.present({
      mode: 'date',
      format: 'dd/MM/yyyy',
      min: minDate,
      max: maxDate,
      date: formattedFromDate,
    }).then(date => {
      if (date && date.value) {
        const [day, month, year] = date.value.split('/');
        this.appliedFilter.fromDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        this.cdr.detectChanges();
      }
    }).catch(error => {
      console.error('Date Picker Error:', error);
    });
  }

  openToDatePicker() {
    if (!this.appliedFilter.fromDate) {
      this.toastService.show("Please select from date first.");
      return;
    }

    const today = new Date();
    let minDate: string | undefined;
    let maxDate: string | undefined;

    // Set min/max dates based on tab type
    if (this.filter.tabType === 'UPCOMING') {
      minDate = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;
      maxDate = undefined;  // No max date for upcoming tab
    } else if (this.filter.tabType === 'CLOSED') {
      minDate = undefined;  // No min date for closed tab
      maxDate = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;
    }

    const fromDate = this.appliedFilter.fromDate ? new Date(this.appliedFilter.fromDate) : today;
    const formattedFromDate = `${fromDate.getDate().toString().padStart(2, '0')}/${(fromDate.getMonth() + 1).toString().padStart(2, '0')}/${fromDate.getFullYear()}`;

    const toDate = this.appliedFilter.toDate ? new Date(this.appliedFilter.toDate) : today;
    const formattedToDate = `${toDate.getDate().toString().padStart(2, '0')}/${(toDate.getMonth() + 1).toString().padStart(2, '0')}/${toDate.getFullYear()}`;

    DatePicker.present({
      mode: 'date',
      format: 'dd/MM/yyyy',
      min: formattedFromDate,
      max: maxDate,
      date: formattedToDate,
    }).then(date => {
      if (date && date.value) {
        const [day, month, year] = date.value.split('/');
        this.appliedFilter.toDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        this.cdr.detectChanges();
      }
    }).catch(error => {
      console.error('Date Picker Error:', error);
    });
  }

  ngOnDestroy(): void {
    // if (this.queryParamsSubscribe) {
    //   this.queryParamsSubscribe.unsubscribe();
    // }
  }
}
