
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-payment-confirm',
  templateUrl: './payment-confirm.component.html',
  styleUrls: ['./payment-confirm.component.scss'],
})
export class PaymentConfirmComponent implements OnInit {

  queryParamsSubscribe: any;
  userSelectPayment: string | null = null;
  successpaymentType: string | null = null;

  constructor(private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly cdr: ChangeDetectorRef,
    private route: ActivatedRoute,
    private readonly commonService: CommonService
  ) {

  }

  ngOnInit() { }

  ionViewWillEnter() {
    this.route.queryParams.subscribe(params => {
      if (this.commonService.isObjectEmpty(params)) {
        return;
      }

      this.userSelectPayment = params['userSelectPayment'];
      this.successpaymentType = params['successPaymentType'];

      this.cdr.detectChanges();
    });
  }

  returnToPayments() {
    //  this.navController.navigateRoot("/portal/payment", { animated: true });
    this.navController.navigateForward(['/payment'], {
      queryParams: {
        userSelectPayment: this.userSelectPayment,
        successPaymentType: this.successpaymentType
      },
      animated: true
    });
  }

}

