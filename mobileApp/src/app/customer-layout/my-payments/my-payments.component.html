<ion-content class="customer-dashboard-page customer-payment-page">
  <app-customer-header [headingText]="'My Payments'" [rightAction]="true" [rightActionIcon]="'Filter'"
    (rightActionCallback)="openFilterPopup()" [profileImageUrl]="profileImageUrl"></app-customer-header>
  <!-- Tabs Section -->
  <div class="customer-body-section has-header-section no-padding"
    [ngClass]="{'full-height': filter?.tabType === 'UPCOMING'}">
    <div class="payment-status-items">
      <div class="payment-status-item" [ngClass]="{'active': filter?.tabType === 'CURRENT'}"
        (click)="onChangeStatusTab('CURRENT')">Current</div>
      <div class="payment-status-item" [ngClass]="{'active': filter?.tabType === 'UPCOMING'}"
        (click)="onChangeStatusTab('UPCOMING')">Upcoming</div>
      <div class="payment-status-item" [ngClass]="{'active': filter?.tabType === 'CLOSED'}"
        (click)="onChangeStatusTab('CLOSED')">Closed</div>
    </div>
    <div class="payment-body-section">
      <ion-content class="payment-body-ion-content scroll">
        @if (loading==='LOADING') {
        <div class="payment-card-container">
          <div class="payment-item-card">
            <div class="payment-item-container">
              <div class="payment-status-container no-top-margin"
                *ngIf="filter.tabType==='CURRENT' || filter.tabType==='CLOSED'">
                <div class="status-info">
                  <span class="status-icon">
                    <ion-icon class="status-ion-icon" slot="start">
                      <ion-skeleton-text [animated]="true" style="width: 45px;height: 45px;"></ion-skeleton-text>
                    </ion-icon>
                  </span>
                  <span class="status-label">
                    <ion-skeleton-text [animated]="true" style="width: 40px;height: 40px;"></ion-skeleton-text>
                  </span>
                </div>
              </div>

              <!-- Round Icon Section -->
              <div class="new-design-container">
                <ion-skeleton-text [animated]="true" style="width: 42px;height: 42px;"></ion-skeleton-text>
                <!-- Text fields -->
                <div>
                  <div class="new-design-id"><ion-skeleton-text [animated]="true"
                      style="width: 150px;height: 22px;"></ion-skeleton-text></div>
                  <div class="new-design-date" *ngIf="filter.tabType==='CURRENT' || filter.tabType==='UPCOMING'">
                    <ion-skeleton-text [animated]="true" style="width: 140px;height: 10px;"></ion-skeleton-text>
                  </div>
                  <div class="new-design-date" *ngIf="filter.tabType==='CLOSED'">
                    <ion-skeleton-text [animated]="true" style="width: 140px;height: 10px;"></ion-skeleton-text>
                  </div>
                </div>
              </div>

              <!-- Payment Details Section -->
              <div class="new-design-payment-container">
                <div class="payment-case payment-date"
                  *ngIf="filter.tabType==='CURRENT' || filter.tabType==='UPCOMING' || filter.tabType==='CLOSED'">
                  <div class="payment-text payment-type no-border"><ion-skeleton-text [animated]="true"
                      style="width: 150px;height: 25px;"></ion-skeleton-text></div>
                </div>
                <div class="new-design-amount-type">
                  <div class="new-design-amount"
                    *ngIf="(filter.tabType === 'CURRENT' || filter.tabType === 'UPCOMING')">
                    <ion-skeleton-text [animated]="true" style="width: 130px;height: 25px;"></ion-skeleton-text>
                  </div>
                  <div class="new-design-amount" *ngIf="filter.tabType==='CLOSED'">
                    <ion-skeleton-text [animated]="true" style="width: 130px;height: 25px;"></ion-skeleton-text>
                  </div>
                  <div class="new-design-amount" *ngIf="filter.tabType==='CLOSED'">
                    <ion-skeleton-text [animated]="true" style="width: 130px;height: 25px;"></ion-skeleton-text>
                  </div>
                </div>
                <!-- <div class="new-design-payment-by-card">{{ formatPaymentAllCapital(payment.modeOfPayment) }}</div> -->
              </div>
            </div>
            <ion-button class="primary-button pay-now-button" expand="full" shape="round"
              type="submit"><ion-skeleton-text [animated]="true"
                style="width: 130px;height: 25px;"></ion-skeleton-text></ion-button>
          </div>
        </div>
        }@else if (loading==='LOADED') {
        <div class="payment-card-container payment-card-ios">
          <div *ngFor="let payment of payments">
            <div class="payment-item-card"
              [ngClass]="{'highlighted-payment': payment.id === paymentId,'margin-bottom-15': !payment?.packageName}">
              <div class="payment-item-container" (click)="openPaymentDetailPopup(payment)">
                <!-- <div class="payment-status-container" *ngIf="filter.tabType==='CURRENT' || filter.tabType==='CLOSED'">
                          <div class="status-info">
                            <span class="status-icon">
                              <ion-icon class="status-ion-icon" [src]="getIconForStatus(payment.status)" slot="start">
                              </ion-icon>
                            </span>
                            <span class="status-label"
                              [ngClass]="getStatusClass(filter.tabType === 'CLOSED' && payment.status === 'COMPLETED' ? 'PAID' : payment.status)">
                              {{ commonService.formatText(filter.tabType === 'CLOSED' && payment.status === 'COMPLETED' ? 'PAID' :
                              payment.status) }}!
                            </span>
                          </div>
                        </div> -->

                <!-- Round Icon Section -->
                <div class="new-design-container">
                  <ion-icon src="/assets/images/svg/payment-hand.svg" slot="start" class="icon-style"></ion-icon>
                  <div>
                    <div class="new-design-id">{{ payment.transactionId }}</div>
                    <div class="new-design-date">{{commonService.formatTextAllCapital(payment.paymentType)}}</div>
                  </div>
                </div>

                <!-- Payment Details Section -->
                <div>
                  <div class="new-design-payment-container">
                    <div class="amount-icon-container">
                      <div class="new-design-amount-type">
                        <div class="new-design-amount"
                          *ngIf="(filter.tabType === 'CURRENT' || filter.tabType === 'UPCOMING') && payment.balanceAmount > 0">
                          {{((filter.tabType === 'CURRENT' || filter.tabType === 'UPCOMING')?
                          payment.UpdatedbalanceAmount: payment.balanceAmount )| currency :'INR':'symbol':'1.0-2' }}
                        </div>
                        <div class="new-design-amount" *ngIf="filter.tabType==='CLOSED' && payment.status!='CANCELLED'">
                          {{ payment.paidAmount | currency :'INR':'symbol':'1.0-2' }}
                        </div>

                        <div class="new-design-amount"
                          *ngIf="filter.tabType==='CLOSED' && payment.status==='CANCELLED'">
                          {{ payment.dueAmount | currency :'INR':'symbol':'1.0-2' }}
                        </div>
                      </div>
                      <div class="new-design-date" [ngClass]="{ 'expired-date': payment.status==='OVERDUE' }">
                        <div *ngIf="filter.tabType==='CURRENT' || filter.tabType==='UPCOMING'">
                          <span>Due Date: </span> <span>{{ commonService.formatDate(payment.paymentDueDate) }}</span>
                        </div>
                        <div *ngIf="filter.tabType==='CLOSED'">
                          <span>Paid On: </span> <span>{{ commonService.formatDate(payment.paymentDate) }}</span>
                        </div>
                      </div>
                    </div>
                    <!-- <div class="new-design-payment-by-card">{{ formatPaymentAllCapital(payment.modeOfPayment) }}</div> -->
                    <div *ngIf="filter.tabType==='CURRENT' || filter.tabType==='CLOSED'">
                      <div class="status-icon-container">
                        <span class="status-info">
                          <ion-icon class="status-icon" [src]="getIconForStatus(payment.status)" slot="start">
                          </ion-icon>
                        </span>
                        <span class="status-label"
                          [ngClass]="getStatusClass(filter.tabType === 'CLOSED' && payment.status === 'COMPLETED' ? 'PAID' : payment.status)">
                          {{ commonService.formatText(filter.tabType === 'CLOSED' && payment.status === 'COMPLETED' ?
                          'PAID'
                          :
                          payment.status) }}!
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
              <ion-button class="primary-button pay-now-button" expand="full" shape="round" type="submit"
                [disabled]="isPaymentProcessing"
                *ngIf="payment.dueAmount > 0 && filter.tabType==='CURRENT' || filter.tabType==='UPCOMING'"
                (click)="openChooseOptionPopup(payment)">
                <div>
                  <span class="pay-now-text">Pay Now</span>
                  <img src="/assets/images/icons/right-arrow-icon.png" alt="Pay Now" width="20px" />
                </div>
              </ion-button>
              <ion-button class="primary-button pay-now-button" expand="full" shape="round" type="submit"
                *ngIf="filter.tabType==='CLOSED' && payment.status != 'CANCELLED'" [disabled]="isPaymentProcessing"
                (click)="downloadInvoice(payment)">
                <div>
                  <span class="pay-now-text">Download Invoice</span>
                  <img src="/assets/images/svg/download.svg" alt="Pay Now" width="22px" />
                </div>
              </ion-button>

            </div>
            <div class="card-bottom-show-detail" *ngIf="payment?.packageName"
              [ngStyle]="{'--package-color': payment?.color}">
              <div class="package-design margin-bottom-8">{{payment?.packageName}}</div>
            </div>
          </div>

          <div class="payment-item margin-top-10" *ngIf="payments.length<=0">
            <div class="no-record" *ngIf="filter.tabType==='CURRENT'">All payments are up to date. No active dues.</div>
            <div class="no-record" *ngIf="filter.tabType==='UPCOMING'">No upcoming payments at this time.</div>
            <div class="no-record" *ngIf="filter.tabType==='CLOSED'">No completed payments available.</div>
          </div>
        </div>

        <ion-infinite-scroll threshold="50px" *ngIf="payments.length > 0" (ionInfinite)="onPageChanged($event)">
          <ion-infinite-scroll-content loadingSpinner="bubbles"
            loadingText="Loading more data..."></ion-infinite-scroll-content>
        </ion-infinite-scroll>
        }
      </ion-content>
    </div>
  </div>
  <!-- Floating Action Button -->
  <!-- <ion-fab vertical="bottom" horizontal="end" slot="fixed" (click)="openFilterPopup()">
    <ion-fab-button>
      <ion-icon src="assets/images/svg/filter.svg" class="fab-filter-icon"></ion-icon>
    </ion-fab-button>
  </ion-fab> -->
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup payment-card-container" #isDetailsPopup
  [isOpen]="isDetailsOpen" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeDetailsPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form payment-item-card ion-modal-payment-item-card" #recordForm="ngForm" novalidate>
          <div class="popup-large-heading">Payment Details</div>

          <div class="job-info margin-top-10 payment-item-container ion-modal-payment-item-container">
            <!-- Use ng-container to group the condition -->
            <ng-container *ngIf="selectedPayment">
              <div class="new-design-container">
                <div>
                  <div class="new-design-id">{{ selectedPayment.transactionId }}</div>
                  <div class="new-design-date">{{commonService.formatTextAllCapital(selectedPayment.paymentType)}}</div>
                </div>
              </div>
              <!-- Due Date -->
              <div class="payment-case ion-modal-payment-case"
                *ngIf="filter.tabType === 'CURRENT' || filter.tabType === 'UPCOMING'">
                <span>Due Date</span> <span class="payment-text "
                  [ngClass]="{ 'due-date-expired': selectedPayment.status==='OVERDUE' }">{{
                  commonService.formatDate(selectedPayment.paymentDueDate)
                  }}</span>

              </div>
              <!-- Payment Date for 'CLOSED' tab -->
              <ng-container *ngIf="filter.tabType === 'CLOSED'">
                <div class="payment-case ion-modal-payment-case">
                  Date <span class="payment-text">{{ commonService.formatDate(selectedPayment.paymentDate) }}</span>
                </div>

                <div class="payment-case ion-modal-payment-case">
                  Amount
                  <span class="payment-text">{{ selectedPayment.dueAmount | currency :'INR':'symbol':'1.0-0' }}</span>
                </div>

                <div class="payment-case ion-modal-payment-case" *ngIf="convenienceFeeGst>0">
                  Convenience fee (incl. GST)
                  <span class="payment-text">{{ convenienceFeeGst | currency :'INR'}}</span>
                </div>
              </ng-container>

              <!-- Payment Amounts Based on Type (ASF, EMI) -->
              <ng-container *ngIf="(filter.tabType === 'CURRENT' || filter.tabType === 'UPCOMING')">
                <!-- Base Amount for ASF and EMI -->
                <div *ngIf="selectedPayment.paymentType === 'ASF' || selectedPayment.paymentType === 'EMI'"
                  class="payment-case ion-modal-payment-case">
                  Amount
                  <span class="payment-text">{{ selectedPayment.baseAmount | currency :'INR':'symbol':'1.0-0'}}</span>
                </div>

                <div
                  *ngIf="selectedPayment.paymentType === 'OTHER' || selectedPayment.paymentType === 'FPH_ADDITIONAL_AMOUNT' || selectedPayment.paymentType === 'BOOKING_ADDITIONAL_AMOUNT'||selectedPayment.paymentType === 'HOTEL_BOOKING'"
                  class="payment-case ion-modal-payment-case">
                  Due Amount
                  <span class="payment-text">{{ selectedPayment.dueAmount | currency :'INR':'symbol':'1.0-2'}}</span>
                </div>

                <!-- Interest Amount for EMI Payment Type -->
                <div *ngIf="selectedPayment.paymentType === 'EMI' && selectedPayment.interestAmount > 0"
                  class="payment-case ion-modal-payment-case">
                  Interest ({{ selectedPayment.interestRate }}%)
                  <span class="payment-text">{{ selectedPayment.interestAmount | currency :'INR':'symbol':'1.0-0'
                    }}</span>
                </div>
                <!-- GST Amount for ASF Payment Type -->
                <div *ngIf="selectedPayment.gstAmount > 0" class="payment-case ion-modal-payment-case">
                  GST ({{ selectedPayment.gstRate }}%)
                  <span class="payment-text">{{ selectedPayment.gstAmount | currency :'INR':'symbol':'1.0-0' }}</span>
                </div>

                <div class="payment-case ion-modal-payment-case"
                  *ngIf="selectedPayment.convenienceFeeConfigurationDetail.asf>0 && selectedPayment.paymentType === 'ASF'">
                  Convenience fee (incl. GST)
                  <span class="payment-text">{{ convenienceFeeGst | currency :'INR':'symbol':'1.0-2' }}</span>
                </div>
                <div class="payment-case ion-modal-payment-case"
                  *ngIf="selectedPayment.convenienceFeeConfigurationDetail.emi>0 && selectedPayment.paymentType === 'EMI'">
                  Convenience fee (incl. GST)
                  <span class="payment-text">{{convenienceFeeGst | currency :'INR' :'symbol':'1.0-2' }}</span>
                </div>
                <div class="payment-case ion-modal-payment-case"
                  *ngIf="selectedPayment.convenienceFeeConfigurationDetail.booking>0 && selectedPayment.paymentType === 'HOTEL_BOOKING'">
                  Convenience fee (incl. GST)
                  <span class="payment-text">{{ convenienceFeeGst | currency :'INR' :'symbol':'1.0-2'}}</span>
                </div>
                <div class="payment-case ion-modal-payment-case"
                  *ngIf="selectedPayment.convenienceFeeConfigurationDetail.additional>0  && selectedPayment.paymentType === 'OTHER'">
                  Convenience fee (incl. GST)
                  <span class="payment-text">{{convenienceFeeGst | currency :'INR' :'symbol':'1.0-2'}}</span>
                </div>
                <div class="payment-case ion-modal-payment-case"
                  *ngIf="selectedPayment.convenienceFeeConfigurationDetail.booking>0  && selectedPayment.paymentType === 'BOOKING_ADDITIONAL_AMOUNT'">
                  Convenience fee (incl. GST)
                  <span class="payment-text">{{convenienceFeeGst | currency :'INR':'symbol':'1.0-2'}}</span>
                </div>
                <div class="payment-case ion-modal-payment-case"
                  *ngIf="selectedPayment.convenienceFeeConfigurationDetail.additional>0  && selectedPayment.paymentType === 'FPH_ADDITIONAL_AMOUNT'">
                  Convenience fee (incl. GST)
                  <span class="payment-text">{{convenienceFeeGst | currency :'INR' :'symbol':'1.0-2'}}</span>
                </div>
              </ng-container>

              <!-- Total, Paid, and Balance Amount -->
              <div class="payment-case ion-modal-payment-case">
                Total Amount
                <span class="payment-text amount-heading">
                  <ng-container *ngIf="filter.tabType === 'CURRENT' || filter.tabType === 'UPCOMING'">
                    {{ (selectedPayment.dueAmount + convenienceFeeGst )| currency :'INR'}}
                  </ng-container>
                  <ng-container *ngIf="filter.tabType === 'CLOSED'">
                    {{ (selectedPayment.status === 'CANCELLED' ? selectedPayment.dueAmount : selectedPayment.paidAmount)
                    | currency :'INR'}}
                  </ng-container>
                </span>
              </div>

              <div class="payment-case ion-modal-payment-case"
                *ngIf="filter.tabType === 'CURRENT' && selectedPayment.paidAmount > 0">
                Paid
                <span class="payment-text">{{ selectedPayment.paidAmount | currency :'INR':'symbol':'1.0-2' }}</span>
              </div>
              <div class="payment-case ion-modal-payment-case"
                *ngIf="filter.tabType === 'CURRENT' && (selectedPayment.paymentType==='ASF' || selectedPayment.paymentType==='EMI') && selectedPayment.balanceAmount > 0">
                Balance
                <span class="payment-text amount-heading">{{(selectedPayment.UpdatedbalanceAmount)| currency
                  :'INR':'symbol':'1.0-2' }}</span>
              </div>
            </ng-container>
            <div class="popup-large-heading" *ngIf="selectedPayment?.refundStatus">Refund Details</div>
            <ng-container *ngIf="selectedPayment?.refundStatus">
              <div class="payment-case ion-modal-payment-case" *ngIf="filter.tabType === 'CLOSED'">
                Refund Status <span class="payment-text">{{ selectedPayment?.refundStatus }}</span>
              </div>
              <div class="refund-text">
                <span [innerHTML]="selectedPayment?.refundToCustomer">{{selectedPayment?.refundToCustomer}}</span>
              </div>

            </ng-container>
          </div>

          <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
            (click)="closeDetailsPopup()">Close</ion-button>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #isPaidPopup [isOpen]="isFullyPaidOrNot"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeFullyPaidPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top" *ngIf="selectedPayment">

        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading" *ngIf="!isOtherAmountSelected">Choose Option</div>
          <div class="popup-large-heading" *ngIf="isOtherAmountSelected">Edit Amount</div>
          <div class="job-info margin-top-10">

            <!-- Full Payment Section -->
            <div *ngIf="!isOtherAmountSelected" class="choose-option-container"
              (click)="view='FULL_PAYMENT'; selectedPaymentOption = 'FullyPaid'; paymentProcess(recordForm.form)">
              <span class="total-amount">Pay Total</span>
              <div class="full-payment-container">
                <span class="full-payment-text">{{ ((filter.tabType === 'UPCOMING' || filter.tabType === 'CURRENT') ?
                  selectedPayment.UpdatedbalanceAmount: selectedPayment.balanceAmount )| currency
                  :'INR':'symbol':'1.0-2'}}</span>
                <ion-icon class="right-arrow-icon" src="assets/images/svg/right-arrow.svg"></ion-icon>
              </div>
            </div>

            <!-- Enter Other Amount Section -->
            <div *ngIf="!isOtherAmountSelected" class="choose-option-container margin-top-10"
              (click)="onSelectPartialPayment(); selectedPaymentOption = 'PartiallyPaid';">
              <span class="total-amount">Enter Other Amount</span>
              <div class="full-payment-container">
                <ion-icon class="right-arrow-icon" src="assets/images/svg/right-arrow.svg"
                  [name]="isDetailsVisible ? 'chevron-up-outline' : 'chevron-down-outline'">
                </ion-icon>
              </div>
            </div>

            <!-- Enter Amount Field & Pay Button -->
            <div *ngIf="isOtherAmountSelected && isDetailsVisible">
              <div class="margin-bottom-15 margin-top-10">
                <ion-item class="site-form-control" lines="none"
                  [ngClass]="{'is-invalid': payment.invalid && onClickValidation}">
                  <ion-icon src="/assets/images/svg/payment.svg" slot="start" class="start-icon"></ion-icon>
                  <ion-input label="Edit Amount" labelPlacement="floating" required="required" name="payment"
                    #payment="ngModel" [(ngModel)]="paymentAmount" mask="separator.2" thousandSeparator=",">
                  </ion-input>
                </ion-item>
                <span class="convenience-text">Convenience fee will be included.</span>
                <app-validation-message [field]="payment"
                  [onClickValidation]="onClickValidation"></app-validation-message>
              </div>

              <ion-button class="site-full-rounded-button primary-button text-capitalize margin-top-20" expand="full"
                shape="round" type="submit" (click)="paymentProcess(recordForm.form)">
                Pay
              </ion-button>
            </div>

          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #filterModal [isOpen]="isFilterPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeFilterPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">Apply Filter</div>
          <div class="job-info margin-top-10">
            <div class="margin-bottom-15">
              <ion-item class="site-form-control" lines="none">
                <ion-input readonly="readonly" (click)="openFromDatePicker()" label="From Date"
                  labelPlacement="floating" required="required" name="checkInDate" #checkInDate="ngModel"
                  [(ngModel)]="appliedFilter.fromDate" [value]="appliedFilter.fromDate | date:'yyyy-MM-dd'">
                </ion-input>
                <ion-icon slot="end" class="toggle-password-icon" (click)="openFromDatePicker()"
                  [src]="'/assets/images/svg/calendar.svg'"></ion-icon>
              </ion-item>
            </div>

            <div class="margin-bottom-15">
              <ion-item class="site-form-control" lines="none">
                <ion-input readonly="readonly" (click)="openToDatePicker()" label="To Date" labelPlacement="floating"
                  required="required" name="checkOutDate" #checkOutDate="ngModel" [(ngModel)]="appliedFilter.toDate"
                  [value]="appliedFilter.toDate | date:'yyyy-MM-dd'">
                </ion-input>
                <ion-icon slot="end" class="toggle-password-icon" (click)="openToDatePicker()"
                  [src]="'/assets/images/svg/calendar.svg'"></ion-icon>
              </ion-item>
            </div>

            <div class="margin-bottom-15" *ngIf="filter.tabType==='CURRENT'">
              <ion-item class="site-form-floating-control" lines="none">
                <ion-label position="floating">Select Status</ion-label>
                <ion-select #status="ngModel" [(ngModel)]="appliedFilter.status" interface="action-sheet"
                  required="required" name="status">
                  <ion-select-option *ngFor="let status of statusTypes" [value]="status.value">
                    {{ status.display }}
                  </ion-select-option>
                </ion-select>
                <ion-icon slot="end" class="select-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
              </ion-item>
            </div>

          </div>

          <ion-button class="site-full-rounded-button" shape="round" type="button" (click)="applyFilter()">
            Apply</ion-button>
          <ion-button class="site-full-rounded-button" shape="round" type="button" (click)="clearFilter()">
            Clear</ion-button>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>