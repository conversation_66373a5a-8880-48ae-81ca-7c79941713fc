<ion-content class="customer-dashboard-page my-notification-list">
  <app-customer-header [innerPage]="true" [headingText]="'Notification'" [isMarkAllAction]="true"
    (markAllActionCallback)="openMarkAllNotificationModal()" [backUrl]="'/dashboard'"></app-customer-header>

  <div class="customer-body-section has-header-section-change-profile no-padding">
    <div class="payment-body-section less-height no-padding">
      <ion-content class="payment-body-ion-content scroll">
        <div class="notification-container">
          <!-- No notifications case -->
          <div class="no-bookings-container" *ngIf="responseReceived && allNotifications.length === 0">
            <div class="no-bookings-icon">
              <ion-icon src="/assets/images/svg/no-notification.svg"></ion-icon>
            </div>
            <p class="no-bookings-message">
              You're all caught up! No new notifications at the moment. Check back later for updates.
            </p>
          </div>

          <!-- Loading state -->
          @if (loading==='LOADING') {
          <div class="notification-card">
            <div class="notification-image">
              <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
            </div>
            <div class="notification-content">
              <div class="notification-heading">
                <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
              </div>
              <div class="notification-description">
                <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
              </div>
            </div>
            <div class="notification-time">
              <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
            </div>
          </div>
          }
          <!-- Loaded state -->
          @else if (loading==='LOADED') {
          <div *ngIf="responseReceived && allNotifications.length > 0">
            <div class="notification-card" *ngFor="let notification of allNotifications" [ngClass]="{
            'unread-notification': !notification.isRead,
            'read-notification': notification.isRead
          }" (click)="readNotification(notification)">
              <div class="notification-image">
                <img src="/assets/images/icons/bell-icon-with-background.svg" alt="Notification Image">
              </div>
              <div class="notification-content">
                <div class="notification-heading">{{ notification.title }}</div>
                <div class="notification-description">{{ notification.message }}</div>
              </div>
              <div class="notification-time">
                <div class="date-text">{{ commonService.formatNotificationDate(notification.createdOn).date }}</div>
                <div class="time-text">{{ commonService.formatNotificationDate(notification.createdOn).time }}</div>
              </div>
            </div>
          </div>
          <ion-infinite-scroll threshold="50px" (ionInfinite)="onPageChanged($event)">
            <ion-infinite-scroll-content loadingSpinner="bubbles"
              loadingText="Loading more notifications..."></ion-infinite-scroll-content>
          </ion-infinite-scroll>
          }
        </div>
      </ion-content>
    </div>
  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #markAllModal [isOpen]="isMarkAllNotificationModalOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container heading-setting">
      <div class="site-custom-popup-header no-padding-bottom padding-top-10">
        <i-feather name="X" (click)="closeModal()"></i-feather>
        <h1>Confirmation!</h1>
      </div>
      <div class="site-custom-popup-body popup-normal-heading ion-padding no-padding-top">
        <div class="custom-modal-notifications"><span>Are you sure you want to mark all notifications as read?</span>
        </div>
        <div class="main-modal-dismiss button-gap">
          <ion-button class="site-full-rounded-button primary-button notification-list-no-button" shape="round"
            type="submit" (click)="closeModal()">No</ion-button>
          <ion-button class="site-full-rounded-button primary-button notification-list-yes-button" shape="round"
            type="submit" (click)="onMarkAllRead()">Yes</ion-button>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>


<ion-modal class="site-custom-popup job-invitation-popup" #cancelOfferModal [isOpen]="isPackageReminderPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closePackageReminderPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading popup-medium-heading">🌟 Welcome to Your Holiday Experience!</div>
          <div class="popup-normal-heading margin-top-5 secondary-text">We noticed you haven’t purchased a holiday
            package yet. With our exclusive packages, you can enjoy:</div>
          <ul class="bullet-text popup-large-heading popup-medium-heading">
            <li>Free holiday nights every year</li>
            <li>Exclusive discounts on bookings</li>
            <li>Hassle-free travel planning</li>
          </ul>
          <div class="popup-large-heading popup-medium-heading">💼 Start Your Journey Today!</div>

          <div class="privacy-container margin-top-30">
            <ion-button (click)="explorePlan();cancelOfferModal.dismiss()"
              class="site-full-rounded-button primary-button" expand="full" shape="round">
              Explore Membership Plans!!
            </ion-button>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #currentYearHolidayExpirePopup
  [isOpen]="isCurrentYearHolidayExpirePopupOpen" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeCurrentYearHolidayExpirePopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading popup-expire-holiday-heading">Holiday Expiring!</div>
          <div class="popup-normal-heading bold-text margin-top-5 secondary-text">Your {{ balanceNights }} holiday
            nights are
            going to expire on
            {{ commonService.formatDateForHolidayExpire(endDate) }}. Do you want to carry forward or lapse?</div>

          <div class="main-modal-dismiss button-gap">
            <ion-button (click)="carryForwardHolidays();currentYearHolidayExpirePopup.dismiss()"
              class="site-full-rounded-button primary-button margin-top-20 notification-list-yes-button" expand="full"
              shape="round">
              Carry Forward
            </ion-button>
            <ion-button (click)="lapseHolidays();currentYearHolidayExpirePopup.dismiss()"
              class="site-full-rounded-button primary-button margin-top-20 notification-list-no-button" expand="full"
              shape="round">
              Lapse
            </ion-button>
          </div>

          <div class="remind-later-container"
            (click)="closeCurrentYearHolidayExpirePopup();currentYearHolidayExpirePopup.dismiss()">
            <p class="remind-later-text">Remind Me Later</p>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #grabDealPopup [isOpen]="isgrabDealPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <!-- <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="grabDealPopup.dismiss()"></i-feather>
      </div> -->
      <div class="site-custom-popup-body no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          @if (loading==='LOADING') {
          <div class="my-booking-card-container">
            <div class="my-booking-card margin-top-10">
              <ion-grid>
                <ion-row>
                  <ion-col>
                    <ion-row class="check-in-out-con my-bookings-check">
                      <ion-col size="8" class="my-booking-code ion-no-padding">
                        <div class="my-booking-text">
                          <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                        </div>
                      </ion-col>
                      <ion-col size="4" class="my-booking-status">
                        <div class="my-booking-text">
                          <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                        </div>
                      </ion-col>
                    </ion-row>

                    <div>
                      <div class="hotel-name my-bookings-hotel-name">
                        <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                      </div>
                      <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                          style="width: 200px"></ion-skeleton-text></div>
                    </div>
                  </ion-col>
                </ion-row>

                <ion-row class="check-in-out-con">
                  <ion-col size="5">
                    <div class="check-in-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                      </div>
                      <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                  <ion-col size="2">
                    <div class="time-duration-con">
                      <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                      <div class="time-duration">
                        <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                      </div>
                    </div>
                  </ion-col>
                  <ion-col size="5">
                    <div class="check-out-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                      </div>
                      <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                </ion-row>

                <ion-row>
                  <ion-col>
                    <div class="guest-and-rooms-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                      </div>
                      <div class="rooms">
                        <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                </ion-row>

              </ion-grid>
            </div>
          </div>
          }
          @else if (loading==='LOADED') {
          <div class="campaign-card" *ngIf="responseReceived && customPushNotificationDetail">
            <div class="campaign-card-header">
              <div class="campaign-image-slide" *ngFor="let attachment of customPushNotificationDetail?.attachments">
                <img *ngIf="attachment.path" [src]="attachment.path" alt="">
                <i-feather name="X" class="close-icon" (click)="closeGrabDealPopup()"></i-feather>
              </div>
              <div class="campaign-image-slide"
                *ngIf="!customPushNotificationDetail?.attachments || customPushNotificationDetail?.attachments?.length === 0">
                <img
                  [src]="customPushNotificationDetail?.type === 'FREE_TRIP' ? '/assets/images/svg/free-trip-2.svg' : customPushNotificationDetail?.type === 'WALLET_TOPUP' ? '/assets/images/svg/wallet-2.svg' : '/assets/images/svg/booking-2.svg'"
                  alt="Default Campaign Image">
                <i-feather name="X" class="close-icon" (click)="closeGrabDealPopup()"></i-feather>
              </div>
            </div>

            <div class="campaign-detail-container">
              <span class="campaign-detail-title margin-top-12">{{customPushNotificationDetail?.title}}</span>
              <p #desc class="campaign-detail-desc text">{{customPushNotificationDetail?.description}}</p>
            </div>

            <ion-button class="grab-deal-button" expand="full" shape="round" type="submit"
              *ngIf="customPushNotificationDetail?.type != 'CARD_MESSAGE' && customPushNotificationDetail?.type != 'MESSAGE'"
              (click)="seeCampaignDetail(customPushNotificationDetail)">
              <div class="grab-deal-container">
                <img src="/assets/images/svg/grab-deal.svg" alt="Pay Now" width="24px" />
                <span class="grab-deal-text">
                  {{ customPushNotificationDetail?.type === 'PACKAGE' ? 'Get It Now' : 'Grab the deal' }}
                </span>
              </div>
            </ion-button>

          </div>
          }

        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>