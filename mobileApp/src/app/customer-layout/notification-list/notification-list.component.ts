import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { EventService } from 'src/shared/event.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-notification-list',
  templateUrl: './notification-list.component.html',
  styleUrls: ['./notification-list.component.scss'],
})
export class NotificationListComponent implements OnInit {

  user: any;
  walletId: string = '';
  allNotificationsRead: boolean = false;
  responseReceived: boolean = false;
  allNotifications: Array<any> = new Array<any>();

  filter: any;
  pageSize: number = 10;
  loading: string = "NOT_STARTED";
  showInfiniteScroll: boolean = false;
  isMarkAllNotificationModalOpen: boolean = false;
  isPackageReminderPopupOpen: boolean = false;
  isCurrentYearHolidayExpirePopupOpen: boolean = false;
  isgrabDealPopupOpen: boolean = false;
  customPushNotificationDetail: any;

  packageId: any;
  balanceNights: any;
  endDate: any;

  // Added the property to track the total count of notifications
  totalNotificationsCount: number = 0;

  constructor(
    private readonly dataService: DataService,
    private readonly authService: AuthService,
    private readonly toastService: ToastService,
    private readonly localStorageService: LocalStorageService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    public readonly commonService: CommonService,
    private readonly eventService: EventService
  ) { }

  ngOnInit() { }

  ionViewWillEnter() {
    this.user = this.authService.getUser();
    if (this.user) {
      this.walletId = this.user.walletId;
    }
    this.showInfiniteScroll = true;
    this.allNotifications = new Array<any>();
    this.filter = {} as any;
    this.filter.offset = 1;

    const subscriptionData = this.localStorageService.getObject("activeSubscriptionData");
    if (subscriptionData) {
      this.packageId = subscriptionData.id;
      this.endDate = new Date(subscriptionData.currentYearDetails.endDate).toISOString().split('T')[0];
      this.balanceNights = subscriptionData.currentYearDetails.nightsAllowed - subscriptionData.currentYearDetails.nightsUsed;
    }
    this.myNotifications();
  }

  myNotifications($event?: any) {
    this.responseReceived = false;
    const payload = {
      pagination: {
        next: this.pageSize,
        offset: this.filter.offset,
      },
    };

    this.loading = "LOADING";
    this.dataService.myNotifications(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.responseReceived = true;
        this.handleNotificationsListResponse(response, $event);
      },
      error: (error: any) => {
        this.loading = "LOADED";
        this.toastService.show(error.message || 'An error occurred');

        if ($event) {
          $event.target.complete();
        }
      },
    });
  }

  handleNotificationsListResponse(response: RestResponse, $event: any) {
    if (response.data && response.data.length > 0) {
      this.allNotifications = [...this.allNotifications, ...response.data];

      // Update the total notifications count if it's provided in the response
      this.totalNotificationsCount = response.data[0]?.totalCount || this.totalNotificationsCount;
    }

    if ($event) {
      $event.target.complete();

      if (this.allNotifications.length >= this.totalNotificationsCount) {
        $event.target.disabled = true;
        this.showInfiniteScroll = false;
      }
    }
  }

  onPageChanged($event: any) {
    if (this.allNotifications.length >= this.totalNotificationsCount) {
      $event.target.complete();
      $event.target.disabled = true;
      return;
    }

    this.filter.offset += 1;

    const payload = {
      pagination: {
        next: this.pageSize,
        offset: this.filter.offset,
      },
    };

    this.dataService.myNotifications(payload).subscribe({
      next: (response: RestResponse) => {
        this.handleNotificationsListResponse(response, $event);
      },
      error: (error: any) => {
        this.toastService.show(error.message || 'An error occurred');
        $event.target.complete();
      },
    });
  }

  readNotification(notification: any) {
    if (notification.isRead) {
      this.handleNotificationAction(notification);
      return;
    }
    this.dataService.readSingleNotification(notification.id).subscribe({
      next: (response: RestResponse) => {
        this.eventService.publish({ key: 'notification:read', value: "SINGLE" });

        // Update only the clicked notification's state, keeping others unaffected
        this.allNotifications = this.allNotifications.map(n =>
          n.id === notification.id ? { ...n, isRead: true } : n
        );

        // Now open the modal based on the notification type
        this.handleNotificationAction(notification);
      },
      error: (error: any) => {
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  handleNotificationAction(notification: any) {
    if (notification.type === "LOW_AGENT_BALANCE" && notification.targetType === "BOOKING") {
      this.resumeBooking(notification.targetTypeId);
    } else if (notification.targetType === "PAYMENT") {
      this.navController.navigateForward(`/payment?paymentId=${notification.targetTypeId}&fcmPaymentType=${notification.type}&message=${notification.message}&status=${notification.status}`, { animated: true });
    } else if ((notification.type === "BOOKING_CONFIRMATION" || notification.type === "HOTEL_BOOKING_CANCELLATION") && notification.targetType === "BOOKING") {
      this.navController.navigateForward(`/booking?bookingId=${notification.targetTypeId}&bookingType=${notification.type}&message=${notification.message}`, { animated: true });
    } else if (
      (notification.type === "START_REDEEM_REQUEST" || notification.type === "CANCEL_REDEEM_REQUEST" || notification.type === "REDEEM_OFFER") &&
      (notification.targetType === "PACKAGE" || notification.targetType === "REDEEM_REQUEST")
    ) {
      this.navController.navigateForward(`/membership?membershipId=${notification.targetTypeId}&membershipType=${notification.type}&message=${notification.message}`, { animated: true });
    } else if (notification.type === "START_REDEEM_REQUEST" && notification.targetType === "REDEEM_REQUEST_LIST") {
      this.navController.navigateForward(`/portal/redeem/gift/list?redeemGiftId=${notification.targetTypeId}&redeemGiftType=${notification.type}&message=${notification.message}`, { animated: true });
    } else if (notification.type === "SUPPORT_TICKET" && notification.targetType === "SUPPORT_REQUEST") {
      this.navController.navigateForward(`/portal/support/request/listing?supportRequestId=${notification.targetTypeId}&supportRequestType=${notification.type}&message=${notification.message}&fromScreen='supportRequest'`, { animated: true });
    } else if (notification.type === "SUPPORT_REQUEST" && notification.targetType === "SUPPORT_TICKET_COMMENT") {
      this.navController.navigateForward(`/portal/support/request/${notification.targetTypeId}/detail?supportRequestType=${notification.type}&tab=COMMENTS&message=${notification.message}&fromScreen='supportRequestComment'`, { animated: true });
    } else if (notification.type === "PROFILE_CHANGE_REQUEST" && notification.targetType === "CUSTOMER_PROFILE_CHANGE_REQUEST") {
      this.navController.navigateForward(`/portal/change/profile/request?changeProfileRequestId=${notification.targetTypeId}&changeProfileRequestType=${notification.type}&message=${notification.message}&fromScreen='changeProfileRequest'`, { animated: true });
    } else if (notification.type === "CURRENT_YEAR_EXPIRATION" && notification.targetType === "PACKAGE") {
      this.openCurrentYearHolidayExpirePopup();
    } else if (notification.type === "PACKAGE_EXPIRATION" && notification.targetType === "PACKAGE") {
      this.openPackageReminderPopup();
    } else if (notification.type === "CAMPAIGN_COMPLETE" && notification.targetType === "CAMPAIGN") {
      this.navController.navigateForward('/portal/my/campaign/details', {
        state: {
          campaignId: notification.targetTypeId,
          campaignType: notification.type,
          message: notification.message,
          walletId: this.walletId,
          fromScreen: 'notification-screen'
        },
        animated: true
      });
    } else if (notification.type === "CUSTOM_PUSH_NOTIFICATION" && notification.targetType === "CUSTOM_PUSH_NOTIFICATION") {
      this.openGrabDealPopup(notification.targetTypeId);
    }
  }

  openPackageReminderPopup() {
    this.isPackageReminderPopupOpen = true;
  }

  closePackageReminderPopup() {
    this.isPackageReminderPopupOpen = false;
  }

  openCurrentYearHolidayExpirePopup() {
    if (this.balanceNights > 0) {
      this.isCurrentYearHolidayExpirePopupOpen = true;
    }
  }

  closeCurrentYearHolidayExpirePopup() {
    this.isCurrentYearHolidayExpirePopupOpen = false;
  }

  openGrabDealPopup(targetId: any) {
    this.isgrabDealPopupOpen = true;
    this.fetchCustomPushNotificationDetails(targetId);
  }

  closeGrabDealPopup() {
    this.isgrabDealPopupOpen = false;
  }

  fetchCustomPushNotificationDetails(id: string) {
    this.responseReceived = false;
    this.loading = "LOADING";
    this.dataService.pushNotificationDetailById(id).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.responseReceived = true;
        this.customPushNotificationDetail = response.data;
      },
      error: (error: any) => {
        this.loading = "LOADED";
      },
    });
  }

  seeCampaignDetail(pushNotificationDetail: any) {
    this.closeGrabDealPopup();
    setTimeout(() => {
      if (pushNotificationDetail?.type === 'CAMPAIGN') {
        this.navController.navigateForward('/portal/my/campaign/details', {
          state: {
            campaignId: pushNotificationDetail?.typeId,
            walletId: this.walletId,
            fromScreen: 'notification-screen'
          },
          animated: true
        });
      } else if (pushNotificationDetail?.type === 'PACKAGE') {
        this.navController.navigateForward('/portal/package/detail', {
          state: {
            packageId: pushNotificationDetail?.typeId,
            fphId: pushNotificationDetail?.fphId,
            offerId: pushNotificationDetail?.offerId,
            fromScreen: 'notification-screen'
          },
          animated: true
        });
      } else if (pushNotificationDetail?.type === 'WALLET_RECHARGE') {
        this.navController.navigateForward("/portal/my/wallet", { animated: true });
      }
    }, 200);
  }

  explorePlan() {
    this.navController.navigateForward("account/register/package/selection", { animated: true });
  }

  carryForwardHolidays() {
    this.dataService.carryForwardHolidays(this.packageId).subscribe({
      next: (response: RestResponse) => {
        this.fetchMyActiveSubscription();
      },
      error: (error: any) => {
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  lapseHolidays() {
    this.dataService.lapseHolidays(this.packageId).subscribe({
      next: (response: RestResponse) => {
        this.fetchMyActiveSubscription();
      },
      error: (error: any) => {
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  fetchMyActiveSubscription() {
    //  this.loadingService.show();
    this.dataService.fetchMyActiveSubscription().subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        const packageData = response.data;

      },
      error: (error: any) => {
        //  this.loadingService.hide();
      }
    })
  }

  resumeBooking(bookingId: string) {
    this.loadingService.show();
    this.dataService.resumeBooking(bookingId).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.handleResumeBookingResponse(response.data);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  handleResumeBookingResponse(data: any) {
    const hotelDetails: any = {
      searchSessionId: data.searchSessionId,
      arrivalDate: data.arrivalDate,
      departureDate: data.departureDate,
      currency: data.currency,
      guestNationality: data.guestNationality,
      countryCode: data.countryCode,
      city: data.city,
      bookingType: data.bookingType,
      hotel: data.hotels
    };
    this.localStorageService.setObject("hotelDetails", JSON.stringify(hotelDetails));
    this.navController.navigateForward(`/portal/hotel/${hotelDetails.hotel.hotelCode}/details`, { animated: true });
  }

  closeModal() {
    this.isMarkAllNotificationModalOpen = false;
  }

  openMarkAllNotificationModal() {
    if (this.allNotifications.length <= 0) {
      // this.toastService.show('No notifications to mark as read');
      // TO DO disabled button
      return;
    }
    this.isMarkAllNotificationModalOpen = true;
  }

  onMarkAllRead() {
    this.readAllNotifications();
    this.closeModal();
  }

  readAllNotifications() {
    this.dataService.readAllNotifications()
      .subscribe({
        next: (response: RestResponse) => {
          this.allNotifications = this.allNotifications.map(notification => ({
            ...notification,
            isRead: true
          }));
          this.toastService.show('All notifications marked as read');
          this.eventService.publish({ key: 'notification:read', value: "ALL" });
        },
        error: (error: any) => {
          this.toastService.show(error.message || 'An error occurred');
        },
      });
  }
}
