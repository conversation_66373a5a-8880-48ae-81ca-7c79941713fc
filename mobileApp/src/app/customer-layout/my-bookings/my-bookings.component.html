<ion-content class="customer-dashboard-page my-booking-page">
  <app-customer-header [innerPage]="false" [headingText]="'My Bookings'" [rightAction]="true"
    [rightActionIcon]="'Filter'" (rightActionCallback)="openFilterPopup()"
    [profileImageUrl]="profileImageUrl"></app-customer-header>

  <div class="customer-body-section has-header-section no-padding">
    <div class="payment-body-section less-height no-padding">
      <ion-content class="payment-body-ion-content scroll">
        <div class="no-bookings-container no-height" *ngIf="responseReceived && bookings.length === 0 && filterApplied">
          <div class="no-bookings-icon">
            <ion-icon name="alert-circle-outline"></ion-icon>
          </div>
          <h2 class="no-bookings-title">No Data Available</h2>
          <p class="no-bookings-message">No bookings match your filter criteria. Please try again with different
            filters.
          </p>
        </div>
        <div class="no-bookings-container no-height"
          *ngIf="responseReceived && bookings.length === 0 && !filterApplied">
          <div class="no-bookings-icon">
            <ion-icon name="calendar-outline"></ion-icon>
          </div>
          <h2 class="no-bookings-title">My Bookings</h2>
          <p class="no-bookings-message">Plan ahead for a seamless experience. Start booking now and dive right into
            your
            adventure!</p>
          <ion-button
            class="my-bookings-ion-button site-full-rounded-button primary-button text-capitalize margin-top-30"
            expand="full" shape="round" (click)="startPlan()">
            Start Planning
          </ion-button>
        </div>
        @if (loading==='LOADING') {
        <div class="my-booking-card-container">
          <div class="my-booking-card margin-top-10">
            <ion-grid>
              <ion-row>
                <ion-col>
                  <ion-row class="check-in-out-con my-bookings-check">
                    <ion-col size="8" class="my-booking-code ion-no-padding">
                      <div class="my-booking-text">
                        <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                      </div>
                    </ion-col>
                    <ion-col size="4" class="my-booking-status">
                      <div class="my-booking-text">
                        <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                      </div>
                    </ion-col>
                  </ion-row>

                  <div>
                    <div class="hotel-name my-bookings-hotel-name">
                      <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                    </div>
                    <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                        style="width: 200px"></ion-skeleton-text></div>
                  </div>
                </ion-col>
              </ion-row>

              <ion-row class="check-in-out-con">
                <ion-col size="5">
                  <div class="check-in-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                    </div>
                    <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
                <ion-col size="2">
                  <div class="time-duration-con">
                    <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                    <!-- <ion-skeleton-text [animated]="true"
                      style="width: 20px;height: 20px"></ion-skeleton-text> -->
                    <div class="time-duration">
                      <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                    </div>
                  </div>
                </ion-col>
                <ion-col size="5">
                  <div class="check-out-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                    </div>
                    <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
              </ion-row>

              <!-- Show total adults per booking -->
              <ion-row>
                <ion-col>
                  <div class="guest-and-rooms-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                    </div>
                    <div class="rooms">
                      <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
              </ion-row>

            </ion-grid>
          </div>
        </div>
        }@else if (loading==='LOADED') {
        <div class="my-booking-card-container">
          <div (click)="openBookingDetail(booking.id)" *ngFor="let booking of bookings">
            <div class="my-booking-card ion-padding"
              [ngClass]="{'highlighted-payment': booking.id === notificationBookingId}">
              <ion-grid class="ion-no-padding">
                <ion-row>
                  <ion-col>
                    <ion-row class="booking-information">
                      <ion-col size="8" class="ion-no-padding" *ngIf="booking.bookingCode">
                        <div class="booking-code">
                          {{ booking.bookingCode }}
                        </div>
                      </ion-col>
                      <ion-col size="4" *ngIf="booking.status" class="ion-text-right">
                        <div class="booking-status"
                          [ngClass]="{'upcoming':booking.status === 'CONFIRMED', 'cancalled':(booking.status === 'CANCELLED' || booking.status === 'FAILED'), 'completed':booking.status === 'COMPLETED'}">
                          {{ commonService.formatTextAllCapital(booking.status) }}
                        </div>
                      </ion-col>
                    </ion-row>
                    <div>
                      <div class="hotel-name">
                        {{ booking.hotelName || booking.hotelDetails?.name || 'Hotel Name Unavailable'}}
                      </div>
                      <div class="hotel-address">
                        {{ booking.hotelDetails?.hotelAddress || 'Address Unavailable' }}
                      </div>
                    </div>
                  </ion-col>
                </ion-row>
                <ion-row class="booking-dates">
                  <ion-col size="5">
                    <div class="check-in-date">
                      <div class="label">CHECK-IN</div>
                      <div class="date">{{ commonService.formatDate(booking.checkInDate) || 'N/A' }}</div>
                    </div>
                  </ion-col>
                  <ion-col size="2">
                    <div class="time-duration-con">
                      <ion-icon src="/assets/images/svg/right-arrow.svg" slot="start" class="star-icon"></ion-icon>
                      <div class="time-duration">
                        <span>{{ booking.totalNights }} {{ booking.totalNights === 1 ? 'Night' : 'Nights' }}</span>
                      </div>
                    </div>
                  </ion-col>
                  <ion-col size="5">
                    <div class="check-out-date">
                      <div class="label">CHECK-OUT</div>
                      <div class="date">{{ commonService.formatDate(booking.checkOutDate) || 'N/A' }}</div>
                    </div>
                  </ion-col>
                </ion-row>
                <!-- Show total adults per booking -->
                <div class="room-and-guest-info">
                  <div class="label">Guest & Rooms</div>
                  <div class="rooms">
                    {{ booking.totalAdults }}
                    {{ booking.totalAdults === 1 ? "Adult" : "Adults" }},
                    {{ booking.totalChildren }}
                    {{ booking.totalChildren === 1 ? "Child" : "Childs" }} &amp;
                    {{ booking.totalRooms }}
                    {{ booking.totalRooms === 1 ? "Room" : "Rooms" }}
                  </div>
                </div>
              </ion-grid>
            </div>
            <div class="card-bottom-show-detail" *ngIf="booking?.bookingType!=='OTHERS'"
              [ngStyle]="{'--package-color': booking?.packageDetail.packageDetail.color}">
              <div class="package-design margin-bottom-8">{{booking?.packageDetail.packageDetail.title}}</div>
            </div>

            <!-- <div class="card-bottom-cancellation-policy"
              *ngIf="booking?.bookingType==='OTHERS' && booking?.status === 'CONFIRMED'"
              (click)="freeCancellationPolicy($event,booking.id)">
              <div class="package-design margin-bottom-8">Cancellation Policy</div>
            </div> -->
            <div class="card-bottom-show-detail" *ngIf="booking?.bookingType==='OTHERS'">
              <div class="package-design margin-bottom-8">OTHER</div>
            </div>
          </div>
        </div>
        }
        <ion-infinite-scroll *ngIf="bookings.length > 0" threshold="50px" (ionInfinite)="onPageChanged($event)">
          <ion-infinite-scroll-content loadingSpinner="bubbles"
            loadingText="Loading more data..."></ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </ion-content>
    </div>
  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #filterModal [isOpen]="isFilterPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeFilterPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">Apply Filter</div>
          <div class="job-info margin-top-10">
            <div class="margin-bottom-15">
              <ion-item class="site-form-control" lines="none">
                <ion-input readonly="readonly" (click)="openDatePicker('fromDate')" label="From Date"
                  labelPlacement="floating" required="required" name="checkInDate" #checkInDate="ngModel"
                  [(ngModel)]="appliedFilter.fromDate" [value]="appliedFilter.fromDate | date:'yyyy-MM-dd'">
                </ion-input>
                <ion-icon slot="end" class="toggle-password-icon" (click)="openDatePicker('fromDate')"
                  [src]="'/assets/images/svg/calendar.svg'"></ion-icon>
              </ion-item>
            </div>

            <div class="margin-bottom-15">
              <ion-item class="site-form-control" lines="none">
                <ion-input readonly="readonly" (click)="openDatePicker('toDate')" label="To Date"
                  labelPlacement="floating" required="required" name="checkOutDate" #checkOutDate="ngModel"
                  [(ngModel)]="appliedFilter.toDate" [value]="appliedFilter.toDate | date:'yyyy-MM-dd'">
                </ion-input>
                <ion-icon slot="end" class="toggle-password-icon" (click)="openDatePicker('toDate')"
                  [src]="'/assets/images/svg/calendar.svg'"></ion-icon>
              </ion-item>
            </div>

            <div class="margin-bottom-15">
              <ion-item class="site-form-floating-control" lines="none">
                <ion-label position="floating">Select Status</ion-label>
                <ion-select #status="ngModel" [(ngModel)]="appliedFilter.status" interface="action-sheet"
                  required="required" name="status">
                  <ion-select-option *ngFor="let status of statusTypes" [value]="status.value">
                    {{ status.display }}
                  </ion-select-option>
                </ion-select>
                <ion-icon slot="end" class="select-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
              </ion-item>
            </div>
          </div>
          <ion-button class="site-full-rounded-button" shape="round" type="button" (click)="applyFilter()">
            Apply</ion-button>
          <ion-button class="site-full-rounded-button" shape="round" type="button" (click)="clearFilter()">
            Clear</ion-button>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<!-- Cancel Policy Modal-->
<ion-modal class="site-custom-popup job-invitation-popup" #cancelPolicyModal [isOpen]="isCancelPolicyPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeCancelPolicyPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading"> Cancellation Policy</div>
          <p class="popup-normal-heading">Here is the information regarding the Cancellation Policy.</p>
          <div class="popup-normal-heading margin-top-5 secondary-text">{{cancellationPolicyData.info}}</div>
          <ul class="bullet-text  popup-normal-heading">
            <!-- <li>Charge -<b>{{cancelInformation.chargeAmount}}{{ cancelInformation.chargeType === 'Percentage' ? '%' : '' }}</b> Applicable if Cancelled After {{cancelInformation.endDate}}</li> -->
            @for (cancelInformation of cancellationInformations; track $index) {
            <li>
              @if (cancelInformation.chargeAmount === "0") {
              <p>
                No Charges Applicable If Cancelled Before<b> {{cancelInformation.endDate |
                  date:'dd/MM/yyyy, h:mm a'}}</b> IST.
              </p>

              } @else if (cancelInformation.chargeType === "Percentage") {
              <p>
                Charge - <b>{{cancelInformation.chargeAmount}}% </b>Applicable If Cancelled After
                <b>{{cancelInformation.startDate
                  | date:'dd/MM/yyyy, h:mm a'}}</b> IST.
              </p>
              } @else if (cancelInformation.chargeType === "Amount") {
              <p>
                Charges - <b>{{cancelInformation.chargeAmount | currency
                  :'INR':'symbol':'1.0-0'}}</b>
                Applicable If Cancelled After <b>{{cancelInformation.startDate | date:'dd/MM/yyyy, h:mm a'}}</b> IST
              </p>
              }
            </li>
            }
          </ul>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>