import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Directory, Filesystem } from '@capacitor/filesystem';
import { NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-my-bookings-list-details',
  templateUrl: './my-bookings-list-details.component.html',
  styleUrls: ['./my-bookings-list-details.component.scss'],
})
export class MyBookingsListDetailsComponent implements OnInit {

  bookingListData: any;
  bookingListHotelDetails: any;
  bookingListPackageDetails: any;
  bookingListCustomerDetails: any;
  bookingListCurrentYearDetails: any;
  bookingId: string | null = null;
  isPaymentProcessing: boolean = false;

  constructor(private readonly localStorageService: LocalStorageService,
    private readonly navController: NavController,
    public readonly commonService: CommonService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private readonly route: ActivatedRoute
  ) {

  }

  ngOnInit() {
    this.bookingId = this.route.snapshot.paramMap.get('id') || "";
    this.bookingListData = this.localStorageService.getObject("selectedBooking");
    if (!this.bookingListData) {
      return;
    }
  }

  ionViewWillEnter() {
    this.bookingId = this.route.snapshot.paramMap.get('id') || "";
    this.bookingListData = this.localStorageService.getObject("selectedBooking");
    if (!this.bookingListData) {
      return;
    }
    if (this.bookingListData.customerDetail) {
      this.bookingListCustomerDetails = this.bookingListData.customerDetail;
    }
    if (this.bookingListData.packageDetail) {
      this.bookingListPackageDetails = this.bookingListData.packageDetail;
    }
    if (this.bookingListData.currentYearDetails) {
      this.bookingListCurrentYearDetails = this.bookingListData.currentYearDetails;
    }
    if (this.bookingListData.hotelDetails) {
      this.bookingListHotelDetails = this.bookingListData.hotelDetails;
    }
    this.dataService.fetchBookingDetail(this.bookingId)
      .subscribe({
        next: (response: RestResponse) => {
        },
        error: (error) => {
          this.toastService.show(error.message || 'An error occurred');
        }
      });
  }

  formatDate(bookingDate: string): string {
    if (!bookingDate) return ''; // Handle if bookingDate is null or undefined

    const date = new Date(bookingDate);
    if (isNaN(date.getTime())) return ''; // Return an empty string if date is invalid

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`; // Format as yyyy-mm-dd
  }

  back() {
    this.navController.navigateBack('/booking', { animated: true });
  }

  cancelBooking() {
    this.navController.navigateForward("/portal/cancel/my/booking", { animated: true });
  }

  returnHomePage() {
    this.navController.navigateRoot("/dashboard", { animated: true });
  }

  downloadInvoice(bookingId: string) {
    //  this.loadingService.show();
    this.isPaymentProcessing = true;
    //  const method = (payment.paymentType === 'BOOKING_PAYMENT' || payment.paymentType === 'BOOKING ADDITIONAL AMOUNT' )? 'invoiceDownloadForBooking' :'invoiceDownloadForOthers';
    const method = 'invoiceDownloadForMyBookings';
    this.dataService[method](bookingId).subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        this.isPaymentProcessing = false;

        if (response && response.data) {
          const fileUrl = response.data.filePath; // URL of the file
          const fileName = response.data.fileName;
          this.saveFileToDownloads(fileUrl, fileName);
        } else {
          this.toastService.show('Failed to retrieve the file URL.');
        }
      },
      error: (error) => {
        //  this.loadingService.hide();
        this.isPaymentProcessing = false;
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }


  async saveFileToDownloads(fileUrl: string, fileName: string) {
    try {
      // Fetch the file
      const response = await fetch(fileUrl);
      const invoiceName = fileName;
      // Save the file to the Documents directory using Capacitor Filesystem
      await Filesystem.writeFile({
        path: invoiceName,
        data: response?.url,
        directory: Directory.Documents, // Save to Documents folder
      });

      this.toastService.show('File saved to Documents folder.');
    } catch (error) {
      console.error('Error saving file:', error);
      this.toastService.show('Failed to save the file.');
    }
  }

}
