.booking-detail-body-container {
  .booking-detail-room-section {
    .label-text {
      font-size: 12px;
      color: #2c3c64;
      opacity: 50%;
    }
    .value-text {
      font-size: 14px;
      font-weight: 500;
    }
    .booking-detail-guest-detail {
      .guest-name {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 5px;
        ion-icon {
          margin-top: 1px;
          margin-right: 5px;
        }
        div,
        ion-icon {
          display: inline-block;
          vertical-align: middle;
        }
        &:last-child {
          margin-bottom: 0px;
        }
      }
    }
  }
  .booking-detail-hotel-description {
    .label-text {
      font-size: 12px;
      color: #2c3c64;
      opacity: 50%;
    }
    .value-text {
      font-size: 13px;
    }
  }
  .booking-payment-section {
    .booking-info-container {
      .card-info-title {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 5px;
      }
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
        .info-label {
          font-size: 12px;
          color: #2c3c64;
        }
        .info-value {
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
    .download-voucher-button {
      margin-top: 10px;
      text-transform: uppercase;
      font-size: 13px;
    }
    .back-search-container {
      text-align: center;
      font-size: 13px;
    }
  }
}
