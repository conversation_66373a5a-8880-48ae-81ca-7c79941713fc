<ion-content class="customer-dashboard-page booking-page my-booking-page">
  <app-customer-header [innerPage]="true" [rightAction]="true"
    [headingText]="'My Booking Details'"></app-customer-header>
  <div class="customer-body-section has-header-section-change-profile no-padding">
    <div class="my-booking-card-container padding-bottom-10">
      <div class="my-booking-card no-box-shadow">
        <ion-grid class="ion-no-padding">
          <ion-row>
            <ion-col>
              <ion-row class="booking-information">
                <ion-col size="8" class="ion-no-padding" *ngIf="bookingListData?.bookingCode">
                  <div class="booking-code">
                    {{ bookingListData?.bookingCode }}
                  </div>
                </ion-col>
                <ion-col size="4" *ngIf="bookingListData.status" class="ion-text-right">
                  <div class="booking-status"
                    [ngClass]="{'upcoming':bookingListData.status === 'CONFIRMED', 'cancalled':(bookingListData.status === 'CANCELLED' || bookingListData.status === 'FAILED'), 'completed':bookingListData.status === 'COMPLETED'}">
                    {{ commonService.formatTextAllCapital(bookingListData.status) }}
                  </div>
                </ion-col>
              </ion-row>
              <div>
                <div class="hotel-name">
                  {{ bookingListData.hotelName || bookingListData.hotelDetails?.name || 'Hotel Name Unavailable'}}
                </div>
                <div class="hotel-address">
                  {{ bookingListData.hotelDetails?.hotelAddress || 'Address Unavailable' }} <a class="locate-link"
                    *ngIf="bookingListData?.hotelAddress || bookingListHotelDetails?.hotelAddress"
                    (click)="commonService.openGoogleMaps(bookingListHotelDetails?.longitude,bookingListHotelDetails?.latitude)">
                    Locate Now
                  </a>
                </div>
                <div class="hotel-contact-number" *ngIf="bookingListHotelDetails?.phone.length > 8">
                  <img src="/assets/images/icons/icons8-phone-40.png" class="max-width-icon">
                  <a [href]="'tel:' + bookingListHotelDetails?.phone">{{bookingListHotelDetails?.phone}}</a>
                </div>
              </div>
            </ion-col>
          </ion-row>
          <ion-row class="booking-dates">
            <ion-col size="5">
              <div class="check-in-date">
                <div class="label">CHECK-IN</div>
                <div class="date">{{ commonService.formatDate(bookingListData.checkInDate) || 'N/A' }}</div>
              </div>
            </ion-col>
            <ion-col size="2">
              <div class="time-duration-con">
                <ion-icon src="/assets/images/svg/right-arrow.svg" slot="start" class="star-icon"></ion-icon>
                <div class="time-duration">
                  <span>{{ bookingListData.totalNights }} {{ bookingListData.totalNights === 1 ? 'Night' : 'Nights'
                    }}</span>
                </div>
              </div>
            </ion-col>
            <ion-col size="5">
              <div class="check-out-date">
                <div class="label">CHECK-OUT</div>
                <div class="date">{{ commonService.formatDate(bookingListData.checkOutDate) || 'N/A' }}</div>
              </div>
            </ion-col>
          </ion-row>
          <!-- Show total adults per booking -->
          <div class="room-and-guest-info">
            <div class="label">Guest & Rooms</div>
            <div class="rooms">
              {{ bookingListData.totalAdults }}
              {{ bookingListData.totalAdults === 1 ? "Adult" : "Adults" }},
              {{ bookingListData.totalChildren }}
              {{ bookingListData.totalChildren === 1 ? "Child" : "Childs" }} &amp;
              {{ bookingListData.totalRooms }}
              {{ bookingListData.totalRooms === 1 ? "Room" : "Rooms" }}
            </div>
          </div>
        </ion-grid>
      </div>
    </div>
    <div class="booking-detail-body-container ion-padding no-padding-top">
      <div class="booking-detail-room-section">
        <div class="label-text">Room</div>
        <div class="value-text">{{bookingListData.roomType}}</div>
      </div>

      <div class="booking-detail-room-section margin-top-10">
        <div class="label-text">Guest Details</div>
        <div class="booking-detail-guest-detail">
          <div class="guest-name" *ngFor="let guest of bookingListData.guestDetails">
            <ion-icon name="people-outline"></ion-icon>
            <div>{{guest.salutation}} {{guest.firstName}} {{guest.lastName}}</div>
          </div>
        </div>
      </div>
      <div class="booking-detail-hotel-description margin-top-10" *ngIf="bookingListHotelDetails?.desc">
        <div class="label-text">Hotel Description</div>
        <div [innerHTML]="bookingListHotelDetails?.desc" class="value-text"></div>
      </div>

      <div class="booking-payment-section margin-top-10">
        <div class="booking-info-container">
          <div class="card-info-title" *ngIf="bookingListData?.additionalAmount>0">
            <span>Payment Summary:</span>
          </div>
          <div class="info-item" *ngIf="bookingListData?.additionalAmount>0">
            <span class="info-label">Amount:</span>
            <span class="info-value">{{bookingListData?.additionalAmount| currency: 'INR' :
              'symbol' : '1.0-2'}}/-</span>
          </div>
          <div class="info-item" *ngIf="bookingListData?.gstAmount>0">
            <span class="info-label">GST:</span>
            <span class="info-value info-room-normal">{{bookingListData?.gstAmount| currency: 'INR' :
              'symbol' : '1.0-2'}}/-</span>
          </div>
          <div class="info-item" *ngIf="bookingListData?.serviceCharge>0">
            <span class="info-label">Convenience Fee(incl. GST):</span>
            <span class="info-value info-room-normal">{{bookingListData?.serviceCharge | currency: 'INR' :
              'symbol' : '1.0-2'}}/-</span>
          </div>
          <div class="info-item" *ngIf="bookingListData?.totalAdditionalAmount>0">
            <span class="info-label">Paid Amount:</span>
            <span class="info-value">{{bookingListData?.totalAdditionalAmount| currency: 'INR' :
              'symbol' : '1.0-2'}}/-</span>
          </div>
        </div>

        <ion-button class="primary-button download-voucher-button" expand="full" shape="round"
          [disabled]="isPaymentProcessing" (click)="downloadInvoice(bookingListData?.id)">
          <span>Download Voucher</span>
        </ion-button>
        <div class="back-search-container margin-top-10"
          *ngIf="bookingListData.status === 'CONFIRMED' && bookingListData.bookingType === 'OTHERS'">
          <a (click)="cancelBooking()">Want to cancel the booking?</a>
        </div>
      </div>
    </div>

  </div>

</ion-content>