import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NavController } from '@ionic/angular';
import { DatePicker } from '@pantrist/capacitor-date-picker';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';


@Component({
  selector: 'app-my-bookings',
  templateUrl: './my-bookings.component.html',
  styleUrls: ['./my-bookings.component.scss'],
})
export class MyBookingsComponent implements OnInit {

  user: any;
  hasBookings: boolean = false;
  responseReceived: boolean = false;
  searchQuery: string = '';
  bookings: Array<any> = new Array<any>();
  cancellationInformations: Array<any> = new Array<any>();
  cancellationPolicyData: any;
  offset: number = 1;
  next: any;
  pageSize: number = 10;
  loading: string = "NOT_STARTED";
  showInfiniteScroll: boolean = false;
  queryParamsObj: any;
  isFilterPopupOpen: boolean = false;
  appliedFilter: any;
  filterApplied = false;
  filter: any;
  statusTypes: Array<{ display: string, value: string }> = [
    { display: 'Confirmed', value: 'CONFIRMED' },
    { display: 'Completed', value: 'COMPLETED' },
    { display: 'Cancelled', value: 'CANCELLED' },
    { display: 'Failed', value: 'FAILED' }
  ];
  eventTarget: any;
  isCancelPolicyPopupOpen: boolean = false;
  notificationBookingId: string = '';
  profileImageUrl: string | null = null;

  constructor(private readonly navController: NavController,
    private readonly toastService: ToastService,
    public readonly commonService: CommonService,
    private readonly dataService: DataService,
    private readonly authService: AuthService,
    private readonly localStorageService: LocalStorageService,
    private route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private router: Router
  ) {

  }

  ngOnInit() {
    // let loginOpend = 

    this.user = this.authService.getUser();
  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();

    this.profileImageUrl = this.localStorageService.get('profileImageUrl');

    this.showInfiniteScroll = true;
    this.bookings = new Array<any>();
    this.cancellationInformations = new Array<any>();
    this.filter = {} as any;
    this.filter.offset = 1;
    this.queryParamsObj = this.route.snapshot.queryParams;
    this.notificationBookingId = this.queryParamsObj.bookingId;
    const message = this.queryParamsObj?.message || "";
    if (!this.user) {
      let currentUrl = this.router.url;
      this.localStorageService.set('redirectAfterLogin', currentUrl);
      this.commonService.openLoginModal();
      return;
    }
    else {
      this.myBookings(null, true);
    }
  }

  startPlan() {
    this.navController.navigateRoot('/dashboard', { animated: true });
  }

  myBookings($event?: any, handleLoading?: boolean) {
    this.responseReceived = false;
    const payload = {
      checkInDate: this.filter.fromDate || null,
      checkOutDate: this.filter.toDate || null,
      status: this.filter.status || null,
      pagination: {
        next: this.pageSize,
        offset: this.filter.offset
      }
    };

    if (handleLoading) { this.loading = "LOADING"; }
    this.dataService.myBookings(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.responseReceived = true;

        if (response.data.length > 0) {
          this.bookings.push(...response.data);
        }

        if ($event) {
          $event.target.complete();
          if (this.bookings.length > 0 && this.bookings.length >= this.bookings[0].totalCount) {
            $event.target.disabled = true;
          }
        }
        this.cdr.detectChanges();
      },
      error: (error: any) => {
        this.loading = "LOADED";
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  onPageChanged($event: any) {
    if (this.bookings.length > 0 && this.bookings.length >= this.bookings[0].totalCount) {
      $event.target.complete();
      $event.target.disabled = true;
      return;
    }
    this.filter.offset = this.filter.offset + 1;
    this.myBookings($event, false);
  }

  openFilterPopup() {
    this.isFilterPopupOpen = true;
    this.appliedFilter = JSON.parse(JSON.stringify(this.filter));

    // Format fromDate and toDate before showing in fields
    if (this.appliedFilter.fromDate) {
      const date = new Date(this.appliedFilter.fromDate);
      this.appliedFilter.fromDate = `${this.commonService.getDisplayValue(date.getFullYear())}-${this.commonService.getDisplayValue(date.getMonth() + 1)}-${date.getDate()}`;
    }

    if (this.appliedFilter.toDate) {
      const date = new Date(this.appliedFilter.toDate);
      this.appliedFilter.toDate = `${this.commonService.getDisplayValue(date.getFullYear())}-${this.commonService.getDisplayValue(date.getMonth() + 1)}-${date.getDate()}`;
    }
  }

  closeFilterPopup() {
    this.isFilterPopupOpen = false;
  }

  clearFilter() {
    // Reset appliedFilter and filter models
    this.appliedFilter.fromDate = undefined;
    this.appliedFilter.toDate = undefined;
    this.appliedFilter.status = undefined;
    this.filter.fromDate = undefined;
    this.filter.toDate = undefined;
    this.filter.status = undefined;

    this.closeFilterPopup();  // Close the filter popup

    // Reset and reload payments
    setTimeout(() => {
      this.filterApplied = false;
      this.bookings = new Array<any>();
      this.filter.offset = 1;
      this.myBookings();
    });
  }

  applyFilter() {
    if (!this.commonService.hasValue(this.appliedFilter.fromDate)
      && !this.commonService.hasValue(this.appliedFilter.toDate)
      && !this.commonService.hasValue(this.appliedFilter.status)) {
      this.toastService.show("Please provide at least one value to filter");
      return;
    }

    if (this.commonService.hasValue(this.appliedFilter.fromDate) && !this.commonService.hasValue(this.appliedFilter.toDate)) {
      this.toastService.show("Please select a valid To Date.");
      return;
    }
    if (!this.commonService.hasValue(this.appliedFilter.fromDate) && this.commonService.hasValue(this.appliedFilter.toDate)) {
      this.toastService.show("Please select a valid From Date.");
      return;
    }

    // Reapply the date format for fromDate and toDate in the correct format
    if (this.appliedFilter.fromDate) {
      const fromDate = new Date(this.appliedFilter.fromDate);
      this.filter.fromDate = `${fromDate.getFullYear()}-${(fromDate.getMonth() + 1).toString().padStart(2, '0')}-${fromDate.getDate().toString().padStart(2, '0')}`;
    }

    if (this.appliedFilter.toDate) {
      const toDate = new Date(this.appliedFilter.toDate);
      this.filter.toDate = `${toDate.getFullYear()}-${(toDate.getMonth() + 1).toString().padStart(2, '0')}-${toDate.getDate().toString().padStart(2, '0')}`;
    }

    this.filter.status = this.appliedFilter.status;
    this.closeFilterPopup();  // Close the filter popup

    // Reset and reload payments with the new filter
    setTimeout(() => {
      this.filterApplied = true;
      this.bookings = new Array<any>();  // Clear existing data
      this.filter.offset = 1;
      this.myBookings();
    });
  }

  openDatePicker(type: 'fromDate' | 'toDate') {
    const today = new Date();
    let dateToUse: Date = today;
    let minDate: string | undefined;
    let maxDate: string | undefined;

    if (type === 'fromDate') {
      dateToUse = this.appliedFilter.fromDate ? new Date(this.appliedFilter.fromDate) : today;
    } else {
      if (!this.appliedFilter.fromDate) {
        this.toastService.show("Please select 'From Date' first.");
        return;
      }
      dateToUse = this.appliedFilter.toDate ? new Date(this.appliedFilter.toDate) : today;
      minDate = `${new Date(this.appliedFilter.fromDate).getDate().toString().padStart(2, '0')}/${(new Date(this.appliedFilter.fromDate).getMonth() + 1).toString().padStart(2, '0')}/${new Date(this.appliedFilter.fromDate).getFullYear()}`;
    }

    const formattedDate = `${dateToUse.getDate().toString().padStart(2, '0')}/${(dateToUse.getMonth() + 1).toString().padStart(2, '0')}/${dateToUse.getFullYear()}`;

    DatePicker.present({
      mode: 'date',
      format: 'dd/MM/yyyy',
      min: minDate,
      date: formattedDate,
    }).then(date => {
      if (date && date.value) {
        const [day, month, year] = date.value.split('/');
        if (type === 'fromDate') {
          this.appliedFilter.fromDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        } else {
          this.appliedFilter.toDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
      }
    }).catch(error => {
      console.error('Date Picker Error:', error);
    });
  }

  openBookingDetail(bookingId: string) {
    const selectedBooking = this.bookings.find(booking => booking.id === bookingId);
    this.localStorageService.setObject('selectedBooking', selectedBooking);
    this.navController.navigateForward(`/portal/my/booking/${bookingId}/detail`, { animated: true });
  }

  freeCancellationPolicy(event: Event, bookingId: string) {
    event.stopPropagation();
    //API Call
    this.dataService.bookingCancellationPolicy(bookingId)
      .subscribe({
        next: (response: RestResponse) => {
          this.cancellationInformations = [...response.data?.cancellationPolicy.cancellationInformation];
          this.cancellationPolicyData = response.data.cancellationPolicy;
          this.openCancelPolicyPopup();
        },
        error: (error: any) => {
          this.loading = "LOADED";
          this.toastService.show(error.message || 'An error occurred');
        }
      });
  }

  openCancelPolicyPopup() {
    this.isCancelPolicyPopupOpen = true;
  }

  closeCancelPolicyPopup() {
    this.isCancelPolicyPopupOpen = false;
  }
}
