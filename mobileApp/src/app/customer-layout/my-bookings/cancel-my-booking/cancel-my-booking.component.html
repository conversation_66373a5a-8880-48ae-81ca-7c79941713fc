<ion-content class="customer-dashboard-page booking-page">
  <form class="custom-form" #recordForm="ngForm" novalidate>
    <app-customer-header [innerPage]="true" [headingText]="'Cancel My Booking'"></app-customer-header>
    <div class="booking-page-container hotel-listing">
      <div class="form-container text-left">

        <div class="image-container no-margin">
          <img src="assets/images/svg/confirm-status.svg" class="two-factor-image" />
        </div>

        <p class="page-sub-heading setup-two-factor booking-confirm">
          Cancellation Information
        </p>

        <div class="popup-medium-heading margin-top-5 secondary-text">{{cancellationPolicyInfo}}</div>
        <ul class="bullet-text  popup-medium-heading">
          @for (cancelInformation of cancellationInformations; track $index) {
          <li>
            @if (cancelInformation.chargeAmount === "0") {
            <p>
              No Charges Applicable If Cancelled Before<b> {{cancelInformation.endDate |
                date:'dd/MM/yyyy, h:mm a'}}</b> IST.
            </p>

            } @else if (cancelInformation.chargeType === "Percentage") {
            <p>
              Charge - <b>{{cancelInformation.chargeAmount}}% </b>Applicable If Cancelled After
              <b>{{cancelInformation.startDate
                | date:'dd/MM/yyyy, h:mm a'}}</b> IST.
            </p>
            } @else if (cancelInformation.chargeType === "Amount") {
            <p>
              Charges - <b>{{cancelInformation.chargeAmount | currency
                :'INR':'symbol':'1.0-0'}}</b>
              Applicable If Cancelled After <b>{{cancelInformation.startDate | date:'dd/MM/yyyy, h:mm a'}}</b> IST
            </p>
            }
          </li>
          }
        </ul>


        <div class="back-search-container margin-top-30">
          <a (click)="openCancelConfirmationModal()">Cancel Booking</a>
        </div>
        <div class="privacy-container margin-top-5">
          <!-- <ion-button class="site-full-rounded-button primary-button" shape="round" type="submit"
          (click)="openCancelConfirmationModal()">
          Cancel Booking
        </ion-button> -->
          <ion-button class="site-full-rounded-button primary-button text-capitalize" shape="round" type="submit"
            (click)="returnHomePage()">
            Return to Home
          </ion-button>

        </div>
      </div>
    </div>
  </form>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #bookingCancelSelectOption
  [isOpen]="isBookingCancelSelectOption" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header book-now-padding">
        <i-feather name="X" (click)="closeCancelBookingSelectOption()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <div class="popup-large-heading">Choose Refund Method</div>
        <div class="popup-normal-heading margin-top-5 secondary-text">
          We're sorry to see you cancel your booking. Please select your preferred refund option below:
        </div>
        <div class="job-info margin-top-20">

          <div class="refund-container">
            <div class="refund-section-container ion-text-left">
              <!-- Wallet Option -->
              <div class="refund-section-item" (click)="onViewChange('MY_CASH')"
                [ngClass]="{'selected': selectedRefundOption === 'MY_CASH'}">
                <div class="refund-checkbox-container">
                  <ion-checkbox [checked]="selectedRefundOption === 'MY_CASH'" [mode]="'md'"></ion-checkbox>
                  <span class="refund-text">Refund to Wallet <span class="wallet-fast-use">(Recommended for Faster Use)
                    </span></span>
                </div>

                <!-- Wallet Info -->
                <div class="refund-info-text" *ngIf="selectedRefundOption === 'MY_CASH'">
                  <span>1. Amount will be credited to your in-app wallet.</span><br />
                  <span>2. You can use this amount for future bookings immediately.</span>
                </div>
              </div>

              <!-- Original Source Option -->
              <div class="refund-section-item" (click)="onViewChange('SOURCE')"
                [ngClass]="{'selected': selectedRefundOption === 'SOURCE'}">
                <div class="refund-checkbox-container margin-right-30">
                  <ion-checkbox [checked]="selectedRefundOption === 'SOURCE'" [mode]="'md'"></ion-checkbox>
                  <span class="refund-text">Refund to Original Payment Source</span>
                </div>

                <!-- Source Info -->
                <div class="refund-info-text" *ngIf="selectedRefundOption === 'SOURCE'">
                  <span>1. Amount will be credited back to your original payment method (Bank/UPI/Card).</span><br />
                  <span>2. May take 5–7 working days to reflect, depending on your bank's processing time.</span>
                </div>
              </div>
            </div>

            <!-- Static Note Section -->
            <div class="refund-note-container margin-top-10" *ngIf="selectedRefundOption === 'MY_CASH'">
              <span class="note-text">⚠️ <strong>Note:</strong> Once the amount is refunded to your wallet, it cannot
                be transferred back to your bank account.</span>
            </div>
          </div>

          <div class="privacy-container margin-top-10">
            <ion-button class="site-full-rounded-button less-border-radius text-capitalize" expand="full" shape="round"
              type="submit" (click)="proceed()">
              Proceed
            </ion-button>
          </div>

        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #bookingModal [isOpen]="isBookingCancelModalOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="close()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">Cancel Booking</div>
          <div class="popup-normal-heading margin-top-5 secondary-text">
            Are you sure you want to cancel this booking?
          </div>
          <div class="job-info margin-top-20">
            <div class="margin-bottom-15">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': bookingCancel.invalid && onClickValidation}">
                <ion-input label="Cancellation Reason" labelPlacement="floating" required="required"
                  name="bookingCancel" #bookingCancel="ngModel" [(ngModel)]="reasonBookingCancel">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="bookingCancel" [onClickValidation]="onClickValidation">
              </app-validation-message>
            </div>
          </div>
          <div class="main-modal-dismiss button-gap">
            <ion-button class="site-full-rounded-button primary-button notification-list-no-button" shape="round"
              type="submit" (click)="close()">No</ion-button>
            <ion-button class="site-full-rounded-button primary-button notification-list-yes-button" shape="round"
              type="submit" (click)="confirmCancellation(recordForm.form)">Yes</ion-button>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<!-- Success Modal -->
<ion-modal class="site-custom-popup job-invitation-popup" #successModal [isOpen]="isSuccessModalOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeSuccessModal()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <h2>Your booking has been successfully cancelled.</h2>
        <div class="main-modal-dismiss">
          <ion-button class="site-full-rounded-button primary-button margin-top-20" shape="round"
            (click)="closeSuccessModal(); successModal.dismiss()">OK</ion-button>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>