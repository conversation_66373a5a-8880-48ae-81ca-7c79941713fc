import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-cancel-my-booking',
  templateUrl: './cancel-my-booking.component.html',
  styleUrls: ['./cancel-my-booking.component.scss'],
})
export class CancelMyBookingComponent implements OnInit {

  isBookingCancelModalOpen: boolean = false;
  isBookingCancelSelectOption: boolean = false;
  isSuccessModalOpen: boolean = false;
  bookingListData: any;
  bookingId: any;
  bookingCode: any;
  cancellationInformations: Array<any> = new Array<any>();
  cancellationPolicyInfo: string | null = null;
  onClickValidation: boolean = false;
  reasonBookingCancel: string | null = null;
  selectedRefundOption: string = "MY_CASH";

  constructor(
    private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly cdr: ChangeDetectorRef
  ) { }

  ngOnInit() {
    this.bookingListData = this.localStorageService.getObject("selectedBooking");
  }

  ionViewWillEnter() {
    this.bookingListData = this.localStorageService.getObject("selectedBooking");

    if (this.bookingListData) {
      this.bookingId = this.bookingListData?.id;
      this.bookingCode = this.bookingListData?.bookingCode;
    }

    this.freeCancellationPolicy(this.bookingId);
  }

  back() {
    this.navController.navigateBack(`/portal/my/booking/${this.bookingId}/detail`, { animated: true });
  }

  returnHomePage() {
    this.navController.navigateRoot("/dashboard", { animated: true });
  }

  openCancelBookingSelectOption() {
    this.isBookingCancelSelectOption = true;
  }

  closeCancelBookingSelectOption() {
    this.isBookingCancelSelectOption = false;
  }

  openCancelConfirmationModal() {
    this.selectedRefundOption = 'MY_CASH';
    this.openCancelBookingSelectOption();
  }

  openBookingReasonModal() {
    this.isBookingCancelModalOpen = true;
  }

  close() {
    this.isBookingCancelModalOpen = false;
    this.onClickValidation = false;
    this.reasonBookingCancel = null;
  }

  onViewChange(newView: 'MY_CASH' | 'SOURCE') {
    this.selectedRefundOption = newView;
  }

  proceed() {
    this.closeCancelBookingSelectOption();
    this.openBookingReasonModal();

    this.onClickValidation = false;
    this.reasonBookingCancel = null;
  }

  confirmCancellation(form: any) {
    this.onClickValidation = !form.valid;

    if (!form.valid) {
      return;
    }
    this.isBookingCancelModalOpen = false;
    this.cancelBooking();
  }

  cancelBooking() {
    const payload = {
      bookingId: this.bookingId,
      cancellationReason: this.reasonBookingCancel,
      isRefundInMyCash: this.selectedRefundOption === 'MY_CASH' ? true : false
    };
    this.dataService.bookingCancel(payload).subscribe({
      next: (response: RestResponse) => {
        this.showSuccessModal();
      },
      error: (error) => {
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  showSuccessModal() {
    this.isSuccessModalOpen = true; // Open the success modal
  }

  closeSuccessModal() {
    this.isSuccessModalOpen = false; // Close success modal
    this.navController.navigateBack("/dashboard", { animated: true }); // Navigate back to dashboard
  }

  freeCancellationPolicy(bookingId: string) {
    //API Call
    this.dataService.bookingCancellationPolicy(bookingId)
      .subscribe({
        next: (response: RestResponse) => {
          this.cancellationInformations = [...response.data?.cancellationPolicy.cancellationInformation];
          this.cancellationPolicyInfo = response.data.cancellationPolicy.info;
        },
        error: (error: any) => {
          this.toastService.show(error.message || 'An error occurred');
        }
      });
  }
}
