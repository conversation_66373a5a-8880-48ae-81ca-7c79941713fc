import { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-customer-see-more-compaigns',
  templateUrl: './customer-see-more-compaigns.component.html',
  styleUrls: ['./customer-see-more-compaigns.component.scss'],
})
export class CustomerSeeMoreCompaignsComponent implements OnInit {
  @ViewChild('textElement') textElement!: ElementRef;
  user: any;
  filter: any;
  pageSize: number = 10;
  loading: string = "NOT_STARTED";
  allCampaigns: Array<any> = new Array<any>();

  walletId!: string;
  backScreen: string | null = null;
  backUrl: string | null = null;

  constructor(private authService: AuthService,
    private readonly toastService: ToastService,
    private readonly dataService: DataService,
    public readonly commonService: CommonService,
    private readonly navController: NavController,
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef
  ) {

  }

  ngOnInit() { }

  ionViewWillEnter() {
    this.user = this.authService.getUser();

    this.walletId = this.route.snapshot.paramMap.get('walletId') || "";

    this.allCampaigns = new Array<any>();
    this.filter = {} as any;
    this.filter.offset = 1;

    this.backScreen = history.state.backScreen || null;
    this.backUrl = this.backScreen === 'home-screen' ? `/dashboard` : `/account1`;

    this.activeCampaigns(null, true);
  }

  activeCampaigns($event?: any, handleLoading?: boolean) {
    const payload = {
      pagination: {
        next: this.pageSize,
        offset: this.filter.offset,
      }
    };
    if (handleLoading) { this.loading = "LOADING"; }
    this.dataService.activeCampaign(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        if (response.data.length > 0) {
          response.data.forEach((campaign: any) => {
            if (campaign.description) {
              campaign.description = campaign.description.replace(/^"(.*)"$/, '$1');
            }
            if (campaign.rewards) {
              campaign.rewards = campaign.rewards.replace(/^"(.*)"$/, '$1');
            }
          });

          this.allCampaigns.push(...response.data);
        }
        if ($event) {
          $event.target.complete();
          if (this.allCampaigns.length > 0 && this.allCampaigns.length >= this.allCampaigns[0].totalCount) {
            $event.target.disabled = true;
          }
        }
        this.cdr.detectChanges();
      },
      error: (error: any) => {
        this.loading = "LOADED";
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  onPageChanged($event: any) {
    if (this.allCampaigns.length > 0 && this.allCampaigns.length >= this.allCampaigns[0].totalCount) {
      $event.target.complete();
      $event.target.disabled = true;
      return;
    }
    this.filter.offset = this.filter.offset + 1;
    this.activeCampaigns($event, false);
  }

  seeCompaignDetail(campaignId: string) {
    this.navController.navigateForward('/portal/my/campaign/details', {
      state: {
        campaignId: campaignId,
        walletId: this.walletId
      },
      animated: true
    });
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

  toggleText(textElement: HTMLElement, event: Event) {
    textElement.classList.toggle('expanded');
    // Change "See More" / "See Less"
    const seeMoreBtn = event.target as HTMLElement;
    seeMoreBtn.textContent = textElement.classList.contains('expanded') ? 'See Less' : 'See More';
  }

  formatText(text: string): string {
    return text ? text.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, char => char.toUpperCase()) : '';
  }

  getImageSrc(campaignType: string): string {
    switch (campaignType) {
      case 'FREE_TRIP':
        return '/assets/images/svg/travel-agency.svg';
      case 'WALLET_TOPUP':
        return '/assets/images/svg/wallet.svg';
      default:
        return '/assets/images/svg/booking.svg';
    }
  }
}
