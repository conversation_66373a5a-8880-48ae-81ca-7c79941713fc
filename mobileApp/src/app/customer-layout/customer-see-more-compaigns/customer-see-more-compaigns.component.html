<ion-content class="customer-dashboard-page customer-payment-page compaigns">
  <app-customer-header [innerPage]="true" [headingText]="'Active Campaigns'" (rightActionCallback)="openNotifications()"
    [backUrl]="backUrl"></app-customer-header>
  <div class="customer-body-section has-header-section-change-profile">
    <div class="payment-body-section less-height no-padding">
      <ion-content class="payment-body-ion-content scroll">
        @if (loading==='LOADING') {
        <div class="my-booking-card-container">
          <div class="my-booking-card margin-top-10">
            <ion-grid>
              <ion-row>
                <ion-col>
                  <ion-row class="check-in-out-con my-bookings-check">
                    <ion-col size="8" class="my-booking-code ion-no-padding">
                      <div class="my-booking-text">
                        <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                      </div>
                    </ion-col>
                    <ion-col size="4" class="my-booking-status">
                      <div class="my-booking-text">
                        <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                      </div>
                    </ion-col>
                  </ion-row>

                  <div>
                    <div class="hotel-name my-bookings-hotel-name">
                      <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                    </div>
                    <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                        style="width: 200px"></ion-skeleton-text></div>
                  </div>
                </ion-col>
              </ion-row>

              <ion-row class="check-in-out-con">
                <ion-col size="5">
                  <div class="check-in-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                    </div>
                    <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
                <ion-col size="2">
                  <div class="time-duration-con">
                    <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                    <div class="time-duration">
                      <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                    </div>
                  </div>
                </ion-col>
                <ion-col size="5">
                  <div class="check-out-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                    </div>
                    <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
              </ion-row>

              <ion-row>
                <ion-col>
                  <div class="guest-and-rooms-con">
                    <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                    </div>
                    <div class="rooms">
                      <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                    </div>
                  </div>
                </ion-col>
              </ion-row>

            </ion-grid>
          </div>
        </div>
        }
        @else if (loading==='LOADED') {
        <div class="card-container-compaigns no-margin">
          <div class="compaign-card" *ngFor="let compaigns of allCampaigns" (click)="seeCompaignDetail(compaigns.id)">
            <div class="compaign-card-header">
              <div class="compaign-image-slide" *ngFor="let attachment of compaigns?.attachments">
                <img *ngIf="attachment.path" [src]="attachment.path" alt="">
              </div>
              <div class="compaign-image-slide" *ngIf="!compaigns?.attachments || compaigns.attachments.length === 0">
                <img
                  [src]="compaigns?.type === 'FREE_TRIP' ? '/assets/images/svg/free-trip-2.svg' : compaigns?.type === 'WALLET_TOPUP' ? '/assets/images/svg/wallet-2.svg' : '/assets/images/svg/booking-2.svg'"
                  alt="Default Campaign Image">
              </div>
            </div>

            <div class="compaign-list-container margin-bottom-5">
              <div class="campagin-type margin-top-10">
                <div class="image-reward-type">
                  <img [src]="getImageSrc(compaigns.type)" alt="campaign image">
                  <span class="margin-left-5">{{formatText(compaigns.type)}}</span>
                </div>
                <div class="fee-amount" *ngIf="compaigns.type==='FREE_TRIP'">
                  <span>*Fees to participate in the offer</span>
                  <div class="fee-amount free-trip-amount-font margin-left-5" *ngIf="compaigns.type==='FREE_TRIP'">
                    <span>₹{{compaigns.minRechargeAmount}}</span>
                  </div>
                </div>
              </div>

              <!-- <p #reward class="compaign-list-rewards text margin-top-5">{{compaigns.rewards}}</p>
              <span *ngIf="compaigns?.rewards?.length > 100" class="see-more" (click)="toggleText(reward,$event)">
                See More
              </span> -->

              <div class="campagin-type margin-top-10">
                <div class="campaign-title">
                  <span>{{ compaigns.title }}</span>
                </div>

              </div>
              <hr class="separator-line-campgaigns" />
              <div class='description margin-top-5'>
                <span class="campaign-description-reward text">
                  <ng-container *ngIf="compaigns?.rewards?.length > 0; else noRewards">
                    {{ compaigns.rewards }}
                  </ng-container>
                  <ng-template #noRewards>
                    {{ compaigns?.description }}
                  </ng-template>
                </span>
              </div>
              <!-- <span *ngIf="compaigns?.rewards?.length > 100" class="see-more" (click)="toggleText(textElement, $event)">
                See More
              </span>-->

              <!-- <p #desc class="compaign-list-desc text">{{compaigns.description}}</p>
              <span *ngIf="compaigns?.description?.length > 100" class="see-more" (click)="toggleText(desc,$event)">
                See More
              </span> -->
            </div>

            <!-- <div class="compaign-list-button-container">
              <ion-button class="site-full-rounded-button compaign-list-button primary-button" expand="full"
                shape="round" type="submit" (click)="seeCompaignDetail(compaigns)">
                View More Detail
              </ion-button>
            </div> -->
          </div>

          <div class="campaign-item margin-top-10" *ngIf="allCampaigns.length <= 0">
            <div class="no-record">No Active Campaigns Available.</div>
          </div>
        </div>
        }
        <ion-infinite-scroll *ngIf="allCampaigns.length > 0" threshold="50px" (ionInfinite)="onPageChanged($event)">
          <ion-infinite-scroll-content loadingSpinner="bubbles"
            loadingText="Loading more compaigns..."></ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </ion-content>
    </div>
  </div>
</ion-content>