import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { AlertController, NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-booking-cancel',
  templateUrl: './booking-cancel.component.html',
  styleUrls: ['./booking-cancel.component.scss'],
})
export class BookingCancelComponent implements OnInit {

  isBookingCancelModalOpen: boolean = false;
  isSuccessModalOpen: boolean = false;
  onClickValidation: boolean = false;
  bookingResponse: any;
  bookingDetails: any;
  bookingId: any;
  bookingCode: any;
  cancellationInformations: Array<any> = new Array<any>();
  cancellationInfo: any;
  isCancelPolicyPopupOpen: boolean = false;
  reasonBookingCancel: string | null = null;
  isBookingCancelSelectOption: boolean = false;
  selectedRefundOption: string = "MY_CASH";

  constructor(
    private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly cdr: ChangeDetectorRef
  ) { }

  ngOnInit() { }

  ionViewWillEnter() {
    this.bookingResponse = this.localStorageService.getObject("confirmBookingResponse");
    if (this.bookingResponse) {
      this.bookingDetails = this.bookingResponse.bookingDetails;
      this.bookingId = this.bookingResponse.customerBookingId;
      this.bookingCode = this.bookingDetails?.bookingCode;
      this.fetchCancellationPolicy();
    }
  }

  back() {
    this.navController.navigateBack("/portal/confirmation/details", { animated: true });
  }

  returnHomePage() {
    this.navController.navigateRoot("/dashboard", { animated: true });
  }

  openCancelBookingSelectOption() {
    this.isBookingCancelSelectOption = true;
  }

  closeCancelBookingSelectOption() {
    this.isBookingCancelSelectOption = false;
  }

  openCancelConfirmationModal() {
    this.selectedRefundOption = 'MY_CASH';
    this.openCancelBookingSelectOption();
  }

  openBookingReasonModal() {
    this.isBookingCancelModalOpen = true;
  }

  close() {
    this.isBookingCancelModalOpen = false;
    this.onClickValidation = false;
    this.reasonBookingCancel = null;
  }

  onViewChange(newView: 'MY_CASH' | 'SOURCE') {
    this.selectedRefundOption = newView;
  }

  proceed() {
    this.closeCancelBookingSelectOption();
    this.openBookingReasonModal();

    this.onClickValidation = false;
    this.reasonBookingCancel = null;
  }

  confirmCancellation(form: any) {
    this.onClickValidation = !form.valid;

    if (!form.valid) {
      return;
    }
    this.isBookingCancelModalOpen = false;
    this.cancelBooking();
  }

  cancelBooking() {
    const payload = {
      bookingId: this.bookingId,
      cancellationReason: this.reasonBookingCancel,
      isRefundInMyCash: this.selectedRefundOption === 'MY_CASH' ? true : false
    };
    //  this.loadingService.show();
    this.dataService.bookingCancel(payload).subscribe({
      next: (response: RestResponse) => {
        //    this.loadingService.hide();
        this.showSuccessModal();
      },
      error: (error) => {
        //   this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  fetchCancellationPolicy() {
    const payload = {
      searchSessionId: this.bookingResponse.booking.searchSessionId,
      bookingType: this.bookingResponse.booking.bookingType,
      arrivalDate: this.bookingResponse.booking.arrivalDate,
      departureDate: this.bookingResponse.booking.departureDate,
      currency: this.bookingResponse.booking.currency,
      guestNationality: this.bookingResponse.booking.guestNationality,
      countryCode: this.bookingResponse.booking.countryCode,
      city: this.bookingResponse.booking.city,
      hotelId: this.bookingResponse.booking.hotelId,
      name: this.bookingResponse.booking.name,
      roomDetails: [
        {
          type: this.bookingResponse.booking.roomDetails[0].type,
          bookingKey: this.bookingResponse.booking.roomDetails[0].bookingKey,
          adults: this.bookingResponse.booking.roomDetails[0].adults,
          children: this.bookingResponse.booking.roomDetails[0].children,
          ChildrenAges: this.bookingResponse.booking.roomDetails[0].childrenAges,
        }
      ]
    };
    this.dataService.hotelCancellationPolicy(payload)
      .subscribe({
        next: (response: RestResponse) => {
          this.cancellationInformations = response.data.cancellationInformation;;
          this.cancellationInfo = response.data.info;

        },
        error: (error: any) => {
          this.toastService.show(error.message || 'An error occurred');
        }
      });
  }
  showSuccessModal() {
    this.isSuccessModalOpen = true; // Open the success modal
  }

  closeSuccessModal() {
    this.isSuccessModalOpen = false; // Close success modal
    this.navController.navigateBack("/dashboard", { animated: true }); // Navigate back to dashboard
  }

}
