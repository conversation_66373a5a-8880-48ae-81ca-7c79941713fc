import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-booking-confirmation-fail',
  templateUrl: './booking-confirmation-fail.component.html',
  styleUrls: ['./booking-confirmation-fail.component.scss'],
})
export class BookingConfirmationFailComponent implements OnInit {

  bookingResponse: any;
  bookingDetails: any;
  bookingType: string | null = null;

  constructor(private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute
  ) {

  }

  ngOnInit() {
    this.bookingResponse = this.localStorageService.getObject("confirmBookingResponse");
  }

  ionViewWillEnter() {
    this.route.queryParams.subscribe(params => {
      this.bookingType = params['bookingType'] || "OTHERS"
    });

    this.bookingResponse = this.localStorageService.getObject("confirmBookingResponse");

    if (this.bookingResponse) {
      this.bookingDetails = this.bookingResponse.bookingDetails;
    }
  }

  retry() {
    this.localStorageService.setObject("resetHotelsListing", true);
    this.localStorageService.setObject("resetGuestFormDetails", true);
    this.navController.navigateForward("/portal/search/hotels",  { animated: true, queryParams: { bookingType: this.bookingType } });
  }

}
