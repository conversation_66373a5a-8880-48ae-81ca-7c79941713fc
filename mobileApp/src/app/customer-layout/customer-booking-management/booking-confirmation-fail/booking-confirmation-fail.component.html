<ion-content class="booking-page">
  <div class="booking-page-container hotel-listing profile-detail-container">
    <div class="form-container text-left">
      <div class="settings-heading-container">
        <div class="settings-heading booking-heading">Booking Confirmation</div>
      </div>

      <div class="image-container">
        <img src="assets/images/svg/booking-fail.svg" class="two-factor-image" />
      </div>

      <p class="page-sub-heading setup-two-factor booking-confirm"
        *ngIf="bookingDetails && bookingDetails?.bookingStatus">
        Booking {{bookingDetails?.bookingStatus}}!
      </p>

      <p class="page-sub-heading variation-heading booking-variation-heading">
        Something went wrong while processing your booking. Please try again.
      </p>

      <div class="privacy-container">
        <ion-button class="site-full-rounded-button primary-button" shape="round" type="submit" (click)="retry()">
          Retry
        </ion-button>
      </div>

    </div>

  </div>
</ion-content>