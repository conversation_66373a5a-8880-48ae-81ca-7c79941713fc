import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-booking-confirmation-details',
  templateUrl: './booking-confirmation-details.component.html',
  styleUrls: ['./booking-confirmation-details.component.scss'],
})
export class BookingConfirmationDetailsComponent implements OnInit {
  bookingResponse: any;
  bookingData: any;
  bookingRoomDetails: any;
  hotelDetails: any;
  fetchHotelDetailById: any;
  bookingType: string | null = null;
  preBookingResponse: any;

  constructor(
    private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    public readonly commonService: CommonService
  ) {

  }

  ngOnInit() {
    this.bookingResponse = this.localStorageService.getObject("confirmBookingDetailResponse");
    this.fetchHotelDetailById = this.localStorageService.getObject("fetchHotelDetailById");
    this.preBookingResponse = this.localStorageService.getObject("preBookingResponse");
  }

  ionViewWillEnter() {
    this.bookingResponse = this.localStorageService.getObject("confirmBookingDetailResponse");
    this.fetchHotelDetailById = this.localStorageService.getObject("fetchHotelDetailById");
    this.preBookingResponse = this.localStorageService.getObject("preBookingResponse");

    this.route.queryParams.subscribe(params => {
      this.bookingType = params['bookingType'] || "OTHERS"
    });

    if (this.bookingResponse) {
      this.bookingData = this.bookingResponse.booking;

      if (this.bookingData.hotelInfo) {
        this.hotelDetails = this.bookingData.hotelInfo;
      }
      if (this.bookingData.roomInfo) {
        this.bookingRoomDetails = this.bookingData.roomInfo;
      }
    }
  }

  formatDate(bookingDate: string): string {
    if (!bookingDate) return ''; // Handle if bookingDate is null or undefined

    const date = new Date(bookingDate);
    if (isNaN(date.getTime())) return ''; // Return an empty string if date is invalid

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`; // Format as yyyy-mm-dd
  }

  getHotelDescription(hotelDescription: string | null): string {
    return hotelDescription ? hotelDescription : '';
  }

  back() {
    this.navController.navigateBack("/portal/confirmation", { animated: true });
  }

  cancelBooking() {
    this.navController.navigateForward("/portal/cancel/booking", { animated: true });
  }

  returnHomePage() {
    this.navController.navigateRoot("/dashboard", { animated: true });
  }

}
