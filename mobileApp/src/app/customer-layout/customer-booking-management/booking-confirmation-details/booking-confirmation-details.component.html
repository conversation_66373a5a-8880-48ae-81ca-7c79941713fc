<ion-content class="customer-dashboard-page booking-page">

  <form class="custom-form" #recordForm="ngForm" novalidate>
    <app-customer-header [innerPage]="true" [headingText]="'Booking Confirmation'"
      [backUrl]="'/booking'"></app-customer-header>
    <div class="booking-page-container hotel-listing">
      <div class="form-container text-left">
        <div class="booking-info-container no-margin" *ngIf="bookingData">
          <div class="date-status-item" *ngIf="bookingData?.bookingId">
            <strong>Booking Id:</strong>
            <span class="booking-reference blue-text">{{ bookingData?.bookingId}}</span>
          </div>
          <div class="dates-container" *ngIf="bookingData?.checkIn && bookingData?.checkOut">
            <span class="check-in check-new-font">Check-In: <strong class="check-date check-new-font">{{
                bookingData?.checkIn }}</strong></span>
            <span class="check-out check-new-font">Check-Out: <strong class="check-date check-new-font">{{
                bookingData?.checkOut
                }}</strong></span>
          </div>
          <hr class="separator-line" *ngIf="bookingData?.bookingdate" />
          <div class="booking-date-status">
            <div class="date-status-item" *ngIf="bookingData?.bookingdate">
              <strong>Booking Date:</strong>
              <span class="booking-date">{{ formatDate(bookingData?.bookingdate)}}</span>
            </div>
            <div class="date-status-item" *ngIf="bookingData?.bookingStatus">
              <strong>Booking Status:</strong>
              <span class="status confirm-booking-status">{{ bookingData?.bookingStatus }}</span>
            </div>
            <div class="date-status-item" *ngIf="hotelDetails && hotelDetails?.hotelName">
              <strong>Hotel Name:</strong>
              <span class="booking-date">{{ hotelDetails?.hotelName }}</span>
            </div>
            <div class="date-status-item" *ngIf="hotelDetails && hotelDetails?.hotelAddress">
              <strong>Hotel Address:</strong>
              <span class="booking-date">{{ hotelDetails?.hotelAddress }} <a class="locate-link"
                  *ngIf="fetchHotelDetailById?.longitude && fetchHotelDetailById?.longitude"
                  (click)="commonService.openGoogleMaps(fetchHotelDetailById?.longitude,fetchHotelDetailById?.latitude)">
                  Locate Now
                </a></span>
            </div>
            <div class="date-status-item" *ngIf="hotelDetails && hotelDetails?.hotelCity">
              <strong>Hotel City:</strong>
              <span class="booking-date">{{ hotelDetails?.hotelCity }}</span>
            </div>
            <!-- <div class="call-locate-now" *ngIf="fetchHotelDetailById?.phone">
              <ion-row>
                <ion-col size="12">
                  <div class="hotel-desc">
                    <div class="call-locate-now">
                      <img src="/assets/images/icons/icons8-phone-40.png" class="max-width-icon">

                      <a class="margin-top-5" [href]="'tel:' + fetchHotelDetailById?.phone">{{fetchHotelDetailById?.phone}}
                      </a>
                    </div>
                  </div>
                </ion-col>
              </ion-row>
            </div> -->
            <div class="date-status-item" *ngIf="fetchHotelDetailById?.phone">
              <strong>Hotel Phone:</strong>
              <!-- <span class="booking-date">{{ fetchHotelDetailById?.phone }}</span> -->
              <div class="call-locate-now">
                <img src="/assets/images/icons/icons8-phone-40.png" class="max-width-icon">
                <a [href]="'tel:' + fetchHotelDetailById?.phone">{{fetchHotelDetailById?.phone}}
                </a>
              </div>
            </div>
          </div>

          <div class="hotel-description-card" *ngIf="hotelDetails?.hotelDescription">
            <div class="hotel-description-title">
              <strong>Hotel Description</strong>
            </div>
            <div class="hotel-description-content">
              {{ getHotelDescription(hotelDetails?.hotelDescription) }}
            </div>
          </div>


          <div class="info-card">
            <div class="info-item" *ngIf="hotelDetails?.leaderFirstName && hotelDetails?.leaderLastName">
              <span class="info-label">Customer Name:</span>
              <span class="info-value">{{hotelDetails?.leaderFirstName}} {{hotelDetails?.leaderLastName}} </span>
            </div>
            <div class="info-item">
              <span class="info-label">Room Info:</span>
              <span class="info-value info-room-normal">{{bookingRoomDetails?.roomType}}</span>
            </div>
          </div>
        </div>
        <!-- <div class="call-locate-now" *ngIf="fetchHotelDetailById?.phone">
          <ion-row>
            <ion-col size="12">
              <div class="hotel-desc">
                <div class="call-locate-now">
                  <img src="/assets/images/icons/icons8-phone-40.png" class="max-width-icon">

                  <a class="margin-top-5" [href]="'tel:' + fetchHotelDetailById?.phone">{{fetchHotelDetailById?.phone}}
                  </a>
                </div>
              </div>
            </ion-col>
          </ion-row>
        </div> -->
        <hr class="separator-line" />
        <div class="info-card" *ngIf="preBookingResponse?.bookingDetails?.additionalAmount>0">
          <h4>Payment Detail</h4>
          <div class="info-item" *ngIf="preBookingResponse?.bookingDetails?.additionalAmount>0">
            <span class="info-label">Amount:</span>
            <span class="info-value">{{preBookingResponse?.bookingDetails?.additionalAmount| currency: 'INR' :
              'symbol' : '1.0-2'}}/-</span>
          </div>
          <div class="info-item" *ngIf="preBookingResponse?.bookingDetails?.gstAmount>0">
            <span class="info-label">Gst:</span>
            <span class="info-value info-room-normal">{{preBookingResponse?.bookingDetails?.gstAmount| currency: 'INR' :
              'symbol' : '1.0-2'}}/-</span>
          </div>
          <div class="info-item" *ngIf="preBookingResponse?.bookingDetails?.serviceCharge>0">
            <span class="info-label">Convenience Fee(incl. gst):</span>
            <span class="info-value info-room-normal">{{preBookingResponse?.bookingDetails?.serviceCharge| currency:
              'INR' :
              'symbol' : '1.0-2'}}/-</span>
          </div>
          <div class="info-item" *ngIf="preBookingResponse?.bookingDetails?.totalAdditionalAmount>0">
            <span class="info-label">Paid Amount:</span>
            <span class="info-value">{{preBookingResponse?.bookingDetails?.totalAdditionalAmount| currency: 'INR' :
              'symbol' : '1.0-2'}}/-</span>
          </div>
        </div>


        <div class="back-search-container" *ngIf="bookingType === 'OTHERS'">
          <a (click)="cancelBooking()">Cancel Booking</a>
        </div>

        <div class="privacy-container margin-top-20" *ngIf="bookingType === 'OTHERS' ">
          <ion-button class="primary-button text-capitalize" expand="full" shape="round" type="submit"
            (click)="returnHomePage()">
            Return to Home
          </ion-button>
        </div>

        <div class="privacy-container margin-top-20" *ngIf="bookingType != 'OTHERS' ">
          <ion-button class="primary-button text-capitalize" expand="full" shape="round" type="submit"
            (click)="returnHomePage()">
            Return to Home
          </ion-button>
        </div>

      </div>
    </div>
  </form>

</ion-content>
