import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';

@Component({
  selector: 'app-guest-detail-coupons',
  templateUrl: './guest-detail-coupons.component.html',
  styleUrls: ['./guest-detail-coupons.component.scss'],
})
export class GuestDetailCouponsComponent implements OnInit {
  preBookingResponse: any;
  loading: string = "NOT_STARTED";
  campaignBookingCashbackResponse: any;
  // campaignId: string | null = null;
  coupons: Array<any> = new Array<any>();
  selectedcampaignId: string | null = null;
  backUrlWithParams: string = '';

  constructor(
    private readonly navController: NavController
  ) { }

  ngOnInit() {

  }

  ionViewWillEnter() {
    this.coupons = history.state.coupons || null;
    this.selectedcampaignId = history.state.selectedBookingCampaignId || null;

    this.backUrlWithParams = `/portal/guest/preview/details?fromScreen=guest-detail-coupon`;
  }

  applyCoupon(coupon: any, index: number) {
    this.selectedcampaignId = coupon.id;
    this.navController.navigateForward('/portal/guest/preview/details', {
      state: {
        coupon: coupon
      },
      animated: true
    });

  }

}
