<ion-content class="customer-dashboard-page guest-coupons">
  <app-customer-header [innerPage]="true" [headingText]="'Campaigns'"
    [backUrl]="backUrlWithParams"></app-customer-header>
  <div class="customer-body-section has-header-section-change-profile">
    <div class="payment-body-section less-height no-padding">
      <ion-content class="payment-body-ion-content scroll">
        <div class="my-coupon-container">
          <div *ngFor="let coupon of coupons; let i = index">
            <div class="my-coupon-card">
              <!-- [ngClass]="{'selected-slide': selectedSlideIndex === i}"
                    (click)="seeCompaignDetail(campaignBookingCashback.id,i)" -->
              <div class="coupon-container">
                <div class="coupon-text-container">
                  <span class="coupon-text">{{coupon?.title}}</span>
                  <ng-container *ngIf="coupon?.rewards; else noRewards">
                    <span class="coupon-amount">{{ coupon?.rewards }}</span>
                  </ng-container>

                  <ng-template #noRewards>
                    <span class="coupon-amount">
                      Get {{ coupon?.myCash + coupon?.promoCash }}% Cashback,
                      <span *ngIf="coupon?.myCash > 0">
                        {{ coupon?.myCash }}% in My Cash
                      </span>
                      <span *ngIf="coupon?.myCash > 0 && coupon?.promoCash > 0"> and </span>
                      <span *ngIf="coupon?.promoCash > 0">
                        {{ coupon?.promoCash }}% in Promo Cash.
                      </span>
                    </span>
                  </ng-template>
                </div>
                <div class="apply-coupon-container">
                  <span class="apply-text-font" (click)="applyCoupon(coupon,i)"
                    [ngStyle]="{ color: selectedcampaignId === coupon.id ? 'red' : '#C29133' }">{{
                    selectedcampaignId ===
                    coupon.id ? 'Applied' : 'Apply' }}</span>
                  <!-- <ion-icon class="balance-icon" src="/assets/images/svg/gift-coupon.svg" slot="start"></ion-icon> -->
                </div>
              </div>

            </div>

          </div>
        </div>
      </ion-content>
    </div>
  </div>
</ion-content>