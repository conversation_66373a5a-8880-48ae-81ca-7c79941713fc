<ion-content class="booking-page">
  <div class="booking-page-container hotel-listing profile-detail-container">
    <div class="form-container text-left">
      <div class="settings-heading-container">
        <div class="settings-heading booking-heading">Booking Confirmation</div>
      </div>

      <div class="image-container">
        <img src="assets/images/svg/confirm.svg" class="two-factor-image" />
      </div>

      <p class="page-sub-heading setup-two-factor booking-confirm">
        Booking Confirmed Successfully!
        <!-- <strong>(2FA)</strong> -->
      </p>

      <p class="page-sub-heading variation-heading booking-variation-heading">
        Thank you for choosing us! Your booking is confirmed. Tap below to view more details about your
        reservation.
      </p>

      <div class="success-container" *ngIf="cashbackDetails">
        <!-- You've earned {{ cashbackDetails?.myCash | currency : 'INR' : 'symbol' : '1.0-0' }} MyCash! 💰 -->
        <span class="earned-text margin-top-10" *ngIf="cashbackDetails?.myCash > 0 || cashbackDetails?.promo > 0">
          You've earned</span>

        <span class="earned-my-cash margin-top-10" *ngIf="cashbackDetails?.myCash > 0">
          ₹{{ cashbackDetails?.myCash }}
          <span class="cash-label">MyCash</span>
        </span>

        <span class="earned-promo-cash margin-top-10" *ngIf="cashbackDetails?.promo > 0">
          ₹{{ cashbackDetails?.promo }}
          <span>PromoCash</span>
        </span>
      </div>

      <div class="privacy-container">
        <ion-button [disabled]="loading == 'LOADING'" class="site-full-rounded-button primary-button text-capitalize"
          shape="round" type="submit" (click)="viewDetails()">
          View Details
        </ion-button>
        <ion-button class="grey-background text-capitalize" shape="round" type="submit" (click)="returnHomePage()">
          Return to Home
        </ion-button>
      </div>

    </div>

  </div>
</ion-content>