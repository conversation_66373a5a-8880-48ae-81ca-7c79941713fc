import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-booking-confirmation',
  templateUrl: './booking-confirmation.component.html',
  styleUrls: ['./booking-confirmation.component.scss'],
})
export class BookingConfirmationComponent implements OnInit {

  bookingResponse: any;
  bookingDetails: any;
  bookingId: any;
  bookingCode: any;
  loading: string = "NOT_STARTED";
  bookingType: string | null = null;

  cashbackDetails:any;

  constructor(private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly cdr: ChangeDetectorRef,
    private readonly route: ActivatedRoute
  ) {

  }

  ngOnInit() { }

  ionViewWillEnter() {
    this.bookingResponse = this.localStorageService.getObject("confirmBookingResponse");

    this.route.queryParams.subscribe(params => {
      this.bookingType = params['bookingType'] || "OTHERS"
    });

    if (this.bookingResponse) {
      this.bookingDetails = this.bookingResponse.bookingDetails;
      this.bookingId = this.bookingDetails?.bookingId;
      this.bookingCode = this.bookingDetails?.bookingCode;
      this.cashbackDetails = this.bookingResponse.cashBackDetails;
    }
  }

  viewDetails() {
    this.loading = "LOADING";
    const payload = {
      bookingId: this.bookingId,
      bookingCode: this.bookingCode
    };
    //  this.loadingService.show();
    this.dataService.bookingDetails(payload).subscribe({
      next: (response: RestResponse) => {
        //   this.loadingService.hide();
        const data = response.data;
        this.handleConfirmDetailsResponse(data);
        this.loading = "LOADED";
      },
      error: (error) => {
        //   this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
        this.loading = "LOADED";
      }
    });
  }

  handleConfirmDetailsResponse(data: any) {
    this.localStorageService.setObject("confirmBookingDetailResponse", data);
    this.navController.navigateForward("/portal/confirmation/details", { animated: true, queryParams: { bookingType: this.bookingType } });
  }

  returnHomePage() {
    this.navController.navigateRoot("/dashboard", { animated: true });
  }

}
