import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Checkout } from 'capacitor-razorpay';
import { environment } from 'src/environments/environment';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import { ActivatedRoute } from '@angular/router';
import { CommonService } from 'src/services/common.service';
import { SwiperComponent } from 'swiper/angular';
import Swiper, { SwiperOptions } from 'swiper';
import { Location } from '@angular/common';

@Component({
  selector: 'app-guest-room-preview-details',
  templateUrl: './guest-room-preview-details.component.html',
  styleUrls: ['./guest-room-preview-details.component.scss'],
})
export class GuestRoomPreviewDetailsComponent implements OnInit {
  @ViewChild(SwiperComponent) swiper!: SwiperComponent;
  config: SwiperOptions = {
    slidesPerView: 1,
    pagination: {
      clickable: true, // Dots will be clickable
      dynamicBullets: true, // Smaller bullets for multiple slides
    },
    watchSlidesProgress: true, // Updates pagination dynamically as slides move
    zoom: true,
  };
  guestDetails: any;
  preBookingResponse: any;
  preBookingData: any;
  preBookingRoomDetails: any;
  preBookingInfo: any;
  bookingType: string | null = null;
  campaignId: string | null = null;
  fromScreen: string | null = null;

  roomDetailsVisible: boolean[] = [];
  user: any;
  isPaymentProcessing: boolean = false;
  isRewardSelected: boolean = false;

  loading: string = "NOT_STARTED";
  totalPayableAmount: any;
  availableCashDetails: any;
  convenienceFee: any;
  convenienceFeeGst: any;
  totalConvenienceFee: any;
  campaignBookingCashbackResponse: any;
  selectedSlideIndex: number | null = null;  // Initially null
  selectedBookingCampaignId: string | null = null;
  coupon: any;
  backUrlWithParams: string = '';

  constructor(
    private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly cdr: ChangeDetectorRef,
    private readonly authService: AuthService,
    private readonly route: ActivatedRoute,
    public readonly commonService: CommonService,
    private location: Location
  ) {
  }

  ngOnInit() {
    this.guestDetails = this.localStorageService.getObject("guestPreviewDetails");
    this.preBookingResponse = this.localStorageService.getObject("preBookingResponse");
    // this.selectedSlideIndex = null;

  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();
    this.guestDetails = this.localStorageService.getObject("guestPreviewDetails");
    this.preBookingResponse = this.localStorageService.getObject("preBookingResponse");
    this.bookingType = this.guestDetails.bookingType;

    this.route.queryParams.subscribe(params => {
      this.campaignId = params['campaignId'] ? `${params['campaignId']}` : null;
      this.fromScreen = params['fromScreen'] ? `${params['fromScreen']}` : null;
      if (params['convienceFeeDetail']) {
        try {
          this.availableCashDetails = JSON.parse(params['convienceFeeDetail']);
        } catch (e) {
          this.availableCashDetails = null;
        }
      } else {
        this.availableCashDetails = null;
      }
    });

    if (this.fromScreen !== 'guest-detail-coupon') {
      this.campaignBookingCashbackResponse = null;
      this.coupon = null;
      this.selectedBookingCampaignId = null;
    }

    this.backUrlWithParams = `/portal/add/guest/details?bookingType=${this.bookingType}&campaignId=${this.campaignId}`;

    if (!this.campaignId || this.campaignId === 'null') {
      this.campaignBookingCashback();
      this.fetchAvailableCashDetails();
    }

    if (history.state?.coupon) {
      this.coupon = history.state?.coupon
      this.selectedBookingCampaignId = this.coupon?.id;
    }

    if (this.preBookingResponse) {
      this.preBookingData = this.preBookingResponse.preBookingRequest.preBooking;
      if (this.preBookingData.roomDetails.length > 0) {
        this.preBookingRoomDetails = this.preBookingData.roomDetails[0];
      }
      this.preBookingInfo = this.preBookingResponse.bookingDetails;
    }

    // Initialize roomDetailsVisible with the first room open
    const roomCount = this.guestDetails.roomDetails[0].guests.length;
    this.roomDetailsVisible = new Array(roomCount).fill(false);
    this.roomDetailsVisible[0] = true; // Open the first room by default

    // convience charges detail //
    const additionalAmount = this.preBookingResponse?.bookingDetails?.additionalAmount || 0;
    const gstAmount = this.preBookingResponse?.bookingDetails?.gstAmount || 0;
    const amount = Math.floor(this.preBookingInfo.totalAdditionalAmount);

    if (this.availableCashDetails && amount > 0) {
      this.convenienceFee = amount * (this.availableCashDetails.convenienceFeeConfigurationDetail.booking / 100);
      this.convenienceFeeGst = (this.convenienceFee * this.availableCashDetails.convenienceFeeConfigurationDetail.gstRate / 100);
      this.totalConvenienceFee = parseFloat((this.convenienceFee + this.convenienceFeeGst).toFixed(2));

      this.totalPayableAmount = additionalAmount + gstAmount + this.totalConvenienceFee;
    }
    else {
      this.totalPayableAmount = additionalAmount + gstAmount;
    }
    this.cdr.detectChanges();
  }

  toggleRoomDetails(index: number) {
    this.roomDetailsVisible[index] = !this.roomDetailsVisible[index];
  }

  getFormattedAdditionalAmount(additionalAmount: number | null): string {
    //  const additionalAmount = this.preBookingRoomDetails?.additionalAmount;
    if (additionalAmount != null) {
      return this.formatNumber(additionalAmount);
    } else {
      return '0.00';
    }
  }

  formatNumber(value: number | null): string {
    if (value == null) {
      return '';
    }

    // Convert the number to a string with two decimal places
    const formattedValue = value.toFixed(2);

    // If you have a pipe that does this, apply it here
    const [integerPart, decimalPart] = formattedValue.split('.');
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    return `${formattedInteger}.${decimalPart}`;
  }

  back() {
    this.navController.navigateBack("/portal/add/guest/details", { animated: true });
  }

  continue() {
    if (this.preBookingInfo.totalAdditionalAmount > 0) {
      const amount = Math.floor(this.preBookingInfo.totalAdditionalAmount);
      setTimeout(() => {
        this.navController.navigateForward("/portal/select/payment/method", {
          animated: true,
          queryParams: {
            paymentType: 'BOOKING',
            amount: amount,
            fromScreen: 'guest-room-preview',
            walletType: 'BOOKING_PAYMENT',
            bookingId: this.preBookingResponse.bookingId,
            campaignId: this.campaignId,
            guestDetails: this.guestDetails,
            bookingType: this.bookingType,
            selectedBookingCampaignId: this.selectedBookingCampaignId
          },
        });
      }, 300);
      // this.processPayment(amount);
      //  this.processPayment(this.preBookingInfo.totalAdditionalAmount);
    }
    else {
      this.confirmBookingWithoutPayment();
    }
  }

  async processPayment(amount: number) {
    this.isPaymentProcessing = true;
    const input = {} as any;
    input.paymentType = 'BOOKING_PAYMENT';
    input.bookingId = this.preBookingResponse.bookingId;
    input.campaignId = this.campaignId;
    input.wallet = 0;
    input.promo = 0;
    input.convenienceFee = this.convenienceFee;
    input.convenienceFeeGst = this.convenienceFeeGst;
    input.amount = amount;

    this.loadingService.show();
    this.dataService.createOrder(input)
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          this.openPaymentPopup(response.data);
        },
        error: (error) => {
          this.loadingService.hide();
          this.isPaymentProcessing = false;
          this.toastService.show(error.message);
        }
      });
  }

  async openPaymentPopup(data: any) {
    const options: any = {
      key: environment.PaymentGateway.Key,
      description: 'Booking Payment',
      image: environment.PaymentGateway.Image,
      order_id: data.orderId,
      currency: environment.PaymentGateway.Currency,
      name: environment.PaymentGateway.Name,
      prefill: {
        email: this.user.email,
        contact: `${this.user.countryCode}${this.user.phoneNumber}`
      },
      theme: {
        color: '#29385B'
      }
    }
    try {
      let paymentResponse = (await Checkout.open(options));
      this.confirmBooking(data, paymentResponse.response);
    } catch (error: any) {
      this.isPaymentProcessing = false;
      if (!error) {
        return;
      }
      let errorObj = JSON.parse(error)
      if (!errorObj || !errorObj.description) {
        return;
      }
      this.toastService.show(errorObj.description);
    }
  }

  confirmBooking(data: any, paymentResponse: any) {
    const payload = {
      orderId: paymentResponse.razorpay_order_id,
      paymentId: paymentResponse.razorpay_payment_id,
      signature: paymentResponse.razorpay_signature,
      bookingRequest: {
        ...this.guestDetails
      }
    }
    this.loadingService.show();
    this.dataService.booking(payload).subscribe({
      next: (response: RestResponse) => {
        this.isPaymentProcessing = false;
        this.loadingService.hide();
        const data = response.data;
        this.handleConfirmBookingResponse(data);
      },
      error: (error) => {
        this.loadingService.hide();
        this.isPaymentProcessing = false;
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  confirmBookingWithoutPayment() {
    const payload = {
      bookingId: this.preBookingResponse.bookingId,
      ...this.guestDetails
    }
    this.loadingService.show();
    this.dataService.bookingWithoutPayment(payload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        const data = response.data;
        this.handleConfirmBookingResponse(data);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  handleConfirmBookingResponse(data: any) {
    this.localStorageService.setObject("confirmBookingResponse", data);
    if (data.bookingDetails.bookingStatus === "Fail") {
      this.navController.navigateForward("/portal/confirmation/status", { animated: true, queryParams: { bookingType: this.bookingType } });
      return;
    }
    this.localStorageService.setObject("resetGuestFormDetails", true);
    this.navController.navigateForward("/portal/confirmation", { animated: true, queryParams: { bookingType: this.bookingType } });
  }

  seeCompaignDetail(campaignId: string, index: number) {
    if (this.selectedSlideIndex === index) {
      this.selectedSlideIndex = null;
      this.selectedBookingCampaignId = null;
      return;
    }
    this.selectedSlideIndex = index;
    this.selectedBookingCampaignId = campaignId;
  }

  fetchAvailableCashDetails() {
    const payload = {
      paymentType: "BOOKING",
      expiryDays: 10
    };

    this.loading = "LOADING";

    this.dataService.availableCashDetails(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.availableCashDetails = response.data;
      },
      error: () => {
        this.loading = "LOADED";
        // this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  campaignBookingCashback() {
    this.loading = "LOADING";
    const payload = {
      price: this.preBookingResponse?.bookingDetails?.additionalAmount || 0
    }
    this.dataService.campaignBookingCashback(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        const data = response.data;
        this.campaignBookingCashbackResponse = data;
      },
      error: (error: any) => {
        this.loading = "LOADED";
        //  this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  goToCoupons() {
    this.navController.navigateForward('/portal/guest/details/coupons', {
      state: {
        coupons: this.campaignBookingCashbackResponse,
        selectedBookingCampaignId: this.selectedBookingCampaignId
      },
      animated: true
    });
  }

  removeSelectedCoupon(couponId: string) {
    this.selectedBookingCampaignId = null;
    const newState = { ...history.state, coupon: null };
    history.replaceState(newState, '', this.location.path());
  }

}
