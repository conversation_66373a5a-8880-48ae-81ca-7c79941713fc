<ion-content class="customer-dashboard-page booking-page customer-payment-page my-wallet-page">
  <app-customer-header [innerPage]="true" [headingText]="'Guest Details'"
    [backUrl]="backUrlWithParams"></app-customer-header>
  <div class="customer-body-section has-header-section-change-profile no-padding">
    <div class="payment-body-section less-height no-padding">
      <ion-content class="payment-body-ion-content scroll">

        <form class="custom-form height-for-ion-scroll " #recordForm="ngForm" novalidate (ngSubmit)="continue()">

          <div class="booking-page-container">
            <div class="form-container text-left">
              <div class="booking-info-container no-margin" *ngIf="guestDetails">
                <div class="booking-reference blue-text" *ngIf="guestDetails.name">
                  {{ guestDetails.name }}
                </div>
                <div class="dates-container" *ngIf="guestDetails.arrivalDate && guestDetails.departureDate">
                  <span class="check-in">Check-In: <strong class="check-date">{{ guestDetails.arrivalDate
                      }}</strong></span>
                  <span class="check-out">Check-Out: <strong class="check-date">{{ guestDetails.departureDate
                      }}</strong></span>
                </div>
                <hr class="separator-line" *ngIf="guestDetails.bookingType" />
                <div class="booking-date-status">
                  <div class="date-status-item" *ngIf="guestDetails.bookingType">
                    <strong>Booking Type:</strong>
                    <span class="booking-date">{{ guestDetails.bookingType }}</span>
                  </div>
                  <div class="date-status-item" *ngIf="guestDetails.currency">
                    <strong>Currency:</strong>
                    <span class="status">{{ guestDetails.currency }}</span>
                  </div>
                  <div class="date-status-item" *ngIf="guestDetails.countryName">
                    <strong>Country Name:</strong>
                    <span class="booking-date">{{ guestDetails.countryName }}</span>
                  </div>
                  <div class="date-status-item" *ngIf="guestDetails.cityName">
                    <strong>City Name:</strong>
                    <span class="booking-date">{{ guestDetails.cityName }}</span>
                  </div>
                </div>
                <hr class="separator-line" *ngIf="guestDetails.roomDetails.length > 0" />
                <div class="booking-date-status room-main-guest" *ngIf="guestDetails.roomDetails.length > 0">Room
                  Details
                  <div class="date-status-item" *ngIf="guestDetails.roomDetails[0].type">
                    <strong>Name:</strong>
                    <span class="booking-date">{{ guestDetails.roomDetails[0].type }}</span>
                  </div>
                  <div class="date-status-item"
                    *ngIf="commonService.getTotalAdults(guestDetails.roomDetails[0].adults) > 0">
                    <strong>Adults:</strong>
                    <span class="booking-date">{{ commonService.getTotalAdults(guestDetails.roomDetails[0].adults)
                      }}</span>
                  </div>
                  <div class="date-status-item"
                    *ngIf="commonService.getTotalChildren(guestDetails.roomDetails[0].children) > 0">
                    <strong>Children:</strong>
                    <span class="booking-date">{{ commonService.getTotalChildren(guestDetails.roomDetails[0].children)
                      }}</span>
                  </div>
                  <div class="date-status-item" *ngIf="guestDetails.roomDetails[0].totalRooms">
                    <strong>Total Rooms:</strong>
                    <span class="booking-date">{{ guestDetails.roomDetails[0].totalRooms }}</span>
                  </div>
                  <!-- <div class="date-status-item"
                    *ngIf="preBookingRoomDetails && preBookingRoomDetails?.additionalAmount > 0">
                    <strong *ngIf="bookingType === 'MEMBERSHIP'">Additional Amount:</strong>
                    <strong *ngIf="bookingType != 'MEMBERSHIP'">Amount:</strong>
                    <span class="booking-date">{{preBookingResponse.bookingDetails.additionalAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </div>
                  <div *ngIf="preBookingResponse.bookingDetails.gstAmount > 0" class="date-status-item">
                    <strong>GST Amount ({{ preBookingResponse.bookingDetails.gstRate }}%):</strong>
                    <span class="booking-date">{{preBookingResponse.bookingDetails.gstAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </div>
                  <div *ngIf="preBookingResponse.bookingDetails.serviceCharge > 0" class="date-status-item">
                    <strong>Service Charge:</strong>
                    <span class="booking-date">{{preBookingResponse.bookingDetails.serviceCharge | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </div>

                  <div *ngIf="totalConvenienceFee > 0" class="date-status-item">
                    <strong>Convenience fee (incl. GST):</strong>
                    <span class="booking-date">
                      {{ totalConvenienceFee | currency :'INR':'symbol':'1.0-2' }}
                    </span>
                  </div> -->

                </div>
                <hr class="separator-line" *ngIf="guestDetails.roomDetails[0].guests.length > 0" />
                <div class="booking-date-status room-main-guest" *ngIf="guestDetails.roomDetails[0].guests.length > 0">
                  Guest
                  Details</div>
                <div *ngFor="let room of guestDetails.roomDetails[0].guests; let roomIndex = index" class="room-card">
                  <div class="room-details-guest">
                    <div class="room-header" (click)="toggleRoomDetails(roomIndex)">
                      <strong class="room-main-section">Room {{ roomIndex + 1 }}:</strong>
                      <span class="arrow-icon">
                        <ion-icon
                          [src]="roomDetailsVisible[roomIndex] ? '/assets/images/svg/chevron-up.svg' : '/assets/images/svg/chevron-down.svg'"
                          class="room-icon"></ion-icon>
                      </span>
                    </div>

                    <div *ngIf="roomDetailsVisible[roomIndex]">
                      <div *ngFor="let guest of room.guest; let personIndex = index" class="guest-details">
                        <div class="guest-info">
                          <strong>
                            {{ guest.salutation === 'Child' ? 'Child ' + (personIndex + 1) + ' :' : 'Adult ' +
                            (personIndex +
                            1) + ' :' }}
                          </strong>
                          <span class="title" *ngIf="guest.salutation !== 'Child'">{{ guest.salutation }} {{
                            guest.firstName
                            }} {{ guest.lastName
                            }}</span>
                          <span class="title" *ngIf="guest.salutation === 'Child'">{{ guest.firstName }} {{
                            guest.lastName
                            }}
                            ({{ guest.Age }})</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <hr class="separator-line" *ngIf="preBookingResponse.bookingDetails?.additionalAmount > 0" />
                <div class="booking-date-status room-main-guest"
                  *ngIf="preBookingResponse.bookingDetails?.additionalAmount > 0">
                  Payment
                  Details
                  <div class="date-status-item"
                    *ngIf="preBookingRoomDetails && preBookingRoomDetails?.additionalAmount > 0">
                    <strong *ngIf="bookingType === 'MEMBERSHIP'">Additional Amount:</strong>
                    <strong *ngIf="bookingType != 'MEMBERSHIP'">Amount:</strong>
                    <span class="booking-date">{{preBookingResponse.bookingDetails.additionalAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </div>
                  <div *ngIf="preBookingResponse.bookingDetails.gstAmount > 0" class="date-status-item">
                    <strong>GST Amount ({{ preBookingResponse.bookingDetails.gstRate }}%):</strong>
                    <span class="booking-date">{{preBookingResponse.bookingDetails.gstAmount | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </div>
                  <div *ngIf="preBookingResponse.bookingDetails.serviceCharge > 0" class="date-status-item">
                    <strong>Service Charge:</strong>
                    <span class="booking-date">{{preBookingResponse.bookingDetails.serviceCharge | currency
                      :'INR':'symbol':'1.0-0'}}</span>
                  </div>

                  <div *ngIf="totalConvenienceFee > 0" class="date-status-item">
                    <strong>Convenience fee (incl. GST):</strong>
                    <span class="booking-date">
                      {{ totalConvenienceFee | currency :'INR':'symbol':'1.0-2' }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="privacy-container booking-pay-now margin-top-10"
                *ngIf="preBookingInfo && preBookingInfo?.totalAdditionalAmount > 0">
                <div class="booking-text">
                  <span>Total Payable Amount:</span>
                  <strong>{{totalPayableAmount | currency
                    :'INR':'symbol':'1.0-2'}}/-</strong>
                </div>

              </div>

              <!-- <div class="my-wallet-container margin-top-10">
                <div class="compaign-list-container">
                  <div class="campaign-slider-container" *ngIf="campaignBookingCashbackResponse?.length > 0">
                    <swiper [config]="config">
                      <ng-template swiperSlide
                        *ngFor="let campaignBookingCashback of campaignBookingCashbackResponse; let i = index">
                        <div class="my-wallet-card gift-voucher-container margin-top-5 booking-card-height"
                          [ngClass]="{'selected-slide': selectedSlideIndex === i}"
                          (click)="seeCompaignDetail(campaignBookingCashback.id,i)">
                          <div class="gift-voucher-bal-container">
                            <div class="balance-container">
                              <div class="balance-text-container margin-top-10">
                                <span class="balance-text voucher-text">{{campaignBookingCashback?.title}}</span>
                                <span
                                  class="balance-amount voucher-desc">{{campaignBookingCashback?.description}}</span>
                              </div>
                              <div class="balance-icon-container">
                                <ion-icon class="balance-icon" src="/assets/images/svg/gift-coupon.svg"
                                  slot="start"></ion-icon>
                              </div>
                            </div>
                          </div>
                        </div>
                      </ng-template>
                    </swiper>
                    <div class="swiper-pagination"></div>
                  </div>
                </div>
              </div> -->

              <div class="coupon-banner selected-coupon-container margin-top-20"
                *ngIf="coupon && selectedBookingCampaignId">
                <div class="coupon-banner-container">
                  <ion-icon src="/assets/images/svg/check-icon.svg" class="coupon-icon coupon-svg-icon"></ion-icon>
                  <div class="coupon-text">
                    <span class="unlock-coupon-text">
                      {{coupon?.title}}
                    </span>
                    <!-- <span class="explore-now-text coupon-rewards-text">{{coupon?.rewards}}</span> -->
                    <span class="applied-text" *ngIf="selectedBookingCampaignId">Applied</span>
                  </div>
                  <div class="remove-coupon-container" (click)="removeSelectedCoupon(coupon.id)">
                    <span class="remove-text">Remove</span>
                  </div>
                </div>
                <hr class="separator-line" />
                <div class="view-all-coupons-container" (click)="goToCoupons()">
                  <span class="view-all-text">View all coupons</span>
                  <ion-icon src="/assets/images/svg/right-arrow-icon.svg" class="right-arrow-icon"></ion-icon>
                </div>
              </div>

              <div class="coupon-banner margin-top-20"
                *ngIf="(campaignId === null || campaignId === 'null') && bookingType==='OTHERS' && !selectedBookingCampaignId && campaignBookingCashbackResponse?.length"
                (click)="goToCoupons()">
                <ion-icon src="/assets/images/svg/coupon-icon.svg" class="coupon-icon coupon-svg-icon"></ion-icon>
                <div class="coupon-text">
                  <span class="unlock-coupon-text">
                    You have unlocked <strong class="new-coupons">{{campaignBookingCashbackResponse?.length}} new
                      coupons</strong>
                  </span>
                  <span class="explore-now-text">Explore Now</span>
                </div>
                <ion-icon src="/assets/images/svg/right-arrow-icon.svg" class="right-arrow-icon"></ion-icon>
              </div>

              <div class="reward-card-container margin-bottom-10 margin-top-10">
                <div class="reward-card" *ngIf="(campaignId === null || campaignId === 'null') 
                  && bookingType === 'OTHERS' 
                  && !selectedBookingCampaignId 
                  && availableCashDetails?.bookingCashbackPerc 
                  && availableCashDetails?.bookingCashbackPerc > 0">
                  <img src="/assets/images/icons/tick-icon.png" class="reward-icon">
                  <div class="reward-text-container">
                    <span class="reward-text">Book now and get
                      {{availableCashDetails?.bookingCashbackPerc}}% Promo Cash (up to
                      ₹{{availableCashDetails?.bookingCashbackAmount}}).</span>
                  </div>
                </div>
              </div>

              <div class="privacy-container booking-pay-now margin-top-10"
                *ngIf="preBookingInfo && preBookingInfo?.totalAdditionalAmount > 0">
                <ion-button class="primary-button text-capitalize margin-top-5" expand="full" shape="round"
                  type="submit" [disabled]="isPaymentProcessing">
                  Confirm Booking
                </ion-button>
                <span class="non-refundable-text margin-top-5"><b>Note:</b> GST and Convenience Fees are non-refundable
                  in case of booking cancellation.
                </span>
              </div>

              <div class="privacy-container margin-top-10"
                *ngIf="preBookingInfo && preBookingInfo?.totalAdditionalAmount <= 0">
                <ion-button class="primary-button text-capitalize" expand="full" shape="round" type="submit">
                  Confirm Booking
                </ion-button>
              </div>

            </div>
          </div>
        </form>
      </ion-content>
    </div>
  </div>
</ion-content>