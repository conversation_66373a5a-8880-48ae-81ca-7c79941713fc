<ion-content class="customer-dashboard-page customer-body-section booking-hotel-detail-page">
  <app-customer-header [innerPage]="true" [headingText]="selectedHotel?.name || 'Hotel Detail'"
    [backUrl]="backUrlWithParams"></app-customer-header>
  <div class="customer-body-section has-header-section-change-profile no-padding">
    <div class="payment-body-section less-height-hotel-detail no-padding">
      <ion-content class="payment-body-ion-content scroll">
        <div *ngFor="let hotel of hotels">
          <div class="hotel-card-container">
            <div class="hotel-card">
              <div class="hotel-card-header">

                <div class="hotel-card-header">
                  <!-- Check if imageObject has more than 0 elements -->
                  <ng-container *ngIf="hotelImages && hotelImages.length > 0; else singleImage">
                    <swiper [config]="config" (slideChange)="onSlideChanged($event)" #slides>
                      <ng-template *ngFor="let item of hotelImages" swiperSlide>
                        <div class="hotel-image-slide">
                          <img [src]="item.image" alt="{{ hotel.alt }}">
                        </div>
                      </ng-template>
                      <div class="swiper-pagination"></div>
                    </swiper>
                  </ng-container>


                  <ng-template #singleImage>
                    <div class="hotel-image-slide">
                      <img [src]="hotel.thumbImages" alt="{{ hotel.name }}">
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>

          </div>

          <div class="hotel-rooms-outer">
            <div class="hotel-detail-card-container">
              <div class="hotel-detail-card">
                <ion-grid class="">
                  <ion-row>
                    <ion-col size="10">
                      <div class="">
                        <div class="hotel-name">{{ hotel.name }}</div>
                        <div class="hotel-address">{{ hotelDetail?.hotelAddress }} <a class="locate-link"
                            *ngIf="hotelDetail?.longitude && hotelDetail?.longitude"
                            (click)="commonService.openGoogleMaps(hotelDetail?.longitude,hotelDetail?.latitude)">
                            Locate Now
                          </a>
                          <div class="hotel-desc" *ngIf="hotelDetail?.phone && hotelDetail?.phone.length===12">
                            <div class="call-locate-now">
                              <img src="/assets/images/icons/icons8-phone-40.png" class="max-width-icon">

                              <a class="margin-top-5" [href]="'tel:' + hotelDetail?.phone">{{hotelDetail?.phone}}
                              </a>
                            </div>
                          </div>
                        </div>

                      </div>
                      <div>

                      </div>
                    </ion-col>
                    <ion-col size="2">
                      <div class="star-rating-outer">
                        <div class="star-rating">
                          <div class="star-rating-content">
                            <ion-icon src="/assets/images/svg/star-fill.svg" class="star-icon"></ion-icon>
                            <div class="rating-text">{{hotel.rating}}</div>
                          </div>
                        </div>
                      </div>
                    </ion-col>
                  </ion-row>

                  <ion-row>
                    <ion-col>
                      <div class="hotel-desc">
                        <div [innerHTML]="hotelDetail?.desc"></div>
                      </div>
                    </ion-col>
                  </ion-row>

                  <ion-row *ngIf="hotelAmenitiesList.length > 0">
                    <!-- First Column -->
                    <ion-col>
                      <ion-row>
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="hotel-amenities">Hotel Amenities</div>
                        </ion-col>
                      </ion-row>
                      <ion-row>
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="amenities">
                            <span *ngFor="let amenity of displayedHotelAmenities">{{ amenity }}</span>
                            <span *ngIf="remainingHotelAmenities.length > 0" class="show-more"
                              (click)="openHotelDetailsPopup()">
                              Show More
                            </span>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>
                  </ion-row>
                  <!-- <div class="call-locate-now">
                  <ion-row>
                    <ion-col size="12">
                      <div class="hotel-desc">
                       
                        <a [href]="'tel:' + hotelDetail?.phone">
                        <img src="/assets/images/icons/phone-number.png" class="max-width-icon">
                      </a>
                      </div>
                    </ion-col>
                  </ion-row>
                </div> -->
                </ion-grid>
              </div>
            </div>

            <div class="hotel-detail-card-container" *ngFor="let roomDetails of hotel.roomDetails">
              <div class="hotel-detail-card">
                <ion-grid>
                  <ion-row>
                    <ion-col>
                      <div>
                        <div class="hotel-name">{{ roomDetails.type }}</div>
                        <div class="hotel-address">{{ roomDetails.roomDescription }}</div>
                      </div>
                    </ion-col>
                  </ion-row>

                  <ion-row *ngIf="roomAmenitiesList.length > 0">
                    <ion-col>
                      <ion-row>
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="hotel-amenities">Room Amenities</div>
                        </ion-col>
                      </ion-row>
                      <ion-row>
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="amenities">
                            <span *ngFor="let amenity of displayedRoomAmenities">{{ amenity }}</span>
                            <span *ngIf="remainingRoomAmenities.length > 0" class="show-more"
                              (click)="openRoomDetailsPopup()">
                              Show More
                            </span>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>
                  </ion-row>
                </ion-grid>

                <ion-row class="ion-no-margin ion-no-padding">
                  <ion-col class="discount-price-flex">
                    <ion-row>
                      <ion-col class="ion-no-margin ion-no-padding">
                        <div class="discounted-price" *ngIf="roomDetails.additionalAmount > 0">
                          <span class="discounted-heading" *ngIf="bookingType === 'MEMBERSHIP'">Additional
                            Amount:</span>
                          <span class="discounted-heading" *ngIf="bookingType != 'MEMBERSHIP'">Amount:</span>
                          {{ roomDetails.additionalAmount | currency :'INR':'symbol':'1.0-0'}}/-
                        </div>

                        <!-- <div class="discounted-price" *ngIf="roomDetails.additionalAmount <= 0">
                          <span class="free-txt-bg">
                            Zero
                          </span>
                        </div> -->

                        <ion-button class="free-text-btn" expand="full" shape="round" type="submit"
                          *ngIf="roomDetails.additionalAmount <= 0">
                          <div class="free-text-btn-container">
                            <!-- <img src="/assets/images/svg/grab-deal.svg" alt="Pay Now" width="18px" /> -->
                            <span class="free-text">₹ 0</span>
                          </div>
                        </ion-button>

                      </ion-col>
                    </ion-row>
                  </ion-col>
                  <!-- <ion-col *ngIf="isRoomSelected(roomDetails)">
                <div class="add-new-button-container"><ion-icon name="alert-circle-outline"
                    (click)="fetchCancellationPolicy(selectedRoom)"></ion-icon></div>
              </ion-col> -->
                  <ion-col>
                    <div class="button-container">
                      <!-- Display "Select" button if not selected -->
                      <ion-button *ngIf="!isRoomSelected(roomDetails)" expand="block" class="custom-button"
                        (click)="selectRoom(roomDetails)">
                        Select
                      </ion-button>

                      <!-- Display "Selected" button if selected -->
                      <ion-button *ngIf="isRoomSelected(roomDetails)" expand="block" class="custom-button-outlined">
                        <ion-icon src="/assets/images/svg/check.svg" slot="start" class="star-icon">
                        </ion-icon>
                        Selected
                      </ion-button>
                    </div>
                  </ion-col>
                </ion-row>

                <div class="card-bottom-cancellation-policy margin-top-20" *ngIf="isRoomSelected(roomDetails)"
                  (click)="fetchCancellationPolicy(selectedRoom)">
                  <div class="padding-top-5 padding-bottom-3 package-design">Cancellation Policy</div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </ion-content>
    </div>
  </div>
  <div class="overlay-discount-body" *ngIf="selectedRoom != null">
    <ion-row class="ion-no-margin ion-no-padding">
      <ion-col class="discount-price-flex">
        <ion-row>
          <ion-col class="ion-no-margin ion-no-padding">
            <div class="discounted-price" *ngIf="selectedRoom.additionalAmount > 0">
              <span class="discounted-heading" *ngIf="bookingType === 'MEMBERSHIP'">Additional Amount:</span>
              <span class="discounted-heading" *ngIf="bookingType != 'MEMBERSHIP'">Amount:</span>
              {{ selectedRoom.additionalAmount | currency :'INR':'symbol':'1.0-0'}}/-
            </div>
            <div *ngIf="selectedRoom.additionalAmount <= 0" class="discounted-price">
              <span class="free-txt-bg">
                ₹ 0
              </span>
            </div>
          </ion-col>
        </ion-row>
      </ion-col>

      <ion-col>
        <div class="button-container">
          <ion-button expand="block" class="custom-button" [disabled]="isProcessing" (click)="preBooking(selectedRoom)">
            Continue
          </ion-button>
        </div>
      </ion-col>
    </ion-row>
  </div>
</ion-content>


<ion-modal class="site-custom-popup job-invitation-popup customer-dashboard-page booking-hotel-detail-page"
  #isHotelDetailsPopup [isOpen]="isHotelDetailsOpen" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <!-- Header -->
      <div class="site-custom-popup-header less-padding">
        <i-feather name="X" (click)="closeHotelDetailsPopup()"></i-feather>
        <div class="header-text">Hotel Amenities</div>
      </div>
      <!-- Body -->
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form " #recordForm="ngForm" novalidate>
          <div class="hotel-rooms-outer no-margin">
            <div class="hotel-detail-card-container">
              <div class="hotel-detail-card hotel-detail-card-ion-modal margin-top-10">
                <div class="amenities">
                  <span *ngFor="let amenity of remainingHotelAmenities">{{ amenity }}</span>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>


<ion-modal class="site-custom-popup job-invitation-popup customer-dashboard-page booking-hotel-detail-page"
  #isRoomDetailsPopup [isOpen]="isRoomDetailsOpen" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <!-- Header -->
      <div class="site-custom-popup-header less-padding">
        <i-feather name="X" (click)="closeRoomDetailsPopup()"></i-feather>
        <div class="header-text">Room Amenities</div>
      </div>
      <!-- Body -->
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form " #recordForm="ngForm" novalidate>
          <div class="hotel-rooms-outer no-margin">
            <div class="hotel-detail-card-container">
              <div class="hotel-detail-card hotel-detail-card-ion-modal margin-top-10">
                <div class="amenities">
                  <span *ngFor="let amenity of remainingRoomAmenities">{{ amenity }}</span>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #cancelPolicyModal [isOpen]="isCancelPolicyPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeCancelPolicyPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading"> Cancellation Policy</div>
          <p class="popup-normal-heading">Here is the information regarding the Cancellation Policy.</p>
          <div class="popup-normal-heading margin-top-5 secondary-text">{{cancellationInfo}}</div>
          <ul class="bullet-text  popup-normal-heading">
            <!-- <li>Charge -<b>{{cancelInformation.chargeAmount}}{{ cancelInformation.chargeType === 'Percentage' ? '%' : '' }}</b> Applicable if Cancelled After {{cancelInformation.endDate}}</li> -->
            @for (cancelInformation of cancellationInformations; track $index) {
            <li>
              @if (cancelInformation.chargeAmount === "0") {
              <p>
                No Charges Applicable If Cancelled Before<b> {{cancelInformation.endDate |
                  date:'dd/MM/yyyy, h:mm a'}}</b> IST.
              </p>

              } @else if (cancelInformation.chargeType === "Percentage") {
              <p>
                Charge - <b>{{cancelInformation.chargeAmount}}% </b>Applicable If Cancelled After
                <b>{{cancelInformation.startDate
                  | date:'dd/MM/yyyy, h:mm a'}}</b> IST.
              </p>
              } @else if (cancelInformation.chargeType === "Amount") {
              <p>
                Charges - <b>{{cancelInformation.chargeAmount | currency
                  :'INR':'symbol':'1.0-0'}}</b>
                Applicable If Cancelled After <b>{{cancelInformation.startDate | date:'dd/MM/yyyy, h:mm a'}}</b> IST
              </p>
              }
            </li>
            }
          </ul>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>