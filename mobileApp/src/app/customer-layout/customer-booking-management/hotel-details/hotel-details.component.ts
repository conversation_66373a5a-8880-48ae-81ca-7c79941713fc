import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import SwiperCore, { Pagination, SwiperOptions } from 'swiper';
import { SwiperComponent } from 'swiper/angular';
import { ActivatedRoute } from '@angular/router';
import { CommonService } from 'src/services/common.service';
import { AuthService } from 'src/shared/authservice';

SwiperCore.use([Pagination]);
@Component({
  selector: 'app-hotel-details',
  templateUrl: './hotel-details.component.html',
  styleUrls: ['./hotel-details.component.scss'],
})
export class HotelDetailsComponent implements OnInit {

  hotels: any[] = []; // Initialize as an empty array
  hotelDetailsString: any;
  hotelDetails: any;
  selectedHotel: any;
  bookingType: string | null = null;
  campaignId: string | null = null;
  swiper?: SwiperComponent;
  currentIndex: number = 0;
  config: SwiperOptions = {
    slidesPerView: 1,
    pagination: {
      clickable: true,
      enabled: true,
      type: "bullets",
      //bulletElement: ".swiper-pagination"
    },
    zoom: true
  };
  isProcessing: boolean = false;
  selectedRoom: any; // This will store the selected room details
  loading: string = "NOT_STARTED";
  hotelDetail: any;
  hotelAmenitiesList: string[] = [];
  displayedHotelAmenities: string[] = [];
  remainingHotelAmenities: string[] = [];
  roomAmenitiesList: string[] = [];
  displayedRoomAmenities: string[] = [];
  remainingRoomAmenities: string[] = [];
  isHotelDetailsOpen: boolean = false;
  isRoomDetailsOpen: boolean = false;
  hotelImages: any;
  imageObject: any;
  cancellationInformations: Array<any> = new Array<any>();
  cancellationInfo: any;
  isCancelPolicyPopupOpen: boolean = false;
  backUrlWithParams: string = '';
  user: any;

  constructor(private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    public readonly commonService: CommonService,
    private readonly authService: AuthService,
    private readonly changeDetectorRef: ChangeDetectorRef,
  ) {
  }

  ngOnInit() {
    this.hotelDetailsString = this.localStorageService.getObject("hotelDetails");
    this.user = this.authService.getUser();
    if (this.hotelDetailsString) {
      this.hotelDetails = JSON.parse(this.hotelDetailsString);
      if (this.hotelDetails) {
        if (Array.isArray(this.hotelDetails)) {
          this.hotels = this.hotelDetails;
        } else if (this.hotelDetails.hotel) {
          this.hotels = this.hotelDetails.hotel;
          this.bookingType = this.hotelDetails.bookingType;
        } else {
          this.hotels = [this.hotelDetails];
        }
      } else {
        this.hotels = [];
      }
    }

  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();
    this.selectedRoom = null;
    this.route.queryParams.subscribe(params => {
      this.bookingType = params['bookingType'] || "OTHERS"
      this.campaignId = params['campaignId'] || null;
    });

    this.backUrlWithParams = `/portal/available/hotels?bookingType=${this.bookingType}&campaignId=${this.campaignId}`;

    this.hotelDetailsString = this.localStorageService.getObject("hotelDetails");

    if (this.hotelDetailsString) {
      this.hotelDetails = JSON.parse(this.hotelDetailsString);
      if (this.hotelDetails) {
        if (Array.isArray(this.hotelDetails)) {
          this.hotels = this.hotelDetails;
          this.selectedHotel = this.hotelDetails.find(h => h.hotelCode);
        } else if (this.hotelDetails.hotel) {
          this.hotels = this.hotelDetails.hotel;
          this.bookingType = this.hotelDetails.bookingType;
          this.selectedHotel = this.hotelDetails.hotel.find((h: { hotelCode: any; }) => h.hotelCode);
        } else {
          this.hotels = [this.hotelDetails];
          this.selectedHotel = this.hotelDetails;
        }
      } else {
        this.hotels = [];
      }
    }

    this.fetchHotelDetailById(this.hotelDetails.hotelCode || this.hotelDetails.hotel[0]?.hotelCode);
  }

  back() {
    this.navController.navigateBack("/portal/search/hotels", { animated: true });
  }

  preBooking(room: any) {
    const payload = {
      searchSessionId: this.hotelDetails.searchSessionId,
      bookingType: this.bookingType,
      arrivalDate: this.hotelDetails.arrivalDate,
      departureDate: this.hotelDetails.departureDate,
      currency: this.hotelDetails.currency,
      guestNationality: this.hotelDetails.guestNationality,
      countryCode: this.hotelDetails.countryCode,
      city: this.hotelDetails.city,
      hotelId: this.hotelDetails.hotelCode || this.hotelDetails.hotel[0].hotelCode,
      name: this.hotelDetails.name || this.hotelDetails.hotel[0].name,
      roomDetails: [
        {
          type: room.type,
          bookingKey: room.bookingKey,
          adults: room.adults,
          children: room.children,
          ChildrenAges: room.childAges,
          totalRooms: room.totalRooms,
          totalRate: room.totalRate,
          additionalNights: room.additionalNights || 0,
          additionalAmount: room.additionalAmount || 0
        }
      ],
      hotelDetailResponse: JSON.stringify(this.hotelDetail)
    };

    //  this.loadingService.show();
    if (this.user) {
      this.isProcessing = true;
      this.dataService.preBooking(payload).subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          const data = response.data;
          this.handlePreBookingResponse(data, room);
        },
        error: (error) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message || 'An error occurred');
        }
      });
    }
    else {
      this.commonService.openLoginModal();
      return;
    }
  }

  handlePreBookingResponse(data: any, room: any) {
    this.localStorageService.setObject("preBookingResponse", data);
    this.localStorageService.setObject("roomDetails", JSON.stringify(room));
    this.navController.navigateForward("/portal/add/guest/details", {
      animated: true,
      queryParams: {
        bookingType: this.bookingType,
        campaignId: this.campaignId
      }
    });
  }

  getRoundedTotalRates(totalRate: number, decimalPlaces: number = 2): string {
    if (totalRate === undefined || totalRate === null) {
      return ''; // Return an empty string if totalRate is undefined or null
    }

    return parseFloat(totalRate.toFixed(decimalPlaces)).toString(); // Round and convert to string
  }

  getUniqueRoomDescriptions(roomDetails: any[]): string[] {
    const uniqueDescriptions = new Set<string>();

    roomDetails.forEach(room => {
      if (room.roomDescription) {
        // Check for both comma and pipe delimiters, trim and add each unique item
        const descriptions = room.roomDescription.split(/[,|]/);
        descriptions.forEach((desc: string) => uniqueDescriptions.add(desc.trim()));
      }
    });

    return Array.from(uniqueDescriptions);
  }

  getUniqueRoomDescriptionType(roomType: string): string {
    const descriptions = roomType.split('|');
    const uniqueDescriptions = new Set(descriptions.map(desc => desc.trim()));
    return Array.from(uniqueDescriptions).join(', '); // Join unique descriptions with a comma
  }

  selectRoom(roomDetails: any) {
    this.localStorageService.setObject("roomDetails", JSON.stringify(roomDetails));

    this.selectedRoom = roomDetails; // Update the selected room
  }

  isRoomSelected(roomDetails: any): boolean {
    return this.selectedRoom === roomDetails; // Check if the room is selected
  }

  fetchHotelDetailById(hotelCode: string) {
    const payload = {
      hotelCode: hotelCode,
    };
    this.loading = "LOADING";
    this.dataService.fetchHotelDetailById(payload)
      .subscribe({
        next: (response: RestResponse) => {
          this.loading = "LOADED";
          this.hotelDetail = response.data;
          // this.hotelDetail.hotelImages
          let hotelDetails = JSON.parse(this.localStorageService.getObject('hotelDetails'));
          this.hotelImages = hotelDetails?.hotelImages ?? [];
          // this.filterImages();
          this.hotelAmenitiesList = this.hotelDetail.hotelAmenities.split(',')
            .map((item: string) => item.trim())
            .filter((item: string | any[]) => item.length > 0);
          this.displayedHotelAmenities = this.hotelAmenitiesList.slice(0, 5);
          //  this.remainingHotelAmenities = this.hotelAmenitiesList.slice(10);
          this.remainingHotelAmenities = this.hotelAmenitiesList.slice();

          this.roomAmenitiesList = this.hotelDetail.roomAmenities.split(',')
            .map((item: string) => item.trim())
            .filter((item: string | any[]) => item.length > 0);
          this.displayedRoomAmenities = this.roomAmenitiesList.slice(0, 3);
          this.remainingRoomAmenities = this.roomAmenitiesList.slice();
        },
        error: (error) => {
          this.loading = "LOADED";
          this.toastService.show(error.message);
        }
      });
  }

  filterImages() {
    this.imageObject = this.hotelImages.map((item: any, index: any) =>
    ({
      image: item,
      thumbImage: item,
      alt: `Hotel Room with code ${item.hotelCode}`,
      // title: `Hotel Room ${item.hotelCode}`,
      index: index + 1,
    })
    );
    if (this.hotelDetail.thumbImages === "http://test.xmlhub.com/images/noimage.gif") {
      this.hotelDetail.thumbImages = "/assets/images/svg/hotel.svg";
    }
  }

  openHotelDetailsPopup() {
    this.isHotelDetailsOpen = true;  // Open the modal
  }

  closeHotelDetailsPopup() {
    this.isHotelDetailsOpen = false;
  }

  openRoomDetailsPopup() {
    this.isRoomDetailsOpen = true;  // Open the modal
  }

  closeRoomDetailsPopup() {
    this.isRoomDetailsOpen = false;
  }

  fetchCancellationPolicy(room: any) {
    const payload = {
      searchSessionId: this.hotelDetails.searchSessionId,
      bookingType: this.bookingType,
      arrivalDate: this.hotelDetails.arrivalDate,
      departureDate: this.hotelDetails.departureDate,
      currency: this.hotelDetails.currency,
      guestNationality: this.hotelDetails.guestNationality,
      countryCode: this.hotelDetails.countryCode,
      city: this.hotelDetails.city,
      hotelId: this.hotelDetails.hotelCode || this.hotelDetails.hotel[0].hotelCode,
      name: this.hotelDetails.name || this.hotelDetails.hotel[0].name,
      roomDetails: [
        {
          type: room.type,
          bookingKey: room.bookingKey,
          adults: room.adults,
          children: room.children,
          ChildrenAges: room.childAges,
          totalRooms: room.totalRooms,
          totalRate: room.totalRate,
          additionalNights: room.additionalNights || 0,
          additionalAmount: room.additionalAmount || 0
        }
      ]
    };

    this.dataService.hotelCancellationPolicy(payload)
      .subscribe({
        next: (response: RestResponse) => {
          this.cancellationInformations = response.data.cancellationInformation;;
          this.cancellationInfo = response.data.info;
          this.openCancelPolicyPopup();
        },
        error: (error: any) => {
          this.loading = "LOADED";
          this.toastService.show(error.message || 'An error occurred');
        }
      });
  }

  openCancelPolicyPopup() {
    this.isCancelPolicyPopupOpen = true;
  }

  closeCancelPolicyPopup() {
    this.isCancelPolicyPopupOpen = false;
  }

  onSlideChanged($event: any): void {
    if (!this.swiper) {
      return;
    }
    this.currentIndex = this.swiper.swiperRef.activeIndex;
    this.changeDetectorRef.detectChanges();
  }

}
