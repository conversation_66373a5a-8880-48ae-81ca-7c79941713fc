import { ChangeDetectorRef, Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import SwiperCore, { Pagination, SwiperOptions } from 'swiper';
import { SwiperComponent } from 'swiper/angular';
SwiperCore.use([Pagination]);

@Component({
  selector: 'app-hotels-listing',
  templateUrl: './hotels-listing.component.html',
  styleUrls: ['./hotels-listing.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HotelsListingComponent implements OnInit {
  @ViewChild('slides', { static: false })
  swiper?: SwiperComponent;
  input: any;
  loading: string = "NOT_STARTED";
  searchResponse: any;
  availableHotels: Array<any> = new Array<any>();
  currentIndex: number = 0;
  config: SwiperOptions = {
    slidesPerView: 1,
    pagination: {
      clickable: true,
      enabled: true,
      type: "bullets",
      //bulletElement: ".swiper-pagination"
    },
    zoom: true
  };
  isNoPackageAvailablePopupOpen: boolean = false;
  bookingType: string | null = null;
  campaignId: string | null = null;
  confirmationModalMessage: string = "";
  isBookingCancelModalOpen: boolean = false;
  additionalNights: number = 0;
  backUrlWithParams: string = '';
  isProcessing: boolean = false;

  searchQuery: string = '';  // Variable to hold the search query
  filteredHotels: any[] = [];  // Array to hold filtered leads

  constructor(
    private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly cdr: ChangeDetectorRef,
    private readonly localStorageService: LocalStorageService,
    private readonly changeDetectorRef: ChangeDetectorRef,
    private readonly route: ActivatedRoute,
    public readonly commonService: CommonService
  ) {
  }

  ngOnInit() {
    this.availableHotels = new Array<any>();
    this.searchQuery = '';
    this.input = this.localStorageService.getObject("search-input");
    if (!this.input) {
      return;
    }

  }

  ionViewWillEnter() {
    this.init();
  }

  init() {
    this.route.queryParams.subscribe(params => {
      this.bookingType = params['bookingType'] || "OTHERS"
      this.input.bookingType = this.bookingType;
      this.campaignId = params['campaignId'] || null;
    });

    this.backUrlWithParams = `/portal/search/hotels?bookingType=${this.bookingType}&campaignId=${this.campaignId}`;

    this.availableHotels = new Array<any>();
    this.searchQuery = '';
    this.input = this.localStorageService.getObject("search-input");
    if (!this.input) {
      return;
    }

    this.localStorageService.remove('HOTEL_PREFERENCES');
    this.localStorageService.remove('HOTEL_BOARD_BASIS');

    this.fetchAvailableHotels();
  }

  fetchAvailableHotels() {
    const payload = {
      arrivalDate: this.formattedDate(this.input.arrivalDate),
      departureDate: this.formattedDate(this.input.departureDate),
      city: this.input.location.cityCode,
      guestNationality: 'IN',
      bookingType: this.bookingType,
      rooms: this.input.rooms,
      hotelRatings: this.input.hotelRatings,
      minPrice: this.input.minPrice,
      maxPrice: this.input.maxPrice,
      preferences: this.input.preferences || null,
      boardBasis: this.input.boardBasis || null
    };
    this.loading = "LOADING";
    this.dataService.searchHotelsByDestination(payload)
      .subscribe({
        next: (response: RestResponse) => {
          this.loading = "LOADED";
          this.searchResponse = response.data;
          this.availableHotels = response.data.hotels;
          this.filteredHotels = [...this.availableHotels];
          this.replaceDefaultImage();
        },
        error: (error) => {
          this.loading = "LOADED";
          this.toastService.show(error.message);
        }
      });
  }

  searchHotel() {
    this.filterHotels();
  }

  filterHotels() {
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      this.filteredHotels = this.availableHotels.filter(hotel =>
        hotel.name?.toLowerCase().includes(query)
      );
    } else {
      this.filteredHotels = [...this.availableHotels];
    }
  }

  replaceDefaultImage() {
    this.availableHotels?.forEach(hotel => {
      if (hotel.thumbImages === "http://test.xmlhub.com/images/noimage.gif") {
        hotel.thumbImages = "/assets/images/svg/hotel.svg";
      }
    });
  }

  formattedDate(selectedDate: Date) {
    selectedDate = new Date(selectedDate);
    return `${selectedDate.getDate().toString().padStart(2, '0')}/${(selectedDate.getMonth() + 1).toString().padStart(2, '0')}/${selectedDate.getFullYear()}`;
  }

  getUniqueRoomDescriptions(roomDetails: any[]): string[] {
    const uniqueDescriptions = new Set<string>();
    roomDetails.forEach(room => {
      if (room.roomDescription) {
        uniqueDescriptions.add(room.roomDescription);
      }
    });
    return Array.from(uniqueDescriptions);
  }

  hotelDetail(selectedHotel: any) {
    // Prepare to pass more details including searchSessionId
    const hotelDetails = {
      searchSessionId: this.searchResponse.searchSessionId,
      arrivalDate: this.searchResponse.arrivalDate,
      departureDate: this.searchResponse.departureDate,
      currency: this.searchResponse.currency,
      guestNationality: this.searchResponse.guestNationality,
      countryCode: this.input.location.countryCode,
      city: this.input.location.cityCode,
      ...selectedHotel
    };
    this.localStorageService.setObject("hotelDetails", JSON.stringify(hotelDetails));
    this.navController.navigateForward(`/portal/hotel/${selectedHotel.hotelCode}/details`, {
      animated: true,
      queryParams: {
        bookingType: this.bookingType,
        campaignId: this.campaignId
      }
    });
  }

  onFilterClick() {
    if (this.loading == "LOADING") {
      return;
    }
    this.commonService.isNoPackageAvailablePopupOpen = true;

  }

  onSlideChanged($event: any): void {
    if (!this.swiper) {
      return;
    }
    this.currentIndex = this.swiper.swiperRef.activeIndex;
    this.changeDetectorRef.detectChanges();
  }

  closeNoPackageAvailablePopup() {
    this.commonService.isNoPackageAvailablePopupOpen = false;
  }


  search(input: any) {
    if (!input.location) {
      this.toastService.show("Location is required");
      return;
    }

    this.input = input;
    this.localStorageService.setObject("search-input", this.input);
    if (this.bookingType !== "MEMBERSHIP") {
      this.fetchAvailableHotels();
      this.commonService.isNoPackageAvailablePopupOpen = false;
      return;
    }
    this.validateBookingAllowed(input);
  }

  validateBookingAllowed(input: any) {
    const payload = {
      totalRooms: input.noOfRooms,
      totalNights: input.totalNights,
      cityId: input.location.cityCode,
      bookingType: this.bookingType,
      fromDate: this.formattedDate(input.arrivalDate),
      toDate: this.formattedDate(input.departureDate),
    };
    //  this.loadingService.show();
    this.isProcessing = true;

    this.dataService.bookingAllowed(payload).subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        this.isProcessing = false;

        const data = response.data;
        this.confirmationModalMessage = data.message;
        if (!data.status) {
          this.openCancelConfirmationModal();
          this.additionalNights = data.additionalNights;
        } else {
          this.commonService.isNoPackageAvailablePopupOpen = false;
          this.fetchAvailableHotels();
        }
      },
      error: (error) => {
        //  this.loadingService.hide();
        this.isProcessing = false;
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  openCancelConfirmationModal() {
    this.isBookingCancelModalOpen = true;
  }

  close() {
    this.isBookingCancelModalOpen = false;
  }

  proceedWithExtraCharges() {
    this.isBookingCancelModalOpen = false;
    if (this.additionalNights > 0) {
      this.bookingType = "MEMBERSHIP";
    } else {
      this.bookingType = "OTHERS";
    }
    this.commonService.isNoPackageAvailablePopupOpen = false;
    this.fetchAvailableHotels();
  }
}
