<ion-content class="customer-dashboard-page booking-hotel-listing-page">
  <app-customer-header [innerPage]="true" [headingText]="'Hotels'" [rightAction]="true" [rightActionIcon]="'Filter'"
    (rightActionCallback)="onFilterClick()" [backUrl]="backUrlWithParams"></app-customer-header>
  <div class="customer-body-section has-header-section no-bottom-tab">
    @if (loading==='LOADING') {
    <div class="loading-card-container">
      <div class="hotel-card">
        <div class="hotel-card-header">
          <swiper [config]="config" (slideChange)="onSlideChanged($event)" #slides>
            <ng-template swiperSlide>
              <div class="hotel-image-slide">
                <ion-skeleton-text [animated]="true" style="height: 200px"></ion-skeleton-text>
              </div>
            </ng-template>
            <div class="swiper-pagination"></div>
          </swiper>
        </div>
        <div class="hotel-card-body margin-left-15">
          <div class="hotel-name"><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></div>
          <div class="hotel-address"><ion-skeleton-text [animated]="true" style="width: 60%"></ion-skeleton-text></div>
          <div class="hotel-hot-amenities">
            <span class="amenities-item amenities-1"><ion-skeleton-text [animated]="true"
                style="width: 80px"></ion-skeleton-text></span>
            <span class="amenities-item amenities-2"><ion-skeleton-text [animated]="true"
                style="width: 50px"></ion-skeleton-text></span>
          </div>
        </div>
      </div>
    </div>
    <div class="loading-card-container">
      <div class="hotel-card">
        <div class="hotel-card-header">
          <swiper [config]="config" (slideChange)="onSlideChanged($event)" #slides>
            <ng-template swiperSlide>
              <div class="hotel-image-slide">
                <ion-skeleton-text [animated]="true" style="height: 200px"></ion-skeleton-text>
              </div>
            </ng-template>
            <div class="swiper-pagination"></div>
          </swiper>
        </div>
        <div class="hotel-card-body margin-left-15">
          <div class="hotel-name"><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></div>
          <div class="hotel-address"><ion-skeleton-text [animated]="true" style="width: 60%"></ion-skeleton-text></div>
          <div class="hotel-hot-amenities">
            <span class="amenities-item amenities-1"><ion-skeleton-text [animated]="true"
                style="width: 80px"></ion-skeleton-text></span>
            <span class="amenities-item amenities-2"><ion-skeleton-text [animated]="true"
                style="width: 50px"></ion-skeleton-text></span>
          </div>
        </div>
      </div>
    </div>
    <div class="loading-card-container">
      <div class="hotel-card">
        <div class="hotel-card-header">
          <swiper [config]="config" (slideChange)="onSlideChanged($event)" #slides>
            <ng-template swiperSlide>
              <div class="hotel-image-slide">
                <ion-skeleton-text [animated]="true" style="height: 200px"></ion-skeleton-text>
              </div>
            </ng-template>
            <div class="swiper-pagination"></div>
          </swiper>
        </div>
        <div class="hotel-card-body margin-left-15">
          <div class="hotel-name"><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></div>
          <div class="hotel-address"><ion-skeleton-text [animated]="true" style="width: 60%"></ion-skeleton-text></div>
          <div class="hotel-hot-amenities">
            <span class="amenities-item amenities-1"><ion-skeleton-text [animated]="true"
                style="width: 80px"></ion-skeleton-text></span>
            <span class="amenities-item amenities-2"><ion-skeleton-text [animated]="true"
                style="width: 50px"></ion-skeleton-text></span>
          </div>
        </div>
      </div>
    </div>
    }@else if (loading==='LOADED') {
    <div class="search-container fixed-search">
      <div class="ion-padding no-padding-bottom padding-top">
        <ion-item class="site-form-control" lines="none">
          <i-feather class="map-pin-icon start-icon" name="Search" slot="start"></i-feather>
          <ion-input label="Search Hotel" labelPlacement="floating" name="searchLocation" [(ngModel)]="searchQuery"
            (ionInput)="searchHotel()" [debounce]="500"></ion-input>
        </ion-item>
      </div>
    </div>
    <div class="card-container-hotel">
      <div class="margin-top-80">
        <div class="hotel-card" *ngFor="let filteredhotel of filteredHotels" (click)="hotelDetail(filteredhotel)">
          <!-- Ribbon Badge -->
          <div class="ribbon-tag-container">
            <div class="ribbon" *ngIf="filteredhotel.price !== null" [ngStyle]="{
                 'background': filteredhotel.price > 0 
                   ? 'linear-gradient(to right, #ff8c00, #ffc107)'  
                   : 'linear-gradient(to right, #228B22, #32CD32)'  
               }">
              <span>
                {{ filteredhotel.price > 0 ? (filteredhotel.price | currency: 'INR':'symbol':'1.0-0') : '₹ 0' }}
              </span>
            </div>
          </div>

          <div class="hotel-card-header">
            <swiper [config]="config" (slideChange)="onSlideChanged($event)" #slides>
              <ng-template *ngFor="let attachment of filteredhotel?.hotelImages" swiperSlide>
                <div class="hotel-image-slide">
                  <img [src]="attachment.image" alt="{{ filteredhotel.name }}">
                </div>
              </ng-template>
              <div class="swiper-pagination"></div>
            </swiper>
          </div>

          <ion-grid class="hotel-card-grid">
            <ion-row>
              <ion-col size="10">
                <div class="hotel-card-body">
                  <div class="hotel-name">{{ filteredhotel.name }}</div>
                </div>
              </ion-col>
              <ion-col size="2">
                <div class="star-rating-outer">
                  <div class="star-rating">
                    <div class="star-rating-content">
                      <ion-icon src="/assets/images/svg/star-fill.svg" class="star-icon"></ion-icon>
                      <div class="rating-text">{{ filteredhotel.rating }}</div>
                    </div>
                  </div>
                </div>
              </ion-col>
            </ion-row>
          </ion-grid>

          <ion-grid *ngIf="filteredhotel.additionalAmount">
            <ion-row>
              <ion-col size="5">
                <ion-row>
                  <ion-col class="ion-no-margin ion-no-padding">
                    <div class="discounted-price">
                      {{ filteredhotel.additionalAmount | currency: 'INR':'symbol':'1.0-0' }}/-
                    </div>
                  </ion-col>
                </ion-row>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>
      </div>
    </div>
    <!-- No hotel Available Message -->
    <div class="customer-body-section has-header-section" *ngIf="searchResponse.totalCount <= 0">
      <div class="no-bookings-container">
        <div class="no-bookings-icon">
          <ion-icon name="alert-circle-outline"></ion-icon>
        </div>
        <h2 class="no-bookings-title">No hotels match your search criteria. Please try different dates or
          filters.</h2>
      </div>
    </div>
    }
  </div>


  <ion-modal class="site-custom-popup job-invitation-popup" #noPackageAvailablePopup
    [isOpen]="commonService.isNoPackageAvailablePopupOpen" [backdropDismiss]="false">
    <ng-template>
      <ion-content class="customer-dashboard-page booking-page">
        <div class="site-custom-popup-header">
          <i-feather name="X" (click)="closeNoPackageAvailablePopup()"></i-feather>
          <div class="header-text">Search Hotels</div>
        </div>
        <div class="customer-body-section has-header-section no-bottom-tab">
          <div class="hotel-search-container">
            <app-booking-filter [input]="input" [bookingType]="bookingType" [searchButtonText]="'Search Hotels'"
              (searchCallback)="search($event)" [showOnlyFilterOptions]="true"
              [isProcessing]="isProcessing"></app-booking-filter>
          </div>
        </div>
      </ion-content>
    </ng-template>
  </ion-modal>

  <ion-modal class="site-custom-popup job-invitation-popup" #bookingModal [isOpen]="isBookingCancelModalOpen"
    [backdropDismiss]="false">
    <ng-template>
      <div class="site-custom-popup-container">
        <div class="site-custom-popup-header no-header-text">
          <i-feather name="X" (click)="close()"></i-feather>
        </div>
        <div class="site-custom-popup-body ion-padding no-padding-top">
          <div class="popup-large-heading">Warning</div>
          <div class="popup-normal-heading margin-top-10 secondary-text">{{confirmationModalMessage}}</div>
          <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
            (click)="proceedWithExtraCharges();bookingModal.dismiss();">
            Proceed with Extra Charges
          </ion-button>
          <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
            (click)="close()">
            Cancel
          </ion-button>
        </div>
      </div>
    </ng-template>
  </ion-modal>
</ion-content>