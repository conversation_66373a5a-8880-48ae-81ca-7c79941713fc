<ion-content class="customer-dashboard-page booking-page">
  <app-customer-header [innerPage]="true" [headingText]="'Search Hotels'" (rightActionCallback)="openNotifications()"
    [backUrl]="'/dashboard'"></app-customer-header>
  <div class="customer-body-section has-header-section no-bottom-tab">
    <div class="hotel-search-container">
      @if (bookingType === 'MEMBERSHIP' && loading === 'LOADING') {
      <div class="no-membership-package-container">
        <ion-skeleton-text [animated]="true" style="width: 300px; height: 35px;"></ion-skeleton-text>
      </div>
      }
      @else if (bookingType === 'MEMBERSHIP' && loading === 'LOADED' && activeSubscriptionData) {
      <div class="membership-booking-package-container"
        [ngStyle]="{'--package-color': activeSubscriptionData?.packageDetail?.color}">
        <span class="booking-package-text">{{activeSubscriptionData?.packageDetail?.title}}</span>
      </div>
      }
      <app-booking-filter [input]="input" [bookingType]="bookingType" [searchButtonText]="'Search Hotels'"
        (searchCallback)="search($event)" [showOnlyFilterOptions]="false" [isProcessing]="isProcessing"
        [reset]="true"></app-booking-filter>
    </div>
  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #bookingModal [isOpen]="isBookingCancelModalOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="close()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <div class="popup-large-heading">Warning</div>
        <div class="popup-normal-heading margin-top-10 secondary-text">{{confirmationModalMessage}}</div>
        <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
          (click)="proceedWithExtraCharges();bookingModal.dismiss();">
          Proceed with Extra Charges
        </ion-button>
        <ion-button class="site-full-rounded-button close-button-hotel margin-top-10" expand="full" shape="round"
          (click)="close()">
          Cancel
        </ion-button>
      </div>
    </div>
  </ng-template>
</ion-modal>