import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-search-hotel',
  templateUrl: './search-hotel.component.html',
  styleUrls: ['./search-hotel.component.scss'],
})
export class SearchHotelComponent implements OnInit {

  bookingType: string | null = null;
  campaignId: string | null = null;
  input: any;
  isBookingCancelModalOpen: boolean = false;
  confirmationModalMessage: string = "";
  additionalNights: number = 0;
  activeSubscriptionData: any;
  isProcessing: boolean = false;
  shouldReset: string | null = null;
  loading: string = "NOT_STARTED";

  constructor(private readonly toastService: ToastService,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
  ) {
  }

  ngOnInit() {
    this.init();
  }

  ionViewDidEnter() {
    this.init();
  }

  init() {
    this.route.queryParams.subscribe(params => {
      this.bookingType = params['bookingType'] || "OTHERS";
      this.campaignId = params['campaignId'] || null;
      const shouldReset = params['reset'];

      this.input = {
        arrivalDate: new Date(new Date().getTime() + 24 * 60 * 60 * 1000),
        departureDate: new Date(new Date().getTime() + 48 * 60 * 60 * 1000),
        noOfRooms: 1,
        noOfAdults: 1,
        noOfChilds: 0,
        childrenAges: [],
        totalNights: 1,
        rooms: [{ type: `Room-1`, noOfAdults: 1, noOfChilds: 0, childrenAges: [], uiChildrenAges: [] }],
        bookingType: this.bookingType,
        hotelRatings: [3, 4, 5],
        minPrice: 0,
        maxPrice: 500000,
        priceRange: { lower: 0, upper: 500000 }
      };

      if (shouldReset) {
        this.input.location = null;
      }
    });
    this.loadActiveSubscriptionData();
  }

  loadActiveSubscriptionData() {
    this.loading = 'LOADING';

    setTimeout(() => {
      this.activeSubscriptionData = this.localStorageService.getObject("activeSubscriptionData");
      if (this.activeSubscriptionData) {
        this.loading = 'LOADED';
      } else {
        this.loading = 'LOADED';  // Even if data is null, mark as loaded to stop skeleton
      }
    }, 2000);
  }

  search(input: any) {
    if (!input.location) {
      this.toastService.show("Location is required");
      return;
    }
    this.input = input;
    this.localStorageService.setObject("search-input", this.input);
    if (this.bookingType !== "MEMBERSHIP") {
      this.navController.navigateForward("/portal/available/hotels", {
        animated: true,
        queryParams: {
          bookingType: this.bookingType,
          campaignId: this.campaignId
        }
      });
      return;
    }
    this.validateBookingAllowed(input);
  }

  validateBookingAllowed(input: any) {
    const payload = {
      totalRooms: input.noOfRooms,
      totalNights: input.totalNights,
      cityId: input.location.cityCode,
      bookingType: this.bookingType,
      fromDate: this.formattedDate(input.arrivalDate),
      toDate: this.formattedDate(input.departureDate),
    };
    //  this.loadingService.show();
    this.isProcessing = true;

    this.dataService.bookingAllowed(payload).subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        this.isProcessing = false;

        const data = response.data;
        this.confirmationModalMessage = data.message;
        if (!data.status) {
          this.openCancelConfirmationModal();
          this.additionalNights = data.additionalNights;
        } else {
          this.navController.navigateForward("/portal/available/hotels", {
            animated: true,
            queryParams:
            {
              bookingType: this.bookingType,
              campaignId: this.campaignId
            }
          });
        }
      },
      error: (error) => {
        //  this.loadingService.hide();
        this.isProcessing = false;
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  formattedDate(selectedDate: Date) {
    selectedDate = new Date(selectedDate);
    return `${selectedDate.getDate().toString().padStart(2, '0')}/${(selectedDate.getMonth() + 1).toString().padStart(2, '0')}/${selectedDate.getFullYear()}`;
  }

  openCancelConfirmationModal() {
    this.isBookingCancelModalOpen = true;
  }

  close() {
    this.isBookingCancelModalOpen = false;
  }

  proceedWithExtraCharges() {
    this.isBookingCancelModalOpen = false;
    if (this.additionalNights > 0) {
      this.bookingType = "MEMBERSHIP";
    } else {
      this.bookingType = "OTHERS";
    }
    this.navController.navigateForward("/portal/available/hotels", {
      animated: true,
      queryParams: {
        bookingType: this.bookingType,
        campaignId: this.campaignId
      }
    });
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }
}
