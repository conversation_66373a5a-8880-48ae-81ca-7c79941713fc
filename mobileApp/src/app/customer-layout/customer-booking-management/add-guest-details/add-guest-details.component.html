<ion-content class="customer-dashboard-page booking-hotel-add-guest-detail-page">
  <app-customer-header [innerPage]="true" [headingText]="'Guest Details'" [backUrl]="backUrlWithParams">
  </app-customer-header>
  <div class="customer-body-section has-header-section-change-profile no-padding">
    <div class="payment-body-section less-height-add-guest no-padding">
      <ion-content class="payment-body-ion-content scroll">

        @if (loading==='LOADING') {
        <div class="customer-body-section ">
          <div class="hotel-card">
            <div class="hotel-card-header">
              <swiper [config]="config" #slides>
                <ng-template swiperSlide>
                  <div class="hotel-image-slide">
                    <!-- <img [src]="hotel.thumbImages" alt="{{ hotel.name }}"> -->
                    <ion-skeleton-text [animated]="true" style="height: 200px"></ion-skeleton-text>
                  </div>
                </ng-template>
                <div class="swiper-pagination"></div>
              </swiper>
            </div>
            <div class="hotel-card-body">
              <div class="hotel-name"><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></div>
              <div class="hotel-address"><ion-skeleton-text [animated]="true" style="width: 60%"></ion-skeleton-text>
              </div>
              <div class="hotel-hot-amenities">
                <span class="amenities-item amenities-1"><ion-skeleton-text [animated]="true"
                    style="width: 80px"></ion-skeleton-text></span>
                <span class="amenities-item amenities-2"><ion-skeleton-text [animated]="true"
                    style="width: 50px"></ion-skeleton-text></span>
              </div>
            </div>
          </div>
          <div class="hotel-card">
            <div class="hotel-card-header">
              <swiper [config]="config" #slides>
                <ng-template swiperSlide>
                  <div class="hotel-image-slide">
                    <!-- <img [src]="hotel.thumbImages" alt="{{ hotel.name }}"> -->
                    <ion-skeleton-text [animated]="true" style="height: 200px"></ion-skeleton-text>
                  </div>
                </ng-template>
                <div class="swiper-pagination"></div>
              </swiper>
            </div>
            <div class="hotel-card-body">
              <div class="hotel-name"><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></div>
              <div class="hotel-address"><ion-skeleton-text [animated]="true" style="width: 60%"></ion-skeleton-text>
              </div>
              <div class="hotel-hot-amenities">
                <span class="amenities-item amenities-1"><ion-skeleton-text [animated]="true"
                    style="width: 80px"></ion-skeleton-text></span>
                <span class="amenities-item amenities-2"><ion-skeleton-text [animated]="true"
                    style="width: 50px"></ion-skeleton-text></span>
              </div>
            </div>
          </div>
          <div class="hotel-card">
            <div class="hotel-card-header">
              <swiper [config]="config" #slides>
                <ng-template swiperSlide>
                  <div class="hotel-image-slide">
                    <!-- <img [src]="hotel.thumbImages" alt="{{ hotel.name }}"> -->
                    <ion-skeleton-text [animated]="true" style="height: 200px"></ion-skeleton-text>
                  </div>
                </ng-template>
                <div class="swiper-pagination"></div>
              </swiper>
            </div>
            <div class="hotel-card-body">
              <div class="hotel-name"><ion-skeleton-text [animated]="true" style="width: 80%"></ion-skeleton-text></div>
              <div class="hotel-address"><ion-skeleton-text [animated]="true" style="width: 60%"></ion-skeleton-text>
              </div>
              <div class="hotel-hot-amenities">
                <span class="amenities-item amenities-1"><ion-skeleton-text [animated]="true"
                    style="width: 80px"></ion-skeleton-text></span>
                <span class="amenities-item amenities-2"><ion-skeleton-text [animated]="true"
                    style="width: 50px"></ion-skeleton-text></span>
              </div>
            </div>
          </div>
        </div>
        }@else if (loading==='LOADED') {

        <div class="hotel-card-container">
          <div class="hotel-card">
            <div class="hotel-card-header">
              <swiper [config]="config" #slides>
                <ng-template swiperSlide *ngFor="let item of hotelImages">
                  <div class="hotel-image-slide">
                    <img *ngIf="hotelDetailsLocal.thumbImages" src="{{ item.image }}" alt="test">
                    <img *ngIf="!hotelDetailsLocal.thumbImages" src="http://test.xmlhub.com/images/noimage.gif"
                      alt="test">
                  </div>
                </ng-template>
                <div class="swiper-pagination"></div>
              </swiper>
            </div>
          </div>

        </div>

        <div class="hotel-rooms-outer">
          <div class="add-guest-card-container">
            <div class="add-guest-card">
              <ion-grid class="">
                <ion-row>
                  <ion-col size="10">
                    <div class="">
                      <div class="hotel-name">{{ hotelDetail?.name }}</div>
                      <div class="hotel-address">{{ hotelDetail?.hotelAddress }}</div>
                    </div>
                  </ion-col>
                  <ion-col size="2">
                    <div class="star-rating-outer">
                      <div class="star-rating">
                        <div class="star-rating-content">
                          <ion-icon src="/assets/images/svg/star-fill.svg" class="star-icon"></ion-icon>
                          <div class="rating-text">{{ hotelDetail?.rating }}</div>
                        </div>
                      </div>
                    </div>
                  </ion-col>
                </ion-row>

                <ion-row class="check-in-out-con">
                  <ion-col size="5">
                    <div class="check-in-con">
                      <div class="label">CHECK-IN</div>
                      <div class="date">{{ preBookingData?.arrivalDate }}</div>
                      <!-- <div class="time">Fri 2PM</div> -->
                    </div>
                  </ion-col>
                  <ion-col size="2">
                    <div class="time-duration-con">
                      <ion-icon src="/assets/images/svg/right-arrow.svg" slot="start" class="star-icon">
                      </ion-icon>
                      <div class="time-duration">
                        <span>{{totalNights}}</span>
                        <span>{{totalNights<=1?' NIGHT':' NIGHTS'}}</span>
                      </div>
                      <!-- <div class="hotel-address">Calzada Porfirio Dhal 127 Col. Reforma C.P. 68050 Oaxaca</div> -->
                    </div>
                  </ion-col>
                  <ion-col size="5">
                    <div class="check-out-con">
                      <div class="label">CHECK-OUT</div>
                      <div class="date">{{ preBookingData?.departureDate }}</div>
                      <!-- <div class="time">Sat 2:00PM</div> -->
                    </div>
                  </ion-col>
                </ion-row>

                <ion-row>
                  <ion-col>
                    <div class="guest-and-rooms-con">
                      <div class="label">Guest & Rooms</div>
                      <div class="rooms">
                        {{commonService.getTotalAdults(preBookingRoomDetails?.adults)}}
                        {{commonService.getTotalAdults(preBookingRoomDetails?.adults) <= 1 ? "Adult " : "Adults " }}>
                          {{preBookingRoomDetails?.totalRooms}}
                          {{preBookingRoomDetails?.totalRooms <= 1 ? "Room" : "Rooms" }} <span
                            *ngIf="commonService.getTotalChildren(preBookingRoomDetails?.children) > 0">
                            >
                            {{ commonService.getTotalChildren(preBookingRoomDetails?.children) }}
                            {{ commonService.getTotalChildren(preBookingRoomDetails?.children) === 1 ? "Child" :
                            "Children"
                            }}
                            </span>

                      </div>
                    </div>
                  </ion-col>
                </ion-row>

                <ion-row>
                  <ion-col>
                    <ion-row>
                      <ion-col class="ion-no-margin ion-no-padding">
                        <div class="room-detail-con">
                          <div class="title">{{ preBookingRoomDetails?.type }}</div>
                          <!-- <div class="size">Room Size : 180sqft</div> -->
                        </div>
                      </ion-col>
                    </ion-row>

                    <ion-row *ngIf="roomAmenitiesList.length > 0">
                      <ion-col>
                        <ion-row>
                          <ion-col class="ion-no-margin ion-no-padding">
                            <div class="hotel-amenities">Room Amenities</div>
                          </ion-col>
                        </ion-row>
                        <ion-row>
                          <ion-col class="ion-no-margin ion-no-padding">
                            <div class="amenities">
                              <span *ngFor="let amenity of displayedRoomAmenities">{{ amenity }}</span>
                              <span *ngIf="remainingRoomAmenities.length > 0" class="show-more"
                                (click)="openRoomDetailsPopup()">
                                Show More
                              </span>
                            </div>
                          </ion-col>
                        </ion-row>
                      </ion-col>
                    </ion-row>
                  </ion-col>
                </ion-row>

                <ion-row>
                  <ion-col class="ion-no-margin ion-no-padding">
                    <div class="room-facility-con">
                      <div class="desc">{{ roomDetails.roomDescription }}</div>
                    </div>
                  </ion-col>
                </ion-row>

              </ion-grid>
            </div>
          </div>

          <div class="add-guest-card-container">
            <div>
              <div class="add-guest-card" *ngIf="preBookingResponse.bookingDetails.totalAdditionalAmount > 0">
                <ion-grid class="payment-con ion-no-padding">
                  <div class="title margin-top-8">Charges</div>
                  <ion-row class="room-row"
                    *ngIf="preBookingResponse && preBookingResponse.bookingDetails.additionalAmount > 0">
                    <ion-col size="8">
                      <ion-row class="ion-no-margin ion-no-padding">
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="">
                            <div class="detail" *ngIf="bookingType === 'MEMBERSHIP'">Additional Amount</div>
                            <div class="detail" *ngIf="bookingType != 'MEMBERSHIP'">Amount</div>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>

                    <ion-col size="4">
                      <ion-row>
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="">
                            <div class="amount">{{preBookingResponse.bookingDetails.additionalAmount | currency
                              :'INR':'symbol':'1.0-0'}}/-</div>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>
                  </ion-row>


                  <ion-row class="total-discount-con" *ngIf="preBookingResponse.bookingDetails.gstAmount > 0">
                    <ion-col size="8">
                      <ion-row class="ion-no-margin ion-no-padding">
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="">
                            <div class="detail">GST Amount ({{ preBookingResponse.bookingDetails.gstRate }}%)</div>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>

                    <ion-col size="4">
                      <ion-row>
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="">
                            <div class="amount">{{preBookingResponse.bookingDetails.gstAmount | currency
                              :'INR':'symbol':'1.0-0'}}/-</div>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>
                  </ion-row>

                  <ion-row class="total-discount-con" *ngIf="preBookingResponse.bookingDetails.serviceCharge > 0">
                    <ion-col size="8">
                      <ion-row class="ion-no-margin ion-no-padding">
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="">
                            <div class="detail">Service Charge</div>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>

                    <ion-col size="4">
                      <ion-row>
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="">
                            <div class="amount">{{preBookingResponse.bookingDetails.serviceCharge | currency
                              :'INR':'symbol':'1.0-0'}}/-</div>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>
                  </ion-row>

                  <ion-row class="total-discount-con margin-top-10" *ngIf="totalConvenienceFee > 0">
                    <ion-col size="8">
                      <ion-row class="ion-no-margin ion-no-padding">
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="">
                            <div class="detail">Convenience fee (incl. GST)</div>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>

                    <ion-col size="4">
                      <ion-row>
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="">
                            <div class="amount">{{ totalConvenienceFee | currency :'INR':'symbol':'1.0-2' }}/-</div>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>
                  </ion-row>

                  <ion-row class="total-amount-paid-con">
                    <ion-col size="8">
                      <ion-row class="ion-no-margin ion-no-padding">
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="">
                            <div class="detail">Total Payable Amount:</div>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>

                    <ion-col size="4">
                      <ion-row>
                        <ion-col class="ion-no-margin ion-no-padding">
                          <div class="">
                            <div class="amount">{{totalPayableAmount | currency
                              :'INR':'symbol':'1.0-2'}}/-</div>
                          </div>
                        </ion-col>
                      </ion-row>
                    </ion-col>
                  </ion-row>
                </ion-grid>
                <div class="card-bottom-cancellation-policy margin-top-20"
                  *ngIf="preBookingResponse.preBookingRequest.preBooking.bookingType==='OTHERS'"
                  (click)="freeCancellationPolicy()">
                  <div class="padding-top-5 padding-bottom-3 package-design">Cancellation Policy</div>
                </div>
              </div>

            </div>
          </div>

          <div class="add-guest-card-container"
            *ngIf="preBookingResponse.preBookingRequest.preBooking.bookingType==='MEMBERSHIP'">
            <div style="position: sticky;">
              <div class="guest-type-section-container ion-text-left">
                <div class="guest-type-section-item self-padding" (click)="onViewChange('CUSTOMER')"
                  [ngClass]="{'selected': view === 'CUSTOMER'}">
                  <ion-checkbox [checked]="view==='CUSTOMER'" [mode]="'md'"></ion-checkbox> <span>
                    Self
                  </span>
                </div>
                <div class="guest-type-section-item" (click)="onViewChange('CO-APPLICANT')"
                  [ngClass]="{'selected': view === 'CO-APPLICANT', 'disabled': !coApplicantDetail || !coApplicantDetail.firstName}">
                  <ion-checkbox [checked]="view==='CO-APPLICANT'" [mode]="'md'"
                    [disabled]="!coApplicantDetail"></ion-checkbox> <span>
                    Co-Applicant
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="add-guest-card-container">
            <div class="add-guest-card">
              <form class="custom-form" #recordForm="ngForm" novalidate (ngSubmit)="continue(recordForm)">
                <div class="">
                  <div class="form-container text-left">
                    <div *ngFor="let room of availableRooms; let roomIndex = index">
                      <div class="room-heading-guest margin-top-15">Room {{ roomIndex + 1 }}:</div>
                      <!-- <div *ngFor="let i of [].constructor(room.adults); let adultIndex = index"> -->
                      <div *ngFor="let i of getAdults(room.adults); let adultIndex = index">
                        <div class="guest-heading margin-top-10">Adult {{ adultIndex + 1 }}</div>
                        <div class="address-fields-container">
                          <div class="field-container small-field">
                            <ion-item class="site-form-control" lines="none"
                              [ngClass]="{'is-invalid': salutation.invalid && onClickValidation}">
                              <ion-select class="no-padding" label="Salutation" labelPlacement="floating"
                                interface="action-sheet" required name="salutation{{roomIndex}}_{{adultIndex}}"
                                #salutation="ngModel" [(ngModel)]="salutations[roomIndex][adultIndex]">
                                <ion-select-option *ngFor="let salutation of salutationTypes" [value]="salutation">
                                  {{ salutation }}
                                </ion-select-option>
                              </ion-select>
                            </ion-item>
                            <app-validation-message [field]="salutation" [onClickValidation]="onClickValidation"
                              [customPatternMessage]="'Please select a salutation.'">
                            </app-validation-message>
                          </div>

                          <div class="field-container large-field">
                            <ion-item class="site-form-control" lines="none"
                              [ngClass]="{'is-invalid': firstName.invalid && onClickValidation}">
                              <ion-input class="no-font-weight" label="First Name" labelPlacement="floating"
                                name="firstName{{roomIndex}}_{{adultIndex}}" #firstName="ngModel"
                                [ngModel]="getFirstName(roomIndex, adultIndex)"
                                (ngModelChange)="setFirstName(roomIndex, adultIndex, $event)" required maxlength="100"
                                pattern="^(?!\s*$)[A-Za-z\s]+$"
                                [disabled]="!isEditable(roomIndex, adultIndex) && bookingType === 'MEMBERSHIP'">
                              </ion-input>

                            </ion-item>
                            <app-validation-message [field]="firstName" [onClickValidation]="onClickValidation"
                              [customPatternMessage]="'Please enter only characters and alphabets.'">
                            </app-validation-message>
                          </div>
                        </div>

                        <div class="margin-top-10">
                          <ion-item class="site-form-control" lines="none"
                            [ngClass]="{'is-invalid': lastName.invalid && onClickValidation}">
                            <ion-input class="no-font-weight" label="Last Name" labelPlacement="floating"
                              name="lastName{{roomIndex}}_{{adultIndex}}" #lastName="ngModel"
                              [ngModel]="getLastName(roomIndex, adultIndex)"
                              (ngModelChange)="setLastName(roomIndex, adultIndex, $event)" required maxlength="100"
                              pattern="^(?!\s*$)[A-Za-z\s]+$"
                              [disabled]="!isEditable(roomIndex, adultIndex) && bookingType === 'MEMBERSHIP'">
                            </ion-input>

                          </ion-item>
                          <app-validation-message [field]="lastName" [onClickValidation]="onClickValidation"
                            [customPatternMessage]="'Please enter only characters and alphabets.'">
                          </app-validation-message>
                        </div>
                      </div>

                      <div *ngIf="room.childrens > 0">
                        <!-- <div *ngFor="let j of [].constructor(room.childrens); let childIndex = index"> -->
                        <div *ngFor="let i of getChildren(room.childrens); let childIndex = index">
                          <div class="guest-heading margin-top-20">Child {{ childIndex + 1 }}</div>

                          <div class="address-fields-container">
                            <div class="field-container small-field">
                              <ion-item class="site-form-control" lines="none">
                                <ion-select class="no-padding" label="Salutation" labelPlacement="floating"
                                  interface="action-sheet" required name="childSalutation{{roomIndex}}_{{childIndex}}"
                                  #childSalutation="ngModel" [(ngModel)]="childSalutations[roomIndex][childIndex]"
                                  disabled>
                                  <ion-select-option value="Child">Child</ion-select-option>
                                </ion-select>
                              </ion-item>
                            </div>

                            <div class="field-container large-field">
                              <ion-item class="site-form-control" lines="none"
                                [ngClass]="{'is-invalid': childFirstName.invalid && onClickValidation}">
                                <ion-input class="no-font-weight" label="First Name" labelPlacement="floating"
                                  name="childFirstName{{roomIndex}}_{{childIndex}}" #childFirstName="ngModel"
                                  [(ngModel)]="childFirstNames[roomIndex][childIndex]" required maxlength="100"
                                  pattern="^(?!\s*$)[A-Za-z\s]+$">
                                </ion-input>
                              </ion-item>
                              <app-validation-message [field]="childFirstName" [onClickValidation]="onClickValidation"
                                [customPatternMessage]="'Please enter only characters and alphabets.'">
                              </app-validation-message>
                            </div>
                          </div>

                          <div class="margin-top-10">
                            <ion-item class="site-form-control" lines="none"
                              [ngClass]="{'is-invalid': childLastName.invalid && onClickValidation}">
                              <ion-input class="no-font-weight" label="Last Name" labelPlacement="floating"
                                name="childLastName{{roomIndex}}_{{childIndex}}" #childLastName="ngModel"
                                [(ngModel)]="childLastNames[roomIndex][childIndex]" required maxlength="100"
                                pattern="^(?!\s*$)[A-Za-z\s]+$">
                              </ion-input>
                            </ion-item>
                            <app-validation-message [field]="childLastName" [onClickValidation]="onClickValidation"
                              [customPatternMessage]="'Please enter only characters and alphabets.'">
                            </app-validation-message>
                          </div>

                          <div class="margin-top-10">
                            <ion-item class="site-form-control" lines="none"
                              [ngClass]="{'is-invalid': childAge.invalid && onClickValidation}">
                              <ion-input class="no-font-weight" label="Age" labelPlacement="floating"
                                name="childAge{{roomIndex}}_{{childIndex}}" #childAge="ngModel"
                                [(ngModel)]="childAges[roomIndex][childIndex]" required type="number" min="2" max="12"
                                disabled>
                              </ion-input>
                            </ion-item>
                            <app-validation-message [field]="childAge" [onClickValidation]="onClickValidation"
                              [customPatternMessage]="'Please enter a valid age between 2 and 12.'">
                            </app-validation-message>
                          </div>

                        </div>
                      </div>
                    </div>

                    <div class="overlay-discount-body">
                      <ion-row class="ion-no-margin ion-no-padding">
                        <ion-col class="discount-price-flex">
                          <ion-row>
                            <ion-col class="ion-no-margin ion-no-padding">
                              <span class="discounted-heading" *ngIf="bookingType === 'MEMBERSHIP'">Additional
                                Amount:</span>
                              <span class="discounted-heading" *ngIf="bookingType != 'MEMBERSHIP'">Amount:</span>
                              <div *ngIf="preBookingResponse.bookingDetails.totalAdditionalAmount > 0"
                                class="discounted-price">
                                {{totalPayableAmount | currency
                                :'INR':'symbol':'1.0-2'}}/-
                              </div>
                              <div class="discounted-price"
                                *ngIf="preBookingResponse.bookingDetails.totalAdditionalAmount <= 0">
                                <span class="free-txt-bg">
                                  ₹ 0
                                </span>
                              </div>
                            </ion-col>
                          </ion-row>
                        </ion-col>

                        <ion-col>
                          <div class="button-container">
                            <ion-button expand="block" class="custom-button" type="submit">
                              Continue
                            </ion-button>
                          </div>
                        </ion-col>
                      </ion-row>
                    </div>

                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
        }
      </ion-content>
    </div>
  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup customer-dashboard-page booking-hotel-detail-page"
  #isRoomDetailsPopup [isOpen]="isRoomDetailsOpen" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <!-- Header -->
      <div class="site-custom-popup-header less-padding">
        <i-feather name="X" (click)="closeRoomDetailsPopup()"></i-feather>
        <div class="header-text">Room Amenities</div>
      </div>
      <!-- Body -->
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form " #recordForm="ngForm" novalidate>
          <div class="hotel-rooms-outer no-margin">
            <div class="hotel-detail-card-container">
              <div class="hotel-detail-card hotel-detail-card-ion-modal margin-top-10">
                <div class="amenities">
                  <span *ngFor="let amenity of remainingRoomAmenities">{{ amenity }}</span>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #cancelPolicyModal [isOpen]="isCancelPolicyPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeCancelPolicyPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading"> Cancellation Policy</div>
          <p class="popup-normal-heading">Here is the information regarding the Cancellation Policy.</p>
          <div class="popup-normal-heading margin-top-5 secondary-text">{{cancellationInfo}}</div>
          <ul class="bullet-text  popup-normal-heading">
            <!-- <li>Charge -<b>{{cancelInformation.chargeAmount}}{{ cancelInformation.chargeType === 'Percentage' ? '%' : '' }}</b> Applicable if Cancelled After {{cancelInformation.endDate}}</li> -->
            @for (cancelInformation of cancellationInformations; track $index) {
            <li>
              @if (cancelInformation.chargeAmount === "0") {
              <p>
                No Charges Applicable If Cancelled Before<b> {{cancelInformation.endDate |
                  date:'dd/MM/yyyy, h:mm a'}}</b> IST.
              </p>

              } @else if (cancelInformation.chargeType === "Percentage") {
              <p>
                Charge - <b>{{cancelInformation.chargeAmount}}% </b>Applicable If Cancelled After
                <b>{{cancelInformation.startDate
                  | date:'dd/MM/yyyy, h:mm a'}}</b> IST.
              </p>
              } @else if (cancelInformation.chargeType === "Amount") {
              <p>
                Charges - <b>{{cancelInformation.chargeAmount | currency
                  :'INR':'symbol':'1.0-0'}}</b>
                Applicable If Cancelled After <b>{{cancelInformation.startDate | date:'dd/MM/yyyy, h:mm a'}}</b> IST
              </p>
              }
            </li>
            }
          </ul>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>