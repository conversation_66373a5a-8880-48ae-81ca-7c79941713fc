import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { LocalStorageService } from 'src/shared/local-storage.service';
import SwiperCore, { Pagination, SwiperOptions } from 'swiper';
import { AuthService } from 'src/shared/authservice';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';
import { DataService } from 'src/services/data.service';
import { CommonService } from 'src/services/common.service';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-add-guest-details',
  templateUrl: './add-guest-details.component.html',
  styleUrls: ['./add-guest-details.component.scss'],
})
export class AddGuestDetailsComponent implements OnInit {

  view: string = "CUSTOMER";
  roomDetailsArray: Array<any> = new Array<any>();
  salutationTypes: string[] = ['Mr', 'Mrs', 'Ms'];
  preBookingResponse: any;
  preBookingData: any;
  preBookingRoomDetails: any;
  bookingType: string | null = null;
  campaignId: string | null = null;
  roomDetails: any;
  cancellationInformations: Array<any> = new Array<any>();
  cancellationInfo: any;
  isCancelPolicyPopupOpen: boolean = false;
  // Validation-related properties
  onClickValidation = false;
  salutations: string[][] = [];
  firstNames: string[][] = [];
  lastNames: string[][] = [];

  // Child-related properties
  childSalutations: string[][] = [];
  childFirstNames: string[][] = [];
  childLastNames: string[][] = [];
  childAges: number[][] = [];

  availableRooms: any;
  config: SwiperOptions = {
    slidesPerView: 1,
    pagination: {
      clickable: true,
      enabled: true,
      type: "bullets",
      //bulletElement: ".swiper-pagination"
    },
    zoom: true
  };
  user: any;
  coApplicantDetail: any;
  loading: string = "NOT_STARTED";
  hotelDetail: any;
  totalNights: any;
  roomAmenitiesList: string[] = [];
  displayedRoomAmenities: string[] = [];
  remainingRoomAmenities: string[] = [];
  isRoomDetailsOpen: boolean = false;
  hotelDetailsString: any;
  hotelDetailsLocal: any;

  totalPayableAmount: any;
  availableCashDetails: any;
  convenienceFee: any;
  convenienceFeeGst: any;
  totalConvenienceFee: any;
  backUrlWithParams: string = '';
  hotelImages: any;

  constructor(
    private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly cdr: ChangeDetectorRef,
    private readonly authService: AuthService,
    private readonly toastService: ToastService,
    private readonly dataService: DataService,
    public readonly commonService: CommonService,
    private readonly route: ActivatedRoute
  ) { }

  ngOnInit() {
    this.onClickValidation = false;
    this.hotelDetailsString = this.localStorageService.getObject("hotelDetails");
    if (this.hotelDetailsString) {
      this.hotelDetailsLocal = JSON.parse(this.hotelDetailsString);
      this.hotelImages = this.hotelDetailsLocal.hotelImages;
    }

  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();

    const coApplicantDetail = this.localStorageService.getObject("coApplicantDetail");

    if (coApplicantDetail !== undefined) {
      this.localStorageService.setObject("coApplicantDetail", coApplicantDetail);
    }

    this.coApplicantDetail = coApplicantDetail;

    const shouldReset = this.localStorageService.getObject("resetGuestFormDetails");
    if (shouldReset) {
      this.resetForm(); // Reset the form if the flag is true
      this.localStorageService.setObject("resetGuestFormDetails", false); // Reset the flag
    }

    this.route.queryParams.subscribe(params => {
      this.bookingType = params['bookingType'] || "OTHERS"
      this.campaignId = params['campaignId'] || null;
    });

    this.preBookingResponse = this.localStorageService.getObject("preBookingResponse");
    this.roomDetails = JSON.parse(this.localStorageService.getObject("roomDetails"));

    if (this.preBookingResponse) {
      this.preBookingData = this.preBookingResponse.preBookingRequest.preBooking;
      this.preBookingRoomDetails = this.preBookingData.roomDetails[0];
      this.fetchHotelDetailById(this.preBookingData.hotelId);
      // this.calculateTotalNights();
      this.totalNights = this.calculateTotalNights();
    }

    this.backUrlWithParams = `/portal/hotel/${this.preBookingData?.hotelId}/details?bookingType=${this.bookingType}&campaignId=${this.campaignId}`;

    // Reset room details array
    if (this.preBookingData.roomDetails.length > 0) {
      this.roomDetailsArray = [this.preBookingData.roomDetails[0]];
      const roomDetail = this.preBookingData.roomDetails[0];

      const rooms = Number(roomDetail.totalRooms);
      const roomName = roomDetail.type ? roomDetail.type.split("|")[0] : "";
      let roomRate = roomDetail.totalRate ? roomDetail.totalRate.split("|")[0] : 0;
      roomRate = Number(Number(roomRate).toFixed(2));

      const roomAdults = roomDetail.adults ? roomDetail.adults.split("|") : [];
      const roomChildren = roomDetail.children ? roomDetail.children.split("|") : [];
      const roomChildrenAges = roomDetail.childrenAges ? roomDetail.childrenAges.split("|") : [];

      this.availableRooms = new Array<any>();

      for (let i = 0; i < rooms; i++) {
        const roomDetail = {} as any;
        roomDetail.adults = roomAdults[i] ? Number(roomAdults[i]) : 0;
        roomDetail.childrens = roomChildren[i] ? Number(roomChildren[i]) : 0;
        roomDetail.name = roomName;
        roomDetail.rate = roomRate;

        // Process children ages
        if (roomChildrenAges[i]) {
          roomDetail.childrenAges = roomChildrenAges[i].split("*").map((age: any) => Number(age));
        } else {
          roomDetail.childrenAges = []; // No ages if not available
        }

        this.availableRooms.push(roomDetail);
      }
    }

    // Initialize arrays for guest details
    this.initializeGuestDetails();
    this.fetchAvailableCashDetails();
  }

  fetchAvailableCashDetails() {
    const payload = {
      paymentType: "BOOKING",
      expiryDays: 10
    };

    this.loading = "LOADING";
    this.dataService.availableCashDetails(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.availableCashDetails = response.data;

        if (this.preBookingResponse?.bookingDetails?.totalAdditionalAmount > 0) {
          const amount = Math.floor(this.preBookingResponse.bookingDetails.totalAdditionalAmount);
          this.convenienceFee = amount * (this.availableCashDetails?.convenienceFeeConfigurationDetail?.booking / 100 || 0);
          this.convenienceFeeGst = (this.convenienceFee * this.availableCashDetails?.convenienceFeeConfigurationDetail?.gstRate / 100 || 0);
          this.totalConvenienceFee = parseFloat((this.convenienceFee + this.convenienceFeeGst).toFixed(2));
        } else {
          this.totalConvenienceFee = 0; // Default if API data is missing
        }

        // Ensure values exist and use fallback values if API fails
        const additionalAmount = this.preBookingResponse?.bookingDetails?.additionalAmount || 0;
        const gstAmount = this.preBookingResponse?.bookingDetails?.gstAmount || 0;

        this.totalPayableAmount = additionalAmount + gstAmount + this.totalConvenienceFee;
      },
      error: (error: any) => {
        this.loading = "LOADED";

        // Fallback calculation if API fails
        const additionalAmount = this.preBookingResponse?.bookingDetails?.additionalAmount || 0;
        const gstAmount = this.preBookingResponse?.bookingDetails?.gstAmount || 0;
        this.totalConvenienceFee = 0; // No convenience fee if API fails
        this.totalPayableAmount = additionalAmount + gstAmount;
      },
    });
  }

  onViewChange(newView: string) {
    //  if (newView === 'CO-APPLICANT' && (!this.user?.coApplicantDetail || this.user.coApplicantDetail.totalCount <= 0)) {
    if (newView === 'CO-APPLICANT' && !this.coApplicantDetail) {
      return;
    }
    this.view = newView;
    this.initializeGuestDetails();  // Reinitialize guest details when the view changes
  }

  resetForm() {
    this.onClickValidation = false;
    this.roomDetailsArray = new Array<any>();
    this.availableRooms = new Array<any>();
    this.salutations = Array.from({ length: this.availableRooms.length }, () => []);
    this.firstNames = Array.from({ length: this.availableRooms.length }, () => []);
    this.lastNames = Array.from({ length: this.availableRooms.length }, () => []);
    this.childSalutations = Array.from({ length: this.availableRooms.length }, () => []);
    this.childFirstNames = Array.from({ length: this.availableRooms.length }, () => []);
    this.childLastNames = Array.from({ length: this.availableRooms.length }, () => []);
    this.childAges = Array.from({ length: this.availableRooms.length }, () => []);

    // Trigger change detection
    this.cdr.detectChanges();
  }

  initializeGuestDetails() {
    this.availableRooms.forEach((room: { adults: any; childrens: any; childrenAges: number[] }, roomIndex: number) => {
      const totalAdults = room.adults;
      const totalChildren = room.childrens;

      // Initialize adult details
      this.salutations.push(new Array(totalAdults).fill(''));
      this.firstNames.push(new Array(totalAdults).fill(''));
      this.lastNames.push(new Array(totalAdults).fill(''));

      if (roomIndex === 0 && totalAdults > 0) {
        if (this.view === 'CO-APPLICANT') {
          // Co-Applicant View: Only first adult should have co-applicant details
          this.firstNames[roomIndex][0] = this.coApplicantDetail?.firstName || '';
          this.lastNames[roomIndex][0] = this.coApplicantDetail?.lastName || '';
          this.salutations[roomIndex][0] = this.coApplicantDetail?.salutation || '';

          // Ensure no other Co-Applicant details are shown
          for (let i = 1; i < totalAdults; i++) {
            this.firstNames[roomIndex][i] = '';
            this.lastNames[roomIndex][i] = '';
            this.salutations[roomIndex][i] = '';
          }
        } else {
          // Customer View: First adult gets User details (Non-Editable)
          this.firstNames[roomIndex][0] = this.user?.firstName || '';
          this.lastNames[roomIndex][0] = this.user?.lastName || '';
          this.salutations[roomIndex][0] = this.user?.salutation || '';

          // Show Co-Applicant in Room 0, Adult 1 (Only for Customer View)
          if (totalAdults > 1 && this.coApplicantDetail?.firstName && this.coApplicantDetail?.lastName) {
            this.firstNames[roomIndex][1] = this.coApplicantDetail.firstName;
            this.lastNames[roomIndex][1] = this.coApplicantDetail.lastName;
            this.salutations[roomIndex][1] = this.coApplicantDetail.salutation;
          }
        }
      }

      // Customer View: Show Co-Applicant in Room 1, Adult 0 only if Room 0, Adult 1 is missing
      else if (roomIndex === 1 && totalAdults > 0 && this.view === 'CUSTOMER') {
        if (this.coApplicantDetail?.firstName && this.coApplicantDetail?.lastName) {
          const hasRoom0Adult1 = this.availableRooms.length > 0 && this.availableRooms[0].adults > 1;

          if (!hasRoom0Adult1) {
            this.firstNames[roomIndex][0] = this.coApplicantDetail.firstName;
            this.lastNames[roomIndex][0] = this.coApplicantDetail.lastName;
            this.salutations[roomIndex][0] = this.coApplicantDetail.salutation;
          }
        }
      }
      // 🔹 Ensure Co-Applicant details are NOT shown in Co-Applicant View (Clear them)
      if (this.view === 'CO-APPLICANT') {
        for (let i = 0; i < totalAdults; i++) {
          if (!(roomIndex === 0 && i === 0)) { // Keep Room 0, Adult 0 as Co-Applicant
            this.firstNames[roomIndex][i] = '';
            this.lastNames[roomIndex][i] = '';
            this.salutations[roomIndex][i] = '';
          }
        }
      }

      // Initialize child details
      this.childSalutations.push(new Array(totalChildren).fill('Child'));
      this.childFirstNames.push(new Array(totalChildren).fill(''));
      this.childLastNames.push(new Array(totalChildren).fill(''));
      this.childAges.push([]);

      const childrenAges = room.childrenAges;
      for (let j = 0; j < totalChildren; j++) {
        if (j < childrenAges.length) {
          this.childSalutations[roomIndex].push('Child');
          this.childAges[roomIndex][j] = childrenAges[j];
        }
      }
    });

    this.cdr.detectChanges();
  }

  fetchHotelDetailById(hotelCode: string) {
    const payload = {
      hotelCode: hotelCode,
    };
    this.loading = "LOADING";
    this.dataService.fetchHotelDetailById(payload)
      .subscribe({
        next: (response: RestResponse) => {
          this.loading = "LOADED";
          this.hotelDetail = response.data;
          this.roomAmenitiesList = this.hotelDetail.roomAmenities.split(',')
            .map((item: string) => item.trim())
            .filter((item: string | any[]) => item.length > 0);
          this.displayedRoomAmenities = this.roomAmenitiesList.slice(0, 3);
          this.remainingRoomAmenities = this.roomAmenitiesList.slice();
        },
        error: (error) => {
          this.loading = "LOADED";
          this.toastService.show(error.message);
        }
      });
  }


  back() {
    this.navController.navigateBack("/portal/hotel/details", { animated: true });
  }

  async continue(form: any): Promise<void> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }

    const guestDetails = {
      searchSessionId: this.preBookingData.searchSessionId,
      bookingType: this.bookingType,
      arrivalDate: this.preBookingData.arrivalDate,
      departureDate: this.preBookingData.departureDate,
      currency: this.preBookingData.currency,
      guestNationality: this.preBookingData.guestNationality,
      countryCode: this.preBookingData.countryCode,
      city: this.preBookingData.city,
      hotelId: this.preBookingData.hotelId,
      roomDetails: this.getRoomDetailsPayload()
    };

    this.localStorageService.setObject("guestPreviewDetails", guestDetails);
    this.localStorageService.setObject("fetchHotelDetailById", this.hotelDetail);
    this.navController.navigateForward("/portal/guest/preview/details", {
      animated: true,
      queryParams: {
        campaignId: this.campaignId,
        convienceFeeDetail: JSON.stringify(this.availableCashDetails)
      }
    });
  }

  getRoomDetailsPayload() {
    return [{
      type: this.preBookingRoomDetails.type,
      bookingKey: this.preBookingRoomDetails.bookingKey,
      adults: this.preBookingRoomDetails.adults,
      children: this.preBookingRoomDetails.children,
      childrenAges: this.preBookingRoomDetails.childrenAges,
      totalRooms: this.preBookingRoomDetails.totalRooms,
      totalRate: this.preBookingRoomDetails.totalRate,
      guests: this.getAllGuestsPayload()
    }];
  }

  getAllGuestsPayload() {
    const guests: { guest: ({ salutation: string; firstName: string; lastName: string; IsChild?: undefined; Age?: undefined; } | { salutation: string; firstName: string; lastName: string; IsChild: string; Age: string; })[]; }[] = [];

    this.availableRooms.forEach((room: { adults: any; childrens: any; }, roomIndex: number) => {
      const roomGuests = [];

      // Push adults
      for (let i = 0; i < room.adults; i++) {
        roomGuests.push({
          salutation: this.salutations[roomIndex][i],
          firstName: this.firstNames[roomIndex][i],
          lastName: this.lastNames[roomIndex][i],
        });
      }

      // Push children
      for (let j = 0; j < room.childrens; j++) {
        roomGuests.push({
          salutation: this.childSalutations[roomIndex][j],
          firstName: this.childFirstNames[roomIndex][j],
          lastName: this.childLastNames[roomIndex][j],
          IsChild: "1",
          Age: String(this.childAges[roomIndex][j]) // Convert age to string
        });
      }

      // Add this room's guests to the guests array
      guests.push({
        guest: roomGuests
      });
    });

    return guests;
  }

  getFirstName(roomIndex: number, adultIndex: number): string {
    if (roomIndex === 0 && adultIndex === 0) {
      return this.view === 'CO-APPLICANT'
        ? this.coApplicantDetail?.firstName || ''
        : this.user?.firstName || '';
    }
    return this.firstNames[roomIndex][adultIndex] || '';
  }

  setFirstName(roomIndex: number, adultIndex: number, value: string): void {
    if (roomIndex === 0 && adultIndex === 0) return; // First adult is fixed in both views
    this.firstNames[roomIndex][adultIndex] = value;
  }

  getLastName(roomIndex: number, adultIndex: number): string {
    if (roomIndex === 0 && adultIndex === 0) {
      return this.view === 'CO-APPLICANT'
        ? this.coApplicantDetail?.lastName || ''
        : this.user?.lastName || '';
    }
    return this.lastNames[roomIndex][adultIndex] || '';
  }

  setLastName(roomIndex: number, adultIndex: number, value: string): void {
    if (roomIndex === 0 && adultIndex === 0) return; // First adult is fixed in both views
    this.lastNames[roomIndex][adultIndex] = value;
  }

  getSalutation(gender: string | undefined): string {
    if (!gender) return '';
    return gender.toLowerCase() === 'male' ? 'Mr' : gender.toLowerCase() === 'female' ? 'Ms' : '';
  }

  isEditable(roomIndex: number, adultIndex: number): boolean {
    // Room 0, Adult 0 is always non-editable in both views
    if (roomIndex === 0 && adultIndex === 0) return false;

    // All other fields should be editable in Customer View
    return true;
  }

  calculateTotalNights() {
    if (!this.preBookingData.arrivalDate || !this.preBookingData.departureDate) {
      console.error('Arrival or departure date is missing.');
      return 0; // Handle missing date input
    }

    // Helper function to parse DD/MM/YYYY format
    const parseDate = (dateString: string): Date => {
      const [day, month, year] = dateString.split('/').map(Number);
      return new Date(year, month - 1, day); // Month is zero-based in JavaScript
    };

    // Parse the dates using the helper function
    const arrivalDate = parseDate(this.preBookingData.arrivalDate);
    const departureDate = parseDate(this.preBookingData.departureDate);

    // Check if parsed dates are valid
    if (isNaN(arrivalDate.getTime()) || isNaN(departureDate.getTime())) {
      console.error('Invalid date format. Please ensure dates are in DD/MM/YYYY format.');
      return 0;
    }

    // Set consistent times for calculation
    const adjustedArrivalDate = arrivalDate.setHours(12, 0, 0, 0);
    const adjustedDepartureDate = departureDate.setHours(23, 59, 59, 999);

    // Calculate the difference in milliseconds
    const diffTime = adjustedDepartureDate - adjustedArrivalDate;

    // If the difference is negative, arrival is after departure
    if (diffTime < 0) {
      console.error('Departure date is before arrival date.');
      return 0; // Handle invalid date range
    }

    // Convert milliseconds to days
    const totalNights = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    return totalNights;
  }

  getTotalAdults(adults: string): number {
    if (!adults) return 0;
    return adults.split('|').map(Number).reduce((a, b) => a + b, 0);
  }


  openRoomDetailsPopup() {
    this.isRoomDetailsOpen = true;  // Open the modal
  }

  getAdults(adultCount: number): number[] {
    return Array.from({ length: adultCount });
  }

  getChildren(childCount: number): number[] {
    return Array.from({ length: childCount });
  }

  closeRoomDetailsPopup() {
    this.isRoomDetailsOpen = false;
  }

  freeCancellationPolicy() {
    const cancellationPolicyPayload = {
      searchSessionId: this.preBookingResponse.preBookingRequest.preBooking.searchSessionId,
      bookingType: this.preBookingResponse.preBookingRequest.preBooking.bookingType,
      arrivalDate: this.preBookingResponse.preBookingRequest.preBooking.arrivalDate,
      departureDate: this.preBookingResponse.preBookingRequest.preBooking.departureDate,
      currency: this.preBookingResponse.preBookingRequest.preBooking.currency,
      guestNationality: this.preBookingResponse.preBookingRequest.preBooking.guestNationality,
      countryCode: this.preBookingResponse.preBookingRequest.preBooking.countryCode,
      city: this.preBookingResponse.preBookingRequest.preBooking.city,
      hotelId: this.preBookingResponse.preBookingRequest.preBooking.hotelId,
      name: this.preBookingResponse.preBookingRequest.preBooking.name,
      roomDetails: [
        {
          type: this.roomDetailsArray[0].type,
          bookingKey: this.roomDetailsArray[0].bookingKey,
          adults: this.roomDetailsArray[0].adults,
          children: this.roomDetailsArray[0].children,
          ChildrenAges: this.roomDetailsArray[0].childrenAges,
        }
      ]
    };
    this.dataService.hotelCancellationPolicy(cancellationPolicyPayload)
      .subscribe({
        next: (response: RestResponse) => {
          this.cancellationInformations = response.data.cancellationInformation;
          this.cancellationInfo = response.data.info;
          this.openCancelPolicyPopup();
        },
        error: (error: any) => {
          this.loading = "LOADED";
          this.toastService.show(error.message || 'An error occurred');
        }
      });
  }

  openCancelPolicyPopup() {
    this.isCancelPolicyPopupOpen = true;
  }

  closeCancelPolicyPopup() {
    this.isCancelPolicyPopupOpen = false;
  }

}
