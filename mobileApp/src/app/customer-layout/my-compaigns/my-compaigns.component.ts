import { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';
import { ActivatedRoute } from '@angular/router';
import { CommonService } from 'src/services/common.service';
import { AuthService } from 'src/shared/authservice';

@Component({
  selector: 'app-my-compaigns',
  templateUrl: './my-compaigns.component.html',
  styleUrls: ['./my-compaigns.component.scss'],
})
export class MyCompaignsComponent implements OnInit {
  @ViewChild('textElement') textElement!: ElementRef;
  user: any;
  filter: any;
  pageSize: number = 10;
  loading: string = "NOT_STARTED";
  showInfiniteScroll: boolean = false;
  totalCount: number = 0;
  campaigns: Array<any> = new Array<any>();
  walletId!: string;

  constructor(private authService: AuthService,
    private readonly toastService: ToastService,
    private readonly dataService: DataService,
    public readonly commonService: CommonService,
    private readonly navController: NavController,
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef
  ) { }

  ngOnInit() { }

  ionViewWillEnter() {
    this.user = this.authService.getUser();

    this.walletId = this.route.snapshot.paramMap.get('walletId') || "";

    this.showInfiniteScroll = true;
    this.campaigns = new Array<any>();
    this.filter = {} as any;
    this.filter.offset = 1;

    this.myCampaigns(null, true);
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

  myCampaigns($event?: any, handleLoading?: boolean) {
    const payload = {
      pagination: {
        next: this.pageSize,
        offset: this.filter.offset,
      }
    };
    if (handleLoading) { this.loading = "LOADING"; }
    this.dataService.myCompaigns(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        if (response.data.length > 0) {
          response.data.forEach((campaign: any) => {
            if (campaign.description) {
              campaign.description = campaign.description.replace(/^"(.*)"$/, '$1');
            }
            if (campaign.rewards) {
              campaign.rewards = campaign.rewards.replace(/^"(.*)"$/, '$1');
            }
          });

          this.campaigns.push(...response.data);
        }
        if ($event) {
          $event.target.complete();
          if (this.campaigns.length > 0 && this.campaigns.length >= this.campaigns[0].totalCount) {
            $event.target.disabled = true;
          }
        }
        this.cdr.detectChanges();
      },
      error: (error: any) => {
        this.loading = "LOADED";
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  onPageChanged($event: any) {
    if (this.campaigns.length > 0 && this.campaigns.length >= this.campaigns[0].totalCount) {
      $event.target.complete();
      $event.target.disabled = true;
      return;
    }
    this.filter.offset = this.filter.offset + 1;
    this.myCampaigns($event, false);
  }

  campaignDetail(campaignId: string) {
    this.navController.navigateForward('/portal/my/campaign/details', {
      state: {
        campaignId: campaignId,
        walletId: this.walletId,
        fromScreen: 'my-campaigns'
      },
      animated: true
    });
  }

  toggleText(textElement: HTMLElement, event: Event) {
    textElement.classList.toggle('expanded');
    // Change "See More" / "See Less"
    const seeMoreBtn = event.target as HTMLElement;
    seeMoreBtn.textContent = textElement.classList.contains('expanded') ? 'See Less' : 'See More';
  }

  formatText(text: string): string {
    return text ? text.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, char => char.toUpperCase()) : '';
  }

  getImageSrc(campaignType: string): string {
    switch (campaignType) {
      case 'FREE_TRIP':
        return '/assets/images/svg/travel-agency.svg';
      case 'WALLET_TOPUP':
        return '/assets/images/svg/wallet.svg';
      default:
        return '/assets/images/svg/booking.svg';
    }
  }
}
