import { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { environment } from 'src/environments/environment';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { ToastService } from 'src/shared/toast.service';
import { Checkout } from 'capacitor-razorpay';
import { LoadingService } from 'src/services/loading.service';
import { Location } from '@angular/common';

@Component({
  selector: 'app-campaign-detail',
  templateUrl: './campaign-detail.component.html',
  styleUrls: ['./campaign-detail.component.scss'],
})
export class CampaignDetailComponent implements OnInit {
  @ViewChild('textElement') textElement!: ElementRef;
  @ViewChild('eligi') eligi!: ElementRef;

  user: any;
  campaignId: string = "";
  campaignDetail: any;
  isAddMoney: boolean = false;
  onClickValidation: boolean = false;
  isPaymentProcessing: boolean = false;
  amount: number | string = 0;
  displayAmount: string = '';
  walletId!: string;
  presetAmounts: number[] = [200, 500, 1000, 2000];
  selectedAmounts: number[] = [];
  userClearedInput: boolean = false; // Track if the user cleared the input
  imageSrc: string = '';

  loading: string = "NOT_STARTED";
  availableCashDetails: any;
  responseReceived: boolean = false;
  isSuccessfulResponse: boolean = false;
  isBookNow: boolean = false;
  successPaymentDetails: any;
  backUrl: string | null = null;
  fromScreen: string | null = null;
  showButton: boolean = false;
  closeSuccessPop: boolean = true;

  limit: number = 240;
  isCollapsed: boolean = true;
  fullText: string = ''; // Make sure this is declared

  constructor(private readonly route: ActivatedRoute,
    private readonly dataService: DataService,
    public readonly commonService: CommonService,
    private readonly navController: NavController,
    private readonly authService: AuthService,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly cdr: ChangeDetectorRef,
    private location: Location
  ) { }

  ngOnInit() {
    setTimeout(() => {
      this.showButton = true;
      this.cdr.detectChanges();
    }, 3000);
  }

  updateCampaignDetails(): void {
    this.imageSrc = this.getImageSrc();
  }

  ionViewWillEnter() {
    this.isCollapsed = true;

    this.user = this.authService.getUser();
    this.walletId = history.state.walletId || null;
    this.campaignId = history.state.campaignId || null;
    this.fromScreen = history.state.fromScreen || null;
    this.successPaymentDetails = history.state.successPaymentDetails || null;
    this.campaignDetails();
    this.fetchAvailableCashDetails();

    if (this.fromScreen === 'my-campaigns') {
      this.backUrl = `/portal/my/${this.walletId}/compaigns`;
    } else if (this.fromScreen === 'notification-screen') {
      this.backUrl = `/portal/notification/list`;
    } else if (this.fromScreen === 'my-wallet') {
      this.backUrl = `/portal/my/wallet`;
    } else if (this.fromScreen === 'home-screen') {
      this.backUrl = `/dashboard`;
    } else {
      this.backUrl = `/portal/see/more/${this.walletId}/compaigns`;
    }
    // this.setImageSrc();
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

  campaignDetails() {
    this.responseReceived = false;
    this.dataService.campaignDetailId(this.campaignId).subscribe({
      next: (response: RestResponse) => {
        this.responseReceived = true;
        this.campaignDetail = response.data;
        if (this.responseReceived) {
          this.updateCampaignDetails();
        }
        if (this.campaignDetail?.description) {
          this.campaignDetail.description = this.campaignDetail.description.replace(/^"(.*)"$/, '$1');
          this.fullText = this.campaignDetail.description;
        }
        if (this.campaignDetail?.rewards) {
          this.campaignDetail.rewards = this.campaignDetail.rewards.replace(/^"(.*)"$/, '$1');
        }

        if (this.successPaymentDetails) {
          this.openSuccessPopup();
          return;
        }
      },
      error(error) {
        console.log("error", error)
      }
    });
  }

  fetchAvailableCashDetails() {
    const payload = {
      paymentType: "ADDITIONAL",
      expiryDays: 10
    };

    this.loading = "LOADING";
    this.dataService.availableCashDetails(payload).subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        const data = response.data;
        this.availableCashDetails = data;
      },
      error: (error: any) => {
        this.loading = "LOADED";
      },
    });
  }

  openAddMoneyPopup() {
    if (this.campaignDetail?.type === "BOOKING_CASHBACK") {
      this.openBookNowPopup();
      return;
    }

    if (this.campaignDetail?.type === "FREE_TRIP") {
      this.amount = this.campaignDetail?.minRechargeAmount > 0 ? this.campaignDetail?.minRechargeAmount : 0;
      setTimeout(() => {
        this.navController.navigateForward("/portal/select/payment/method", {
          animated: true,
          queryParams: {
            paymentType: 'ADDITIONAL',
            amount: this.amount,
            fromScreen: 'campaign-detail',
            myCampaignBackScreen: this.fromScreen,
            walletId: this.walletId,
            walletType: 'CAMPAIGN',
            campaigns: this.campaignDetail,
            successPaymentDetails: {} as any
          }
        });
      }, 300);
      // this.createOrder(this.campaignId, 'CAMPAIGN', this.amount)
      // return;
    }

    this.isAddMoney = ["WALLET_TOPUP"].includes(this.campaignDetail?.type);
    if (!this.isAddMoney) return;

    this.onClickValidation = false;
    this.amount = this.campaignDetail?.minRechargeAmount > 0 ? this.campaignDetail?.minRechargeAmount : "";
    this.selectedAmounts = [];
    this.userClearedInput = false;

    // Set the formatted displayAmount
    this.displayAmount = this.commonService.formatAmount(this.amount);
  }

  closeAddMoneyPopup() {
    this.isAddMoney = false;
  }

  handleInputChange(event: any, field: any) {
    let rawValue = event.target.value.replace(/,/g, ''); // Remove commas
    rawValue = rawValue.replace(/\D/g, ''); // Remove non-numeric characters

    if (rawValue === "") {
      this.amount = "";
      this.displayAmount = "";
      this.userClearedInput = true;
      this.selectedAmounts = [];
      field.control.setErrors({ customMinAmount: true, minAmount: this.campaignDetail?.minRechargeAmount });
      this.onClickValidation = true;
      return;
    }

    this.amount = parseInt(rawValue, 10);
    this.userClearedInput = false;

    // **Check minRechargeAmount validation**
    if (this.amount < this.campaignDetail?.minRechargeAmount) {
      field.control.setErrors({ customMinAmount: true, minAmount: this.campaignDetail?.minRechargeAmount });
      this.onClickValidation = true;
      return;
    } else {
      field.control.setErrors(null); // Clear errors if valid
    }

    this.displayAmount = this.commonService.formatAmount(this.amount);
  }

  selectAmount(amt: number, field: any) {
    this.amount = amt; // Set selected amount
    this.displayAmount = this.commonService.formatAmount(this.amount); // Format display

    this.userClearedInput = false; // Reset flag since the user clicked a button
    this.selectedAmounts = [amt]; // Store selected amount

    if (this.amount < this.campaignDetail?.minRechargeAmount) {
      field.controls.payment.setErrors({
        customMinAmount: true,
        minAmount: this.campaignDetail?.minRechargeAmount
      });
      return;
    }
  }

  get filteredPresetAmounts(): number[] {
    return this.presetAmounts.filter(amt => amt >= (this.campaignDetail?.minRechargeAmount || 0));
  }

  proceedToAddMoney(form: any) {
    this.onClickValidation = true;

    // **Check minRechargeAmount validation before proceeding**
    if (this.amount < this.campaignDetail?.minRechargeAmount) {
      form.controls.payment.setErrors({
        customMinAmount: true,
        minAmount: this.campaignDetail?.minRechargeAmount
      });
      return;
    }

    // **Check max amount validation (500000)**
    if (Number(this.amount) > 500000) {
      form.controls.payment.setErrors({
        customMaxAmount: true,
        maxAmount: `₹ ${this.commonService.formatNumber(500000)}`
      });
      return;
    }

    // setTimeout(() => {
    //   this.navController.navigateForward("/portal/select/payment/method", {
    //     animated: true,
    //     queryParams: {
    //       paymentType: 'ADDITIONAL',
    //       amount: this.amount,
    //       fromScreen: 'campaign-detail',
    //       walletId: this.walletId,
    //       walletType: 'WALLET_RECHARGE',
    //       campaigns: this.campaignDetail
    //     }
    //   });
    // }, 300);
    this.createOrder(this.campaignDetail?.id, 'WALLET_RECHARGE', this.amount)
  }

  async createOrder(campaignId: any, paymentType: any, amount: any) {
    this.isPaymentProcessing = true;
    const input = {} as any;
    if (this.campaignDetail?.type === 'WALLET_TOPUP') {
      input.walletId = this.walletId;
    }
    input.campaignId = campaignId;
    input.paymentType = paymentType;
    input.wallet = 0;
    input.promo = 0;
    input.convenienceFee = amount * (this.availableCashDetails.convenienceFeeConfigurationDetail.additional / 100);
    input.convenienceFeeGst = (input.convenienceFee * this.availableCashDetails.convenienceFeeConfigurationDetail.gstRate / 100);
    input.amount = amount;

    this.dataService.createOrder(input)
      .subscribe({
        next: (response: RestResponse) => {
          this.closeAddMoneyPopup();
          this.openPaymentPopup(response.data, paymentType);
          setTimeout((() => this.isPaymentProcessing = false));
        },
        error: (error) => {
          this.toastService.show(error.message);
          setTimeout((() => this.isPaymentProcessing = false));
        }
      });
  }

  async openPaymentPopup(data: any, paymentType: any) {
    const options: any = {
      key: environment.PaymentGateway.Key,
      description: paymentType,
      image: environment.PaymentGateway.Image,
      order_id: data.orderId,
      currency: environment.PaymentGateway.Currency,
      name: environment.PaymentGateway.Name,
      prefill: {
        email: this.user.email,
        contact: `${this.user.countryCode}${this.user.phoneNumber}`
      },
      theme: {
        color: '#29385B'
      }
    }
    try {
      let paymentResponse = (await Checkout.open(options));
      this.paymentProcessing(data, paymentResponse.response, paymentType);
    } catch (error: any) {
      this.isPaymentProcessing = false;
      if (!error) {
        return;
      }
      let errorObj = JSON.parse(error)
      if (!errorObj || !errorObj.description) {
        return;
      }
      this.toastService.show(errorObj.description);
    }
  }

  paymentProcessing(data: any, paymentResponse: any, paymentType: string) {
    const payload = {
      orderId: paymentResponse.razorpay_order_id,
      paymentId: paymentResponse.razorpay_payment_id,
      signature: paymentResponse.razorpay_signature
    }
    this.loadingService.show();
    const method = this.campaignDetail?.type === 'FREE_TRIP' ? 'freeTrip' : 'walletRecharge';
    this.dataService[method](payload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.isPaymentProcessing = false;

        this.closeAddMoneyPopup();

        this.successPaymentDetails = response.data;
        this.openSuccessPopup();

        //  this.campaignDetails();
      },
      error: (error) => {
        this.loadingService.hide();
        this.isPaymentProcessing = false;
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  // toggleText(textElement: HTMLElement, event: Event) {
  //   textElement.classList.toggle('expanded');
  //   // Change "See More" / "See Less"
  //   const seeMoreBtn = event.target as HTMLElement;
  //   seeMoreBtn.textContent = textElement.classList.contains('expanded') ? 'Read Less' : 'Read More';
  // }

  openSuccessPopup() {
    this.isSuccessfulResponse = true;
  }

  closeSuccessPopup() {
    this.isSuccessfulResponse = false;
  }

  openBookNowPopup() {
    this.isBookNow = true;
  }

  closeBookNowPopup() {
    this.isBookNow = false;
  }

  proceed() {
    this.closeBookNowPopup();

    setTimeout(() => {
      this.navController.navigateForward('/portal/search/hotels', {
        animated: true, queryParams: {
          bookingType: "OTHERS",
          campaignId: this.campaignId
        }
      });
    }, 200);
  }

  closeDetails() {
    this.closeSuccessPopup();
    //  this.campaignDetails();
    this.successPaymentDetails = null;

    // const { successPaymentDetails, ...newState } = history.state;

    const newState = { ...history.state, successPaymentDetails: null };
    history.replaceState(newState, '', this.location.path());

    // setTimeout(() => {
    //   this.navController.navigateForward(`/portal/see/more/${this.walletId}/compaigns`, { animated: true });
    // }, 200);
  }

  openDetails(successPaymentDetails: any) {
    this.closeSuccessPopup();

    setTimeout(() => {
      if (
        this.successPaymentDetails?.promo === 0 &&
        this.successPaymentDetails?.myCash === 0
      ) {
        //  this.campaignDetails();
        this.successPaymentDetails = null;

        const newState = { ...history.state, successPaymentDetails: null };
        history.replaceState(newState, '', this.location.path());
        return;
      }
      this.navController.navigateForward("/portal/my/wallet", {
        state: {
          successPaymentDetails: successPaymentDetails
        },
        animated: true
      });
      this.successPaymentDetails = null;
    }, 200);
  }

  get participantIcon(): string {
    switch (this.campaignDetail?.participantStatus) {
      case 'WINNER':
        return '/assets/images/svg/trophy.svg';
      case 'MISSED':
        return '/assets/images/svg/lose.svg';
      case 'PARTICIPANT':
        return '/assets/images/svg/gift-box.svg';
      default:
        // return '/assets/images/svg/trophy.svg';
        return '/assets/images/svg/gift-box.svg';
    }
  }

  get participantMessage(): { title: string; text: string } | null {
    switch (this.campaignDetail?.participantStatus) {
      case 'WINNER':
        return {
          title: 'Congratulations!',
          text: 'You won an exciting free trip offer! Can’t wait for this amazing adventure!'
        };
      case 'PARTICIPANT':
        return {
          title: 'Thank You!',
          text: 'For participating in the campaign! Your results are on the way.'
        };
      case 'MISSED':
        return {
          title: "Don't worry-",
          text: 'Better luck next time! Keep trying, and your adventure awaits!'
        };
      default:
        return null;
    }
  }

  get formattedCampaignType(): string {
    return this.campaignDetail?.type
      ? this.campaignDetail.type.replace('_', ' ').toLowerCase().replace(/\b\w/g, (char: any) => char.toUpperCase())
      : '';
  }

  convertMonths(months: number): string {
    if (!months || months <= 0) {
      return 'N/A';
    }

    if (months === 12) {
      return '1 Year';
    } else if (months > 12) {
      const years = Math.floor(months / 12);
      const remainingMonths = months % 12;
      return remainingMonths === 0 ? `${years} Years` : `${years} Years ${remainingMonths} Months`;
    } else {
      return `${months} Months`;
    }
  }

  getImageSrc(): string {
    switch (this.campaignDetail?.type) {
      case 'FREE_TRIP':
        return '/assets/images/svg/travel-agency.svg';
      case 'WALLET_TOPUP':
        return '/assets/images/svg/wallet.svg';
      default:
        return '/assets/images/svg/booking.svg';
    }
  }

  toggleText() {
    this.isCollapsed = !this.isCollapsed;
  }

  get displayText(): string {
    if (this.fullText?.length <= this.limit) {
      return this.fullText;
    }
    return this.isCollapsed
      ? this.fullText.substring(0, this.limit) + '...'
      : this.fullText;
  }

}
