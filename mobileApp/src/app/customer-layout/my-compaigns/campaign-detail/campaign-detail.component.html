<div class="customer-dashboard-page compaign-detail">

  <div class="customer-body-section no-padding campaign-background-scroll">
    @if (loading==='LOADING') {
    <div class="my-booking-card-container">
      <div class="my-booking-card margin-top-10">
        <ion-grid>
          <ion-row>
            <ion-col>
              <ion-row class="check-in-out-con my-bookings-check">
                <ion-col size="8" class="my-booking-code ion-no-padding">
                  <div class="my-booking-text">
                    <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                  </div>
                </ion-col>
                <ion-col size="4" class="my-booking-status">
                  <div class="my-booking-text">
                    <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                  </div>
                </ion-col>
              </ion-row>

              <div>
                <div class="hotel-name my-bookings-hotel-name">
                  <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                </div>
                <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                    style="width: 200px"></ion-skeleton-text></div>
              </div>
            </ion-col>
          </ion-row>

          <ion-row class="check-in-out-con">
            <ion-col size="5">
              <div class="check-in-con">
                <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                </div>
                <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                </div>
              </div>
            </ion-col>
            <ion-col size="2">
              <div class="time-duration-con">
                <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                <div class="time-duration">
                  <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                </div>
              </div>
            </ion-col>
            <ion-col size="5">
              <div class="check-out-con">
                <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                </div>
                <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                </div>
              </div>
            </ion-col>
          </ion-row>

          <ion-row>
            <ion-col>
              <div class="guest-and-rooms-con">
                <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                </div>
                <div class="rooms">
                  <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                </div>
              </div>
            </ion-col>
          </ion-row>

        </ion-grid>
      </div>
    </div>
    }
    @else if (loading==='LOADED') {
    <div class="compaign-wrapper" *ngIf="responseReceived && campaignDetail">
      <div class="compaign-card-header-new">
        <div class="compaign-image-slide" *ngFor="let attachment of campaignDetail?.attachments">
          <img *ngIf="attachment.path" [src]="attachment.path" alt="">
        </div>
        <div class="compaign-image-slide"
          *ngIf="!campaignDetail?.attachments || campaignDetail.attachments.length === 0">
          <img
            [src]="campaignDetail?.type === 'FREE_TRIP' ? '/assets/images/svg/freetrip_aero.svg' : campaignDetail?.type === 'WALLET_TOPUP' ? '/assets/images/svg/reward_coin.svg' : '/assets/images/svg/booking_aeroplane.svg'"
            alt="Default Campaign Image">
        </div>
      </div>
      <div class="header-compaign-container">
        <app-customer-header [innerPage]="true" [headingText]="'Campaign Detail'"
          (rightActionCallback)="openNotifications()" [backUrl]="backUrl"></app-customer-header>
      </div>
      <div class="scrollable-card margin-bottom-15" *ngIf="responseReceived && campaignDetail">
        <!-- <div> -->
        <div class="compaign-card">
          <div class="compaign-detail-container text-container">
            <div class="padding-section">
              <div class="image-reward-type">
                <img [src]="imageSrc" alt="Default Campaign Image">
                <span class="margin-left-5 margin-top-5">{{ formattedCampaignType}}</span>
              </div>
              <div class="compaign-detail-title margin-top-10">
                <span>{{campaignDetail?.title}}</span>
              </div>
              <hr class="separator-line-campgaigns" />
              <div class="read-more-container">
                <p class="compaign-detail-desc margin-bottom-5">
                  {{ displayText }}
                  <a (click)="toggleText()" class="see-more" *ngIf="(fullText?.length || 0) >= limit">
                    {{ isCollapsed ? 'Read More' : 'Read Less' }}
                  </a>
                </p>
              </div>
            </div>
            <!-- congratulations-section -->
            <div class="compaign-card-bg-trophy margin-top-20"
              *ngIf="campaignDetail?.type === 'FREE_TRIP' && participantMessage">
              <!-- <img src="/assets/images/svg/group.svg" class="background-svg" alt="group"> -->

              <div class="congratulations-content-trophy">
                <!-- <div class="overlay-svg"> -->
                <img [src]="participantIcon" alt="icon" height="58">
                <!-- </div> -->
                <span class="text-content-trophy">
                  <b>{{ participantMessage.title }}</b> {{ participantMessage.text }}
                </span>
              </div>
            </div>

            <div class="congratulations-section margin-top-15" *ngIf="campaignDetail?.type === 'FREE_TRIP'">
              <img src="/assets/images/svg/note_notext.svg" class="background-svg" alt="group">

              <div class="congratulations-content">
                <div class="rupee-note-text">
                  <span>{{campaignDetail?.minRechargeAmount| currency :
                    'INR' : 'symbol' : '1.0-2'}}/-</span>
                  <div class="fee-info"><span>*Fees to participate in the offer</span></div>
                </div>
              </div>
            </div>

            <div class="margin-top-5 padding-congratulations-container compaign-card-bg"
              *ngIf="campaignDetail?.type === 'BOOKING_CASHBACK' || campaignDetail?.type === 'WALLET_TOPUP'">
              <!-- <img src="/assets/images/svg/group_booking.svg" class="background-svg" alt="group"> -->
              <div class="congratulations-content-money">
                <div class="overlay-svg">
                  <img src="/assets/images/svg/money-bag.svg" width="38" height="48" alt="money-bag">
                </div>
                <span class="text-content-money">
                  {{ campaignDetail?.myCash + campaignDetail?.promoCash }}% Cashback,
                  <span *ngIf="campaignDetail?.myCash > 0">
                    {{ campaignDetail?.myCash }}% in My Cash
                  </span>
                  <span *ngIf="campaignDetail?.myCash > 0 && campaignDetail?.promoCash > 0"> and </span>
                  <span *ngIf="campaignDetail?.promoCash > 0">
                    {{ campaignDetail?.promoCash }}% in Promo Cash.
                  </span>
                </span>
              </div>
            </div>
            <div class="eligibility-card">
              <div class="eligibility margin-top-10">
                <span>Eligibility:</span>
                <div class="margin-top-5 no-font-weight">
                  <span
                    *ngIf="campaignDetail?.type === 'FREE_TRIP' && campaignDetail?.eligibility">{{campaignDetail?.eligibility}}</span>
                  <!-- <span *ngIf="campaignDetail?.description?.length > 100" class="see-more" (click)="toggleText(eligi,$event)">
                  Read More
                </span> -->
                  <span *ngIf="campaignDetail?.type === 'FREE_TRIP' && !campaignDetail?.eligibility">Participate in the
                    campaign by
                    paying a minimum entry fee of {{campaignDetail?.minRechargeAmount | currency :
                    'INR' : 'symbol' : '1.0-2'}}/-</span>
                  <span *ngIf="campaignDetail?.type === 'WALLET_TOPUP'">Recharge must be above
                    <b>{{campaignDetail?.minRechargeAmount | currency :
                      'INR' : 'symbol' : '1.0-2'}}/-</b>. Open to all users.</span>
                  <span *ngIf="campaignDetail?.type === 'BOOKING_CASHBACK'">Minimum booking amount of
                    <b>{{campaignDetail?.minRechargeAmount | currency :
                      'INR' : 'symbol' : '1.0-2'}}/-</b>. Open to all users making a new booking during the campaign
                    period.</span>
                </div>
              </div>
              <div class="left-half-circle">
                <span class="card-half-circle"></span>
              </div>
              <div class="right-half-circle">
                <span class="card-half-circle"></span>
              </div>
            </div>
            <div class="compaign-card-container margin-top-10">
              <div class="compaign-card"
                *ngIf="campaignDetail?.type === 'FREE_TRIP' || campaignDetail?.type === 'BOOKING_CASHBACK'">
                <span class="compaign-max-participant">Max Participants</span>
                <div class="compaign-max-participant-container margin-top-10">
                  <ion-icon class="max-participant-icon" src="/assets/images/svg/user-icon.svg" slot="start"></ion-icon>
                  <span class="max-participant-text" *ngIf="campaignDetail?.maxParticipants > 0">
                    {{ campaignDetail?.maxParticipants }}
                  </span>
                  <span class="max-participant-text" *ngIf="!(campaignDetail?.maxParticipants > 0)">
                    Unlimited
                  </span>
                </div>
                <span *ngIf="campaignDetail?.type === 'BOOKING_CASHBACK'"
                  class="day-time-text margin-top-10">Participant Limit</span>
                <span *ngIf="campaignDetail?.type === 'FREE_TRIP'" class="day-time-text margin-top-10">User Limit</span>
              </div>
              <div class="compaign-card start-date-padding" *ngIf="campaignDetail?.type === 'WALLET_TOPUP'">
                <span class="compaign-max-participant">Minimum Recharge</span>
                <span class="start-date-text end-date margin-top-10">{{campaignDetail?.minRechargeAmount| currency :
                  'INR' : 'symbol' : '1.0-2'}}</span>
                <span class="day-time-text margin-top-10">Limit</span>
              </div>
              <div class="compaign-card start-date-padding" *ngIf="campaignDetail?.type === 'FREE_TRIP'">
                <span class="compaign-max-participant">Free Trip Value</span>
                <span class="start-date-text margin-top-10">{{campaignDetail?.maxRewardValue | currency :
                  'INR' : 'symbol' : '1.0-2'}}</span>
                <span class="day-time-text margin-top-10">Max Value</span>
              </div>
              <div class="compaign-card start-date-padding"
                *ngIf="campaignDetail?.type === 'WALLET_TOPUP' || campaignDetail?.type === 'BOOKING_CASHBACK'">
                <span class="compaign-max-participant">Redeemed Expiry Date</span>
                <span
                  class="start-date-text end-date margin-top-10">{{convertMonths(campaignDetail?.expiryMonths)}}</span>
                <span class="day-time-text margin-top-10">Expiry Date</span>
              </div>
              <div class="compaign-card start-date-padding">
                <span class="compaign-max-participant">End Date</span>
                <span
                  class="start-date-text end-date margin-top-10">{{commonService.formatDateForCampaignEndDate(campaignDetail?.endDate).date}}</span>
                <span
                  class="day-time-text margin-top-10">{{commonService.formatDateForCampaignEndDate(campaignDetail?.endDate).time
                  }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ion-button class="grab-deal-button" expand="full" shape="round" type="submit"
      *ngIf="responseReceived && campaignDetail && campaignDetail?.canGrab === true && campaignDetail?.status === 'ACTIVE'"
      (click)="openAddMoneyPopup()">
      <div class="grab-deal-container">
        <img src="/assets/images/svg/grab-deal.svg" alt="Pay Now" width="24px" />
        <span class="grab-deal-text">{{ campaignDetail?.type === 'BOOKING_CASHBACK' ? 'Book your stay now' : 'Grab the
          deal'
          }}</span>
      </div>
    </ion-button>

    <div class="compaign-offer-end-conatiner"
      *ngIf="responseReceived && campaignDetail?.type === 'WALLET_TOPUP' || campaignDetail?.type === 'FREE_TRIP'">
      <span class="offer-end-text">
        Offer ends on <strong>{{commonService.formatDateForCampaignEndDate(campaignDetail?.endDate).date}}
        </strong>
      </span>
    </div>
    <div class="font-cashback" *ngIf="responseReceived && campaignDetail?.type === 'BOOKING_CASHBACK'">
      <span *ngIf="campaignDetail?.maxParticipants > 0">
        Offer valid for the first <b>{{ campaignDetail?.maxParticipants }}</b> customers only.
      </span>
      <span *ngIf="!(campaignDetail?.maxParticipants > 0)">
        Offer valid for <b>Unlimited</b> customers.
      </span>
    </div>
    }
  </div>
</div>

<ion-modal class="site-custom-popup job-invitation-popup" #isPaidPopup [isOpen]="isAddMoney" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeAddMoneyPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">

        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          <div class="popup-large-heading">Recharge Now</div>
          <div class="popup-normal-heading secondary-text">Add funds to your wallet and use for payments.
          </div>
          <div class="job-info margin-top-10">

            <div class="margin-top-20">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': payment.invalid && onClickValidation}">
                <ion-input label="Enter Amount" labelPlacement="floating" required="required" name="payment"
                  #payment="ngModel" [(ngModel)]="displayAmount" type="tel"
                  (ionInput)="handleInputChange($event,payment)" (keypress)="commonService.restrictInput($event)">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="payment"
                [onClickValidation]="onClickValidation"></app-validation-message>
            </div>
            <span class="convenience-text" *ngIf="campaignDetail?.type==='WALLET_TOPUP'">Convenience fee will be
              included.</span>

            <!-- <div class="amount-selection-container-wallet-modal">
              <div *ngFor="let amt of presetAmounts" class="amount-selection-modal margin-top-15" [ngClass]="{'selected-amount': selectedAmounts.includes(amt),
              'unselected-amount': selectedAmounts.length > 0 && !selectedAmounts.includes(amt)
               }" (click)="selectAmount(amt,recordForm.form)">
                <span class="amount-text">{{ amt | currency : 'INR' : 'symbol' : '1.0-0' }}</span>
              </div>
            </div> -->

            <div class="amount-selection-container-wallet-modal" *ngIf="filteredPresetAmounts.length > 0">
              <div *ngFor="let amt of filteredPresetAmounts" class="amount-selection-modal margin-top-15" [ngClass]="{'selected-amount': selectedAmounts.includes(amt),
                               'unselected-amount': selectedAmounts.length > 0 && !selectedAmounts.includes(amt)
                              }" (click)="selectAmount(amt, recordForm.form)">
                <span class="amount-text">{{ amt | currency : 'INR' : 'symbol' : '1.0-0' }}</span>
              </div>
            </div>

            <div class="privacy-container margin-top-10">
              <ion-button class="site-full-rounded-button less-border-radius" expand="full" shape="round" type="submit"
                [disabled]="isPaymentProcessing" (click)="proceedToAddMoney(recordForm.form)">
                Proceed & add money
              </ion-button>
            </div>

          </div>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>


<ion-modal class="site-custom-popup job-invitation-popup" #isSuccessPopup [isOpen]="isSuccessfulResponse"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-body no-padding-top">

        <div class="campaign-card">
          <div class="campaign-card-header success-modal-card-header">
            <div class="campaign-image-slide success-modal-card-image-slide">
              <i-feather name="X" class="close-icon white-icon" (click)="closeDetails()"></i-feather>
            </div>
          </div>
          <div class="coin-container">
            <ion-icon class="balance-icon" [src]="'/assets/images/svg/success-icon.svg'"></ion-icon>
            <img [src]="'/assets/images/svg/reward.gif'" class="reward-overlay">
          </div>
          <div class="campaign-detail-container">
            <span class="campaign-detail-title success-text">Congratulations!</span>

            <div class="success-container margin-top-10" *ngIf="successPaymentDetails">
              <span class="earned-text" *ngIf="successPaymentDetails?.myCash > 0 || successPaymentDetails?.promo > 0">
                You've earned</span>

              <span class="earned-my-cash my-cash margin-top-7" *ngIf="successPaymentDetails?.myCash > 0">
                ₹{{ successPaymentDetails?.myCash }}
                <span class="cash-label">MyCash</span>
              </span>

              <span class="earned-promo-cash margin-top-2" *ngIf="successPaymentDetails?.promo > 0">
                ₹{{ successPaymentDetails?.promo }}
                <span>PromoCash</span>
              </span>
            </div>

          </div>

          <div class="detail-button" (click)="openDetails(successPaymentDetails)">
            <span class="detail-text">{{ successPaymentDetails?.promo === 0 && successPaymentDetails?.myCash === 0 ?
              'View Detail' : 'Go To Wallet' }}</span>
          </div>

          <!-- <div class="privacy-container margin-top-20">
            <ion-button class="site-full-rounded-button less-border-radius margin-left-20 margin-right-20" expand="full"
              shape="round" type="submit" (click)="openDetails(successPaymentDetails)">
              {{ successPaymentDetails?.promo === 0 && successPaymentDetails?.myCash === 0 ? 
              'View Detail' : 'Go To Wallet' }}
            </ion-button>
          </div> -->

        </div>

      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #isBookNowPopup [isOpen]="isBookNow"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header book-now-padding">
        <i-feather name="X" (click)="closeBookNowPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">

        <div class="popup-large-heading book-now-heading">Important Update!</div>
        <div class="book-now-medium-heading margin-top-10 margin-bottom-15">Make a booking above
          <strong>{{campaignDetail?.minRechargeAmount |
            currency :
            'INR' : 'symbol' : '1.0-2'}}/-</strong> to unlock this deal.
        </div>
        <div class="job-info">

          <div class="privacy-container proceed-button">
            <ion-button class="site-full-rounded-button less-border-radius" expand="full" shape="round" type="submit"
              (click)="proceed()">
              Proceed
            </ion-button>
          </div>

        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>