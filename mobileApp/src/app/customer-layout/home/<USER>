import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IonModal, NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import { SwiperOptions } from 'swiper';
import { SwiperComponent } from 'swiper/angular';
import SwiperCore, { Autoplay, Pagination, Zoom } from 'swiper';

SwiperCore.use([Autoplay, Pagination, Zoom]);

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class HomeComponent implements OnInit, OnDestroy {
  @ViewChild(IonModal) loginModal!: IonModal;
  @ViewChild('slides', { static: false })

  swiper?: SwiperComponent;
  currentIndex: number = 0;
  loginIsOpened: boolean = false;
  defaultSliderConfig: SwiperOptions = {
    slidesPerView: 1,
    pagination: {
      clickable: true,
      enabled: true
    },
    zoom: true
  };

  activeCampaignConfig: SwiperOptions = {
    slidesPerView: 1,
    pagination: {
      clickable: true,
      enabled: true
    },
    autoplay: {
      delay: 3000,  // 3 seconds delay
      disableOnInteraction: false
    },
    loop: true,
    zoom: true
  };

  isNoPackageAvailablePopupOpen: boolean = false;
  isNoOnboarding: boolean = false;
  isPanCard: boolean = false;
  panCard: string | null = null;
  bookingTypeInSkip!: string;
  onClickValidation: boolean = false;
  user: any;
  activePackage: any;
  panCardAvailable: any;
  walletId: string = '';
  IsPackageFromSuccessPage: boolean = false;
  loading: string = "NOT_STARTED";
  dashboardResponse: any;
  profileImageUrl: string | null = null;
  selectBalanceOption: string | null = null;
  homeBackScreen: string | null = 'home-screen';
  packageDetail: any;

  panCardMask = this.commonService.panCardMask;
  maskPredicate = this.commonService.maskPredicate;
  hasLoad: boolean = true;

  randomCampaignDetail: any;
  responseReceived: boolean = false;
  isRandomCampaignModalOpen: boolean = false;

  constructor(private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly authService: AuthService,
    private readonly dataService: DataService,
    private readonly changeDetectorRef: ChangeDetectorRef,
    public commonService: CommonService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly route: ActivatedRoute,
    private router: Router,
  ) {
  }

  async ngOnInit() {
    this.user = this.authService.getUser();
    this.route.queryParams.subscribe((params) => {
      const currentNavigation = this.router.getCurrentNavigation();
      if (currentNavigation?.extras?.state) {
        this.IsPackageFromSuccessPage = currentNavigation.extras.state as any;
        this.fetchMyActiveSubscription();
      }
    });
  }

  ionViewWillEnter() {
    this.hasLoad = false;
    this.user = this.authService.getUser();
    this.loginIsOpened = this.localStorageService.get('isLoginOpened') === 'true';
    setTimeout(() => {
      this.hasLoad = true;
    })
    if (!this.loginIsOpened) {
      this.authService.logout();
      if (!this.commonService.hasLoginModalBeenShown()) {
        this.commonService.openLoginModal();
        this.commonService.setLoginModalShown();

      }
    }
    // this.commonService.openLoginModal();
    this.panCardAvailable = this.localStorageService.get('panCard') || this.user?.panCard;
    if (this.user) {
      this.walletId = this.user.walletId;
    }
    this.fetchMyActiveSubscription();
    this.getDashboardDetails();

    if (!this.commonService.hasRandomCampaignBeenFetched()) {
      this.commonService.setRandomCampaignFetched();  // Mark immediately
      setTimeout(() => {
        this.fetchRandomCampaigns();
      }, 3000);
    }
  }

  openRandomCampaignModal() {
    this.isRandomCampaignModalOpen = true;
  }

  closeRandomCampaignModal() {
    this.isRandomCampaignModalOpen = false;
  }

  fetchRandomCampaigns() {
    this.responseReceived = false;
    this.loading = "LOADING";
    this.dataService.fetchRandomCampaigns().subscribe({
      next: (response: RestResponse) => {
        this.loading = "LOADED";
        this.responseReceived = true;
        this.openRandomCampaignModal();
        this.randomCampaignDetail = response.data;

        //  this.commonService.closeLoginModal();
      },
      error: (error: any) => {
        this.loading = "LOADED";
      }
    })
  }

  async randomCampaignDetailButton() {
    this.closeRandomCampaignModal();
    if (!this.user) {
      this.localStorageService.set('compaignsWithoutLogin', true);
      this.localStorageService.set('campaignId', this.randomCampaignDetail?.id);
      //  this.commonService.openLoginModal();

      // Close any existing login modal and open fresh
      await this.commonService.openLoginModal();
      return;
    }
    setTimeout(() => {
      this.navController.navigateForward('/portal/my/campaign/details', {
        state: {
          campaignId: this.randomCampaignDetail?.id,
          walletId: this.walletId,
          fromScreen: this.homeBackScreen
        },
        animated: true
      });
    }, 200);
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

  fetchMyActiveSubscription() {
    this.activePackage = null;
    //  this.loadingService.show();
    this.dataService.fetchMyActiveSubscription().subscribe({
      next: (response: RestResponse) => {
        //   this.loadingService.hide();
        this.activePackage = response.data;
        this.localStorageService.setObject("activeSubscriptionData", response.data);
        this.localStorageService.setObject('coApplicantDetail', response.data.coApplicantDetail);
      },
      error: (error: any) => {
        //   this.loadingService.hide();
      }
    })
  }

  ngOnDestroy() { }

  getDashboardDetails() {
    this.loading = "LOADING";
    this.dataService.getDashboardDetails()
      .subscribe({
        next: (response: RestResponse) => {
          this.loading = "LOADED";
          this.dashboardResponse = response.data;
          this.localStorageService.set('profileImageUrl', this.dashboardResponse?.profileImageUrl || '/assets/images/icons/user.png');
          if (this.dashboardResponse?.packageDetails) {
            this.packageDetail = this.dashboardResponse?.packageDetails;
          }
          if (
            this.dashboardResponse?.activeCampaignDetails &&
            this.dashboardResponse?.activeCampaignDetails.length > 0 &&
            this.dashboardResponse?.activeCampaignDetails[0]?.totalCount <= 1
          ) {
            this.activeCampaignConfig.pagination = false;
          }
        },
        error: (error: any) => {
          this.loading = "LOADED";
        },
      });
  }

  openActiveCompaigns() {
    if (!this.user) {
      this.localStorageService.set('activeCampaignWithoutLogin', true);
      this.commonService.openLoginModal();
      return;
    }
    this.navController.navigateForward(`/portal/see/more/${this.walletId}/compaigns`, {
      state: {
        backScreen: this.homeBackScreen
      },
      animated: true
    });
  }

  seeCampaignDetail(campaignId: any) {
    if (!this.user) {
      this.localStorageService.set('compaignsWithoutLogin', true);
      this.localStorageService.set('campaignId', campaignId);
      this.commonService.openLoginModal();
      return;
    }
    this.navController.navigateRoot(`/portal/my/campaign/details/${campaignId}`, {
      state: {
        campaignId: campaignId,
        walletId: this.walletId,
        fromScreen: this.homeBackScreen
      },
      animated: true
    });
  }

  openMyWallet() {
    this.navController.navigateForward("/portal/my/wallet", {
      state: {
        userSelectBalanceOption: this.selectBalanceOption,
        backScreen: this.homeBackScreen
      },
      animated: true
    });
  }

  openPanCardModel() {
    this.isPanCard = true;
  }

  convertToUpperCase(event: any) {
    const value = this.commonService.convertToUpperCase(event); // Use CommonService to convert input
    this.panCard = value; // Update the model
  }

  closePanCardModel() {
    this.isPanCard = false;
  }

  openBookingPage(bookingType: string) {
    if (!this.user) {
      this.localStorageService.set('bookingTypeNoLogin', 'MEMBERSHIP');
      this.commonService.openLoginModal();
      return;
    }
    const hasSkippedPanCard = this.localStorageService.get('skipPanCard') || this.user?.skipPanCard;
    if (
      this.user?.countryCode === '+91' &&
      (!this.panCardAvailable || this.panCardAvailable?.trim() === "") &&
      !hasSkippedPanCard
    ) {
      this.isPanCard = true; // Open the PAN card modal
      this.bookingTypeInSkip = bookingType;
      return;
    }

    if (bookingType === "OTHERS") {
      this.redirectToBookingPage(bookingType);
      return;
    }

    //getting issue here
    if (!this.activePackage || !this.activePackage.isBookingAllowed || this.activePackage.status !== "ACTIVE") {
      this.isNoPackageAvailablePopupOpen = true;
      return;
    }
    if (this.activePackage && !this.user?.isOnBoardingComplete) {
      this.openNoOnboardingPopup();
      return;
    }
    this.redirectToBookingPage(bookingType);
  }

  onSkipPanCard() {
    this.isPanCard = false;

    const payload = {
      panCard: null,
      skipPanCard: true,
    };

    this.user.skipPanCard = true;
    this.localStorageService.set('skipPanCard', true);

    //  this.loadingService.show();
    this.dataService.updatePanCard(payload).subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        this.redirectBasedOnBooking();
      },
      error: (error: any) => {
        //  this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred while saving PAN card preference.');
      },
    });
  }

  savePanCard(form: any) {
    this.onClickValidation = !form.valid;

    if (!form.valid) {
      return;
    }

    const payload = {
      panCard: this.panCard || null,
      skipPanCard: false,
    };

    // this.loadingService.show();
    this.dataService.updatePanCard(payload).subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        this.isPanCard = false;

        this.panCardAvailable = this.panCard;
        this.localStorageService.set('panCard', this.panCard);
        this.user.panCard = this.panCard; // Update the user object
      },
      error: (error: any) => {
        //  this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred while saving the PAN card.');
      },
    });
  }

  redirectBasedOnBooking() {
    if (this.bookingTypeInSkip === "OTHERS") {
      this.redirectToBookingPage(this.bookingTypeInSkip);
      return;
    }

    if (!this.activePackage || !this.activePackage.isBookingAllowed || this.activePackage.status !== "ACTIVE") {
      this.isNoPackageAvailablePopupOpen = true;
      return;
    }

    this.redirectToBookingPage(this.bookingTypeInSkip);
  }

  redirectToBookingPage(bookingType: string) {
    this.navController.navigateForward('/portal/search/hotels', { animated: true, queryParams: { bookingType: bookingType } });
  }

  closeNoPackageAvailablePopup() {
    this.isNoPackageAvailablePopupOpen = false;
  }

  openNoOnboardingPopup() {
    this.isNoOnboarding = true;
  }

  closeNoOnboardingPopup() {
    this.isNoOnboarding = false;
  }

  continueToOnboarding() {
    this.closeNoOnboardingPopup();
    setTimeout(() => {
      this.navController.navigateForward(`/account/register/basic`, {
        state: {
          fromScreen: 'dashboard'
        }, animated: true
      });
    }, 200);
  }

  explorePlan() {
    this.isNoPackageAvailablePopupOpen = false;
    this.navController.navigateForward("account/register/package/selection", { animated: true });
  }

  subscribePlan() {
    if (!this.user) {
      this.localStorageService.set('subscribeNowWithoutLogin', true);
      this.commonService.openLoginModal();
      this.localStorageService.setObject('packageDetail', this.packageDetail);
      return;
    }
    // this.navController.navigateForward('/account/register/package/selection', {
    //   state: {
    //     fromScreen: this.homeBackScreen
    //   },
    //   animated: true
    // });
    this.navController.navigateForward(`/account/register/package/${this.packageDetail.id}/${this.homeBackScreen}/pay`, { state: this.packageDetail, animated: true });
  }

  onSlideChanged($event: any): void {
    if (!this.swiper) {
      return;
    }
    this.currentIndex = this.swiper.swiperRef.activeIndex;
    this.changeDetectorRef.detectChanges();
  }

  openSearchBooking(bookingType: string) {
    this.localStorageService.set('bookingWithoutLogin', bookingType);
    this.navController.navigateRoot('/portal/search/hotels', {
      queryParams: {
        reset: true
      }
    });
  }

}
