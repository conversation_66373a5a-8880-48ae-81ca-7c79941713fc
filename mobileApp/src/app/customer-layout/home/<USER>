<ion-content class="customer-dashboard-page">
  <app-customer-header [rightAction]="false" (rightActionCallback)="openNotifications()"
    [profileImageUrl]="dashboardResponse?.profileImageUrl" [isLogggedIn]="true" *ngIf="hasLoad"></app-customer-header>
  <div class="customer-body-section has-header-section no-padding">
    @if (loading==='LOADING') {
    <div class="slider-container">
      <swiper [config]="defaultSliderConfig">
        <ng-template swiperSlide *ngFor="let i of [1,2,3]">
          <div class="slide">
            <ion-skeleton-text [animated]="true"
              style="width: 100%; height: 200px; display: block;"></ion-skeleton-text>
            <div class="slide-inner-container"></div>
            <div class="image-info">
              <div class="image-info-container">
                <div class="text-container">
                  <div class="image-text main-heading">
                    <ion-skeleton-text [animated]="true" style="width: 150px;"></ion-skeleton-text>
                  </div>
                  <div class="image-text inner-heading">
                    <ion-skeleton-text [animated]="true" style="width: 200px;"></ion-skeleton-text>
                  </div>
                </div>
                <div class="rating-card">
                  <ion-icon [animated]="true" style="width: 30px;"></ion-icon>
                  <ion-skeleton-text [animated]="true" style="width: 30px;"></ion-skeleton-text>
                </div>
              </div>
            </div>
          </div>
        </ng-template>
      </swiper>
    </div>
    <div class="balance-card">
      <div class="balance-card-inner-container">
        <div class="balance-card-container">
          <span class="bal-text">
            <ion-skeleton-text [animated]="true" style="width: 60px;"></ion-skeleton-text>
            <ion-skeleton-text [animated]="true" style="width: 80px;"></ion-skeleton-text>
          </span>
        </div>
        <div class="balance-card-container">
          <span class="bal-text">
            <ion-skeleton-text [animated]="true" style="width: 60px;"></ion-skeleton-text>
            <ion-skeleton-text [animated]="true" style="width: 80px;"></ion-skeleton-text>
          </span>
        </div>
      </div>
    </div>
    <div class="holiday-selection-container">
      <div class="other-holiday-selection-card margin-top-10">
        <div class="other-holiday-selection-card-container">
          <div class="book-your-hotel-container">
            <span class="book-hotel-text">
              <ion-skeleton-text [animated]="true" style="width: 100px;"></ion-skeleton-text>
            </span>
            <span class="margin-top-15">
              <ion-skeleton-text [animated]="true" style="width: 120px;"></ion-skeleton-text>
            </span>
            <span>
              <ion-skeleton-text [animated]="true" style="width: 100px;"></ion-skeleton-text>
            </span>
            <div class="offers-text">
              <span class="click-here">
                <ion-skeleton-text [animated]="true" style="width: 80px;"></ion-skeleton-text>
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="active-campaign-container">
        <swiper [config]="activeCampaignConfig">
          <ng-template swiperSlide *ngFor="let i of [1,2,3]">
            <div class="active-campaign">
              <!-- Header Image Skeleton -->
              <div class="active-campaign-card-header">
                <ion-skeleton-text [animated]="true"
                  style="width: 100%; height: 120px; display: block; border-radius: 12px;"></ion-skeleton-text>
              </div>

              <!-- Campaign Detail Skeleton -->
              <div class="active-campaign-detail-container">
                <div class="active-campaign-text-container">
                  <ion-skeleton-text [animated]="true"
                    style="width: 24px; height: 24px; border-radius: 50%;"></ion-skeleton-text>
                  <ion-skeleton-text [animated]="true"
                    style="width: 100px; height: 16px; margin-top: 3px;"></ion-skeleton-text>
                  <hr class="active-campaign-vertical-separator" />
                  <ion-skeleton-text [animated]="true"
                    style="width: 20px; height: 16px; margin-top: 3px;"></ion-skeleton-text>
                </div>
                <div class="additional-info margin-top-3 margin-right-5">
                  <div class="campaign-container">
                    <ion-skeleton-text [animated]="true" style="width: 70px; height: 14px;"></ion-skeleton-text>
                    <ion-skeleton-text [animated]="true"
                      style="width: 18px; height: 18px; border-radius: 50%; margin-left: 8px;"></ion-skeleton-text>
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
        </swiper>
      </div>

      <div class="membership-card-wrapper">
        <span>
          <ion-skeleton-text [animated]="true" style="width: 80px;"></ion-skeleton-text></span>
        <div class="membership-holiday-selection-card margin-top-10">
          <div class="membership-text">
            <div class="membership-title">
              <ion-skeleton-text [animated]="true" style="width: 100px;"></ion-skeleton-text>
            </div>
            <div class="membership-title holiday">
              <ion-skeleton-text [animated]="true" style="width: 100px;"></ion-skeleton-text>
            </div>
          </div>
          <div class="offers-text">
            <ion-skeleton-text [animated]="true" style="width: 80px;"></ion-skeleton-text>
          </div>
        </div>
      </div>
      <!-- <div class="other-holiday-selection-card skeleton-less-padding margin-top-5">
        <ion-skeleton-text [animated]="true" style="width: 40px; height: 40px; border-radius: 50%;"></ion-skeleton-text>
        <ion-skeleton-text [animated]="true" style="width: 150px;"></ion-skeleton-text>
      </div> -->

      <div class="package-subscribe-card margin-top-10">
        <div class="package-subscribe-card-container">
          <div class="package-subscribe-container">
            <ion-skeleton-text [animated]="true" style="width: 100px;"></ion-skeleton-text>
            <ion-skeleton-text [animated]="true" style="width: 140px;"></ion-skeleton-text>
            <ion-skeleton-text [animated]="true" style="width: 180px;"></ion-skeleton-text>
            <ion-skeleton-text [animated]="true"
              style="width: 100px; height: 40px; border-radius: 10px; margin-top: 10px;"></ion-skeleton-text>
          </div>
        </div>
      </div>

      <div class="travel-card margin-top-10">
        <div class="travel-card-container">
          <div class="travel-container">
            <div class="travel-feature-container">
              <ion-skeleton-text [animated]="true"
                style="width: 30px; height: 30px; border-radius: 50%;"></ion-skeleton-text>
              <span class="travel-text">
                <ion-skeleton-text [animated]="true" style="width: 100px;"></ion-skeleton-text>
              </span>
            </div>
            <span class="travel-working-text margin-top-15"><ion-skeleton-text [animated]="true"
                style="width: 100px;"></ion-skeleton-text></span>
            <span class="travel-working-text margin-top-5">
              <ion-skeleton-text [animated]="true" style="width: 100px;"></ion-skeleton-text>
            </span>
            <span class="travel-working-text">
              <ion-skeleton-text [animated]="true" style="width: 100px;"></ion-skeleton-text>
            </span>
          </div>
        </div>
      </div>
    </div>
    }
    @else if (loading==='LOADED') {
    <!-- <div class="search-bar" id="open-modal" expand="block" (click)="openSearchBooking('OTHERS')" *ngIf="!loginIsOpened">
      <div class="hotel-custom-input-icon">
        <i-feather name="search"></i-feather>
      </div>
      <div class="hotel-custom-input-text">
        <div class="destination-city">{{'Where do you want to stay?'}}</div>
      </div>
    </div> -->
    <div class="slider-container">
      <ng-container *ngIf="dashboardResponse?.hotelImageDetails?.length; else defaultSlider">
        <swiper [config]="defaultSliderConfig" (slideChange)="onSlideChanged($event)" #slides>
          <ng-template swiperSlide *ngFor="let data of dashboardResponse?.hotelImageDetails">
            <div class="slide">
              <img [src]="data?.fileDetail?.path || 'assets/images/icons/image1.png'" alt="" />
              <div class="slide-inner-container"></div>
              <div class="image-info">
                <div class="image-info-container">
                  <div class="text-container">
                    <div class="image-text main-heading">{{data?.title}}</div>
                    <div class="image-text inner-heading">{{data?.description}}</div>
                  </div>
                  <div class="rating-card">
                    <ion-icon src="assets/images/svg/star-fill.svg" class="star-icon"></ion-icon> {{ data?.rating }}
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
        </swiper>
      </ng-container>

      <!-- Default Swiper when dashboardResponse?.hotelImageDetails is not available -->
      <ng-template #defaultSlider>
        <swiper [config]="defaultSliderConfig" (slideChange)="onSlideChanged($event)" #slides>
          <ng-template swiperSlide>
            <div class="slide">
              <img src="assets/images/icons/image1.png" alt="" />
              <div class="slide-inner-container"></div>
              <div class="image-info">
                <div class="image-info-container">
                  <div class="text-container">
                    <div class="image-text main-heading">Emerald Oasis Mansion</div>
                    <div class="image-text inner-heading">2118 Thornridge Cir, Syracuse, Connecticut</div>
                  </div>
                  <div class="rating-card">
                    <ion-icon src="assets/images/svg/star-fill.svg" class="star-icon"></ion-icon> 4
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
          <ng-template swiperSlide>
            <div class="slide">
              <img src="assets/images/icons/image2.png" alt="" />
              <div class="slide-inner-container"></div>
              <div class="image-info">
                <div class="image-info-container">
                  <div class="text-container">
                    <div class="image-text main-heading">Emerald Oasis Mansion</div>
                    <div class="image-text inner-heading">2118 Thornridge Cir, Syracuse, Connecticut</div>
                  </div>
                  <div class="rating-card">
                    <ion-icon src="assets/images/svg/star-fill.svg" class="star-icon"></ion-icon> 4
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
          <ng-template swiperSlide>
            <div class="slide">
              <img src="assets/images/icons/image1.png" alt="" />
              <div class="slide-inner-container"></div>
              <div class="image-info">
                <div class="image-info-container">
                  <div class="text-container">
                    <div class="image-text main-heading">Emerald Oasis Mansion</div>
                    <div class="image-text inner-heading">2118 Thornridge Cir, Syracuse, Connecticut</div>
                  </div>
                  <div class="rating-card">
                    <ion-icon src="assets/images/svg/star-fill.svg" class="star-icon"></ion-icon> 4
                  </div>
                </div>
              </div>
            </div>
          </ng-template>
        </swiper>
      </ng-template>
    </div>
    <div class="balance-card" *ngIf="loginIsOpened">
      <div class="balance-card-inner-container">
        <div class="balance-card-container" (click)="selectBalanceOption = 'MyCash'; openMyWallet();">
          <span class="bal-text">
            <strong>My Cash</strong>
            <span class="bal-amount">₹{{ commonService.formatAmount(dashboardResponse?.myCash ? dashboardResponse.myCash
              : 0) }}/-</span>
          </span>
        </div>
        <div class="vertical-separator"></div> <!-- Vertical Line -->
        <div class="balance-card-container" (click)="selectBalanceOption = 'PromoCash'; openMyWallet();">
          <span class="bal-text">
            <strong>Promo Cash</strong>
            <span class="bal-amount">₹{{ commonService.formatAmount(dashboardResponse?.promoCash ?
              dashboardResponse.promoCash
              : 0) }}/-</span>
          </span>
        </div>
      </div>
    </div>
    <div class="holiday-selection-container margin-top-10">

      <!-- <div class="other-holiday-selection-card" (click)="openBookingPage('OTHERS')" *ngIf="loginIsOpened">
        <div class="other-holiday-selection-card-container">
          <div class="book-your-hotel-container">
            <span class="book-hotel-text">Book Your Hotel Now!</span>
            <span class="exclusive-hotel-detail margin-top-15">Exclusive hotel deals and discounts!</span>
            <span class="exclusive-hotel-detail margin-top-3">Get the best prices delivered</span>
            <div class="offers-text">
              <span class="click-here"><strong class="strong-text">Click</strong> for hotel booking</span>
            </div>
          </div>
        </div>
        <img class="book-hotel-icon" src="/assets/images/svg/other-service-icon.svg" alt="">
      </div> -->

      <div class="other-holiday-selection-card">
        <div class="other-holiday-selection-card-container">
          <div class="book-your-hotel-container">
            <span class="book-hotel-text">Book Your Hotel Now!</span>
            <!-- <span class="exclusive-hotel-detail margin-top-15">Exclusive hotel deals and discounts!</span>
            <span class="exclusive-hotel-detail margin-top-3">Get the best prices delivered</span>
            <div class="offers-text">
              <span class="click-here"><strong class="strong-text">Click</strong> for hotel booking</span>
            </div> -->
            <div class="search-bar margin-top-10" id="open-modal" expand="block" (click)="openSearchBooking('OTHERS')">
              <div class="hotel-custom-input-icon">
                <i-feather name="search"></i-feather>
              </div>
              <div class="hotel-custom-input-text">
                <div class="destination-city">{{'Where do you want to stay?'}}</div>
              </div>
            </div>
          </div>
        </div>
        <img class="book-hotel-icon" src="/assets/images/svg/other-service-icon.svg" alt="">
      </div>

      <div class="active-campaign-container" *ngIf="dashboardResponse?.activeCampaignDetails?.length > 0">
        <div class="active-campaign no-campaign-background">

          <!-- Show Swiper only if more than 1 campaign -->
          <swiper *ngIf="dashboardResponse?.activeCampaignDetails?.length > 1" [config]="activeCampaignConfig" #slides>
            <ng-template swiperSlide *ngFor="let campaigns of dashboardResponse?.activeCampaignDetails">
              <div class="active-campaign slider-active-campaign" (click)="seeCampaignDetail(campaigns?.id)">
                <div class="active-campaign-card-header">
                  <div *ngFor="let attachment of campaigns?.attachments">
                    <img class="active-campaign-image-slide" *ngIf="attachment.path" [src]="attachment.path" alt="">
                  </div>
                  <div *ngIf="!campaigns?.attachments || campaigns.attachments.length === 0">
                    <img class="active-campaign-image-slide" [src]="campaigns?.type === 'FREE_TRIP' ? '/assets/images/svg/free-trip-2.svg' :
                        campaigns?.type === 'WALLET_TOPUP' ? '/assets/images/svg/wallet-2.svg' :
                        '/assets/images/svg/booking-2.svg'" alt="Default">
                  </div>
                </div>
              </div>
            </ng-template>
          </swiper>

          <!-- Show image if only 1 campaign -->
          <div class="active-campaign margin-top-10" *ngIf="dashboardResponse?.activeCampaignDetails?.length === 1"
            (click)="seeCampaignDetail(dashboardResponse.activeCampaignDetails[0].id)">
            <div class="active-campaign-card-header">
              <div *ngFor="let attachment of dashboardResponse.activeCampaignDetails[0]?.attachments">
                <img class="active-campaign-image-slide" *ngIf="attachment.path" [src]="attachment.path" alt="">
              </div>
              <div *ngIf="!dashboardResponse.activeCampaignDetails[0]?.attachments?.length">
                <img class="active-campaign-image-slide" [src]="dashboardResponse.activeCampaignDetails[0]?.type === 'FREE_TRIP'
                    ? '/assets/images/svg/free-trip-2.svg'
                    : dashboardResponse.activeCampaignDetails[0]?.type === 'WALLET_TOPUP'
                    ? '/assets/images/svg/wallet-2.svg'
                    : '/assets/images/svg/booking-2.svg'" alt="Default">
              </div>
            </div>
          </div>

          <div class="swiper-pagination" *ngIf="dashboardResponse?.activeCampaignDetails.length > 1"></div>
          <div class="active-campaign-detail-container" (click)="openActiveCompaigns()">
            <div class="active-campaign-text-container">
              <ion-icon class="active-campaign-icon" src="/assets/images/svg/gift-coupon.svg" slot="start"></ion-icon>
              <span class="active-campaign-text margin-top-3">Active Campaigns</span>
              <hr class="active-campaign-vertical-separator" />
              <span class="active-campaign-count">
                {{ dashboardResponse?.activeCampaignDetails[0]?.totalCount || 0 }}
              </span>
            </div>
            <div class="additional-info margin-right-5">
              <div class="campaign-container">
                <span class="offer-text">See All</span>
                <span class="buy-voucher-container">
                  <ion-icon class="voucher-icon" src="/assets/images/svg/arrow-right-white.svg" slot="start"></ion-icon>
                </span>
              </div>
            </div>
          </div>

        </div>
      </div>
      <div class="membership-card-wrapper">
        <span class="membership-package" *ngIf="activePackage"
          [ngStyle]="{'--package-color': activePackage?.packageDetail?.color}">
          {{activePackage?.packageDetail?.title?.split('-')[0].trim()}}</span>

        <div class="membership-holiday-selection-card margin-top-10" (click)="openBookingPage('MEMBERSHIP')">
          <div class="membership-text">
            <div class="membership-title">Membership</div>
            <div class="membership-title holiday">Holiday</div>
          </div>
          <div class="offers-text">
            <span class="click-here">Click</span> for booking
          </div>
        </div>
      </div>
      <!-- <div class="other-holiday-selection-card margin-top-7" (click)="openBookingPage('OTHERS')">
        <ion-icon src="/assets/images/svg/plus-circle-dotted.svg" class="service-icon"></ion-icon>
        <span class="service-text">Other Travel Services</span>
      </div> -->

      <div class="package-subscribe-card margin-top-10">
        <div class="package-subscribe-card-container">
          <div class="subscribe-heading-text margin-top-5">Subscribe now and enjoy Amazing Nights across incredible
            destinations!</div>
          <div class="package-subscribe-container">
            <!-- <span class="subscribe-text">Subscribe Now!</span> -->
            <span class="get-price-text margin-top-5">You will get total {{packageDetail?.packageDetails?.totalNights}}
              Nights</span>
            <span class="get-price-text other-text"
              *ngIf="packageDetail?.packageDetails?.indianNights">{{packageDetail?.packageDetails?.indianNights}}
              Indian Night
            </span>
            <span class="get-price-text other-text"
              *ngIf="packageDetail?.packageDetails?.asianNights">{{packageDetail?.packageDetails?.asianNights}}
              Asian Night
            </span>
            <span class="get-price-text other-text margin-bottom-10"
              *ngIf="packageDetail?.packageDetails?.internationalNights">{{packageDetail?.packageDetails?.internationalNights}}
              International Night
            </span>

          </div>

        </div>
        <ion-button class="view-detail-button" expand="full" shape="round" type="submit" (click)="subscribePlan()">
          <div class="view-detail-button-container">
            <img src="/assets/images/svg/grab-deal.svg" alt="Pay Now" width="15px" />
            <span class="view-detail-text margin-right-10">
              Subscribe Now!
            </span>
          </div>
        </ion-button>
        <img class="package-subscribe-icon" src="/assets/images/svg/hotel-image.svg" alt="">
      </div>

      <div class="travel-card margin-top-10">
        <div class="travel-card-container">
          <div class="travel-container">
            <div class="travel-feature-container">
              <ion-icon class="flight-icon" src="/assets/images/svg/flight-icon.svg" slot="start"></ion-icon>
              <span class="travel-text">Travel Features Coming Soon!</span>
            </div>
            <span class="travel-working-text margin-top-10">We're working on:</span>
            <span class="travel-working-text margin-top-5"><strong>-Book Flights</strong> directly from the app</span>
            <span class="travel-working-text"><strong>-Reserve Land Services</strong> like transfers
              rentals & tours</span>
            <span class="travel-coming-text margin-top-10">Coming soon-stay tuned!</span>
          </div>
        </div>
        <img class="travel-icon" src="/assets/images/svg/travel-feature-icon.svg" alt="">
      </div>

    </div>
    }
  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #isPanCardPopup [isOpen]="isPanCard"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header skip-header no-header-text">
        <span class="skip-text" (click)="onSkipPanCard()">Skip</span>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate"
          (ngSubmit)="savePanCard(recordForm.form)">
          <div class="popup-large-heading">Add Pan Card</div>
          <div class="popup-normal-heading margin-top-5 secondary-text">
            To proceed with your booking, please provide your PAN card details.
          </div>
          <div class="job-info margin-top-20">
            <div class="margin-bottom-15">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': pan.invalid && onClickValidation}">
                <ion-input label="Pan Card" labelPlacement="floating" name="pan" (ionInput)="convertToUpperCase($event)"
                  #pan="ngModel" [(ngModel)]="panCard" [maskito]="panCardMask" [maskitoElement]="maskPredicate"
                  pattern="^[A-Z]{5}[0-9]{4}[A-Z]{1}$" required="required">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="pan" [onClickValidation]="onClickValidation"
                [panCardPatternMessage]="'PAN Card number must contain 5 letters (uppercase), followed by 4 digits, and end with 1 letter (uppercase).'">
              </app-validation-message>
            </div>
          </div>
          <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
            type="submit">
            Add
          </ion-button>
        </form>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #noPackageAvailablePopup
  [isOpen]="isNoPackageAvailablePopupOpen" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeNoPackageAvailablePopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <div *ngIf="!activePackage">
          <div class="popup-large-heading">Oops! No Membership Found</div>
          <div class="popup-normal-heading margin-top-10 secondary-text">It looks like you don't have an active
            membership plan yet. Only members can enjoy exclusive holiday benefits! 🎉</div>
          <div class="popup-normal-heading margin-top-15 secondary-text">
            Unlock special offers, enjoy hassle-free holiday breaks, and more with our membership plans. Purchase a plan
            today to start enjoying your benefits!
          </div>
          <div class="popup-normal-heading margin-top-15 secondary-text">Don’t miss out on our special holiday deals!
          </div>
          <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
            (click)="explorePlan();noPackageAvailablePopup.dismiss()">
            Explore Membership Plans
          </ion-button>
        </div>
        <div *ngIf="activePackage && (!activePackage.isBookingAllowed || activePackage.status !== 'ACTIVE')">
          <div class="popup-large-heading">Booking Unavailable for Your Membership</div>
          <div class="popup-normal-heading margin-top-10 secondary-text">It seems your current membership plan doesn’t
            allow holiday bookings at this time.</div>
          <div class="popup-normal-heading margin-top-15 secondary-text">
            <b>Reason: </b>{{activePackage?.bookingNotAllowedNote}}
          </div>
          <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
            (click)="closeNoPackageAvailablePopup()">
            Contact
          </ion-button>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>


<ion-modal class="site-custom-popup job-invitation-popup" [isOpen]="isRandomCampaignModalOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-body no-padding-top">
        <form class="custom-form" #recordForm="ngForm" novalidate="novalidate">
          @if (loading==='LOADING') {
          <div class="my-booking-card-container">
            <div class="my-booking-card margin-top-10">
              <ion-grid>
                <ion-row>
                  <ion-col>
                    <ion-row class="check-in-out-con my-bookings-check">
                      <ion-col size="8" class="my-booking-code ion-no-padding">
                        <div class="my-booking-text">
                          <ion-skeleton-text [animated]="true" style="width: 150px"></ion-skeleton-text>
                        </div>
                      </ion-col>
                      <ion-col size="4" class="my-booking-status">
                        <div class="my-booking-text">
                          <ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                        </div>
                      </ion-col>
                    </ion-row>

                    <div>
                      <div class="hotel-name my-bookings-hotel-name">
                        <ion-skeleton-text [animated]="true" style="width: 170px"></ion-skeleton-text>
                      </div>
                      <div class="hotel-address"> <ion-skeleton-text [animated]="true"
                          style="width: 200px"></ion-skeleton-text></div>
                    </div>
                  </ion-col>
                </ion-row>

                <ion-row class="check-in-out-con">
                  <ion-col size="5">
                    <div class="check-in-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                      </div>
                      <div class="date"><ion-skeleton-text [animated]="true" style="width: 100px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                  <ion-col size="2">
                    <div class="time-duration-con">
                      <ion-skeleton-text [animated]="true" style="width: 30px;height: 30px"></ion-skeleton-text>
                      <div class="time-duration">
                        <span><ion-skeleton-text [animated]="true" style="width: 55px"></ion-skeleton-text></span>
                      </div>
                    </div>
                  </ion-col>
                  <ion-col size="5">
                    <div class="check-out-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                      </div>
                      <div class="date"><ion-skeleton-text [animated]="true" style="width: 80px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                </ion-row>

                <ion-row>
                  <ion-col>
                    <div class="guest-and-rooms-con">
                      <div class="label"><ion-skeleton-text [animated]="true" style="width: 120px"></ion-skeleton-text>
                      </div>
                      <div class="rooms">
                        <ion-skeleton-text [animated]="true" style="width: 290px;height: 30px"></ion-skeleton-text>
                      </div>
                    </div>
                  </ion-col>
                </ion-row>

              </ion-grid>
            </div>
          </div>
          }
          @else if (loading==='LOADED') {
          <div class="campaign-card" *ngIf="responseReceived && randomCampaignDetail">
            <div class="campaign-card-header">
              <div class="campaign-image-slide" *ngFor="let attachment of randomCampaignDetail?.attachments">
                <img *ngIf="attachment.path" [src]="attachment.path" alt="" (click)="randomCampaignDetailButton()">
                <i-feather name="X" class="close-icon" (click)="closeRandomCampaignModal()"></i-feather>
              </div>
              <div class="campaign-image-slide"
                *ngIf="!randomCampaignDetail?.attachments || randomCampaignDetail.attachments?.length === 0">
                <img
                  [src]="randomCampaignDetail?.type === 'FREE_TRIP' ? '/assets/images/svg/free-trip-2.svg' : randomCampaignDetail?.type === 'WALLET_TOPUP' ? '/assets/images/svg/wallet-2.svg' : '/assets/images/svg/booking-2.svg'"
                  alt="Default Campaign Image" (click)="randomCampaignDetailButton()">
                <i-feather name="X" class="close-icon" (click)="closeRandomCampaignModal()"></i-feather>
              </div>
            </div>

            <div class="campaign-detail-container" (click)="randomCampaignDetailButton()">
              <span
                class="campaign-detail-title random-campaign-title margin-top-15">{{randomCampaignDetail?.title}}</span>

              <!-- Show rewards if available -->
              <ng-container *ngIf="randomCampaignDetail?.rewards">
                <p class="rewards">{{ randomCampaignDetail.rewards }}</p>
              </ng-container>

              <!-- Show description if type is FREE_TRIP and no rewards -->
              <ng-container *ngIf="!randomCampaignDetail?.rewards && randomCampaignDetail?.type === 'FREE_TRIP'">
                <p class="rewards">{{ randomCampaignDetail?.description }}</p>
              </ng-container>

              <!-- Show cashback section if no rewards and type is BOOKING_CASHBACK or WALLET_TOPUP -->
              <ng-container
                *ngIf="!randomCampaignDetail?.rewards && 
                    (randomCampaignDetail?.type === 'BOOKING_CASHBACK' || randomCampaignDetail?.type === 'WALLET_TOPUP')">
                <div class="padding-congratulations-container compaign-card-bg random-campaign-card-bg">
                  <div class="congratulations-content-money">
                    <div class="overlay-svg">
                      <img src="/assets/images/svg/money-bag.svg" width="38" height="48" alt="money-bag">
                    </div>
                    <span class="text-content-money random-campaign-text margin-left-12">
                      {{ randomCampaignDetail?.myCash + randomCampaignDetail?.promoCash }}% Cashback,
                      <span *ngIf="randomCampaignDetail?.myCash > 0">
                        {{ randomCampaignDetail?.myCash }}% in My Cash
                      </span>
                      <span *ngIf="randomCampaignDetail?.myCash > 0 && randomCampaignDetail?.promoCash > 0"> and </span>
                      <span *ngIf="randomCampaignDetail?.promoCash > 0">
                        {{ randomCampaignDetail?.promoCash }}% in Promo Cash.
                      </span>
                    </span>
                  </div>
                </div>
              </ng-container>

            </div>

            <!-- <ion-button class="grab-deal-button random-compaign-button" expand="full" shape="round" type="submit"
              (click)="randomCampaignDetailButton()">
              <div class="grab-deal-container">
                <img src="/assets/images/svg/grab-deal.svg" alt="Pay Now" width="24px" />
                <span class="grab-deal-text">
                  View Detail
                </span>
              </div>
            </ion-button> -->

          </div>
          }

        </form>
      </div>

    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup job-invitation-popup" #noOnboardingPopup [isOpen]="isNoOnboarding"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeNoOnboardingPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <div>
          <div class="popup-large-heading">Incomplete Information</div>
          <div class="popup-normal-heading margin-top-10 secondary-text">Your profile needs to be updated before process
            this request. Please click 'Continue' to update your details.</div>
          <ion-button class="site-full-rounded-button primary-button margin-top-20" expand="full" shape="round"
            (click)="continueToOnboarding()">
            Continue
          </ion-button>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>