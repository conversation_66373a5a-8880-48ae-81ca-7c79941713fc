<div class="login-container">
  <div class="login-inner-container">
    <div class="login-header-container">
      <div class="left-side-info-section">
        
      </div>
    </div>
    <div class="login-body-container">
        <div class="user-info">
          <ion-avatar class="avatar">
            <img src="assets/images/icons/logo.png" alt="User Image"> 
          </ion-avatar>
          <h2 class="user-name">Nav<PERSON><PERSON></h2> 
          <p class="phone-number">**********</p> 
        </div>
      
    <ion-card>
      <ion-list lines="full">
      <ion-item routerLink="/edit-profile" detail="true">
        <ion-icon slot="start" name="person-circle-outline"></ion-icon>
        Edit Profile
      </ion-item>
  
      <ion-item routerLink="/account-settings" detail="true">
        <ion-icon slot="start" name="settings-outline"></ion-icon>
        Account Settings
      </ion-item>
  
      <ion-item routerLink="/payment-details" detail="true">
        <ion-icon slot="start" name="card-outline"></ion-icon>
        Payment Details
      </ion-item>

      <ion-item routerLink="/logout" detail="true">
        <ion-icon slot="start" name="log-out-outline"></ion-icon>
        Log Out
      </ion-item>
      
    </ion-list>
  </ion-card>
    </div>
  </div>
</div>