<ion-toolbar  class="sale-toolbar-show-title-and-icon">
  <ion-buttons slot="start" class="back-button">
    <ion-button fill="clear" (click)="backToLeads()">
      <!-- Icon for the back button -->
      <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
    </ion-button>
  </ion-buttons>
  <ion-title class="sales-custom-title">{{title}}</ion-title>
</ion-toolbar>


<ion-content>
  <!-- Container for tabbed navigation -->
  <ion-tabs>
    <!-- Tab bar placed at the top of the content area -->
    <ion-tab-bar class="lead-detail-container" slot="top">
      <!-- Tab button for "Basic Detail" tab -->
      <ion-tab-button tab="basic-detail">
        <!-- Wrapper for tab content, including icon and label -->
        <div class="tab-content">
          <!-- Icon for the "Basic Detail" tab -->
          <ion-icon src="assets/images/svg/basic-detail.svg"></ion-icon>
          <!-- Label for the "Basic Detail" tab -->
          <ion-label class="tab-label">Basic Detail</ion-label>
        </div>
      </ion-tab-button>

      <!-- Tab button for "Activity" tab -->
      <ion-tab-button tab="activity">
        <!-- Wrapper for tab content, including icon and label -->
        <div class="tab-content">
          <!-- Icon for the "Activity" tab -->
          <ion-icon src="assets/images/svg/activity-icon.svg"></ion-icon>
          <!-- Label for the "Activity" tab -->
          <ion-label class="tab-label">Activity</ion-label>
        </div>
      </ion-tab-button>
    </ion-tab-bar>
  </ion-tabs>
</ion-content>