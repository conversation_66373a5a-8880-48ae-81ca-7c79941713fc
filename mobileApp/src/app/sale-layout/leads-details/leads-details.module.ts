import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { LeadsDetailsComponent } from './leads-details.component';
import { BasicDetailsComponent } from './basic-details/basic-details.component';
import { ActivityComponent } from './activity/activity.component';
import { LeadsDetailsRoutingModule } from './leads-details-routing';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    LeadsDetailsRoutingModule
  ],
  declarations: [
    LeadsDetailsComponent,
    BasicDetailsComponent,
    ActivityComponent
  ]
})
export class LeadsDetailsModule {}
