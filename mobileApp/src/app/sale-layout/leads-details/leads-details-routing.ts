import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LeadsDetailsComponent } from './leads-details.component';
import { BasicDetailsComponent } from './basic-details/basic-details.component';
import { ActivityComponent } from './activity/activity.component';

const routes: Routes = [
  {
    path: '',
    component: LeadsDetailsComponent,
    children: [
      {
        path: 'basic-detail',
        component: BasicDetailsComponent
      },
      {
        path: 'activity',
        component: ActivityComponent
      },
      {
        path: '',
        redirectTo: 'basic-detail',
        pathMatch: 'full'
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class LeadsDetailsRoutingModule {}
