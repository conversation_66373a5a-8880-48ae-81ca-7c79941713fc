<ion-content>
  <!-- Container for the basic details section -->
  <div class="basic-details-container">
    <div class="form-container">

      <!-- Contact Information Section -->
      <div class="contact-info">
        <!-- Display the full name of the lead -->
        <ion-label class="full-name">{{detail.fullName}}</ion-label>
        <div class="phone-container">
          <!-- Icon representing a phone call -->
          <ion-icon src="assets/images/svg/call-outgoing.svg" class="phone-icon"></ion-icon>
          <!-- Display the phone number of the lead -->
          <ion-label class="phone-number">{{detail.phoneNumber}}</ion-label>
        </div>
      </div>

      <!-- Horizontal Separator Line -->
      <div class="separator-line"></div>

      <!-- Information Sections, organized into columns -->
      <div class="info-sections">
        <!-- Email and other details section -->
        <div class="info-column email-section">
          <div class="email-container">
            <!-- Label for Email -->
            <ion-label class="email-label">Email</ion-label>
            <!-- Display the email address of the lead -->
            <ion-label class="email-value large-email">{{detail.email}}</ion-label>
          </div>
          <div class="other-email-container">
            <!-- Label for Assigned Sales Member -->
            <ion-label class="email-label">Assigned Sales Member</ion-label>
            <!-- Display the name of the assigned sales member -->
            <ion-label class="email-value">{{detail.assignedSalesMember}}</ion-label>
          </div>
          <div class="other-email-container">
            <!-- Label for Tele Caller's Name -->
            <ion-label class="email-label">Tele Caller's Name</ion-label>
            <!-- Display the name of the tele caller -->
            <ion-label class="email-value">{{detail.teleCallerName}}</ion-label>
          </div>
        </div>

        <!-- Vertical Separator Line -->
        <!-- Uncomment the next line if you want a vertical separator -->
        <!-- <div class="separator-line-vertical"></div> -->

        <!-- Calling Status Section -->
        <div class="info-column calling-status-section">
          <div class="calling-status-container">
            <!-- Label for Calling Status -->
            <ion-label class="status-label">Calling Status</ion-label>
            <!-- Display the calling status of the lead -->
            <ion-label class="status-value">{{detail.callingStatus}}</ion-label>
          </div>
          <div class="other-calling-status-container">
            <!-- Label for Status -->
            <ion-label class="status-label">Status</ion-label>
            <div class="status-section">
              <!-- Icon representing a confirmation status -->
              <ion-icon class="status-icon" src="assets/images/svg/confirmation.svg"></ion-icon>
              <!-- Display the current status of the lead -->
              <ion-label class="status-label">{{detail.status}}</ion-label>
            </div>
          </div>
        </div>
      </div>

      <!-- Tele Caller's Comments Section -->
      <div class="tele-caller-comment-container">
        <!-- Label for Tele Caller's Comments -->
        <ion-label class="comment-label">Tele Caller's Comments</ion-label>
        <!-- Display the comments provided by the tele caller -->
        <ion-label class="comment-value">{{detail.teleCallerComments}}</ion-label>
      </div>
      <div class="tele-caller-comment-other-container">
        <!-- Label for additional Comments -->
        <ion-label class="comment-label">Comments</ion-label>
        <!-- Display any additional comments -->
        <ion-label class="comment-value">{{detail.comments}}</ion-label>
      </div>
    </div>
  </div>
</ion-content>