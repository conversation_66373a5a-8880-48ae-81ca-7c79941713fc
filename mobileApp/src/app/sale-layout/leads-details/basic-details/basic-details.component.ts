import { Component, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-basic-details',
  templateUrl: './basic-details.component.html',
  styleUrls: ['./basic-details.component.scss'],
})
export class BasicDetailsComponent implements OnInit {

  detail: any; // Property to hold the details of the lead
  subscription: Subscription = new Subscription(); // Subscription for login request

  constructor(private dataService: DataService,
    private loadingService: LoadingService,
    private toastService: ToastService,
  ) {

  }

  ngOnInit() {
    this.initializeDetails(); // Initialize the details with static data
  }

  // Method to initialize lead details with default values
  initializeDetails() {
    this.detail = {} as any;
    this.detail.fullName = '<PERSON> Mathews';
    this.detail.phoneNumber = '+91 85855-85855';
    this.detail.email = '<EMAIL>';
    this.detail.assignedSalesMember = 'Jordan Mathews';
    this.detail.teleCallerName = 'johndoe';
    this.detail.callingStatus = 'Interested';
    this.detail.status = 'Confirmed';
    this.detail.teleCallerComments = 'There are many variations of passages but the majority have suffered alteration in some form, by injected humour.';
    this.detail.comments = 'Needs follow-up after two weeks.';
  }

  // Method to fetch lead details from the server
  async getLeadBasicDetails(): Promise<any> {
    this.loadingService.show(); // Show loading indicator

    // Subscribe to the data service to get lead details
    this.subscription = this.dataService.getDetails()
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide(); // hide loading indicator
          const data = response.data;
        }, error: (error) => {
          this.loadingService.hide(); // hide loading indicator
          this.toastService.show(error.message || 'Unexpected error occurred');
        }
      })
  }

  ngOnDestroy() {
    // Unsubscribe from any subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
