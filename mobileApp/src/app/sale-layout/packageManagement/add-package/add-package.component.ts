import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-add-package',
  templateUrl: './add-package.component.html',
  styleUrls: ['./add-package.component.scss'],
})
export class AddPackageComponent  implements OnInit {

  selectedPackage: string;
  packageNights: number;
  packageCost: number;
  packageTenure: number;
  offerNights: number;
  offerFlights: number;
  fullyPaidHolidays: number;
  personsInFPH: number;
  shoppingVouchers: number;
  whiteGoods: number;
  enrollmentBenefits: number;
  couponDiscount: number;
  discountAmount: number;
  payableAmount: number;

  constructor() {
    this.selectedPackage = '';
    this.packageNights = 0;
    this.packageCost = 0;
    this.packageTenure = 0;
    this.offerNights = 0;
    this.offerFlights = 0;
    this.fullyPaidHolidays = 0;
    this.personsInFPH = 1;
    this.shoppingVouchers = 0;
    this.whiteGoods = 0;
    this.enrollmentBenefits = 0;
    this.couponDiscount = 0;
    this.discountAmount = 0;
    this.payableAmount = 0;
   }

  ngOnInit() {}

  packageChange() {
    // Reset fields when package changes
    this.packageNights = 0;
    this.packageCost = 0;
    this.packageTenure = 0;
  }

}
