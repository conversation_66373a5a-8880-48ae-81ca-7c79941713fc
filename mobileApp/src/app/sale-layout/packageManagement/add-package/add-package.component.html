<ion-header>
  <ion-toolbar>
    <ion-title>
      Package Details
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-card>
    <ion-card-header>
      <ion-card-title>Select Package</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-item lines="none" class="ion-align-items-start"> <!-- Remove default lines -->
        <ion-label slot="start">Package name</ion-label>
        <ion-select slot="end" interface="popover" [(ngModel)]="selectedPackage" (ionChange)="packageChange()"
            placeholder="Please select an option">
          <ion-select-option value="basic">Basic</ion-select-option>
          <ion-select-option value="deluxe">Deluxe</ion-select-option>
          <!-- Add more options as needed -->
        </ion-select>
      </ion-item>
    </ion-card-content>
  </ion-card>

  <ion-card *ngIf="selectedPackage">
    <ion-card-header>
      <ion-card-title>{{ selectedPackage | titlecase }} Details</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-item lines="none">
        <ion-label>Package nights</ion-label>
        <ion-text>12 nights</ion-text>
      </ion-item>
      <ion-item lines="none">
        <ion-label>Package cost</ion-label>
        <ion-text>5,00,000</ion-text>
      </ion-item>
      <ion-item lines="none">
        <ion-label>Tenure</ion-label>
        <ion-text>5 years</ion-text>
      </ion-item>
    </ion-card-content>
  </ion-card>
  
  <ion-card>
    <ion-card-header>
      <ion-card-title>Other Details</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-item lines="none" class="ion-align-items-start">
        <ion-label slot="start">Offer nights</ion-label>
        <ion-input slot="end" type="text" [(ngModel)]="offerNights" inputmode="numeric"></ion-input>
      </ion-item>      
      <ion-item lines="none" class="ion-align-items-start">
        <ion-label slot="start">Offer flights</ion-label>
        <ion-input slot="end" type="text" [(ngModel)]="offerFlights" inputmode="numeric"></ion-input>
      </ion-item>
      <ion-item lines="none" class="ion-align-items-start">
        <ion-label slot="start">Fully Paid Holidays (FPH)</ion-label>
        <ion-input slot="end" type="text" [(ngModel)]="fullyPaidHolidays" inputmode="numeric"></ion-input>
      </ion-item>
      <ion-item lines="none" class="ion-align-items-start"> <!-- Remove default lines -->
        <ion-label slot="start">Number of persons in FPH</ion-label>
        <ion-select slot="end" interface="popover" [(ngModel)]="personsInFPH"  placeholder="Please select an option">
          <ion-select-option value="1">1</ion-select-option>
          <ion-select-option value="2">2</ion-select-option>
          <!-- Add more options as needed -->
        </ion-select>
      </ion-item>      
      <ion-item lines="none" class="ion-align-items-start">
        <ion-label slot="start">Shopping vouchers</ion-label>
        <ion-input slot="end" type="text" [(ngModel)]="shoppingVouchers" inputmode="numeric"></ion-input>
      </ion-item>
      <ion-item lines="none" class="ion-align-items-start">
        <ion-label slot="start">White goods worth</ion-label>
        <ion-input slot="end" type="text" [(ngModel)]="whiteGoods" inputmode="numeric"></ion-input>
      </ion-item>      
      <ion-item lines="none" class="ion-align-items-start">
        <ion-label slot="start">Other enrollment benefits worth</ion-label>
        <ion-input slot="end" type="text" [(ngModel)]="enrollmentBenefits" inputmode="numeric"></ion-input>
      </ion-item>
    </ion-card-content>
  </ion-card>

  <ion-card>
    <ion-card-header>
      <ion-card-title>Coupon Discount</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-item lines="none" class="ion-align-items-start">
        <ion-label slot="start">Package cost</ion-label>
        <ion-text>5,00,000</ion-text>
      </ion-item>
      <ion-item lines="none" class="ion-align-items-start">
        <ion-label slot="start">Discount amount</ion-label>
        <ion-input type="text" [(ngModel)]="discountAmount" inputmode="numeric"></ion-input>
      </ion-item>
      <ion-item lines="none" class="ion-align-items-start">
        <ion-label slot="start">Payable amount</ion-label>
        <ion-text>60,000</ion-text>
      </ion-item>
    </ion-card-content>
  </ion-card>

  <ion-row class="ion-margin-top ion-justify-content-between">
    <ion-col>
      <ion-button expand="block" color="medium">Previous</ion-button>
    </ion-col>
    <ion-col>
      <ion-button expand="block">Next</ion-button>
    </ion-col>
  </ion-row>

</ion-content>
