ion-content {
  --background: #f0f2f5;
}

ion-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 20px;
  
  ion-card-header {
    background-color: #4CAF50;
    color: #fff;
    text-align: center;
    padding: 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    font-weight: bold;
    font-size: 1.2em;
  }
  
  ion-card-content {
    ion-item {
      --padding-start: 16px;
      --padding-end: 16px;
      
      /* Optional: Adjusting ion-label width */
      ion-label {
       flex: 1; /* Take up all available space */
       min-width: 50%; /* Minimum width of label */
       max-width: 50%; /* Maximum width of label */
       }

     /* Optional: Adjusting ion-input width */
      ion-input {
       flex: 1; /* Take up all available space */
       min-width: 50%; /* Minimum width of input */
       max-width: 50%; /* Maximum width of input */
     }

      ion-select {
        width: 50%;
        max-width: 50%;
        --padding-end: 0;
      }
    }
  }
}

ion-button {
  --border-radius: 10px;
  --background: linear-gradient(to right, #4CAF50, #009688);
  --color: white;
  font-weight: bold;
  margin: 10px;
  
  &.medium {
    --background: #ddd;
    --color: #333;
  }
}

ion-row {
  ion-col {
    text-align: center;
  }
  ion-button {
    min-width: 150px; /* Adjust minimum width */
  }
}
