<ion-toolbar class="sale-toolbar-show-title-and-icon">
  <ion-buttons slot="start" class="back-button">
    <ion-button fill="clear" (click)="backToDashboard()">
      <!-- Icon for the back button -->
      <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
    </ion-button>
  </ion-buttons>
  <ion-title class="sales-custom-title">{{title}}</ion-title>
</ion-toolbar>

<ion-content>
  <div class="sales-dashboard-container">
    <div class="dashboard-main-container">
      <!-- This is a placeholder for any main content or header related to the dashboard -->
    </div>
    <div class="form-container">
      <!-- Input field for searching by name -->
      <div class="margin-bottom-15">
        <div class="input-container">
          <ion-item class="site-form-control" lines="none" [ngClass]="{'is-invalid': searchQuery && onClickValidation}">
            <ion-icon src="assets/images/svg/search-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input label="Search by Name" labelPlacement="floating" name="searchByName" [(ngModel)]="searchQuery"
              (ngModelChange)="search()" [ngClass]="{'is-invalid': searchQuery && onClickValidation}"></ion-input>
          </ion-item>
        </div>
        <!-- Filter icon -->
        <div class="filter-icon-container" (click)="openFilterModal()">
          <ion-icon src="assets/images/svg/filter.svg" class="filter-icon"></ion-icon>
        </div>
      </div>
       <!-- Conditionally display message when there are no leads -->
       <div *ngIf="filteredLeads.length === 0" class="no-data-message">
        No data available
      </div>
      <!-- Card view for displaying leads -->
      <ion-card *ngFor="let detail of filteredLeads" class="custom-card" (click)="openLeadDetails()">
        <ion-card-content class="card-content">
          <!-- Lead full name -->
          <ion-label class="full-name">{{detail.fullName}}</ion-label>
          <div class="phone-container">
            <!-- Phone icon and phone number -->
            <ion-icon src="assets/images/svg/call-outgoing.svg" class="phone-icon"></ion-icon>
            <ion-label class="phone-number">{{detail.phoneNumber}}</ion-label>
          </div>
          <div class="separator-line"></div>
          <!-- Lead address -->
          <ion-label class="address">{{detail.address}}</ion-label>
          <div class="button-container">
            <!-- Action buttons -->
            <ion-button expand="full" shape="round" class="action-button blue-button">UNQUALIFY</ion-button>
            <ion-button class="action-button white-button">CONVERT TO CUSTOMER</ion-button>
          </div>
        </ion-card-content>
      </ion-card>
    </div>
  </div>
</ion-content>