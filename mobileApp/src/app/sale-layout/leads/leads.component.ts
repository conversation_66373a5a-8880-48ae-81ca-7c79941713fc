import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Modal<PERSON>ontroller, NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';
import { FilterModalComponent } from './filter-modal/filter-modal.component';

@Component({
  selector: 'app-leads',
  templateUrl: './leads.component.html',
  styleUrls: ['./leads.component.scss'],
})
export class LeadsComponent implements OnInit, OnDestroy {

  onClickValidation: boolean = false;  // Flag to control validation state
  searchQuery: string = '';  // Variable to hold the search query
  filteredLeads: any[] = [];  // Array to hold filtered leads
  subscription: Subscription = new Subscription(); // Subscription for data service
  title: string = 'All Leads';

  // Static array of leads for initial display
  leadListing = [
    {
      fullName: 'Jordan Mathews',
      phoneNumber: '+91 85855-85855',
      address: 'New Delhi, India'
    },
    {
      fullName: '<PERSON>',
      phoneNumber: '+91 12345-67890',
      address: 'Punjab, India'
    }
  ];

  constructor(
    private dataService: DataService,
    private loadingService: LoadingService,
    private toastService: ToastService,
    private navController: NavController,
    private modalController: ModalController
  ) { }

  ngOnInit() {
    this.filteredLeads = [...this.leadListing];
  }

  openLeadDetails() {
    this.navController.navigateForward("/sale-portal/leads-details");
  }

  backToDashboard() {
    this.navController.navigateBack("/sale-portal/dashboard");
  }

  async getLeadListing(): Promise<any> {
    this.loadingService.show(); // Show loading indicator
    this.subscription = this.dataService.getDetails()
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide(); // Hide loading indicator
          this.leadListing = response.data;
          this.filterLeads(); // Apply filter to the fetched data
        },
        error: (error) => {
          this.loadingService.hide(); // Hide loading indicator
          this.toastService.show(error.message || 'Unexpected error occurred');
        }
      });
  }

  // Method to search leads based on the form input
  search() {
    this.filterLeads();
  }

  filterLeads() {
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      this.filteredLeads = this.leadListing.filter(lead =>
        lead.fullName.toLowerCase().includes(query) ||
        lead.phoneNumber.toLowerCase().includes(query)
      );
    } else {
      this.filteredLeads = [...this.leadListing];
    }
  }


  async openFilterModal() {
    const modal = await this.modalController.create({
      component: FilterModalComponent
    });

    modal.onDidDismiss().then((result) => {
      if (result.data) {
        // Handle the filters applied by the user
        console.log('Filters applied:', result.data);
        // Apply the filters to the leadListing if needed
      }
    });

    return await modal.present();
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
