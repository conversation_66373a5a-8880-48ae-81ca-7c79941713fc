import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-filter-modal',
  templateUrl: './filter-modal.component.html',
  styleUrls: ['./filter-modal.component.scss'],
})
export class FilterModalComponent implements OnInit {

  constructor(private modalController: ModalController) { }

  filters = {
    dateOfLeadGeneration: '',
    status: 'all'
  };

  ngOnInit() { }

  closeModal() {
    this.modalController.dismiss();
  }

  applyFilters() {
    // Logic for applying filters
    this.modalController.dismiss(this.filters); // Close modal and return filters
  }

}
