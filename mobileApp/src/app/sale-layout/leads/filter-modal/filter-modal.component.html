<ion-header>
  <ion-toolbar>
    <ion-title>Filters</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="closeModal()">Close</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div class="filter-container">
    <!-- Filter by Date of Lead Generation -->
    <ion-item>
      <ion-label>Date of Lead Generation</ion-label>
      <ion-datetime displayFormat="MM/DD/YYYY" [(ngModel)]="filters.dateOfLeadGeneration"></ion-datetime>
    </ion-item>
    <!-- Filter by Status -->
    <ion-item>
      <ion-label>Status</ion-label>
      <ion-select [(ngModel)]="filters.status">
        <ion-select-option value="all">All</ion-select-option>
        <ion-select-option value="qualified">Qualified</ion-select-option>
        <ion-select-option value="unqualified">Unqualified</ion-select-option>
        <ion-select-option value="rescheduled">Rescheduled</ion-select-option>
      </ion-select>
    </ion-item>
    <!-- Apply Filters Button -->
    <ion-button expand="full" (click)="applyFilters()">Apply Filters</ion-button>
  </div>
</ion-content>
