import { Routes } from "@angular/router";
import { AddFamilyDetailsComponent } from "./add-family-details/add-family-details.component";
import { AddProfileComponent } from "./add-profile/add-profile.component";
import { AddPackageComponent } from "./packageManagement/add-package/add-package.component";
import { SaleLayoutComponent } from "./sale-layout.component";
import { SalesAccountComponent } from "./sales-account/sales-account.component";
import { SalesDashboardComponent } from "./sales-dashboard/sales-dashboard.component";
import { LeadsComponent } from "./leads/leads.component";

export const SALELAYOUTSROUTING: Routes = [
  {
    path: "",
    component: SaleLayoutComponent,
    children: [
      {
        path: "account",
        component: SalesAccountComponent
      },
      {
        path: "dashboard",
        component: SalesDashboardComponent
      },
      {
        path: "profile",
        component: AddProfileComponent
      },
      {
        path: "family/profile",
        component: AddFamilyDetailsComponent
      },
      {
        path: "add/package",
        component: AddPackageComponent
      },
      {
        path: "leads",
        component: LeadsComponent
      },
      {
        path: "leads-details",
        loadChildren: () => import('./leads-details/leads-details.module').then(m => m.LeadsDetailsModule)
      },
    ],
  },
];
