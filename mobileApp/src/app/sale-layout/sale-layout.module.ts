import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { IConfig, provideEnvironmentNgxMask } from 'ngx-mask';
import { AddFamilyDetailsComponent } from './add-family-details/add-family-details.component';
import { AddProfileComponent } from './add-profile/add-profile.component';
import { AddPackageComponent } from './packageManagement/add-package/add-package.component';
import { SaleLayoutComponent } from './sale-layout.component';
import { SALELAYOUTSROUTING } from './sale-layout.routing';
import { SalesAccountComponent } from './sales-account/sales-account.component';
import { SalesDashboardComponent } from './sales-dashboard/sales-dashboard.component';
import { NgApexchartsModule } from 'ng-apexcharts';
import { LeadsComponent } from './leads/leads.component';
import { SharedModuleModule } from "../../shared/shared-module.module";
import { FilterModalComponent } from './leads/filter-modal/filter-modal.component';
import { MaskitoDirective } from '@maskito/angular';
import { HttpAuthInterceptor } from 'src/shared/http.interceptor';
import { IconsModule } from 'src/shared/icon.module';
import { ImageCropperModule } from 'ngx-image-cropper';

const maskConfig: Partial<IConfig> = {
  validation: false,
};

@NgModule({
  declarations: [
    SaleLayoutComponent,
    SalesAccountComponent,
    SalesDashboardComponent,
    AddProfileComponent,
    AddFamilyDetailsComponent,
    AddPackageComponent,
    LeadsComponent,
    FilterModalComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ImageCropperModule,
    NgApexchartsModule,
    RouterModule.forChild(SALELAYOUTSROUTING),
    SharedModuleModule,
    MaskitoDirective,
    IconsModule
  ],
  providers: [
    provideHttpClient(withInterceptorsFromDi()),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpAuthInterceptor,
      multi: true
    }
  ],
  schemas: []
})
export class SaleLayoutModule { }
