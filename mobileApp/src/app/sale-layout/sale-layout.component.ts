import { Component, OnInit } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { MenuController, NavController } from '@ionic/angular';
import { filter } from 'rxjs';
import { AuthService } from 'src/shared/authservice';

@Component({
  selector: 'app-sales',
  templateUrl: './sale-layout.component.html',
  styleUrls: ['./sale-layout.component.scss'],
})
export class SaleLayoutComponent implements OnInit {

  showToolbar = true;
  title: string = 'Dashboard';

  constructor(private navController: NavController,
    private authService: AuthService,
    private menuController: MenuController,
    private router: Router,
  ) { }

  ngOnInit() {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      this.updateToolbarVisibility();
    });
  }

  private updateToolbarVisibility() {
    const currentRoute = this.router.url;

    // Default: Hide the toolbar
    this.showToolbar = false;

    switch (currentRoute) {
      case '/sale-portal/dashboard':
      case '/sale-portal/profile':
      case '/sale-portal/account':
        this.showToolbar = true;
        break;
      default:
        // For any other routes, keep the toolbar hidden
        break;
    }
  }

  openMenu() {
    this.menuController.open('menu');
  }

  openSalesDashboard() {
    this.navController.navigateForward("/sale-portal/dashboard");
  }

  openSalesAccount() {
    this.navController.navigateForward("/sale-portal/account");
  }

  openSalesProfile() {
    this.navController.navigateForward("/sale-portal/profile");
  }

  logout() {
    this.authService.logout();
    this.navController.navigateRoot("/account/login");
  }

}
