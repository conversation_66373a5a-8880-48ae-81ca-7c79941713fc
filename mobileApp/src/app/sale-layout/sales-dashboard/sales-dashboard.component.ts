import { Component, OnDestroy, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-sales-dashboard',
  templateUrl: './sales-dashboard.component.html',
  styleUrls: ['./sales-dashboard.component.scss'],
})
export class SalesDashboardComponent implements OnInit, OnDestroy {

  detail: any;  // Object to hold dashboard details
  chart: any;   // Chart instance
  chartSubscription: Subscription = new Subscription();  // Subscription for any future data streams

  constructor(
    private navController: NavController,
    private dataService: DataService,
    private loadingService: LoadingService,
    private toastService: ToastService
  ) { }

  // Method to navigate to the leads page
  openLeads() {
    this.navController.navigateForward("/sale-portal/leads");
  }

  ngOnInit() {
    this.initializeDetails();  // Initialize the dashboard details
    this.initializeChart();  // Initialize and render the chart
  }

  // Initialize static details for the dashboard
  initializeDetails() {
    this.detail = {} as any;
    this.detail.totalPendingLeads = 250;
    this.detail.totalAssignedLeadsToday = '456+';
    this.detail.todayRevenueGenerated = '₹555';
    this.detail.totalRevenueGenerated = '₹2680';
    this.detail.pendingPayments = '₹250';
    this.detail.customerPendingForApprovals = 139;
  }

  // Initialize and render the chart
  initializeChart() {
    var options = this.getChartOptions();  // Get chart options

    // Check if ApexCharts is available and render the chart
    const ApexCharts: any = 'ApexCharts' in window && window.ApexCharts;
    if (typeof ApexCharts === 'function') {
      this.chart = new ApexCharts(document.querySelector("#dashboard-chart"), options);
      this.chart.render();
    }
  }

  async getDashboardDetails(): Promise<any> {
    this.loadingService.show();  // Show loading spinner
    this.dataService.getDetails()
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();  // Hide loading spinner
          const data = response.data;
        },
        error: (error) => {
          this.loadingService.hide();  // Hide loading spinner
          this.toastService.show(error.message || 'Unexpected error occurred');
        }
      });
  }

  // Method to return chart configuration options
  getChartOptions() {
    return {
      series: [{
        name: 'Prices',
        data: [1000, 2000, 3000, 2000, 3000, 4000, 3000]  // Sample price values
      }],
      chart: {
        height: 300,
        type: 'bar',
        toolbar: {
          show: true,  // Show toolbar with custom options
          tools: {
            download: false,
            selection: false,
            zoom: false,
            zoomin: false,
            zoomout: false,
            pan: false,
            reset: false,
            customIcons: [
              {
                icon: '<div class="custom-toolbar-icon"><img src="assets/images/svg/discount.svg"></div>',
                index: 0,
                title: 'Custom Reset',
                click: function (e: any) {
                  console.log('Custom Reset icon clicked');  // Log click event for custom icon
                }
              }
            ]
          },
          export: {
            csv: {
              filename: undefined,
              columnDelimiter: ',',
              headerCategory: 'category',
              headerValue: 'value',
              categoryFormatter(x: any) {
                return new Date(x).toDateString();
              },
              valueFormatter(y: any) {
                return y;
              }
            },
            svg: {
              filename: undefined,
            },
            png: {
              filename: undefined,
            }
          },
          autoSelected: 'zoom'
        }
      },
      plotOptions: {
        bar: {
          borderRadius: 10,
          dataLabels: {
            position: 'top',
          },
        }
      },
      fill: {
        colors: ['#F1F3F7', '#2C3C64', '#000000']  // Color scheme for the bars
      },
      dataLabels: {
        enabled: true,
        formatter: function (val: any) {
          return '₹' + val;  // Format data labels with currency
        },
        style: {
          colors: ['#000'],
          fontSize: '12px',
          fontWeight: 'bold',
          background: '#F1F3F7',
          padding: {
            left: 8,
            right: 8,
            top: 4,
            bottom: 4
          }
        },
        offsetY: -20  // Adjust label position
      },
      xaxis: {
        categories: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "July"],  // X-axis categories
        position: 'bottom',
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        },
        crosshairs: {
          fill: {
            type: 'gradient',
            gradient: {
              colorFrom: '#D8E3F0',
              colorTo: '#BED1E6',
              stops: [0, 100],
              opacityFrom: 0.4,
              opacityTo: 0.5,
            }
          }
        },
        tooltip: {
          enabled: true
        },
        labels: {
          style: {
            fontSize: '16px',
          }
        },
        grid: {
          show: false
        }
      },
      yaxis: {
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false,
        },
        labels: {
          show: false,
        },
        grid: {
          show: false
        }
      },
      tooltip: {
        enabled: false
      },
      title: {
        text: 'Monthly Revenue',
        floating: true,
        offsetY: 5,
        offsetX: 10,
        align: 'left',
        style: {
          color: '#000000',
          fontSize: '12px',
          fontWeight: '500'
        }
      },
      subtitle: {
        text: '₹25,250',
        offsetY: 20,
        offsetX: 10,
        style: {
          color: '#444',
          background: 'transparent',
          fontSize: '36px',
          fontWeight: 'bold'
        }
      },
      grid: {
        show: false
      }
    };
  }

  ngOnDestroy() {
    if (this.chartSubscription) {
      this.chartSubscription.unsubscribe();  // Unsubscribe from any subscriptions
    }
    if (this.chart) {
      this.chart.destroy();  // Destroy the chart instance
    }
  }

}
