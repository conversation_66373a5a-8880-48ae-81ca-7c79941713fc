<ion-content>
  <!-- Main container for the sales dashboard -->
  <div class="sales-dashboard-container">
    <!-- Container for the main dashboard greeting and any potential icons -->
    <div class="dashboard-main-container">
      <span class="heading">Welcome Back,</span>
      <!-- <div class="dashboard-icon-container">
        <ion-img src="assets/images/icons/oval.png"></ion-img>
        <div class="dashboard-icon-background">
          <ion-icon src="assets/images/svg/setting.svg" class="dashboard-icon"></ion-icon>
        </div>
      </div> -->
    </div>
    <!-- Container for dashboard cards displaying key metrics -->
    <div class="dashboard-container">
      <!-- Row containing the first set of dashboard cards -->
      <div class="dashboard-card-row">
        <!-- Card displaying the total pending leads -->
        <div class="dashboard-card total-pending-leads" (click)="openLeads()">
          <ion-icon src="assets/images/svg/magnet-user.svg" class="icon-left"></ion-icon>
          <ion-icon src="assets/images/svg/file-circle-info.svg" class="icon-right"></ion-icon>
          <div class="dashboard-card-content">
            <h2>{{detail?.totalPendingLeads}}</h2>
            <p class="page-heading">Total Pending Leads</p>
          </div>
        </div>
        <!-- Card displaying the total assigned leads for today -->
        <div class="dashboard-card total-assigned-leads-today">
          <ion-icon src="assets/images/svg/lead.svg" class="icon-left"></ion-icon>
          <ion-icon src="assets/images/svg/file-circle-info.svg" class="icon-right"></ion-icon>
          <div class="dashboard-card-content">
            <h2>{{detail?.totalAssignedLeadsToday}}</h2>
            <p class="page-heading">Total Assigned Leads Today</p>
          </div>
        </div>
      </div>
      <!-- Row containing the second set of dashboard cards -->
      <div class="dashboard-card-row">
        <!-- Card displaying today's revenue generated -->
        <div class="dashboard-card today-revenue">
          <ion-icon src="assets/images/svg/revenue-alt.svg" class="icon-left"></ion-icon>
          <ion-icon src="assets/images/svg/file-circle-info.svg" class="icon-right"></ion-icon>
          <div class="dashboard-card-content">
            <h2>{{detail?.todayRevenueGenerated}}</h2>
            <p class="page-heading">Today's Revenue Generated</p>
          </div>
        </div>
        <!-- Card displaying total revenue generated -->
        <div class="dashboard-card total-revenue">
          <ion-icon src="assets/images/svg/revenue.svg" class="icon-left-total-revenue"></ion-icon>
          <ion-icon src="assets/images/svg/file-circle-info-white.svg" class="icon-right"></ion-icon>
          <div class="dashboard-card-content">
            <h2 class="heading-title">{{detail?.totalRevenueGenerated}}</h2>
            <p class="page-heading-total-revenue">Total Revenue Generated</p>
          </div>
        </div>
      </div>
      <!-- Row containing the third set of dashboard cards -->
      <div class="dashboard-card-row">
        <!-- Card displaying pending payments -->
        <div class="dashboard-card pending-payments">
          <ion-icon src="assets/images/svg/hand.svg" class="icon-left"></ion-icon>
          <ion-icon src="assets/images/svg/file-circle-info.svg" class="icon-right"></ion-icon>
          <div class="dashboard-card-content">
            <h2>{{detail?.pendingPayments}}</h2>
            <p class="page-heading">Pending Payments</p>
          </div>
        </div>
        <!-- Card displaying customers pending for approvals -->
        <div class="dashboard-card customer-pending-approvals">
          <ion-icon src="assets/images/svg/user-trust.svg" class="icon-left"></ion-icon>
          <ion-icon src="assets/images/svg/file-circle-info.svg" class="icon-right"></ion-icon>
          <div class="dashboard-card-content">
            <h2>{{detail?.customerPendingForApprovals}}</h2>
            <p class="page-heading">Customer Pending For Approvals</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Container for the dashboard chart -->
    <div class="dashboard-chart-container">
      <div id="dashboard-chart" class="apex-chart monthly-chart">
      </div>
    </div>
  </div>
</ion-content>