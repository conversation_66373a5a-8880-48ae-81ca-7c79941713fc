<!-- Conditionally render the toolbar -->
<ion-toolbar *ngIf="showToolbar" class="sale-toolbar">
  <ion-buttons slot="start">
    <!-- Menu <PERSON> -->
    <ion-button class="sale-menu-button" (click)="openMenu()">
      <ion-icon src="assets/images/svg/menu.svg"></ion-icon>
    </ion-button>
  </ion-buttons>
  <!-- Title in the center of the toolbar -->
  <ion-title class="sales-custom-title">{{ title }}</ion-title>
  <ion-buttons slot="end" class="sale-toolbar-icon-container">
    <!-- Icon and image on the right end of the toolbar -->
    <ion-img src="assets/images/icons/Oval.png"></ion-img>
    <!-- <div class="sale-toolbar-icon-backgrounds">
      <ion-icon src="assets/images/svg/setting.svg" class="sale-toolbar-icon"></ion-icon>
    </div> -->
  </ion-buttons>
</ion-toolbar>

<!-- Side Menu -->
<ion-menu side="start" menuId="menu" contentId="main-content" disabled="false">
  <ion-header>
    <!-- Header of the side menu -->
    <ion-toolbar class="sales-custom-toolbar">
      <ion-title class="side-menu-custom-title">Forbcorp</ion-title>
    </ion-toolbar>
  </ion-header>
  <ion-content>
    <ion-list>
      <!-- Menu items with clickable options -->
      <ion-menu-toggle auto-hide="false">
        <ion-item (click)="openSalesDashboard()" detail="false" class="side-menu-item">
          <div class="side-menu-icon-container">
            <ion-icon class="selected-icon" [src]="'/assets/images/svg/magnet-user.svg'"></ion-icon>
          </div>
          <ion-label>Dashboard</ion-label>
        </ion-item>
      </ion-menu-toggle>
      <ion-menu-toggle auto-hide="false">
        <ion-item (click)="openSalesProfile()" detail="false" class="side-menu-item">
          <div class="side-menu-icon-container">
            <ion-icon class="selected-icon" [src]="'/assets/images/svg/magnet-user.svg'"></ion-icon>
          </div>
          <ion-label>Profile</ion-label>
        </ion-item>
      </ion-menu-toggle>
      <ion-menu-toggle auto-hide="false">
        <ion-item (click)="openSalesAccount()" detail="false" class="side-menu-item">
          <div class="side-menu-icon-container">
            <ion-icon class="selected-icon" [src]="'/assets/images/svg/magnet-user.svg'"></ion-icon>
          </div>
          <ion-label>Account</ion-label>
        </ion-item>
      </ion-menu-toggle>
    </ion-list>
    <!-- Logout Card -->
    <ion-card class="logout-card" (click)="logout()">
      <ion-card-content class="logout-text">
        Logout
      </ion-card-content>
    </ion-card>
  </ion-content>
</ion-menu>

<ion-content>
  <ion-router-outlet id="main-content"></ion-router-outlet>
</ion-content>