import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ModalController, ToastController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';

@Component({
  selector: 'app-add-family-details',
  templateUrl: './add-family-details.component.html',
  styleUrls: ['./add-family-details.component.scss'],
})
export class AddFamilyDetailsComponent  implements OnInit {

  familyMember = {
    name: '',
    gender: '',
    dateOfBirth: '',
    adhaarNumber : '', 
    passportDetails: ''
  };

  genderDetails = [
    { id: 1, name: 'Male' },
    { id: 2, name: 'Female' },
    { id: 3, name: 'Other' },
  ];

  onClickValidation!: boolean;
  @ViewChild('saveButton', { static: true }) saveButton: any; 

  constructor(private modalCtrl: ModalController,private router:Router,
    private loadingService:LoadingService,private dataService:DataService,
    private toastController:ToastController) {
    this.onClickValidation = false; 
  }

  ngOnInit() {
    this.onClickValidation = false; 
  }

  dismiss() {
    this.modalCtrl.dismiss();
  }

  async saveBasicInfo(event: Event,form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    this.loadingService.show();
      this.dataService.customerRegister(this.familyMember)
        .subscribe({
          next: (response: RestResponse) => {
            this.loadingService.hide();
            const data = response.data;
            
            console.log('Saving Family Information:', this.familyMember);
            setTimeout(() => {
              this.router.navigate(['/sale-portal/add/package']);
            }, 500);
          }, error: (error:any) => {
            this.loadingService.hide();
//          this.presentToast(error.message);
            this.presentToast("Api was not correct.");
            setTimeout(() => {
             this.router.navigate(['/sale-portal/add/package']);
            }, 500);
          }
        })
  }

  skipFamilyDetails(){
    this.router.navigate(['/sale-portal/add/package']);
  }

  openPreviousPage(){
    this.router.navigate(['/sale-portal/profile']);
  }

  async presentToast(message: string) {
    const toast = await this.toastController.create({
      message: message,
      duration: 2000,
      position: 'bottom'
    });
    toast.present();
  }

}
