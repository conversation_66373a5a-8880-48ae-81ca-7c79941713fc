
<ion-content>
  <div class="login-container">
    <div class="login-inner-container">
      <div class="login-header-container">
        <div class="left-side-info-section">
          <div class="skip-link margin-top-10">
            <ion-row >
              <ion-col >
                <a routerLink="/skip" (click)="skipFamilyDetails()">Skip</a>
              </ion-col>
            </ion-row>
          </div>
          <h2>Add Family Details</h2>
        </div>
      </div>
      <div class="login-body-container">
        <form class="site-form" novalidate="novalidate" #userForm="ngForm" >
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Name Of Family Member" labelPlacement="floating"
              placeholder="Name Of Family Member" required="required" name="nameOfFamilyMember" #nameOfFamilyMember="ngModel" [(ngModel)]="familyMember.name"
              [ngClass]="{'is-invalid':nameOfFamilyMember.invalid && onClickValidation}"></ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && nameOfFamilyMember.errors && nameOfFamilyMember.errors['required']">
              Please provide a valid value.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-card-content>
              <ion-item lines="none" class="site-form-field" fill="outline" mode="md">
                <ion-label slot="start">Gender</ion-label>
                <ion-select interface="popover" placeholder="Please select an option"
                required="required" name="gender" #gender="ngModel" [(ngModel)]="familyMember.gender" 
                [ngClass]="{'is-invalid':gender.invalid && onClickValidation}">
                  <ion-select-option *ngFor="let gender of genderDetails" [value]="gender.id">{{ gender.name }}</ion-select-option>
                </ion-select>
              </ion-item>
              <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && gender.errors && gender.errors['required']">
              Please select gender.
            </ion-note>
            </ion-card-content>
          </div>
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Date of Birth" labelPlacement="floating"
              type="date" placeholder="Date of Birth" required="required" name="dateofBirth" #dateofBirth="ngModel"
              [(ngModel)]="familyMember.dateOfBirth" [ngClass]="{'is-invalid':dateofBirth.invalid && onClickValidation}">
            </ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && dateofBirth.errors && dateofBirth.errors['required']">
              Please select date.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Adhaar Number" labelPlacement="floating"
              placeholder="Adhaar Number" required="required" name="adhaarNumber" #adhaarNumber="ngModel"
              [(ngModel)]="familyMember.adhaarNumber" [ngClass]="{'is-invalid':adhaarNumber.invalid && onClickValidation}"
              mask="0000 0000 0000"></ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && adhaarNumber.errors && adhaarNumber.errors['required']">
              Please provide a valid value.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Passport Details" labelPlacement="floating"
              placeholder="Passport Details" required="required" name="passportDetails" #passportDetails="ngModel"
              [(ngModel)]="familyMember.passportDetails" [ngClass]="{'is-invalid':passportDetails.invalid && onClickValidation}"
              ></ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && passportDetails.errors && passportDetails.errors['required']">
              Please provide a valid value.
            </ion-note>
          </div>
          <ion-button color="primary" class="family-button margin-top-20" >
            Add Another Family Member
          </ion-button>
          
          <div class="family-button">
            <ion-button color="primary" class="previous-button uppercase margin-top-20" (click)="openPreviousPage()">
              Previous
            </ion-button>
            <ion-button color="primary" class="next-button uppercase margin-top-20" (click)="saveBasicInfo($event, userForm.form)" #saveButton>
              Save & Next
            </ion-button>
          </div>
        </form>
      </div>
    </div>
  </div>
</ion-content>
