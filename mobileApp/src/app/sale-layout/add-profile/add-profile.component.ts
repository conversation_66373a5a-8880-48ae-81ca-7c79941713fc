import { Component, OnInit } from '@angular/core';
import { NavController, ModalController, ToastController } from '@ionic/angular';
import { AddFamilyDetailsComponent } from '../add-family-details/add-family-details.component';
import { Router } from '@angular/router';
import { LoadingService } from 'src/services/loading.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';

@Component({
  selector: 'app-add-profile',
  templateUrl: './add-profile.component.html',
  styleUrls: ['./add-profile.component.scss'],
})
export class AddProfileComponent  implements OnInit {

  profile = {
    name : '',
    email: '',
    phoneNumber : '', 
    adhaarNumber : '', 
    dateOfBirth: '',
    weddingAnniversary: '',
    address : '',
    country: '',
    city: '',
    specialInterests: '',
    passportNumber: '',
    passportDateOfIssue: '',
    passportDateOfExpiry: '',
    passportCountry: '',
    emergencyContactNumber: '',
    emergencyContactName: '',
    emergencyContactEmail: '',
  };

  twoFactorEnabled: boolean = false;
  onClickValidation!: boolean;
  selectedFile: File | null = null;
  showCountryList: boolean = false;

  countries = [
    { name: 'United States', code: 'US' },
    { name: 'Canada', code: 'CA' },
    { name: 'United Kingdom', code: 'UK' },
    { name: 'Australia', code: 'AU' },
    { name: 'Germany', code: 'DE' },
    { name: 'France', code: 'FR' },
  ];

  cities = [
    { id: 1, name: 'New York' },
    { id: 2, name: 'Los Angeles' },
    { id: 3, name: 'Chicago' },
    { id: 4, name: 'Houston' },
    { id: 5, name: 'Phoenix' },
  ];

  constructor(private navCtrl: NavController,private modalCtrl: ModalController,
    private router:Router,private loadingService:LoadingService,private dataService:DataService,
    private toastController:ToastController) {
    this.onClickValidation = false; 
   }

  ngOnInit() {
    this.onClickValidation = false; 
 //   this.profile.country = 'US'; 
  }

  toggleTwoFactor() {
    this.twoFactorEnabled = !this.twoFactorEnabled;
    if(this.twoFactorEnabled){
    //  this.router.navigate(['/account/two/factor']);
    }
  }

  async saveBasicInfo(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    this.loadingService.show();
      this.dataService.customerRegister(this.profile)
        .subscribe({
          next: (response: RestResponse) => {
            this.loadingService.hide();
            const data = response.data;
            
            console.log('Saving Basic Information:', this.profile);
            setTimeout(() => {
              this.router.navigate(['/sale-portal/family/profile']);
            }, 500);
          }, error: (error:any) => {
            this.loadingService.hide();
//          this.presentToast(error.message);
            this.presentToast("Api was not correct.");
            setTimeout(() => {
              this.router.navigate(['/sale-portal/family/profile']);
            }, 500);
          }
        })
  }

  async presentToast(message: string) {
    const toast = await this.toastController.create({
      message: message,
      duration: 2000,
      position: 'bottom'
    });
    toast.present();
  }

  toggleCountryList() {
    this.showCountryList = !this.showCountryList;
  }

  countryChanged(event:any) {
    console.log('Selected Country:', event.detail.value);
    // Implement any logic when country selection changes
  }

  onFileSelected(event: any): void {
    const file: File = event.target.files[0];
    if (file) {
      this.selectedFile = file;
      // You can perform additional actions here, such as uploading the file
    }
  }

}
