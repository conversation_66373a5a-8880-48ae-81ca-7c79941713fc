
<ion-content>
  <div class="login-container">
    <div class="login-inner-container">
      <div class="login-header-container">
        <div class="left-side-info-section">
          <h2>Add Profile</h2>
        </div>
      </div>
      <div class="login-body-container">
        <form class="site-form" novalidate="novalidate" #userForm="ngForm" (ngSubmit)="saveBasicInfo(userForm.form)">
          <div class="site-form-field-container">
   <!-- <label for="nameField">Name*</label>  when this label line uncomment then this id add in ion-input :- id="nameField" -->
            <ion-input class="site-form-field" fill="outline" mode="md" label="Name" labelPlacement="floating"
              placeholder="Name" required="required" name="name" #name="ngModel" [(ngModel)]="profile.name"
              [ngClass]="{'is-invalid':name.invalid && onClickValidation}"></ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && name.errors && name.errors['required']">
              Please provide a valid value.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <input type="file" (change)="onFileSelected($event)" accept=".pdf,.doc,.docx,.txt">
            <!-- <p *ngIf="selectedFile">Selected File: {{ selectedFile.name }}</p> -->
          </div>
          <div class="site-form-field-container margin-top-20">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Email Address"
              labelPlacement="floating" placeholder="Email Address" required="required" name="userEmail" #userEmail="ngModel"
              pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$" [(ngModel)]="profile.email"
              [ngClass]="{'is-invalid':userEmail.invalid && onClickValidation}"></ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && userEmail.errors && userEmail.errors['required']">
              Please provide a valid value.
            </ion-note>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && userEmail.errors && userEmail.errors['pattern']">
              Please provide a valid email address.
            </ion-note>
          </div>
          <div class="site-form-field-container margin-top-20">
            <ion-input type="text" class="site-form-field" fill="outline" mode="md" label="Phone Number"
              labelPlacement="floating" placeholder="Phone Number" required="required" name="userPhone"
              #userPhone="ngModel" [(ngModel)]="profile.phoneNumber"
              [ngClass]="{'is-invalid':userPhone.invalid && onClickValidation}" mask="************"></ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && userPhone.errors && userPhone.errors['required']">
              Please provide a valid value.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Adhaar Number" labelPlacement="floating"
              placeholder="Adhaar Number" required="required" name="adhaarNumber" #adhaarNumber="ngModel"
              [(ngModel)]="profile.adhaarNumber" [ngClass]="{'is-invalid':adhaarNumber.invalid && onClickValidation}"
              mask="0000 0000 0000"></ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && adhaarNumber.errors && adhaarNumber.errors['required']">
              Please provide a valid value.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Date of Birth" labelPlacement="floating"
              type="date" placeholder="Date of Birth" required="required" name="dateofBirth" #dateofBirth="ngModel"
              [(ngModel)]="profile.dateOfBirth" [ngClass]="{'is-invalid':dateofBirth.invalid && onClickValidation}">
            </ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && dateofBirth.errors && dateofBirth.errors['required']">
              Please select date.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Wedding Anniversary"
              labelPlacement="floating" type="date" placeholder="Wedding Anniversary" required="required"
              name="weddingAnniversary" #weddingAnniversary="ngModel" [(ngModel)]="profile.weddingAnniversary"
              [ngClass]="{'is-invalid':weddingAnniversary.invalid && onClickValidation}">
            </ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && weddingAnniversary.errors && weddingAnniversary.errors['required']">
              Please select date.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-textarea rows="5" class="site-form-field" fill="outline" mode="md" label="Address"
              labelPlacement="floating" type="text" placeholder="Address" required="required" name="address"
              #address="ngModel" [(ngModel)]="profile.address" [ngClass]="{'is-invalid':address.invalid && onClickValidation}">
            </ion-textarea>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && address.errors && address.errors['required']">
              Please provide a valid value.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-card-content>
              <ion-item lines="none" class="site-form-field" fill="outline" mode="md">
                <ion-label slot="start">Country</ion-label>
                <ion-select interface="popover" placeholder="Please select an option"
                required="required" name="country" #country="ngModel" [(ngModel)]="profile.country" 
                [ngClass]="{'is-invalid':country.invalid && onClickValidation}">
                  <ion-select-option *ngFor="let country of countries" [value]="country.code">{{ country.name }}</ion-select-option>
                </ion-select>
              </ion-item>
              <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && country.errors && country.errors['required']">
              Please select country.
            </ion-note>
            </ion-card-content>
          </div>
          <div class="site-form-field-container">
            <ion-card-content>
              <ion-item lines="none" class="site-form-field" fill="outline" mode="md">
                <ion-label slot="start">City</ion-label>
                <ion-select interface="popover" placeholder="Please select an option"
                   required="required" name="city" #city="ngModel" [(ngModel)]="profile.city" 
                  [ngClass]="{'is-invalid': city.invalid && onClickValidation}">
                  <ion-select-option *ngFor="let city of cities" [value]="city.id">{{ city.name }}</ion-select-option>
                </ion-select>
              </ion-item>
              <ion-note slot="error" class="error-text"
                *ngIf="onClickValidation && city.errors && city.errors['required']">
                Please select a city.
              </ion-note>
            </ion-card-content>
          </div>       
          <div class="site-form-field-container">
            <ion-textarea rows="5" class="site-form-field" fill="outline" mode="md" label="Special Interest"
              labelPlacement="floating" type="text" placeholder="Special Interest" required="required" name="specialInterest"
              #specialInterest="ngModel" [(ngModel)]="profile.specialInterests" [ngClass]="{'is-invalid':specialInterest.invalid && onClickValidation}">
            </ion-textarea>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && specialInterest.errors && specialInterest.errors['required']">
              Please provide a valid value.
            </ion-note>
          </div>   
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Passport Number" labelPlacement="floating"
              placeholder="Passport Number" required="required" name="passportNumber" #passportNumber="ngModel"
              [(ngModel)]="profile.passportNumber" [ngClass]="{'is-invalid':passportNumber.invalid && onClickValidation}"
              ></ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && passportNumber.errors && passportNumber.errors['required']">
              Please provide a valid value.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Passport Date Of Issue"
              labelPlacement="floating" type="date" placeholder="Passport Date Of Issue" required="required"
              name="passportDateOfIssue" #passportDateOfIssue="ngModel" [(ngModel)]="profile.passportDateOfIssue"
              [ngClass]="{'is-invalid':passportDateOfIssue.invalid && onClickValidation}">
            </ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && passportDateOfIssue.errors && passportDateOfIssue.errors['required']">
              Please select date.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Passport Date Of Expiry"
              labelPlacement="floating" type="date" placeholder="Passport Date Of Expiry" required="required"
              name="passportDateOfExpiry" #passportDateOfExpiry="ngModel" [(ngModel)]="profile.passportDateOfExpiry"
              [ngClass]="{'is-invalid':passportDateOfExpiry.invalid && onClickValidation}">
            </ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && passportDateOfExpiry.errors && passportDateOfExpiry.errors['required']">
              Please select date.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Passport Country"
              labelPlacement="floating" type="text" placeholder="Passport Country" required="required"
              name="passportCountry" #passportCountry="ngModel" [(ngModel)]="profile.passportCountry"
              [ngClass]="{'is-invalid':passportCountry.invalid && onClickValidation}">
            </ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && passportCountry.errors && passportCountry.errors['required']">
              Please provide a valid value.
            </ion-note>
          </div>
          <div class="site-form-field-container">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Emergency Contact Number"
              labelPlacement="floating" type="text" placeholder="Emergency Contact Number" required="required"
              name="emergencyContactNumber" #emergencyContactNumber="ngModel" [(ngModel)]="profile.emergencyContactNumber"
              mask="0000 0000 0000" [ngClass]="{'is-invalid':emergencyContactNumber.invalid && onClickValidation}">
            </ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && emergencyContactNumber.errors && emergencyContactNumber.errors['required']">
              Please select date.
            </ion-note>
          </div>
          <div class="site-form-field-container">
           <ion-input class="site-form-field" fill="outline" mode="md" label="Emergency Contact Name" labelPlacement="floating"
              placeholder="Emergency Contact Name" required="required" name="emergencyContactName" #emergencyContactName="ngModel" 
              [(ngModel)]="profile.emergencyContactName" [ngClass]="{'is-invalid':emergencyContactName.invalid && onClickValidation}"></ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && emergencyContactName.errors && emergencyContactName.errors['required']">
              Please provide a valid value.
            </ion-note>
          </div>
          <div class="site-form-field-container margin-top-20">
            <ion-input class="site-form-field" fill="outline" mode="md" label="Emergency Contact Email"
              labelPlacement="floating" placeholder="Emergency Contact Email" required="required" 
              name="emergencyContactEmail" #emergencyContactEmail="ngModel"
              pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,3}$" [(ngModel)]="profile.email"
              [ngClass]="{'is-invalid':emergencyContactEmail.invalid && onClickValidation}"></ion-input>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && emergencyContactEmail.errors && emergencyContactEmail.errors['required']">
              Please provide a valid value.
            </ion-note>
            <ion-note slot="error" class="error-text"
              *ngIf="onClickValidation && emergencyContactEmail.errors && emergencyContactEmail.errors['pattern']">
              Please provide a valid email address.
            </ion-note>
          </div>

          <ion-button color="primary" class="site-button large-button full-width uppercase margin-top-20" type="submit">
            Save & Next
          </ion-button>
        </form>
      </div>
    </div>
  </div>
</ion-content>
