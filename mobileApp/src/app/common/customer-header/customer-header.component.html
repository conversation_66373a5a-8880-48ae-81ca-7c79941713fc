<div class="customer-header-section">
  <div class="customer-header-container">
    <div class="dashboard-heading" *ngIf="!innerPage">
      <div class="dashboard-heading-text">Hello,</div>
      <div class="dashboard-subheading-text">{{ user?.fullName?.trim() ? user.fullName.trim() : 'Traveller' }}</div>
    </div>
    <div class="back-icon-container" (click)="back()" *ngIf="innerPage">
      <i-feather name="arrow-left" class="back-icon"></i-feather>
    </div>
    <div class="dashboard-heading inner-page-heading" *ngIf="innerPage">
      <div class="dashboard-subheading-text">
        {{headingText}}
      </div>
    </div>

    <div class="dashboard-icon-container">
      <div class="dashboard-icon-background" *ngIf="!innerPage && !membershipPage && !isLogggedIn">
        <img [src]="profileImageUrl||'/assets/images/icons/user.png'" alt="notification" />
      </div>
      <div *ngIf="!innerPage && membershipPage && (packageDetail?.packageCount ?? 0) > 1"
        class="dashboard-icon-background">
        <img src="/assets/images/svg/airplane.svg" alt="notification" (click)="onPackageActionClick()" />
      </div>
      <!-- Bell Icon -->
      <div class="dashboard-icon-background" *ngIf="!rightAction && !isMarkAllAction && !isLogggedIn">
        <img src="/assets/images/icons/bell-icon-with-background.svg" alt="notification"
          (click)="onRightActionClick()" />
        <span class="notification-count" *ngIf="notificationCount > 0">{{ notificationCount }}</span>
      </div>
      <!-- Right Action Icon -->
      <div class="dashboard-icon-background" *ngIf="rightAction && !isMarkAllAction ">
        <i-feather [name]="rightActionIcon" class="back-icon" (click)="onRightActionClick()"></i-feather>
      </div>

      <!-- Delete Icon Condition -->
      <div class="dashboard-icon-background" *ngIf="isMarkAllAction">
        <img class="notification-mark-all-image" src="/assets/images/svg/check-all.svg" alt="delete"
          (click)="onMarkAllActionClick()" />
        <span class="notification-count" *ngIf="notificationCount > 0">{{ notificationCount }}</span>
      </div>

    </div>
  </div>
</div>