import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { <PERSON><PERSON><PERSON>roller, ModalController, NavController } from '@ionic/angular';
import { AuthService } from 'src/shared/authservice';
import { DataService } from 'src/services/data.service'; // Add this import for notification service
import { ToastService } from 'src/shared/toast.service'; // Handle errors
import { EventService } from 'src/shared/event.service';
import { RestResponse } from 'src/shared/auth.model';
import { PackageData } from 'src/modals/customerShowPackageDetails';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { LoginComponent } from 'src/app/authentication/login/login.component';
import { CommonService } from 'src/services/common.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-customer-header',
  templateUrl: './customer-header.component.html',
  styleUrls: ['./customer-header.component.scss'],
})
export class CustomerHeaderComponent implements OnInit, OnDestroy {
  @Input() innerPage: boolean = false;
  @Input() membershipPage: boolean = false;
  @Input() isLogggedIn: boolean = false;
  @Input() package: any;
  @Input() headingText: string = "";
  @Input() rightAction: boolean = false;
  @Input() rightActionIcon: string = "";
  @Input() isMarkAllAction: boolean = false; // New input for mark-all-read action
  @Input() notificationCount: number = 0; // Dynamic notification count
  @Input() packageDetail: PackageData | null = null;

  @Output() rightActionCallback: EventEmitter<any> = new EventEmitter<any>();
  @Output() markAllActionCallback: EventEmitter<any> = new EventEmitter<any>(); // New output for mark-all-read action
  @Output() packageActionCallback: EventEmitter<any> = new EventEmitter<any>();

  user: any;
  private intervalId: any; // For periodic updates

  @Input() backUrl: string | null = null;
  @Input() profileImageUrl: string | null = null;
  loginIsOpened: boolean = false;
  eventSub!: Subscription;
  constructor(
    private authService: AuthService,
    private navController: NavController,
    private dataService: DataService, // For fetching unread count
    private toastService: ToastService, // For showing errors
    private eventService: EventService,
    private localStorageService: LocalStorageService,
    private commonService: CommonService,

  ) { }

  ngOnInit() {
    this.eventSub = this.eventService.event.subscribe((data: any) => {
      if (!data) {
        return;
      }
      if (data.key === "notification:read") {
        if (data.value === "ALL") {
          // TO DO ZERO
          this.notificationCount = 0;
        } else if (data.value === "SINGLE") {
          // TO DO count = count - 1
          this.notificationCount = this.notificationCount - 1;
        }
      }
      data = undefined;
    });
    this.user = this.authService.getUser();
    if (this.user) {
      this.fetchUnreadNotificationCount();
      this.startPeriodicUpdate(); // Start periodic updates
      this.fetchMyActiveSubscription();
      this.isLogggedIn = false;
    }
  }


  ionViewWillEnter() {
    this.user = this.authService.getUser();
    if (this.user) {
      this.fetchUnreadNotificationCount();
    }
    if (!this.profileImageUrl) {
      this.profileImageUrl = this.localStorageService.get('profileImageUrl') || '/assets/images/icons/user.png';
    }
  }

  ngOnDestroy() {
    this.stopPeriodicUpdate(); // Clear interval on component destruction
    if (this.eventSub) {
      this.eventSub.unsubscribe(); // Unsubscribe from event
    }
  }

  back() {
    if (this.backUrl?.startsWith('/portal/hotel/') && this.backUrl.includes('/details')) {
      this.localStorageService.setObject("resetGuestFormDetails", true);
    }
    if (this.backUrl) {
      this.navController.navigateBack(this.backUrl);
    } else {
      this.navController.back({ animated: true });
    }
  }

  onRightActionClick() {
    this.rightActionCallback.emit(null);
  }

  onPackageActionClick() {
    this.packageActionCallback.emit(null);
  }

  onMarkAllActionClick() {
    this.markAllActionCallback.emit();
  }

  fetchUnreadNotificationCount() {
    this.dataService.getUnreadNotificationCount().subscribe({
      next: (response: any) => {
        this.notificationCount = response.data || 0; // Update notification count
      },
      error: (error: any) => {
        this.notificationCount = 0; // Reset count in case of error
        // this.toastService.show(error.message || 'Unable to fetch notifications');
      },
    });
  }

  startPeriodicUpdate() {
    this.intervalId = setInterval(() => {
      this.user = this.authService.getUser();
      if (this.user) {
        this.fetchUnreadNotificationCount();
      }
    }, 30000); // Update every 30 seconds (adjust as needed)
  }

  stopPeriodicUpdate() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }
  fetchMyActiveSubscription() {
    this.dataService.fetchMyActiveSubscription()
      .subscribe({
        next: (response: RestResponse) => {
          this.packageDetail = response.data;
          this.localStorageService.setObject('coApplicantDetail', response.data.coApplicantDetail);
        },
        error: (error: any) => {
        }
      })
  }
}
