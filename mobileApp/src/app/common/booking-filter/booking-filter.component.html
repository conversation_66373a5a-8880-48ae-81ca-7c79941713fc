<div class="hotel-custom-input-row" id="open-modal" expand="block" (click)="openLocationSelectionPopup()">
  <div class="hotel-custom-input-icon">
    <i-feather name="search"></i-feather>
  </div>
  <div class="hotel-custom-input-text">
    <div class="destination-city">{{input.location?input.location.name:'Where do you want to stay?'}}</div>
    <div class="destination-country" *ngIf="input.location">{{input.location.countryName}}</div>
  </div>
</div>
<div class="hotel-custom-input-row">
  <div class="hotel-custom-input-icon">
    <i-feather name="calendar"></i-feather>
  </div>
  <div class="hotel-custom-input-text">
    <!-- <div class="destination-city">
      <span class="destination-date" (click)="openDatePicker('CHECK_IN_DATE')">
        <span>{{input.arrivalDate| date:'dd MMM'}}</span>
        <span class="destination-date-year">'{{input.arrivalDate| date:'yy'}}</span>
      </span>
      <span class="destination-night-counts">
        <span>{{input.totalNights}}</span>
        <span class="night-text">{{input.totalNights<=1?'NIGHT':'NIGHTS'}} </span>
        </span>
        <span class="destination-date" (click)="openDatePicker('CHECK_OUT_DATE')">
          <span>{{input.departureDate| date:'dd MMM'}}</span>
          <span class="destination-date-year">'{{input.departureDate| date:'yy'}}</span>
        </span>
    </div> -->

    <div class="destination-city">
      <span class="destination-date" (click)="openDatePicker('CHECK_IN_DATE')">
        <span>{{input.arrivalDate | date:'dd MMM'}}</span>
        <span class="destination-date-year">{{input.arrivalDate | date:'yy'}}</span>
      </span>
      <span class="destination-night-counts">
        <span>{{input.totalNights}}</span>
        <span class="night-text">{{input.totalNights <= 1 ? 'NIGHT' : 'NIGHTS' }}</span>
        </span>
        <span class="destination-date" (click)="openDatePicker('CHECK_OUT_DATE')">
          <span>{{input.departureDate | date:'dd MMM'}}</span>
          <span class="destination-date-year">{{input.departureDate | date:'yy'}}</span>
        </span>
    </div>


  </div>
</div>
<div class="hotel-custom-input-row" (click)="openAudltSelectionPopup()">
  <div class="hotel-custom-input-icon">
    <i-feather name="user"></i-feather>
  </div>
  <div class="hotel-custom-input-text">
    <div class="destination-city">
      <span>{{input.noOfRooms}} {{input.noOfRooms<=1?'Room':'Rooms'}}, {{input.noOfAdults}}
          {{input.noOfAdults<=1?'Adult':'Adults'}} & {{input.noOfChilds}}
          {{input.noOfAdults<=1?'Children':'Childrens'}}</span>
    </div>
  </div>
</div>

<div class="hotel-custom-input-row shadow-for-rating-section" *ngIf="bookingType === 'OTHERS' && showOnlyFilterOptions">
  <div class="hotel-custom-input-icon">
    <ion-icon name="star-outline" style="font-size: 20px;"></ion-icon>
  </div>
  <div class="hotel-custom-input-text">
    <div class="star-rating-row">
      <button class="star-btn star-padding-not-for-modal"
        [ngClass]="{ 'selected-star': input.hotelRatings && input.hotelRatings.includes(3) }" (click)="toggleRating(3)">
        3 Star
      </button>
      <button class="star-btn star-padding-not-for-modal"
        [ngClass]="{ 'selected-star': input.hotelRatings && input.hotelRatings.includes(4) }" (click)="toggleRating(4)">
        4 Star
      </button>
      <button class="star-btn star-padding-not-for-modal"
        [ngClass]="{ 'selected-star': input.hotelRatings && input.hotelRatings.includes(5) }" (click)="toggleRating(5)">
        5 Star
      </button>
    </div>
  </div>
</div>
<div class="error-message margin-top-10 margin-bottom-15" *ngIf="ratingErrorMessage">
  {{ratingErrorMessage}}
</div>

<ion-list *ngIf="showOnlyFilterOptions">
  <ion-item>
    <ion-label>Preferences:</ion-label>
    <ion-select class="preference-container" interface="alert" [(ngModel)]="tempPreferences" [multiple]="true"
      placeholder="Select Preferences" (ionChange)="onPreferencesConfirm()" (ionCancel)="onPreferencesCancel()"
      [interfaceOptions]="{ okButtonText: 'OK', cancelButtonText: 'Cancel' }">

      <ion-select-option *ngFor="let pref of PREFERENCES_OPTIONS" [value]="pref.id">
        {{ pref.name }}
      </ion-select-option>
    </ion-select>
  </ion-item>
</ion-list>

<ion-list *ngIf="showOnlyFilterOptions">
  <ion-item>
    <ion-label>Board Basis</ion-label>
    <ion-select #boardSelect class="preference-container" interface="alert" [(ngModel)]="tempBoardBasis"
      [multiple]="true" placeholder="Select Board Basis" (ionChange)="onBoardSelectConfirm()"
      (ionCancel)="onBoardSelectCancel()" [interfaceOptions]="{ okButtonText: 'OK', cancelButtonText: 'Cancel' }">
      <ion-select-option *ngFor="let option of BOARD_BASIS_OPTIONS" [value]="option.id">
        {{ option.name }}
      </ion-select-option>
    </ion-select>
  </ion-item>
</ion-list>



<hr class="hotel-horizontal-separator-line" *ngIf="bookingType === 'OTHERS' && showOnlyFilterOptions" />

<div class="price-range-section" *ngIf="bookingType === 'OTHERS' && showOnlyFilterOptions">
  <span class="section-title">Price Per Night</span>

  <div class="range-container margin-top-10">
    <div class="range-labels">
      <span class="range-text">₹ {{ commonService.formatAmount(input.minPrice) }}</span>
      <span class="range-text">
        ₹ {{ input.maxPrice === 500000 ? '5,00,000' : commonService.formatAmount(input.maxPrice) }}
      </span>
    </div>

    <ion-range dualKnobs="true" pin="true" min="0" max="500000" [(ngModel)]="input.priceRange"
      (ionChange)="onPriceChange($event)" class="custom-price-range"></ion-range>
  </div>
</div>

<ion-button class="site-full-rounded-button search-booking-filter margin-top-20" expand="full" shape="round"
  [disabled]="isProcessing" (click)="search()">
  {{searchButtonText?searchButtonText:'Search'}}
</ion-button>

<ion-modal class="location-popup" #locationSelectionPopup [isOpen]="isLocationSelectionPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>Select location</ion-title>
        <ion-buttons slot="start">
          <i-feather name="X" (click)="closeLocationSelectionPopup()"></i-feather>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content>
      <div class="ion-padding no-padding-bottom">
        <ion-item class="site-form-control" lines="none">
          <i-feather class="map-pin-icon start-icon" name="map-pin" slot="start"></i-feather>
          <ion-input label="Search Location" labelPlacement="floating" name="searchLocation"
            [(ngModel)]="searchLocation" (ionInput)="fetchPlaces()" [debounce]="500"></ion-input>
        </ion-item>
      </div>
      <div class="google-places-container">
        <ion-list class="google-places">
          <!-- <ion-item class="google-place" lines="full" (click)="onLoadMyCurrentLocation();modal.dismiss()">
            <i-feather class="map-pin-icon" name="navigation" slot="start"></i-feather>
            <ion-label>Use current location</ion-label>
          </ion-item> -->
          @if(loadingCities==='LOADING'){
          <ion-item class="google-place" lines="full">
            <i-feather class="map-pin-icon" name="map-pin" slot="start"></i-feather>
            <ion-label class="ion-text-wrap">
              <div class="city-name"><ion-skeleton-text [animated]="true" style="width: 40%;"></ion-skeleton-text></div>
              <div class="country-name"><ion-skeleton-text [animated]="true" style="width: 20%;"></ion-skeleton-text>
              </div>
            </ion-label>
          </ion-item>
          <ion-item class="google-place" lines="full">
            <i-feather class="map-pin-icon" name="map-pin" slot="start"></i-feather>
            <ion-label class="ion-text-wrap">
              <div class="city-name"><ion-skeleton-text [animated]="true" style="width: 40%;"></ion-skeleton-text></div>
              <div class="country-name"><ion-skeleton-text [animated]="true" style="width: 20%;"></ion-skeleton-text>
              </div>
            </ion-label>
          </ion-item>
          <ion-item class="google-place" lines="full">
            <i-feather class="map-pin-icon" name="map-pin" slot="start"></i-feather>
            <ion-label class="ion-text-wrap">
              <div class="city-name"><ion-skeleton-text [animated]="true" style="width: 40%;"></ion-skeleton-text></div>
              <div class="country-name"><ion-skeleton-text [animated]="true" style="width: 20%;"></ion-skeleton-text>
              </div>
            </ion-label>
          </ion-item>
          <ion-item class="google-place" lines="full">
            <i-feather class="map-pin-icon" name="map-pin" slot="start"></i-feather>
            <ion-label class="ion-text-wrap">
              <div class="city-name"><ion-skeleton-text [animated]="true" style="width: 40%;"></ion-skeleton-text></div>
              <div class="country-name"><ion-skeleton-text [animated]="true" style="width: 20%;"></ion-skeleton-text>
              </div>
            </ion-label>
          </ion-item>
          <ion-item class="google-place" lines="full">
            <i-feather class="map-pin-icon" name="map-pin" slot="start"></i-feather>
            <ion-label class="ion-text-wrap">
              <div class="city-name"><ion-skeleton-text [animated]="true" style="width: 40%;"></ion-skeleton-text></div>
              <div class="country-name"><ion-skeleton-text [animated]="true" style="width: 20%;"></ion-skeleton-text>
              </div>
            </ion-label>
          </ion-item>
          }
          <ion-item class="google-place" *ngFor="let place of availablePlaces" lines="full"
            (click)="onSelectLocation(place);locationSelectionPopup.dismiss()">
            <i-feather class="map-pin-icon" name="map-pin" slot="start"></i-feather>
            <ion-label class="ion-text-wrap">
              <div class="city-name">{{place.name}}</div>
              <div class="country-name">{{place.countryName}}</div>
            </ion-label>
          </ion-item>
        </ion-list>
      </div>
    </ion-content>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup adults-selection-popup" #audltSelectionPopup [isOpen]="isAudltSelectionPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header">
        <i-feather name="X" (click)="closeAudltSelectionPopup()"></i-feather>
        <div class="header-text">Select Room and Guests</div>
      </div>
      <div class="site-custom-popup-body no-padding-top guest-preview-padding-bottom" *ngIf="selectedInput">
        <div class="adults-selection-rows">
          <div class="adults-selection-row ion-padding no-margin-bottom">
            <div class="adults-selection-text">Rooms</div>
            <div class="adults-selection-select">
              <ion-select aria-label="Rooms" interface="popover" placeholder="Select" name="rooms"
                [(ngModel)]="selectedInput.noOfRooms" (ionChange)="onRoomChanged(selectedInput.noOfRooms)">
                <ion-select-option [value]="i+1" *ngFor="let item of [].constructor(8); let i = index"><span
                    *ngIf="i<9">0</span>{{i+1}}</ion-select-option>
              </ion-select>
            </div>
          </div>
          <div class="room-card" *ngFor="let room of selectedInput.rooms;let pi=index">
            <div class="room-heading">Room {{pi+1}}</div>
            <div class="room-body">
              <div class="adults-selection-row">
                <div class="adults-selection-text">Adults</div>
                <div class="adults-selection-select">
                  <ion-select aria-label="Adults" interface="popover" placeholder="Select" name="adults"
                    [(ngModel)]="room.noOfAdults">
                    <ion-select-option [value]="i+1" *ngFor="let item of [].constructor(6); let i = index"><span
                        *ngIf="i<9">0</span>{{i+1}}</ion-select-option>
                  </ion-select>
                </div>
              </div>
              <div class="adults-selection-row">
                <div class="adults-selection-text">
                  <div>Children</div>
                  <div class="help-text">Age: 2 - 12</div>
                </div>
                <div class="adults-selection-select">
                  <ion-select aria-label="Children" interface="popover" placeholder="Select" name="children"
                    [(ngModel)]="room.noOfChilds" (ionChange)="onChildrenChanged(room, room.noOfChilds)">
                    <ion-select-option [value]="0">00</ion-select-option>
                    <ion-select-option [value]="i+1" *ngFor="let item of [].constructor(5); let i = index"><span
                        *ngIf="i<9">0</span>{{i+1}}</ion-select-option>
                  </ion-select>
                </div>
              </div>
              @if(room.uiChildrenAges && room.uiChildrenAges.length>0){
              <div class="age-of-child-text">Age of Children</div>
              <div class="best-text">Please provide right number of children along with their age for best options and
                prices.</div>
              <div class="adults-selection-row" *ngFor="let item of room.uiChildrenAges;let i=index">
                <div class="adults-selection-text">
                  <div>Child {{i+1}}</div>
                  <div class="help-text">Age</div>
                </div>
                <div class="adults-selection-inner-row">
                  <div class="adults-selection-select" *ngIf="room.noOfRooms>1">
                    <ion-select aria-label="Room" interface="popover" placeholder="Select" name="childrenRoom"
                      [(ngModel)]="item.room">
                      <ion-select-option [value]="i+1"
                        *ngFor="let item of [].constructor(room.noOfRooms); let i = index"><span
                          *ngIf="i<9">0</span>{{i+1}}</ion-select-option>
                    </ion-select>
                  </div>
                  <div class="adults-selection-select margin-left-10">
                    <ion-select aria-label="Children" interface="popover" placeholder="Select" name="childrenAge"
                      [(ngModel)]="item.age">
                      <ion-select-option [value]="2">02</ion-select-option>
                      <ion-select-option [value]="3">03</ion-select-option>
                      <ion-select-option [value]="4">04</ion-select-option>
                      <ion-select-option [value]="5">05</ion-select-option>
                      <ion-select-option [value]="6">06</ion-select-option>
                      <ion-select-option [value]="7">07</ion-select-option>
                      <ion-select-option [value]="8">08</ion-select-option>
                      <ion-select-option [value]="9">09</ion-select-option>
                      <ion-select-option [value]="10">10</ion-select-option>
                      <ion-select-option [value]="11">11</ion-select-option>
                      <ion-select-option [value]="12">12</ion-select-option>
                    </ion-select>
                  </div>
                </div>
              </div>
              }
            </div>
          </div>
        </div>
        <div class="ion-padding">
          <div class="error-message margin-bottom-10" *ngIf="errorMessage">
            {{errorMessage}}
          </div>
          <ion-button class="site-full-rounded-button primary-button no-margin" expand="full" shape="round"
            (click)="compelte()">
            Done
          </ion-button>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="site-custom-popup adults-selection-popup" [isOpen]="isRatingSelectionPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header">
        <i-feather name="X" (click)="onDoneAfterSelectStars()"></i-feather>
        <div class="header-text">Rating</div>
      </div>
      <div class="site-custom-popup-body no-padding-top guest-preview-padding-bottom">
        <div class="adults-selection-rows">
          <div class="star-rating-row star-rating-row-padding-for-modal">
            <button class="star-btn"
              [ngClass]="{ 'selected-star': input.hotelRatings && input.hotelRatings.includes(3) }"
              (click)="toggleRating(3)">
              3 Star
            </button>
            <button class="star-btn"
              [ngClass]="{ 'selected-star': input.hotelRatings && input.hotelRatings.includes(4) }"
              (click)="toggleRating(4)">
              4 Star
            </button>
            <button class="star-btn"
              [ngClass]="{ 'selected-star': input.hotelRatings && input.hotelRatings.includes(5) }"
              (click)="toggleRating(5)">
              5 Star
            </button>
          </div>
        </div>
        <div class="ion-padding">
          <div class="error-message margin-bottom-15" *ngIf="ratingErrorMessage">
            {{ratingErrorMessage}}
          </div>
          <!-- <ion-button class="site-full-rounded-button primary-button no-margin" expand="full" shape="round"
            (click)="onDoneAfterSelectStars()">
            Done
          </ion-button> -->
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>