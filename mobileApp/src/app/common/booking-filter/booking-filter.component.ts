import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Geolocation } from '@capacitor/geolocation';
import { NativeGeocoder, ReverseOptions } from '@capgo/nativegeocoder';
import { IonInput } from '@ionic/angular';
import { DatePicker } from '@pantrist/capacitor-date-picker';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-booking-filter',
  templateUrl: './booking-filter.component.html',
  styleUrls: ['./booking-filter.component.scss'],
})
export class BookingFilterComponent implements OnInit {
  @Input()
  input: any;
  @Input()
  searchButtonText: string = "";
  @Input()
  bookingType: string | null = null;
  @Input() showOnlyFilterOptions: boolean = false;
  @Output()
  searchCallback: EventEmitter<any> = new EventEmitter<any>();
  @Input() isProcessing: boolean = false;
  @Input() reset: boolean = false;

  presentingElement: any = null;
  searchLocation: string | undefined;
  availablePlaces: Array<any> = new Array<any>();
  availablePopularPlaces: Array<any> = new Array<any>();
  loadingCities: string = "NOT_STARTED";
  selectedInput: any;
  isLocationSelectionPopupOpen: boolean = false;
  isAudltSelectionPopupOpen: boolean = false;
  isRatingSelectionPopupOpen: boolean = false;
  errorMessage: string | null = null;
  ratingErrorMessage: string | null = null;
  user: any;
  selectedPreferences: string[] = [];
  selectedBoardBasis: string[] = [];
  tempBoardBasis: string[] = [];
  tempPreferences: string[] = [];

  @ViewChild('locationInput') locationInput!: IonInput;

  constructor(
    private readonly toastService: ToastService,
    private readonly dataService: DataService,
    private readonly route: ActivatedRoute,
    private readonly changeDetectorRef: ChangeDetectorRef,
    public readonly commonService: CommonService,
    private readonly authService: AuthService,
    private readonly localStorageService: LocalStorageService,
  ) { }

  ngOnInit() {
    this.user = this.authService.getUser();
    if (!this.input) {
      this.input = {
        arrivalDate: new Date(new Date().getTime() + 24 * 60 * 60 * 1000), // Tomorrow
        departureDate: new Date(new Date().getTime() + 48 * 60 * 60 * 1000), // Day after tomorrow
        noOfRooms: 1,
        noOfAdults: 1,
        noOfChilds: 0,
        childrenAges: [],
        totalNights: 1,
        rooms: [{ type: `Room-1`, noOfAdults: 1, noOfChilds: 0, childrenAges: [], uiChildrenAges: [] }],
        bookingType: this.bookingType,
        hotelRatings: [3, 4, 5],
        minPrice: 0,
        maxPrice: 500000,
        priceRange: { lower: 0, upper: 500000 }
      } as any;
    }

    this.route.queryParams.subscribe(params => {
      this.bookingType = params['bookingType'] || "OTHERS"
    });

    this.presentingElement = document.querySelector('.ion-page');
    this.availablePlaces = new Array<any>();
    this.availablePopularPlaces = new Array<any>();
    this.input.boardBasis = null;
    this.input.preferences = null;

    if (this.reset) {
      this.searchLocation = '';
      // this.locationInput?.setFocus();
      // this.openLocationSelectionPopup();
    }

    // Load saved preferences
    const savedPreferences = this.localStorageService.getObject('HOTEL_PREFERENCES');
    if (savedPreferences && Array.isArray(savedPreferences)) {
      this.selectedPreferences = [...savedPreferences];
      this.tempPreferences = [...savedPreferences];
      this.input.preferences = this.selectedPreferences.toString();
    }

    // Load saved board basis
    const savedBoardBasis = this.localStorageService.getObject('HOTEL_BOARD_BASIS');
    if (savedBoardBasis && Array.isArray(savedBoardBasis)) {
      this.selectedBoardBasis = [...savedBoardBasis];
      this.tempBoardBasis = [...savedBoardBasis];
      this.input.boardBasis = this.selectedBoardBasis.toString();
    }

    this.fetchPopularAvailableCities();
  }

  ionViewWillEnter() {
    this.route.queryParams.subscribe(params => {
      this.bookingType = params['bookingType'] || "OTHERS"
    });
  }

  onPriceChange(event: any) {
    const range = event.detail.value;
    const minGap = 500;

    // If the right knob is being moved before the left knob + gap
    if (range.upper < range.lower) {
      // Cancel this change, keep previous range
      event.preventDefault();
      return;
    }

    // Now it's a valid move, update values
    this.input.priceRange = { lower: range.lower, upper: range.upper };
    this.input.minPrice = range.lower;
    this.input.maxPrice = range.upper;
  }

  openRatingSelectionPopup() {
    this.isRatingSelectionPopupOpen = true;
  }

  closeRatingSelectionPopup() {
    this.isRatingSelectionPopupOpen = false;
  }

  toggleRating(star: number) {
    if (!this.input.hotelRatings) {
      this.input.hotelRatings = [];
    }
    const index = this.input.hotelRatings.indexOf(star);
    if (index > -1) {
      if (this.input.hotelRatings.length === 1) { // Prevent unselecting the last selected star
        this.ratingErrorMessage = 'Must choose a rating to continue.';
        return;
      }
      this.input.hotelRatings.splice(index, 1); // Unselect
      this.ratingErrorMessage = null; // Clear error if more than one star remains
    } else {
      this.input.hotelRatings.push(star); // Add star to selection
      this.input.hotelRatings.sort((a: number, b: number) => a - b);
      this.ratingErrorMessage = null; // Clear error on valid selection
    }
  }

  onDoneAfterSelectStars() {
    this.ratingErrorMessage = null;
    this.closeRatingSelectionPopup();
    this.changeDetectorRef.detectChanges();
  }

  fetchPlaces() {
    if (!this.searchLocation || this.searchLocation === '') {
      this.availablePlaces = new Array<any>();
      return;
    }
    this.availablePlaces = new Array<any>();
    this.fetchAvailableCities();
  }

  fetchPopularAvailableCities() {
    const payload = {
      pagination: {
        offset: 1,
        next: 100
      },
      isPopular: true,
      bookingType: this.bookingType
    };
    this.fetchCities(payload);
  }

  fetchCities(payload: any) {
    this.loadingCities = "LOADING";
    this.dataService.availableCities(payload).subscribe({
      next: (response: RestResponse) => {
        this.loadingCities = "LOADED";
        this.availablePlaces = response.data;
      },
      error: (error: any) => {
        this.loadingCities = "LOADED";
        this.toastService.show(error.message);
      }
    });
  }

  fetchAvailableCities() {
    const payload = {
      pagination: {
        offset: 1,
        next: 100
      },
      searchText: this.searchLocation,
      bookingType: this.bookingType
    };
    this.fetchCities(payload);
  }

  onSelectLocation(place: any) {
    this.input.location = place;
    setTimeout(() => {
      this.searchLocation = undefined;
      this.availablePlaces = new Array<any>();
    }, 500);
  }

  onLoadMyCurrentLocation() {
    this.processLocations().then((data: any) => {
      if (!data.isLocation) {
        this.toastService.show("Location services not available. Please resolve and try again.");
        return;
      }
      this.fetchAddress(data.coords);
      setTimeout(() => {
        this.searchLocation = undefined;
        this.availablePlaces = new Array<any>();
      }, 500);
    });
  }

  async processLocations() {
    const response = { isGps: true, isLocation: false };
    const locationPromise = new Promise((resolve, reject) => {
      this.getLocation(response, resolve);
    });
    return locationPromise;
  }

  getLocation(response: any, resolve: any) {
    const options = {
      enableHighAccuracy: response.isGps,
      timeout: 20000,
      maximumAge: 0
    };
    Geolocation.getCurrentPosition(options)
      .then((data: any) => {
        response.coords = data.coords;
        response.isLocation = true;
        resolve(response);
      }, () => {
        resolve(response);
      });
  }

  async fetchAddress(data: any) {
    if (!data || !data.latitude || !data.longitude) {
      return;
    }
    let options: ReverseOptions = {
      latitude: data.latitude,
      longitude: data.longitude,
      useLocale: true,
      maxResults: 5
    } as ReverseOptions;
    try {
      const response: any = await NativeGeocoder.reverseGeocode(options)
      if (response.addresses.length <= 0) {
        return;
      }
      const selectedAddress = response.addresses[0];
      //selectedAddress.locality
      //selectedAddress.countryName
    } catch (e) {
      this.toastService.show("Location services not available. Please resolve and try again.");
    }
  }

  openDatePicker(field: 'CHECK_IN_DATE' | 'CHECK_OUT_DATE'): void {
    const today = new Date();

    // Get today's date in dd/MM/yyyy format
    const formattedToday = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;

    // Calculate the maximum date (1 year later)
    const maxDate = new Date();
    maxDate.setFullYear(today.getFullYear() + 1);
    const formattedMaxDate = `${maxDate.getDate().toString().padStart(2, '0')}/${(maxDate.getMonth() + 1).toString().padStart(2, '0')}/${maxDate.getFullYear()}`;

    let minDate = formattedToday; // Default for check-in
    let selectedDate = today;    // Default to today if no prior selection

    // Handle check-in and check-out date selection
    if (field === 'CHECK_IN_DATE') {
      if (this.input.arrivalDate) {
        selectedDate = new Date(this.input.arrivalDate);
      }
    } else if (field === 'CHECK_OUT_DATE') {
      if (this.input.departureDate) {
        selectedDate = new Date(this.input.departureDate);
      }
      if (this.input.arrivalDate) {
        const arrivalDate = new Date(this.input.arrivalDate);
        const minCheckoutDate = new Date(arrivalDate);
        minCheckoutDate.setDate(arrivalDate.getDate() + 1); // Add 1 day to arrival date
        minDate = `${minCheckoutDate.getDate().toString().padStart(2, '0')}/${(minCheckoutDate.getMonth() + 1).toString().padStart(2, '0')}/${minCheckoutDate.getFullYear()}`;
      }
    }

    // Convert the selected date to dd/MM/yyyy format
    const formattedSelectedDate = `${selectedDate.getDate().toString().padStart(2, '0')}/${(selectedDate.getMonth() + 1).toString().padStart(2, '0')}/${selectedDate.getFullYear()}`;

    // Open the Date Picker
    DatePicker.present({
      mode: 'date',
      format: 'dd/MM/yyyy',
      min: minDate,               // Set the min date (today)
      max: formattedMaxDate,      // Set the max date (1 year later)
      date: formattedSelectedDate  // Set the selected date
    }).then(date => {
      this.onDateSelection(field, date.value); // Handle the date selection
    }).catch(error => {
      console.error("Date Picker error:", error);
    });
  }

  onDateSelection(field: string, date: any) {
    if (field === 'CHECK_IN_DATE') {
      this.onCheckinDateSelection(date);
    } else if (field === 'CHECK_OUT_DATE') {
      this.onCheckoutDateSelection(date);
    }
    this.input.totalNights = this.calculateTotalNights();  // Recalculate total nights after date change
  }

  calculateTotalNights() {
    if (!this.input.arrivalDate || !this.input.departureDate) {
      return 0; // Return 0 if either date is not selected
    }

    const departureDate = new Date(this.input.departureDate).setHours(23, 59, 59);
    const arrivalDate = new Date(this.input.arrivalDate).setHours(12, 0, 0);
    const diffTime = Math.abs(departureDate - arrivalDate);
    return Math.floor(diffTime / (1000 * 60 * 60 * 24)); // Calculate and return total nights
  }

  onCheckinDateSelection(date: any) {
    const [day, month, year] = date.split('/');
    const selectedDate = new Date(`${year}-${month}-${day}`);
    this.input.arrivalDate = selectedDate;

    // Ensure the departure date is always one day after the arrival date
    const newDepartureDate = new Date(selectedDate);
    newDepartureDate.setDate(selectedDate.getDate() + 1); // Set departure date 1 day after arrival
    this.input.departureDate = newDepartureDate;

    // Trigger Angular's change detection to reflect the updated dates in the UI
    // Uncomment this if you're using ChangeDetectorRef and it's required for the UI to update
    // this.cdr.detectChanges();

    // Recalculate total nights after arrival date change
    this.input.totalNights = this.calculateTotalNights();
  }


  onCheckoutDateSelection(date: any) {
    const [day, month, year] = date.split('/');
    const selectedDate = new Date(`${year}-${month}-${day}`);

    // Ensure departure date is at least 1 day after arrival date
    if (this.input.arrivalDate && selectedDate.getTime() <= new Date(this.input.arrivalDate).getTime()) {
      alert("Departure date must be at least 1 day after arrival date.");
    } else {
      this.input.departureDate = selectedDate; // Update departure date if valid
      this.input.totalNights = this.calculateTotalNights(); // Recalculate total nights after departure date change
    }

    // Trigger Angular's change detection to reflect the updated dates in the UI
    // this.cdr.detectChanges();
  }

  closeAudltSelectionPopup() {
    this.isAudltSelectionPopupOpen = false;
  }

  openLocationSelectionPopup() {
    // Reset popup state to ensure click always triggers
    this.isLocationSelectionPopupOpen = false;
    this.changeDetectorRef.markForCheck();

    setTimeout(() => {
      this.selectedInput = JSON.parse(JSON.stringify(this.input));
      this.isLocationSelectionPopupOpen = true;
      this.changeDetectorRef.detectChanges();
    }, 100); // Small timeout to allow change detection
  }

  closeLocationSelectionPopup() {
    this.isLocationSelectionPopupOpen = false;
  }

  openAudltSelectionPopup() {
    this.selectedInput = JSON.parse(JSON.stringify(this.input));
    this.isAudltSelectionPopupOpen = true;
  }

  onRoomChanged(noOfRooms: number) {
    const currentNoOfRooms = this.selectedInput.rooms.length;

    // Add or remove rooms dynamically
    if (currentNoOfRooms < noOfRooms) {
      for (let i = currentNoOfRooms; i < noOfRooms; i++) {
        this.selectedInput.rooms.push({
          type: `Room-${i + 1}`,
          noOfAdults: 1,
          noOfChilds: 0,
          uiChildrenAges: [],
        });
      }
    } else if (currentNoOfRooms > noOfRooms) {
      this.selectedInput.rooms.splice(noOfRooms, currentNoOfRooms - noOfRooms);
    }

    this.validateRooms(); // Validate after modifying rooms
  }

  onChildrenChanged(room: any, noOfChilds: number) {
    const currentNoOfChilds = room.uiChildrenAges.length;

    // Add or remove children dynamically
    if (currentNoOfChilds < noOfChilds) {
      for (let i = currentNoOfChilds; i < noOfChilds; i++) {
        room.uiChildrenAges.push({ age: 2, room: 1 });
      }
    } else if (currentNoOfChilds > noOfChilds) {
      room.uiChildrenAges.splice(noOfChilds, currentNoOfChilds - noOfChilds);
    }

    this.validateRoomOccupancy(room); // Validate room occupancy
  }


  compelte() {
    this.errorMessage = null;

    if (!this.validateAll()) {
      return; // Stop if any validation fails
    }

    // Update children ages
    this.selectedInput.rooms.forEach((room: any) => {
      room.childrenAges = room.uiChildrenAges.map((x: any) => x.age);
    });

    this.input = JSON.parse(JSON.stringify(this.selectedInput));
    this.calculateData();
    this.closeAudltSelectionPopup();
  }


  calculateData() {
    this.input.noOfAdults = this.input.rooms.map((x: any) => x.noOfAdults).reduce((sum: number, a: number) => sum + a, 0);
    this.input.noOfChilds = this.input.rooms.map((x: any) => x.noOfChilds).reduce((sum: number, a: number) => sum + a, 0);
  }

  search() {
    this.searchCallback.emit(this.input);
  }

  validateRooms(): boolean {
    if (this.selectedInput.noOfRooms <= 0) {
      this.errorMessage = "Select at least one room to proceed with the hotel search.";
      return false;
    }
    this.errorMessage = null;
    return true;
  }

  validateRoomOccupancy(room: any): boolean {
    const totalMembers = room.noOfAdults + room.noOfChilds;
    if (totalMembers > 6) {
      this.errorMessage = "Maximum occupancy for a single room is 6 people.";
      return false;
    }
    this.errorMessage = null;
    return true;
  }

  validateAllRooms(): boolean {
    for (const room of this.selectedInput.rooms) {
      if (!this.validateRoomOccupancy(room)) {
        return false;
      }
    }
    return true;
  }

  validateAll(): boolean {
    return this.validateRooms() && this.validateAllRooms();
  }

  PREFERENCES_OPTIONS = [
    { id: 'Balcony', name: 'Balcony' },
    { id: 'Beachfront', name: 'Beachfront' },
    { id: 'Club Room', name: 'Club Room' },
    { id: 'Cottage', name: 'Cottage' },
    { id: 'Duplex', name: 'Duplex' },
    { id: 'Jacuzzi', name: 'Jacuzzi (in-room)' },
    { id: 'Kitchenotto', name: 'Kitchenotto' },
    { id: 'Private Pool', name: 'Private Pool' },
    { id: 'Sea View', name: 'Sea View' },
    { id: 'Suite', name: 'Suite' },
    { id: 'Transfer', name: 'Transfer' },
    { id: 'Villa', name: 'Villa' }
  ];



  BOARD_BASIS_OPTIONS = [
    { id: 'Room Only', name: 'Room Only' },
    { id: 'Breakfast', name: 'Breakfast' },
    { id: 'Half Board', name: 'Half Board' },
    { id: 'Full Board', name: 'Full Board' },
    { id: 'All Inclusive', name: 'All Inclusive' }
  ];

  onPreferencesConfirm() {
    this.selectedPreferences = [...this.tempPreferences];
    this.input.preferences = this.selectedPreferences.length ? this.selectedPreferences.toString() : null;

    if (this.selectedPreferences.length) {
      this.localStorageService.setObject('HOTEL_PREFERENCES', this.selectedPreferences);
    } else {
      this.localStorageService.remove('HOTEL_PREFERENCES');
    }
  }

  onPreferencesCancel() {
    this.tempPreferences = [...this.selectedPreferences];
    this.input.preferences = this.tempPreferences.length ? this.tempPreferences.toString() : null;
  }

  onBoardSelectConfirm() {
    this.selectedBoardBasis = [...this.tempBoardBasis];
    this.input.boardBasis = this.selectedBoardBasis.length ? this.selectedBoardBasis.toString() : null;

    if (this.selectedBoardBasis.length) {
      this.localStorageService.setObject('HOTEL_BOARD_BASIS', this.selectedBoardBasis);
    } else {
      this.localStorageService.remove('HOTEL_BOARD_BASIS');
    }
  }

  onBoardSelectCancel() {
    this.tempBoardBasis = [...this.selectedBoardBasis];
    this.input.boardBasis = this.tempBoardBasis.length ? this.tempBoardBasis.toString() : null;
  }

}
