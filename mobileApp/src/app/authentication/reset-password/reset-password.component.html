<ion-content class="login-page">
  <div class="login-page-container">
    <ion-buttons slot="start" class="back-button">
      <!-- <ion-button (click)="goToLoginPage()" fill="clear">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button> -->
    </ion-buttons>
    <div class="login-container">
      <ion-img src="/assets/images/svg/two-factor-authentication.svg"></ion-img>
      <h2>Reset<span>Password</span></h2>
      <p class="page-heading-title no-margin-top">Please enter your new password to reset your account.</p>
    </div>
    <!-- Container for the Reset Password form -->
    <div class="form-container">
      <form #resetPasswordForm="ngForm" novalidate="novalidate" class="custom-form" autocomplete="off"
        (ngSubmit)="resetPassword(resetPasswordForm.form)">

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':!newPasswords.valid && onClickValidation}">
            <ion-input label="New Password*" labelPlacement="floating" mode="md" [type]="passwordFieldType"
              name="newPasswords" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
              [ngClass]="{'is-invalid':newPasswords.invalid && onClickValidation}" #newPasswords="ngModel"
              [(ngModel)]="data.password" required="required"></ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="data.password && data.password.length>0 && passwordFieldType==='password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="data.password && data.password.length>0 && passwordFieldType!=='password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="newPasswords" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & be greater than 8 characters'">
          </app-validation-message>
        </div>

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':!confirmNewPassword.valid && onClickValidation}">
            <ion-input label="Confirm New Password*" labelPlacement="floating" mode="md" [type]="passwordFieldType"
              name="confirmNewPassword" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
              [ngClass]="{'is-invalid':confirmNewPassword.invalid && onClickValidation}" #confirmNewPassword="ngModel"
              [(ngModel)]="data.confirmPassword" required="required"></ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="data.confirmPassword && data.confirmPassword.length>0 && passwordFieldType==='password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="data.confirmPassword && data.confirmPassword.length>0 && passwordFieldType!=='password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="confirmNewPassword" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & be greater than 8 characters'">
          </app-validation-message>
        </div>

        <!-- Submit button for the Reset Password form -->
        <ion-button class="margin-top-20" expand="full" shape="round" type="submit" [disabled]="isProcessing">
          Continue
        </ion-button>
      </form>
    </div>
    <!-- Container for privacy and terms links -->
    <div class="privacy-container">
      <p>
        By continuing you are agree to our
        <a href="https://www.forbcorp.com/Terms%20and%20Conditions.pdf" target="_blank" rel="noopener noreferrer">
          Terms of Service
        </a>
        and
        <a href="https://www.forbcorp.com/privacy.php" target="_blank" rel="noopener noreferrer">
          Privacy Policy
        </a>
      </p>
    </div>
  </div>
</ion-content>