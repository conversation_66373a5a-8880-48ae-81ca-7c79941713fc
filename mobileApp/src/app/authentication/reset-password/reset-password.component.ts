import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { NavigationDataService } from 'src/services/navigation.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss'],
})
export class ResetPasswordComponent implements OnInit {

  onClickValidation: boolean = false;
  passwordFieldType: string = 'password';
  data: any = {};
  isProcessing: boolean = false;

  constructor(
    private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly navigationService: NavigationDataService,
    private readonly cdr: ChangeDetectorRef,
    private readonly route: ActivatedRoute
  ) { }

  ngOnInit() {
    this.resetForm();
    this.route.queryParams.subscribe(params => {
      this.data = {
        resetPasswordOtpData: params['resetPasswordOtpData'] || null,
      };
      if (!this.data.resetPasswordOtpData) {
        this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
        this.navController.navigateRoot("/account/login", { animated: true });
        return;
      }
    });
  }

  resetForm() {
    this.data = {} as any;
    this.onClickValidation = false;

    // Trigger change detection
    this.cdr.detectChanges();
  }

  validatePasswords(): boolean {
    if (this.data.password !== this.data.confirmPassword) {
      this.toastService.show('New Password and Confirm Password do not match');
      return false;
    }
    return true;
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  goToLoginPage() {
    this.resetForm();
    this.navController.navigateBack("/account/login", { animated: true });
  }

  async resetPassword(form: any): Promise<void> {
    this.onClickValidation = true;
    if (!form.valid || !this.validatePasswords()) {
      return;
    }
    // Show loading indicator
    //  this.loadingService.show();
    this.isProcessing = true;

    const resetPasswordPayload = {
      uniqueCode: this.data.resetPasswordOtpData,
      password: this.data.password,
      confirmPassword: this.data.confirmPassword
    };
    this.dataService.resetPassword(resetPasswordPayload).subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        this.isProcessing = false;

        this.toastService.show(response.message);
        setTimeout(() => {
          this.navController.navigateBack("/account/login");
        }, 200);
      },
      error: (error) => {
        //  this.loadingService.hide();
        this.isProcessing = false;
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

}
