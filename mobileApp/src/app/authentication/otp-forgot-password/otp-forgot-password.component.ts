import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NavController, ToastController } from '@ionic/angular';
import { interval, Subscription } from 'rxjs';
import { Login } from 'src/modals/login';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-otp-forgot-password',
  templateUrl: './otp-forgot-password.component.html',
  styleUrls: ['./otp-forgot-password.component.scss'],
})
export class OtpForgotPasswordComponent implements OnInit {

  data: any; // data object
  subscription: Subscription = new Subscription();  // Subscription for verify request
  resendSubscription: Subscription = new Subscription();  // Subscription for resend request
  onClickValidation!: boolean; // Flag to control validation state
  counter!: number;
  responseReceived: boolean = false;
  isProcessing: boolean = false;
  loginView: string = "";

  constructor(private readonly navController: NavController,
    public commonService: CommonService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly toastService: ToastService,
    private readonly route: ActivatedRoute,
    private readonly authService: AuthService
  ) {
    this.onClickValidation = false;
  }

  ngOnInit() {
    this.onClickValidation = false;
    this.route.queryParams.subscribe(params => {
      this.data = {
        forgotData: params['forgotData'] || null,
        phoneNumber: params['phoneNumber'] || null,
        sendotp: params['sendotp'] || false
      };
      this.loginView = params['loginView'];
      if (!this.data.forgotData) {
        this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
        this.navController.navigateRoot("/account/login", { animated: true });
        return;
      }
      if (this.data.sendotp) {
        this.resendOtp();
      }
    });
  }

  ionViewDidEnter() {
    this.disableResendOtp();
    this.responseReceived = false;
  }

  continue(form: any): void {
    if (!this.data.forgotData) {
      this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
      this.navController.navigateRoot("/account/login", { animated: true });
      return;
    }
    this.responseReceived = false;
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    //  this.loadingService.show();
    this.isProcessing = true;
    const otpString = this.commonService.getOTPFromInputs(
      this.data.otpInput1,
      this.data.otpInput2,
      this.data.otpInput3,
      this.data.otpInput4,
      this.data.otpInput5,
      this.data.otpInput6
    );
    const resetPasswordOtp = {
      uniqueCode: this.data.forgotData,
      otpCode: otpString
    };
    // Make HTTP request to verify OTP
    this.subscription = this.dataService.verifyResetPasswordOtp(resetPasswordOtp)
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          this.responseReceived = true;
          const data = response;
          setTimeout(() => {
            this.redirectToResetPassword(data);
          }, 500);
        },
        error: (error: any) => {
          this.responseReceived = false;
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.data?.message || error.message);
        }
      });
  }

  redirectToResetPassword(data: any) {
    this.navController.navigateForward("/account/reset/password", {
      queryParams: {
        resetPasswordOtpData: data.data,
      }, animated: true
    });
  }

  goToForgotPasswordPage() {
    this.navController.navigateBack("/account/forgot/password", { queryParams: { "loginView": this.loginView }, animated: true });
  }

  resendOtp() {
    if (!this.data.forgotData) {
      this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
      this.navController.navigateRoot("/account/login", { animated: true });
      return;
    }
    const input = {} as any;
    input.userName = this.data.phoneNumber;
    //  this.loadingService.show();
    this.resendSubscription = this.dataService.forgotPasswordMobile(input)
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.disableResendOtp();
        },
        error: (error: any) => {
          //  this.loadingService.hide();
          this.toastService.show(error.message);
        }
      });
  }

  disableResendOtp() {
    this.counter = 60;
    this.subscription = interval(1000)
      .subscribe(x => {
        --this.counter;
        if (this.counter <= 0) {
          this.subscription.unsubscribe();
        }
      });
  }

  ngOnDestroy() {
    // Unsubscribe from any subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    if (this.resendSubscription) {
      this.resendSubscription.unsubscribe();
    }
  }

}
