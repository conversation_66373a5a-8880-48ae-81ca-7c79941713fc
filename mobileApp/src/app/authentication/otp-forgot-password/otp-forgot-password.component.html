<ion-content class="login-page">
  <div class="login-page-container">
    <ion-buttons slot="start" class="back-button">
      <ion-button (click)="goToForgotPasswordPage()" fill="clear">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <div class="login-container">
      <ion-img src="/assets/images/svg/two-factor-authentication.svg"></ion-img>
      <h2>Forgot<span>Password?</span></h2>
      <p class="page-heading-title no-margin-top">Check your WhatsApp for the verification code and enter the 6-digit
        OTP below.</p>
    </div>
    <!-- Container for the 2FA form -->
    <div class="form-container">
      <form #otpForgotPasswordForm="ngForm" novalidate="novalidate" class="custom-form" autocomplete="off"
        (ngSubmit)="continue(otpForgotPasswordForm.form)">
        <div class="margin-bottom-100">
          <div class="otp-validate-container" [ngClass]="{'active': !responseReceived,
          'disabled': responseReceived }">
            <ion-input required="required" minlength="1" maxlength="1" inputmode="numeric" type="text" placeholder="*"
              [debounce]="100" name="otpInput1" [ngClass]="{ 'is-invalid': onClickValidation && otpInput1.invalid }"
              [(ngModel)]="data.otpInput1" #otpInput1="ngModel" (ionInput)="commonService.changeInputFocus($event)"
              (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" [debounce]="100"
              name="otpInput2" #otpInput2="ngModel" [(ngModel)]="data.otpInput2"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput2.invalid }" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput3"
              [debounce]="100" #otpInput3="ngModel" [(ngModel)]="data.otpInput3"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput3.invalid}" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput4"
              [debounce]="100" #otpInput4="ngModel" [(ngModel)]="data.otpInput4"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput4.invalid }" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput5"
              [debounce]="100" #otpInput5="ngModel" [(ngModel)]="data.otpInput5"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput5.invalid}" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput6"
              [debounce]="100" #otpInput6="ngModel" [(ngModel)]="data.otpInput6"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput6.invalid}" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
          </div>
        </div>
        <!-- Submit button for the OTP form -->
        <ion-button class="margin-top-20" expand="full" shape="round" type="submit" *ngIf="!responseReceived"
          [disabled]="isProcessing">
          Continue
        </ion-button>
      </form>
      <div class="ion-text-center margin-bottom-10 margin-top-20 resend-otp-text">
        <div *ngIf="counter>0 && !responseReceived">Resend enable in {{counter}} seconds</div>
        <a (click)="resendOtp()" *ngIf="counter<=0  && !responseReceived">Resend</a>
        <a class="green-text" *ngIf="responseReceived">Otp Verified!</a>
      </div>
    </div>
    <!-- Container for privacy and terms links -->
    <div class="privacy-container">
      <p>
        By continuing you are agree to our
        <a href="https://www.forbcorp.com/Terms%20and%20Conditions.pdf" target="_blank" rel="noopener noreferrer">
          Terms of Service
        </a>
        and
        <a href="https://www.forbcorp.com/privacy.php" target="_blank" rel="noopener noreferrer">
          Privacy Policy
        </a>
      </p>
    </div>
  </div>
</ion-content>