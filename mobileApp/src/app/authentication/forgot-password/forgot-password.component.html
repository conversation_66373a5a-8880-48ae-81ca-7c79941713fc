<ion-content class="login-page">
  <div class="login-page-container for-login-height">

    <ion-buttons slot="start" class="back-button">
      <ion-button (click)="goToLoginPage()" fill="clear">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>

    <div class="login-container">
      <ion-img src="/assets/images/svg/reset-password.svg"></ion-img>
      <h2>Forgot<span>Password?</span></h2>
      <p class="page-heading-title">Enter your registered details to reset your password.</p>
    </div>

    <!-- <div class="register-container" *ngIf="!view">
      <ion-button class="login-buttons" expand="full" shape="round" type="submit"
        (click)="tabChange('FORGOT_PASSWORD_VIA_EMAIL')">Forgot
        With
        Email</ion-button>
      <p>Or</p>
      <ion-button class="login-buttons" expand="full" shape="round" type="submit"
        (click)="tabChange('FORGOT_PASSWORD_VIA_MOBILE')">Forgot
        With
        Mobile</ion-button>
    </div> -->

    <div class="form-container" *ngIf="view">
      <form class="custom-form" #recordForm="ngForm" novalidate="novalidate"
        (ngSubmit)="forgotPassword(recordForm.form)">

        <div class="margin-bottom-15" *ngIf="view==='FORGOT_PASSWORD_VIA_EMAIL'">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userEmail.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/envelope-plus.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input #forgotEmailInput label="Email Address" labelPlacement="floating" required="required"
              name="userEmail" #userEmail="ngModel" pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$"
              [(ngModel)]="data.email" [ngClass]="{'is-invalid':userEmail.invalid && onClickValidation}"></ion-input>
          </ion-item>
          <app-validation-message [field]="userEmail" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>
        <div class="margin-bottom-15" *ngIf="view==='FORGOT_PASSWORD_VIA_MOBILE'">
          <ion-item class="site-form-control mobile-form-control" lines="none"
            [ngClass]="{'is-invalid':username.invalid && onClickValidation}">
            <ion-icon name="call-outline" slot="start" class="start-icon"></ion-icon>
            <input #forgotMobileInput class="custom-mobile-input" inputmode="tel" [attr.pattern]="pattern"
              [maskito]="mask" required="required" name="username" #username="ngModel" [(ngModel)]="data.userName"
              placeholder="Contact Number" />
            <img width="30" height="30" [attr.alt]="countryIsoCode" [src]="countryIsoCode" slot="end"
              [style.border-radius.%]="50" *ngIf="countryIsoCode!==''" />
          </ion-item>
          <app-validation-message [field]="username" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div>

        <ion-button class="margin-top-20" expand="full" shape="round" *ngIf="view==='FORGOT_PASSWORD_VIA_EMAIL'" type
          [disabled]="isProcessing">
          Forgot Password
        </ion-button>
        <ion-button class="margin-top-20" expand="full" shape="round" *ngIf="view==='FORGOT_PASSWORD_VIA_MOBILE'" type
          [disabled]="isProcessing">
          Send Otp
        </ion-button>
      </form>

      <div class="register-container">
        <p>Or</p>
        <p>
          <a
            (click)="tabChange(view === 'FORGOT_PASSWORD_VIA_EMAIL' ? 'FORGOT_PASSWORD_VIA_MOBILE' : 'FORGOT_PASSWORD_VIA_EMAIL')">
            {{ view === 'FORGOT_PASSWORD_VIA_EMAIL' ? 'Reset With Mobile' : 'Reset With Email' }}
          </a>
        </p>
      </div>

    </div>

    <div class="register-container" *ngIf="view==='FORGOT_PASSWORD_VIA_EMAIL'">
      <!-- <p>Or</p> -->
      <p>
        <a (click)="goToLoginPage()">Know your password? Sign in</a>
      </p>
    </div>

    <div class="privacy-container">
      <p>
        By signing in, creating an account, you are agreeing to our
        <a href="https://www.forbcorp.com/Terms%20and%20Conditions.pdf" target="_blank" rel="noopener noreferrer">
          Terms of Use
        </a>
        and
        our
        <a href="https://www.forbcorp.com/privacy.php" target="_blank" rel="noopener noreferrer">
          Privacy Policy
        </a>
      </p>
    </div>
  </div>
</ion-content>

<!-- <ion-content class="login-page">
  <div class="login-page-container">

    <ion-buttons slot="start" class="back-button">
      <ion-button (click)="goToLoginPage()" fill="clear">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>

    <div class="login-container">
      <ion-img src="/assets/images/svg/reset-password.svg"></ion-img>
      <h2>Forgot<span>Password?</span></h2>
      <p class="page-heading-title">Enter your registered details to reset your password.</p>
    </div>

    <div class="form-container">
      <form class="custom-form" #recordForm="ngForm" novalidate="novalidate"
        (ngSubmit)="forgotPassword(recordForm.form)">
        <div class="login-type-section-container ion-text-left">
          <div class="login-type-section-item" (click)="tabChange(view='FORGOT_PASSWORD_VIA_EMAIL')">
            <ion-checkbox [checked]="view==='FORGOT_PASSWORD_VIA_EMAIL'" [mode]="'md'"></ion-checkbox> <span>Forgot
              With
              Email</span>
          </div>
          <div class="login-type-section-item" (click)="tabChange(view='FORGOT_PASSWORD_VIA_MOBILE')">
            <ion-checkbox [checked]="view==='FORGOT_PASSWORD_VIA_MOBILE'" [mode]="'md'"></ion-checkbox> <span>Forgot
              With
              Mobile</span>
          </div>
        </div>

        <div class="margin-bottom-15" *ngIf="view==='FORGOT_PASSWORD_VIA_EMAIL'">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userEmail.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/envelope-plus.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input label="Email Address" labelPlacement="floating" required="required" name="userEmail"
              #userEmail="ngModel" pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$" [(ngModel)]="data.email"
              [ngClass]="{'is-invalid':userEmail.invalid && onClickValidation}"></ion-input>
          </ion-item>
          <app-validation-message [field]="userEmail" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>
        <div class="margin-bottom-15" *ngIf="view==='FORGOT_PASSWORD_VIA_MOBILE'">
          <ion-item class="site-form-control mobile-form-control" lines="none"
            [ngClass]="{'is-invalid':username.invalid && onClickValidation}">
            <ion-icon name="call-outline" slot="start" class="start-icon"></ion-icon>
            <input class="custom-mobile-input" inputmode="tel" [attr.pattern]="pattern" [maskito]="mask"
              required="required" name="username" #username="ngModel" [(ngModel)]="data.userName"
              placeholder="Contact Number" />
            <img width="30" height="30" [attr.alt]="countryIsoCode" [src]="countryIsoCode" slot="end"
              [style.border-radius.%]="50" *ngIf="countryIsoCode!==''" />
          </ion-item>
          <app-validation-message [field]="username" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div>

        <ion-button class="margin-top-20" expand="full" shape="round" *ngIf="view==='FORGOT_PASSWORD_VIA_EMAIL'" type
          [disabled]="isPaymentProcessing">
          Forgot Password
        </ion-button>
        <ion-button class="margin-top-20" expand="full" shape="round" *ngIf="view==='FORGOT_PASSWORD_VIA_MOBILE'" type
          [disabled]="isPaymentProcessing">
          Send Otp
        </ion-button>
      </form>

      <div class="register-container">
        <p>Or</p>
        <p>
          <a (click)="goToLoginPage()">Know your password? Sign in</a>
        </p>
      </div>
    </div>

    <div class="privacy-container">
      <p>
        By signing in, creating an account, you are agreeing to our
        <a href="https://www.forbcorp.com/Terms%20and%20Conditions.pdf" target="_blank" rel="noopener noreferrer">
          Terms of Use
        </a>
        and
        our
        <a href="https://www.forbcorp.com/privacy.php" target="_blank" rel="noopener noreferrer">
          Privacy Policy
        </a>
      </p>
    </div>
  </div>
</ion-content> -->