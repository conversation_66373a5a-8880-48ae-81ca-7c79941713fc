import { <PERSON>mpo<PERSON>, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { IonInput, NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { Login } from 'src/modals/login';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';
import mask from './../../../shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/metadata.min.json';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss'],
})
export class ForgotPasswordComponent implements OnInit, OnD<PERSON>roy {

  onClickValidation!: boolean; // Flag to control validation state
  data: Login = new Login(); // Login data object
  subscription: Subscription = new Subscription(); // Subscription to manage observable lifecycle
  view: string = "";
  loginView: string = "";
  isApple: boolean = false;
  code: string = "";
  isProcessing: boolean = false;
  protected readonly mask = mask;

  @ViewChild('forgotMobileInput') mobileInputRef!: ElementRef;
  @ViewChild('forgotEmailInput') emailInput!: IonInput;

  constructor(private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly route: ActivatedRoute
  ) {
    // Initialize data and validation flag
    this.data = new Login();
    this.onClickValidation = false;
  }

  ngOnInit(): void {
    this.onClickValidation = false;

    this.route.queryParams.subscribe(params => {
      this.loginView = params['loginView'];

      if (this.loginView === 'LOGIN_VIA_MOBILE' || this.loginView === 'FORGOT_PASSWORD_VIA_MOBILE') {
        this.view = 'FORGOT_PASSWORD_VIA_MOBILE';
        this.data.userName = '+91'; // default code
      } else {
        this.view = 'FORGOT_PASSWORD_VIA_EMAIL';
      }

      setTimeout(() => this.focusField(), 200);
    });
  }

  private focusField() {
    if (this.view === 'FORGOT_PASSWORD_VIA_MOBILE' && this.mobileInputRef?.nativeElement) {
      this.mobileInputRef.nativeElement.focus();
    } else if (this.view === 'FORGOT_PASSWORD_VIA_EMAIL' && this.emailInput) {
      this.emailInput.setFocus();
    }
  }

  async forgotPassword(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !this.data.isValidForgotPasswordRequest(form, this.view)) {
      return; // Exit if form is invalid
    }
    if (this.view === "FORGOT_PASSWORD_VIA_MOBILE") {
      this.data.userName = this.data.userName.replace(/[^\d+]/g, '').trim();
    }
    this.isProcessing = true;
    //  this.loadingService.show();
    const method = this.view === "FORGOT_PASSWORD_VIA_EMAIL" ? 'forgotPassword' : 'forgotPasswordMobile';
    this.subscription = this.dataService[method](this.data.forForgotPasswordRequest(this.view))
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          const data = response;
          this.toastService.show(data.message);
          setTimeout(() => {
            if (this.view === "FORGOT_PASSWORD_VIA_MOBILE") {
              this.redirectToResetOtp(data);
              return;
            }
            this.navController.navigateBack("/account/login");
          }, 200);
        }, error: (error: any) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message || 'Unexpected error occurred');
        }
      })
  }

  redirectToResetOtp(data: any) {
    this.navController.navigateForward("/account/reset/otp", {
      queryParams: {
        forgotData: data.data,
        phoneNumber: this.data.userName,
        loginView: this.view
      }, animated: true
    });
  }

  goToLoginPage() {
    this.navController.navigateBack("/account/login", { animated: true });
  }

  protected get countryIsoCode(): string {
    this.code = maskitoGetCountryFromNumber(this.data.userName ?? '', metadata) ?? '';
    if (this.code === '') {
      return "";
    }
    return `/assets/images/icons/flags/${this.code.toLocaleLowerCase()}.png`;
  }

  protected get pattern(): string {
    return this.isApple ? '+[0-9-]{1,20}' : '';
  }

  tabChange(view: string) {
    this.view = view;

    setTimeout(() => {
      if (view === 'FORGOT_PASSWORD_VIA_MOBILE' && this.mobileInputRef?.nativeElement) {
        this.mobileInputRef.nativeElement.focus();
        this.data.userName = '+91';
      } else if (view === 'FORGOT_PASSWORD_VIA_EMAIL' && this.emailInput) {
        this.emailInput.setFocus();
        this.data.userName = '';
      }
    }, 200);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
