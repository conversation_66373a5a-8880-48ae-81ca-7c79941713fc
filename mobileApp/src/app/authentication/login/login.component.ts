import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { IonInput, IonModal, ModalController, NavController } from '@ionic/angular';
import { filter, Subscription } from 'rxjs';
import { Login } from 'src/modals/login';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import mask from './../../../shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/metadata.min.json';
import { Geolocation } from '@capacitor/geolocation';
import { CommonService } from 'src/services/common.service';
import parsePhoneNumberFromString from 'libphonenumber-js/core';
import { NavigationEnd, Router } from '@angular/router';
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit, OnDestroy {

  onClickValidation: boolean;  // Flag to control validation state
  subscription: Subscription = new Subscription(); // Subscription for login request
  data: Login = new Login(); // Login data object
  passwordFieldType!: string;
  view: string = "";
  isApple: boolean = false;
  code: string = "";
  request: any = {} as any;
  loginWithOtp: boolean = false;
  isProcessing: boolean = false;
  is: boolean = false;

  @ViewChild('mobileInput') mobileInputRef!: ElementRef;
  @ViewChild(IonModal) loginModal!: IonModal;

  presentingElement: any = null;

  @ViewChild('emailInput') emailInput!: IonInput;

  protected readonly mask = mask;

  constructor(private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly authService: AuthService,
    private modalCtrl: ModalController,
    private commonService: CommonService,
    private router: Router
  ) {
    this.data = new Login();
    this.onClickValidation = false;
  }

  ngOnInit(): void {
    this.init();
  }

  ionViewWillEnter() {
    this.init();
  }

  init() {

    this.data = new Login();
    this.onClickValidation = false;
    this.passwordFieldType = "password";

    this.view = "LOGIN_VIA_MOBILE";
    if (this.view === 'LOGIN_VIA_EMAIL') {
      //  this.emailInput?.setFocus();
      this.data.userName = ''; // Clear email input
    } else if (this.view === 'LOGIN_VIA_MOBILE') {
      // this.mobileInputRef.nativeElement.focus?.();
      this.data.userName = '+91';
    }
  }

  toggleOtpLogin(): void {
    this.loginWithOtp = !this.loginWithOtp;

    if (this.loginWithOtp) {
      this.data.password = '';
    }
  }


  async processLocations() {
    const response = { isGps: false, isLocation: false };
    const locationPromise = new Promise((resolve, reject) => {
      this.getLocation(response, resolve);
    });
    return locationPromise;
  }

  getLocation(response: any, resolve: any) {
    const options = {
      enableHighAccuracy: response.isGps,
      timeout: 20000,
      maximumAge: 0
    };
    Geolocation.getCurrentPosition(options)
      .then((data: any) => {
        response.coords = data.coords;
        response.isLocation = true;
        resolve(response);
      }, () => {
        resolve(response);
      });
  }

  fetchCountryCode(longitude: number, latitude: number) {
    this.dataService.getCountryCode(longitude, latitude).subscribe({
      next: (response) => {
        if (response.address.country_code === 'in')
          this.data.userName = '+91';
      },
      error: (error) => {
        this.toastService.show(error.message || 'Unexpected error occurred');
      }
    })
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  protected get countryIsoCode(): string {
    this.code = maskitoGetCountryFromNumber(this.data.userName ?? '', metadata) ?? '';
    if (this.code === '') {
      return "";
    }
    return `/assets/images/icons/flags/${this.code.toLocaleLowerCase()}.png`;
  }

  protected get pattern(): string {
    return this.isApple ? '+[0-9-]{1,20}' : '';
  }

  tabChange(viewType: 'LOGIN_VIA_EMAIL' | 'LOGIN_VIA_MOBILE') {
    this.view = viewType;
    this.data.userName = viewType === 'LOGIN_VIA_EMAIL' ? '' : '+91';
    setTimeout(() => {
      if (viewType === 'LOGIN_VIA_EMAIL') {
        // this.emailInput?.setFocus();
      } else if (this.mobileInputRef?.nativeElement) {
        // this.mobileInputRef.nativeElement.focus?.();
      }
    }, 200); // Add slight delay to ensure DOM updates
  }

  async loginButton(form: any): Promise<any> {
    // if (this.view === 'LOGIN_VIA_MOBILE') {
    this.loginWithOtpApi(form);
    return;
    // }
    // this.login(form);
  }

  async login(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !this.data.isValidLoginRequest(form)) {
      return; // Exit if form is invalid
    }
    if (this.view === "LOGIN_VIA_MOBILE") {
      this.data.userName = this.data.userName.replace(/[^\d+]/g, '').trim();
    }
    // Show loading indicator
    this.isProcessing = true;
    //  this.loadingService.show();
    this.subscription = this.dataService.login(this.data.forRequest())
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          const data = response.data;

          if (data.user && !data.user.phoneNumberConfirmed) {
            this.redirectToAccountVerificationFlow(data.user);
            return;
          }
          if (data.user && !data.user.isOnBoardingComplete) {
            this.redirectToOnboardingFlow(data);
            return;
          }
          // Check if two-factor authentication is enable
          if (data?.twoFactorEnabled) {
            this.redirectToVerificationFlow(data);
            this.localStorageService.setObject('twoFactorLoginData', data);
            return;
          }
          this.redirectToDashboard(data);
        }, error: (error) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.message || 'Unexpected error occurred');
        }
      })
  }

  async loginWithOtpApi(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !this.data.isValidLoginRequestForMobile(form)) {
      return;
    }

    let payload = {
      userName: this.data.userName,
      countryCode: this.data.countryCode || null
    };

    if (this.view === "LOGIN_VIA_MOBILE") {
      const rawInput = this.data.userName || '';

      // Remove spaces and unnecessary symbols
      const cleanedInput = rawInput.replace(/[^\d+]/g, '');

      // Use libphonenumber-js to parse the number
      const phoneNumber = parsePhoneNumberFromString(cleanedInput, metadata as any);

      if (phoneNumber && phoneNumber.isValid()) {
        const countryCode = `+${phoneNumber.countryCallingCode}`;
        const nationalNumber = phoneNumber.nationalNumber;

        this.data.countryCode = countryCode;
        // Do NOT modify data.userName as per your need

        // Only for payload, use clean data
        payload = {
          userName: nationalNumber,
          countryCode: countryCode
        };
      } else {
        console.warn('Invalid phone format');
        this.toastService.show("Invalid phone number format.");
        return;
      }
    }

    this.isProcessing = true;

    this.subscription = this.dataService.loginWithOtp(payload).subscribe({
      next: (response: RestResponse) => {
        this.isProcessing = false;
        const data = response.data;
        this.dismissLogin();
        this.redirectToLoginWithOtp(data);
      },
      error: (error) => {
        this.isProcessing = false;
        this.toastService.show(error.message || 'Unexpected error occurred');
      }
    });
  }

  redirectToLoginWithOtp(data: any) {
    this.navController.navigateForward(`/account/dashboard/otp/verification`, { queryParams: { "customerId": data }, animated: true });
  }

  redirectToVerificationFlow(data: any) {
    this.navController.navigateForward("/account/verification", {
      queryParams: {
        verificationToken: data.verificationToken,
      }, animated: true
    });
  }

  redirectToAccountVerificationFlow(data: any) {
    this.navController.navigateForward(`/account/register/verification`, { queryParams: { "customerId": data.id, 'sendotp': true }, animated: true });
  }

  redirectToOnboardingFlow(data: any) {
    data.token.expires_at = new Date(data.token.expires).getTime();
    this.localStorageService.setObject('token', data.token);
    this.localStorageService.setObject('user', data.user);

    this.navController.navigateRoot(`/account/register/basic`, { animated: true });
  }

  redirectToDashboard(data: any) {
    // Store token and user data in local storage
    data.token.expires_at = new Date(data.token.expires).getTime();
    this.localStorageService.setObject('token', data.token);
    this.localStorageService.setObject('user', data.user);
    const redirectUrl = localStorage.getItem('redirectAfterLogin');
    this.localStorageService.remove('redirectAfterLogin');
    // Navigate to the appropriate dashboard based on user role
    if (redirectUrl) {
      this.navController.navigateRoot(redirectUrl);
      return;
    }
    if (this.authService.isCustomer()) {
      this.navController.navigateRoot("/dashboard");
      return;
    }
    if (this.authService.isSale()) {
      this.navController.navigateRoot("/sale-portal/dashboard");
      return;
    }
    this.toastService.show("Sorry, Something went wrong while processing your request. Please contact to administrator");
    this.authService.logout();
    this.data = new Login();
  }

  goToForgotPassword() {
    this.navController.navigateForward("/account/forgot/password", { queryParams: { "loginView": this.view }, animated: true });
  }

  registerAccount() {
    this.localStorageService.remove("CUSTOMER_ONBOARDING");
    this.navController.navigateForward("/account/register", { animated: true });
  }

  dismissLogin() {
    let bookingWithoutLogin = this.localStorageService.get('bookingWithoutLogin');
    let subscribeNowWithoutLogin = this.localStorageService.get('subscribeNowWithoutLogin');
    if (bookingWithoutLogin || subscribeNowWithoutLogin) {
      this.modalCtrl.dismiss(); // if used in a modal
      // this.localStorageService.remove('subscribeNowWithoutLogin');
      // this.localStorageService.remove('bookingWithoutLogin');
      return;
    }
    this.modalCtrl.dismiss(); // if used in a modal
    //this.localStorageService.remove('bookingTypeNoLogin');
    // this.localStorageService.remove('subscribeNowWithoutLogin');
    this.navController.navigateRoot("/dashboard");
    // or this.navCtrl.back(); // if you want to navigate back
  }

  closeModal() {
    const currentUrl = this.router.url;

    // Check that you're still on the hotel details page with bookingType=OTHERS
    const isHotelDetailsPage = currentUrl.includes('/portal/hotel/')
      && currentUrl.includes('/details')
      && currentUrl.includes('bookingType=OTHERS');

    if (isHotelDetailsPage) {
      // ✅ Just close modal, no navigation
      this.modalCtrl.dismiss(); // or this.showModal = false;
      return;
    }
    this.authService.logout();
    this.modalCtrl.dismiss();
    this.navController.navigateRoot("/dashboard");
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  shouldCloseModal(url: string): boolean {
    return url.startsWith('/portal/hotel/') && url.includes('/details');
  }

}
