<ion-header class="login-header">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="closeModal()">
        <ion-icon name="close-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="login-page">
  <div class="login-page-container for-login-height">

    <div class="login-container">
      <ion-img src="/assets/images/icons/zforb.png"></ion-img>
      <h2>Welcome to <span>ZForb</span></h2>
      <p class="page-heading-title">Log in to unlock your personalized experience.</p>
    </div>

    <!-- Login Form -->
    <div class="form-container">
      <form class="custom-form" #recordForm="ngForm" novalidate (ngSubmit)="loginButton(recordForm.form)">

        <!-- Email <PERSON> -->
        <div class="margin-bottom-15" *ngIf="view === 'LOGIN_VIA_EMAIL'">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':username.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/envelope-plus.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input #emailInput label="Email Address" labelPlacement="floating" required name="username"
              #username="ngModel" pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$" [(ngModel)]="data.userName">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="username" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>

        <!-- Mobile Login -->
        <div class="margin-bottom-15" *ngIf="view === 'LOGIN_VIA_MOBILE'">
          <ion-item class="site-form-control mobile-form-control" lines="none"
            [ngClass]="{'is-invalid':username.invalid && onClickValidation}">
            <ion-icon name="call-outline" slot="start" class="start-icon"></ion-icon>
            <input #mobileInput class="custom-mobile-input" inputmode="tel" [attr.pattern]="pattern" [maskito]="mask"
              required name="username" #username="ngModel" [(ngModel)]="data.userName" placeholder="Contact Number" />
            <img width="30" height="30" [attr.alt]="countryIsoCode" [src]="countryIsoCode" slot="end"
              [style.border-radius.%]="50" *ngIf="countryIsoCode !== ''" />
          </ion-item>
          <app-validation-message [field]="username" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div>

       

        <!-- Forgot Password -->
        <!-- <div class="forgot-password margin-bottom-20">
          <a class="forgot-password" (click)="goToForgotPassword()">Trouble Logging In?</a>
        </div> -->

        <!-- OTP Switch -->
        <!-- <div class="register-container" *ngIf="view === 'LOGIN_VIA_MOBILE' && !loginWithOtp">
          <p>
            <a (click)="toggleOtpLogin()">
              {{ loginWithOtp ? 'Login With Password' : 'Login With OTP' }}
            </a>
          </p>
        </div> -->

        <!-- Submit -->
        <ion-button class="margin-top-20" expand="full" shape="round" type="submit" [disabled]="isProcessing">
          Send OTP
        </ion-button>
      </form>

      <!-- Toggle Login Type -->
      <div class="register-container">
        <p>Or</p>
        <p>
          <a (click)="tabChange(view === 'LOGIN_VIA_EMAIL' ? 'LOGIN_VIA_MOBILE' : 'LOGIN_VIA_EMAIL')">
            {{ view === 'LOGIN_VIA_EMAIL' ? 'Login With Mobile' : 'Login With Email' }}
          </a>
        </p>
      </div>
    </div>

    <!-- Register -->
    <!-- <div class="register-container">
      <p>
        Don't have an account? <a (click)="registerAccount()">Create new account</a>
      </p>
    </div> -->

    <!-- Terms -->
    <div class="privacy-container">
      <p>
        By signing in, creating an account, you are agreeing to our
        <a href="https://www.forbcorp.com/Terms%20and%20Conditions.pdf" target="_blank" rel="noopener noreferrer">Terms
          of Use</a>
        and our
        <a href="https://www.forbcorp.com/privacy.php" target="_blank" rel="noopener noreferrer">Privacy Policy</a>
      </p>
    </div>

  </div>
</ion-content>

<!-- <ion-content class="login-page">
  <div class="login-page-container">

    <div class="login-container">
      <ion-img src="/assets/images/svg/logo.svg"></ion-img>

      <h2>Welcome to <span>Forbclub</span></h2>
      <p class="page-heading-title">Log in to unlock your personalized experience.</p>
    </div>

    <div class="form-container">
      <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="loginButton(recordForm.form)">
        <div class="login-type-section-container ion-text-left">
          <div class="login-type-section-item" (click)="tabChange(view='LOGIN_VIA_EMAIL')">
            <ion-checkbox [checked]="view==='LOGIN_VIA_EMAIL'" [mode]="'md'"></ion-checkbox> <span>Login With
              Email</span>
          </div>
          <div class="login-type-section-item" (click)="tabChange(view='LOGIN_VIA_MOBILE')">
            <ion-checkbox [checked]="view==='LOGIN_VIA_MOBILE'" [mode]="'md'"></ion-checkbox> <span>Login With
              Mobile</span>
          </div>
        </div>
        <div class="margin-bottom-15" *ngIf="view==='LOGIN_VIA_EMAIL'">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':username.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/envelope-plus.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input label="Email Address" labelPlacement="floating" required="required" name="username"
              #username="ngModel" pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$"
              [(ngModel)]="data.userName"></ion-input>
          </ion-item>
          <app-validation-message [field]="username" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>
        <div class="margin-bottom-15" *ngIf="view==='LOGIN_VIA_MOBILE'">
          <ion-item class="site-form-control mobile-form-control" lines="none"
            [ngClass]="{'is-invalid':username.invalid && onClickValidation}">
            <ion-icon name="call-outline" slot="start" class="start-icon"></ion-icon>
            <input class="custom-mobile-input" inputmode="tel" [attr.pattern]="pattern" [maskito]="mask"
              required="required" name="username" #username="ngModel" [(ngModel)]="data.userName"
              placeholder="Contact Number" />
            <img width="30" height="30" [attr.alt]="countryIsoCode" [src]="countryIsoCode" slot="end"
              [style.border-radius.%]="50" *ngIf="countryIsoCode!==''" />
          </ion-item>
          <app-validation-message [field]="username" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div>

        <div class="margin-bottom-15" *ngIf="!(view === 'LOGIN_VIA_MOBILE' && loginWithOtp)">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userPassword.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/lock.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input mode="md" label="Password" labelPlacement="floating" [type]="passwordFieldType"
              name="userPassword" #userPassword="ngModel" [(ngModel)]="data.password"
              [required]="!(view === 'LOGIN_VIA_MOBILE' && loginWithOtp)"
              pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"></ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="data.password && data.password.length>0 && passwordFieldType==='password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="data.password && data.password.length>0 && passwordFieldType!=='password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="userPassword" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & be greater than 8 characters'">
          </app-validation-message>
        </div>

        <div class="forgot-password margin-bottom-20">
          <a class="forgot-password" (click)="goToForgotPassword()">Trouble Logging In?</a>
        </div>

        <div class="register-container" *ngIf="view==='LOGIN_VIA_MOBILE'">
          <p>
            <a (click)="toggleOtpLogin()">{{ (view === 'LOGIN_VIA_MOBILE' && loginWithOtp) ? 'Login With Password' :
              'Login With Otp' }}</a>
          </p>
        </div>

        <ion-button class="margin-top-20" expand="full" shape="round" type="submit" [disabled]="isPaymentProcessing">
          {{ (view === 'LOGIN_VIA_MOBILE' && loginWithOtp) ? 'Send OTP' : 'Login' }}
        </ion-button>
      </form>

      <div class="register-container">
        <p>Or</p>
        <p>
          Don't have an account? <a (click)="registerAccount()">Create new account</a>
        </p>
      </div>
    </div>

    <div class="privacy-container">
      <p>
        By signing in, creating an account, you are agreeing to our
        <a href="https://www.forbcorp.com/Terms%20and%20Conditions.pdf" target="_blank" rel="noopener noreferrer">
          Terms of Use
        </a>
        and
        our
        <a href="https://www.forbcorp.com/privacy.php" target="_blank" rel="noopener noreferrer">
          Privacy Policy
        </a>
      </p>
    </div>
  </div>
</ion-content> -->