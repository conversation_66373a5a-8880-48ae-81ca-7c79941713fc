<ion-content class="login-page">
  <div class="login-page-container">
    <ion-buttons slot="start" class="back-button">
      <ion-button (click)="goToLoginPage()" fill="clear">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <div class="login-container">
      <ion-img src="/assets/images/svg/two-factor-authentication.svg"></ion-img>
      <h2>Two Factor Authentication Code <span>(2FA)</span></h2>
      <p class="page-heading-title">Please enter the verification code to proceed</p>
    </div>
    <!-- Container for the 2FA form -->
    <div class="form-container">
      <form #twoFactorOtpForm="ngForm" novalidate="novalidate" class="custom-form" autocomplete="off"
        (ngSubmit)="verifyTwoFactorOtp(twoFactorOtpForm.form)">
        <div class="margin-bottom-100">
          <div class="otp-validate-container">
            <ion-input required="required" minlength="1" maxlength="1" inputmode="numeric" type="text" placeholder="*"
              [debounce]="100" name="otpInput1" [ngClass]="{ 'is-invalid': onClickValidation && otpInput1.invalid }"
              [(ngModel)]="verification.otpInput1" #otpInput1="ngModel"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" [debounce]="100"
              name="otpInput2" #otpInput2="ngModel" [(ngModel)]="verification.otpInput2"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput2.invalid }" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput3"
              [debounce]="100" #otpInput3="ngModel" [(ngModel)]="verification.otpInput3"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput3.invalid}" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput4"
              [debounce]="100" #otpInput4="ngModel" [(ngModel)]="verification.otpInput4"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput4.invalid }" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput5"
              [debounce]="100" #otpInput5="ngModel" [(ngModel)]="verification.otpInput5"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput5.invalid}" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
            <ion-input required="required" inputmode="numeric" type="text" placeholder="*" name="otpInput6"
              [debounce]="100" #otpInput6="ngModel" [(ngModel)]="verification.otpInput6"
              [ngClass]="{ 'is-invalid': onClickValidation && otpInput6.invalid}" minlength="1" maxlength="1"
              (ionInput)="commonService.changeInputFocus($event)" (keydown)="commonService.handleBackspace($event)">
            </ion-input>
          </div>
        </div>
        <!-- Submit button for the OTP form -->
        <ion-button class="margin-top-20" expand="full" shape="round" type="submit" [disabled]="isProcessing">
          Continue
        </ion-button>
      </form>
      <div class="register-container">
        <p>Or</p>
        <p>
          <a (click)="goToLoginPage()">Want to log in with a different account? Click here</a>
        </p>
      </div>
    </div>
    <!-- Container for privacy and terms links -->
    <div class="privacy-container">
      <p>
        By signing in, creating an account, you are agreeing to our
        <a href="https://www.forbcorp.com/Terms%20and%20Conditions.pdf" target="_blank" rel="noopener noreferrer">Terms
          of Use</a>
        and
        our <a href="https://www.forbcorp.com/privacy.php" target="_blank" rel="noopener noreferrer">Privacy Policy</a>
      </p>
    </div>
  </div>
</ion-content>