import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-two-factor-otp',
  templateUrl: './two-factor-otp.component.html',
  styleUrls: ['./two-factor-otp.component.scss'],
})
export class TwoFactorOtpComponent implements OnInit, OnDestroy {

  verification: any; // data object
  subscription: Subscription = new Subscription();  // Subscription for login request
  onClickValidation!: boolean; // Flag to control validation state
  isProcessing: boolean = false;

  constructor(private readonly navController: NavController,
    public commonService: CommonService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly toastService: ToastService,
    private readonly route: ActivatedRoute,
    private readonly authService: AuthService,
  ) {
    this.onClickValidation = false;
  }

  ngOnInit() {
    this.onClickValidation = false;
    this.route.queryParams.subscribe(params => {
      this.verification = {
        verificationToken: params['verificationToken'] || null
      };
      if (!this.verification.verificationToken) {
        this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
        this.navController.navigateBack("/account/login", { animated: true });
      }
    });
  }

  async verifyTwoFactorOtp(form: any): Promise<any> {
    if (!this.verification.verificationToken) {
      this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
      this.navController.navigateBack("/account/login", { animated: true });
      return;
    }
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    //  this.loadingService.show();
    this.isProcessing = true;
    const otpString = this.commonService.getOTPFromInputs(
      this.verification.otpInput1,
      this.verification.otpInput2,
      this.verification.otpInput3,
      this.verification.otpInput4,
      this.verification.otpInput5,
      this.verification.otpInput6
    );
    const twoFactorData = {
      verificationToken: this.verification.verificationToken,
      code: otpString
    };

    // Make HTTP request to verify OTP
    this.subscription = this.dataService.verifyOtp(twoFactorData)
      .subscribe({
        next: (response: RestResponse) => {
          //  this.loadingService.hide();
          this.isProcessing = false;

          const data = response.data;
          this.processAndRedirectToDashboard(data);
        },
        error: (error: any) => {
          //  this.loadingService.hide();
          this.isProcessing = false;
          this.toastService.show(error.data?.message || error.message);
        }
      });
  }

  processAndRedirectToDashboard(data: any) {
    // Store token and user data in local storage
    data.token.expires_at = new Date(data.token.expires).getTime();
    this.localStorageService.setObject('token', data.token);
    this.localStorageService.setObject('user', data.user);

    // Navigate to the appropriate dashboard based on user role
    if (this.authService.isCustomer()) {
      this.navController.navigateRoot("/dashboard");
      return;
    }
    this.navController.navigateRoot("/sale-portal/dashboard");
  }

  goToLoginPage() {
    this.navController.navigateBack("/account/login", { animated: true });
  }

  ngOnDestroy() {
    // Unsubscribe from any subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
