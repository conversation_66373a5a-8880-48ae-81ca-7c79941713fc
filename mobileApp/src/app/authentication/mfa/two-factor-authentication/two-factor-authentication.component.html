<ion-content class="customer-dashboard-page customer-payment-page booking-page">
  <app-customer-header [innerPage]="true" [headingText]="'2FA Authentication'"
    [rightAction]="true"></app-customer-header>
  <form class="custom-form" #recordForm="ngForm" novalidate="novalidate" (ngSubmit)="continue()">
    <div class="booking-page-container">
      <div class="form-container text-left">

        <!-- Initial Content: Always shown when entering the screen -->
        <div *ngIf="!twoFactorEnabled || !userInteractedWithToggle">

          <div class="image-container no-margin">
            <img src="assets/images/svg/two-factor-authentication.svg" class="two-factor-image" />
          </div>

          <p class="page-sub-heading setup-two-factor">
            Setup Two Factor
            Authentication
            <strong>(2FA)</strong>
          </p>

          <p class="page-sub-heading variation-heading">
            To enhance your account security, toggle the switch to enable 2FA. Next, use your mobile authenticator app
            to scan the displayed QR code. Follow the app's instructions to complete the setup.
          </p>

          <div class="toggle-container">
            <span>{{ twoFactorEnabled ? 'Disable 2FA' : 'Enable 2FA' }}</span>
            <ion-toggle name="twoFactorToggle" (ionChange)="onToggleChange()"
              [(ngModel)]="twoFactorEnabled"></ion-toggle>
          </div>
        </div>

        <!-- 2FA Setup UI: Shown after enabling 2FA -->
        <div *ngIf="userInteractedWithToggle && twoFactorEnabled">

          <p class="page-sub-heading margin-left-heading margin-top-10">
            Scan the <strong class="strong-code">QR code</strong> with an authenticator app.
          </p>

          <div class="image-container scan-image-container">
            <img *ngIf="qrCodeImageUrl" [src]="qrCodeImageUrl" class="custom-image" />
          </div>

          <p class="page-sub-heading download-scan-heading">
            <strong class="download-strong-code">Download</strong>
            an authenticator app on your mobile.
          </p>

          <p class="page-sub-heading generated-text">
            Generated Key
          </p>

          <div class="document-items generate-key">
            <div class="document-upload-container generate-key-container">
              <div class="code-container">
                <span class="code-text">{{ code }}</span>
              </div>
              <p (click)="copyText(code)" [ngClass]="{'copy-clipboard-clicked': copiedClickedText}"
                class="copy-clipboard">
                {{ copiedClickedText ? 'Copied!' : 'Copy Link' }}
              </p>
            </div>
          </div>

          <div class="privacy-container">
            <ion-button class="site-full-rounded-button primary-button text-capitalize" expand="full" shape="round"
              type="submit" [disabled]="isProcessing">
              Continue
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  </form>
</ion-content>