import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { ProfileDetail } from 'src/modals/profileDetail';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-two-factor-authentication',
  templateUrl: './two-factor-authentication.component.html',
  styleUrls: ['./two-factor-authentication.component.scss'],
})
export class TwoFactorAuthenticationComponent implements OnInit {

  profile: ProfileDetail = new ProfileDetail();
  code: string = '';
  accessToken!: string;
  copiedClickedText: boolean = false;
  qrCodeImageUrl: string | null = null;
  twoFactorEnabled: boolean = false; // Initially set to false
  userInteractedWithToggle: boolean = false; // Flag for user interaction
  isProcessing: boolean = false;

  constructor(
    private readonly navController: NavController,
    private readonly toastService: ToastService,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly authService: AuthService
  ) { }

  ngOnInit() {
    // Retrieve twoFactorEnabled status from local storage
    const user = this.authService.getUser();
    if (!user) {
      return;
    }
    this.twoFactorEnabled = user.twoFactorEnabled || false;
  }

  copyText(text: string) {
    navigator.clipboard.writeText(text).then(() => {
      this.copiedClickedText = true;
      setTimeout(() => this.copiedClickedText = false, 1000);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  }

  back() {
    this.navController.navigateBack("/account");
  }

  openNotifications() {
    this.navController.navigateForward("/portal/notification/list", { animated: true });
  }

  async generateQrCode() {
    this.accessToken = this.authService.getToken().accessToken;

    this.loadingService.show();
    this.dataService.generateQRCode(this.accessToken).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        const data = response.data;
        this.qrCodeImageUrl = data.qrCodeSetupImageUrl || null;
        this.code = data.manualEntryKey || null;
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  continue() {
    //  this.loadingService.show();
    this.isProcessing = true;
    this.dataService.initiateMFA().subscribe({
      next: (response: RestResponse) => {
        //  this.loadingService.hide();
        this.isProcessing = false;
        const data = response.data;
        this.redirectToCustomerTwoFactorOtp(data);
      },
      error: (error) => {
        //  this.loadingService.hide();
        this.isProcessing = false;
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  onToggleChange() {
    this.userInteractedWithToggle = true; // Mark toggle as interacted by user
    if (this.twoFactorEnabled) {
      this.generateQrCode(); // Generate QR code if enabling 2FA
    } else {
      this.qrCodeImageUrl = null;
      this.continue(); // Proceed if disabling 2FA
    }

  }

  redirectToCustomerTwoFactorOtp(data: any) {
    this.navController.navigateForward("/portal/verification/two/factor/otp", {
      queryParams: {
        verificationToken: data.verificationToken,
      }, animated: true
    });
  }
}

