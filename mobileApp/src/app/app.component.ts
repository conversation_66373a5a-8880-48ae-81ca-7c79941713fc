import { After<PERSON>iewInit, Component, OnDestroy, ViewChild, ElementRef, Renderer2 } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { SplashScreen } from '@capacitor/splash-screen';
import { Badge } from '@capawesome/capacitor-badge';
import { IonModal, IonRouterOutlet, ModalController, NavController, Platform } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { EventService } from 'src/shared/event.service';
import { FcmService } from 'src/shared/fcm.service';
import { ToastService } from 'src/shared/toast.service';
import { LoginComponent } from './authentication/login/login.component';
import { filter, Subscription } from 'rxjs';
import { CustomerHeaderComponent } from './common/customer-header/customer-header.component';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { AuthService } from 'src/shared/authservice';
import { GestureBlockService } from 'src/services/gesture-block.service';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent implements AfterViewInit, OnDestroy {
  @ViewChild('routerOutlet', { static: false }) routerOutlet!: IonRouterOutlet;
  // @ViewChild('loginModal', { static: false }) loginModal!: IonModal;
  // private modalShown = false;
  exitShown!: boolean;
  showTabs = true;
  private routeSub!: Subscription;
  user: any;

  constructor(private platform: Platform,
    private navCtrl: NavController,
    private toastService: ToastService,
    private router: Router,
    private eventService: EventService,
    private dataService: DataService,
    private fcmService: FcmService,
    private commonService: CommonService,
    private localStorageService: LocalStorageService,
    private readonly authService: AuthService,
    private elementRef: ElementRef,
    private renderer: Renderer2,
    private gestureBlockService: GestureBlockService
  ) {

  }

  async ngOnInit(): Promise<void> {
    // setTimeout(() => {
    //   this.navCtrl.navigateRoot('/dashboard');
    // }, 3000);
    this.user = this.authService.getUser();

    this.routeSub = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.checkRoute();
        // Ensure swipe gesture remains disabled on route changes
        this.disableSwipeGesture();
      });
    if (!this.user) {
      this.localStorageService.set('isLoginOpened', 'false');
    }

    this.localStorageService.remove('redirectAfterLogin');
    this.localStorageService.set('redirectAfterLogin', '/dashboard');
    this.initializeApp();
    this.eventService.event.subscribe((data) => {
      if (!data) {
        return;
      }
      if (data.key === "http:forbidden") {
        this.navCtrl.navigateRoot('/dashboard', { animated: true });
      }
      if (data.key === "http:logout") {
        this.processLogout(data.value)
      }
      data = undefined;
    });
  }
  ngAfterViewInit() {
    // Disable swipe gesture app-wide for all platforms
    this.disableSwipeGesture();
    this.blockAllSwipeGestures();

    // Use the comprehensive gesture blocking service
    this.gestureBlockService.blockAllSwipeGestures();
  }

  private disableSwipeGesture() {
    if (this.routerOutlet) {
      console.log('Disabling swipe gesture app-wide');
      this.routerOutlet.swipeGesture = false;

      // Force disable at the element level
      const routerElement = (this.routerOutlet as any).el;
      if (routerElement) {
        this.renderer.setStyle(routerElement, 'touch-action', 'pan-y');
        this.renderer.setStyle(routerElement, '-webkit-touch-callout', 'none');
        this.renderer.setAttribute(routerElement, 'swipe-gesture', 'false');
      }
    } else {
      console.warn('routerOutlet not ready, retrying...');
      // Retry after a short delay if routerOutlet is not ready
      setTimeout(() => {
        this.disableSwipeGesture();
      }, 100);
    }
  }

  private blockAllSwipeGestures() {
    // Block gestures at the document level
    const appElement = this.elementRef.nativeElement;

    // Prevent all touch gestures that could trigger navigation
    const preventGesture = (event: TouchEvent): boolean | void => {
      if (event.touches.length > 1) return; // Allow multi-touch

      const touch = event.touches[0];
      const startX = touch.clientX;

      // Block swipes from the edges (common trigger areas)
      if (startX < 50 || startX > window.innerWidth - 50) {
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
    };

    // Add event listeners to prevent swipe gestures
    this.renderer.listen(appElement, 'touchstart', preventGesture);
    this.renderer.listen(appElement, 'touchmove', (event: TouchEvent): boolean | void => {
      // Prevent horizontal swipes
      if (event.touches.length === 1) {
        const touch = event.touches[0];
        const deltaX = Math.abs(touch.clientX - (touch as any).startX);
        const deltaY = Math.abs(touch.clientY - (touch as any).startY);

        if (deltaX > deltaY && deltaX > 10) {
          event.preventDefault();
          event.stopPropagation();
          return false;
        }
      }
    });

    // Block gesture events
    this.renderer.listen(appElement, 'gesturestart', (event: Event) => {
      event.preventDefault();
      event.stopPropagation();
      return false;
    });

    this.renderer.listen(appElement, 'gesturechange', (event: Event) => {
      event.preventDefault();
      event.stopPropagation();
      return false;
    });

    this.renderer.listen(appElement, 'gestureend', (event: Event) => {
      event.preventDefault();
      event.stopPropagation();
      return false;
    });
  }
  private initializeApp(): void {
    this.platform.ready().then(() => {
      setTimeout(() => {
        SplashScreen.hide();
      }, 1000);

      this.initializeBackButton();
      if (this.platform.is('cordova')) {
        this.fcmService.initializeNotificationReceiver();
      }
      this.clearNotificationCount();

      // Ensure swipe gesture is disabled after platform is ready
      this.disableSwipeGesture();

      // Apply comprehensive gesture blocking
      this.gestureBlockService.blockAllSwipeGestures();
    });
  }

  clearNotificationCount() {
    Badge.isSupported()
      .then(result => {
        if (result.isSupported) {
          Badge.clear();
        }
      });
  }

  initializeBackButton(): void {
    this.exitShown = false;

    this.platform.backButton.subscribeWithPriority(10, (processNext: () => void) => {
      const currentUrl = this.router.url.split('?')[0];

      // Your popup dismiss logic
      if (this.commonService.isLocationSelectionPopupOpen) {
        this.commonService.isLocationSelectionPopupOpen = false;
        return;
      }
      if (this.commonService.isCitySelectionPopupOpen) {
        this.commonService.isCitySelectionPopupOpen = false;
        return;
      }
      if (this.commonService.isNoPackageAvailablePopupOpen) {
        this.commonService.isNoPackageAvailablePopupOpen = false;
        return;
      }

      // Your main page exit logic
      const isMainPage =
        currentUrl.startsWith('/dashboard') ||
        currentUrl.startsWith('/booking') ||
        currentUrl.startsWith('/payment') ||
        currentUrl.startsWith('/membership') ||
        currentUrl.startsWith('/account1') ||
        currentUrl.startsWith('/sale-portal');

      if (isMainPage) {
        if (!this.exitShown) {
          this.exitShown = true;
          this.toastService.show('Press again to exit');
          setTimeout(() => this.exitShown = false, 2000);
          return;
        }
        (navigator as any).app.exitApp();
        return;
      }

      // 🚫 Block hardware back for all other screens
      console.log('Hardware back disabled on this screen:', currentUrl);
      return;
    });
  }

  //this below function is working for allow back button in account screens but not working correclty // 
  initializeBackButtons(): void {
    this.exitShown = false;
    // this.platform.backButton
    //   .subscribe(() => {
    this.platform.backButton.subscribeWithPriority(
      10,
      (processNext: () => void) => {
        if (this.commonService.isLocationSelectionPopupOpen) {
          this.commonService.isLocationSelectionPopupOpen = false;
          return;                          // stop here → no navigation
        }
        if (this.commonService.isCitySelectionPopupOpen) {
          this.commonService.isCitySelectionPopupOpen = false;
          return;
        }

        // Check if the noPackageAvailablePopup modal is open
        if (this.commonService.isNoPackageAvailablePopupOpen) {
          this.commonService.isNoPackageAvailablePopupOpen = false;
          return;
        }

        const url = this.router.url;
        const isMainPage =
          url.startsWith('/dashboard') ||
          url.startsWith('/booking') ||
          url.startsWith('/payment') ||
          url.startsWith('/membership') ||
          url.startsWith('/account1') ||
          url.startsWith('/sale-portal');

        if (isMainPage) {
          if (!this.exitShown) {
            this.exitShown = true;
            this.toastService.show('Press again to exit');
            return;                       // swallow first press
          }
          (navigator as any).app.exitApp();
          return;
        }

        /* let Ionic handle normal back navigation */
        processNext();
      }
    );
  }

  processLogout(uuid: string) {
    console.log("Sending logout request");
    if (!uuid) {
      console.log("Device Id is not found.");
      return;
    }
    const input = {} as any;
    input.deviceId = uuid;
    this.dataService.logout(input)
      .subscribe({
        next: (response: RestResponse) => {
          console.log("Logout Successfully From Server.");
        }, error: (error: any) => {
        }
      });
  }

  private checkRoute() {
    let currentRoute = this.router.url;
    // if(currentRoute==='/dashboard?redirectTo=%2Faccount1')
    // {
    //   currentRoute = '/dashboard'
    // }
    // Define the routes where tabs should be shown
    const hideTabRoutes = ['/dashboard',
      '/account1',
      '/booking',
      '/membership',
      '/payment'
    ];
    this.showTabs = hideTabRoutes.some(route => currentRoute.startsWith(route));
  }

  async onTabChange(event: any) {
    const selectedTab = event.tab;
    if (selectedTab === 'booking' || selectedTab === 'payment') {
      this.router.navigate(['/', selectedTab], {
        queryParams: {}, // Reset query params
        skipLocationChange: true, // Optional, resets the query params
      });
      await this.checkRoute();
    }
  }
  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }

    // Clean up gesture blocking
    this.gestureBlockService.unblockAllGestures();
  }
}
