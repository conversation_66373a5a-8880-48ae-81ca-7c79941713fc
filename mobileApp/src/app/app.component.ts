import { AfterViewInit, Component, ViewChild } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { SplashScreen } from '@capacitor/splash-screen';
import { Badge } from '@capawesome/capacitor-badge';
import { IonModal, IonRouterOutlet, ModalController, NavController, Platform } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { EventService } from 'src/shared/event.service';
import { FcmService } from 'src/shared/fcm.service';
import { ToastService } from 'src/shared/toast.service';
import { LoginComponent } from './authentication/login/login.component';
import { filter, Subscription } from 'rxjs';
import { CustomerHeaderComponent } from './common/customer-header/customer-header.component';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { AuthService } from 'src/shared/authservice';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent implements AfterViewInit {
  @ViewChild(IonRouterOutlet, { static: false }) routerOutlet!: IonRouterOutlet;
  // @ViewChild('loginModal', { static: false }) loginModal!: IonModal;
  // private modalShown = false;
  exitShown!: boolean;
  showTabs = true;
  private routeSub!: Subscription;
  user: any;

  constructor(private platform: Platform,
    private navCtrl: NavController,
    private toastService: ToastService,
    private router: Router,
    private eventService: EventService,
    private dataService: DataService,
    private fcmService: FcmService,
    private commonService: CommonService,
    private localStorageService: LocalStorageService,
    private readonly authService: AuthService
  ) {

  }

  async ngOnInit(): Promise<void> {
    // setTimeout(() => {
    //   this.navCtrl.navigateRoot('/dashboard');
    // }, 3000);
    this.user = this.authService.getUser();

    this.routeSub = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.checkRoute();
      });
    if (!this.user) {
      this.localStorageService.set('isLoginOpened', 'false');
    }

    this.localStorageService.remove('redirectAfterLogin');
    this.localStorageService.set('redirectAfterLogin', '/dashboard');
    this.initializeApp();
    this.eventService.event.subscribe((data) => {
      if (!data) {
        return;
      }
      if (data.key === "http:forbidden") {
        this.navCtrl.navigateRoot('/dashboard', { animated: true });
      }
      if (data.key === "http:logout") {
        this.processLogout(data.value)
      }
      data = undefined;
    });
  }
  ngAfterViewInit() {
    if (this.platform.is('ios')) {
      if (this.routerOutlet) {
        console.log('Disabling swipe gesture on iOS');
        this.routerOutlet.swipeGesture = false;
      } else {
        console.warn('routerOutlet not ready');
      }
    }
  }
  private initializeApp(): void {
    this.platform.ready().then(() => {
      setTimeout(() => {
        SplashScreen.hide();
      }, 1000);

      this.initializeBackButton();
      if (this.platform.is('cordova')) {
        this.fcmService.initializeNotificationReceiver();
      }
      this.clearNotificationCount();

      // if (this.platform.is('ios')) {
      //   this.routerOutlet.swipeGesture = false; // Disable swipe-back
      // }

    });
  }

  clearNotificationCount() {
    Badge.isSupported()
      .then(result => {
        if (result.isSupported) {
          Badge.clear();
        }
      });
  }

  initializeBackButton(): void {
    this.exitShown = false;

    this.platform.backButton.subscribeWithPriority(10, (processNext: () => void) => {
      const currentUrl = this.router.url.split('?')[0];

      // Your popup dismiss logic
      if (this.commonService.isLocationSelectionPopupOpen) {
        this.commonService.isLocationSelectionPopupOpen = false;
        return;
      }
      if (this.commonService.isCitySelectionPopupOpen) {
        this.commonService.isCitySelectionPopupOpen = false;
        return;
      }
      if (this.commonService.isNoPackageAvailablePopupOpen) {
        this.commonService.isNoPackageAvailablePopupOpen = false;
        return;
      }

      // Your main page exit logic
      const isMainPage =
        currentUrl.startsWith('/dashboard') ||
        currentUrl.startsWith('/booking') ||
        currentUrl.startsWith('/payment') ||
        currentUrl.startsWith('/membership') ||
        currentUrl.startsWith('/account1') ||
        currentUrl.startsWith('/sale-portal');

      if (isMainPage) {
        if (!this.exitShown) {
          this.exitShown = true;
          this.toastService.show('Press again to exit');
          setTimeout(() => this.exitShown = false, 2000);
          return;
        }
        (navigator as any).app.exitApp();
        return;
      }

      // 🚫 Block hardware back for all other screens
      console.log('Hardware back disabled on this screen:', currentUrl);
      return;
    });
  }

  //this below function is working for allow back button in account screens but not working correclty // 
  initializeBackButtons(): void {
    this.exitShown = false;
    // this.platform.backButton
    //   .subscribe(() => {
    this.platform.backButton.subscribeWithPriority(
      10,
      (processNext: () => void) => {
        if (this.commonService.isLocationSelectionPopupOpen) {
          this.commonService.isLocationSelectionPopupOpen = false;
          return;                          // stop here → no navigation
        }
        if (this.commonService.isCitySelectionPopupOpen) {
          this.commonService.isCitySelectionPopupOpen = false;
          return;
        }

        // Check if the noPackageAvailablePopup modal is open
        if (this.commonService.isNoPackageAvailablePopupOpen) {
          this.commonService.isNoPackageAvailablePopupOpen = false;
          return;
        }

        const url = this.router.url;
        const isMainPage =
          url.startsWith('/dashboard') ||
          url.startsWith('/booking') ||
          url.startsWith('/payment') ||
          url.startsWith('/membership') ||
          url.startsWith('/account1') ||
          url.startsWith('/sale-portal');

        if (isMainPage) {
          if (!this.exitShown) {
            this.exitShown = true;
            this.toastService.show('Press again to exit');
            return;                       // swallow first press
          }
          (navigator as any).app.exitApp();
          return;
        }

        /* let Ionic handle normal back navigation */
        processNext();
      }
    );
  }

  processLogout(uuid: string) {
    console.log("Sending logout request");
    if (!uuid) {
      console.log("Device Id is not found.");
      return;
    }
    const input = {} as any;
    input.deviceId = uuid;
    this.dataService.logout(input)
      .subscribe({
        next: (response: RestResponse) => {
          console.log("Logout Successfully From Server.");
        }, error: (error: any) => {
        }
      });
  }

  private checkRoute() {
    let currentRoute = this.router.url;
    // if(currentRoute==='/dashboard?redirectTo=%2Faccount1')
    // {
    //   currentRoute = '/dashboard'
    // }
    // Define the routes where tabs should be shown
    const hideTabRoutes = ['/dashboard',
      '/account1',
      '/booking',
      '/membership',
      '/payment'
    ];
    this.showTabs = hideTabRoutes.some(route => currentRoute.startsWith(route));
  }

  async onTabChange(event: any) {
    const selectedTab = event.tab;
    if (selectedTab === 'booking' || selectedTab === 'payment') {
      this.router.navigate(['/', selectedTab], {
        queryParams: {}, // Reset query params
        skipLocationChange: true, // Optional, resets the query params
      });
      await this.checkRoute();
    }
  }
  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }
}
