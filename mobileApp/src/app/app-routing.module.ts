import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { AuthGuard } from 'src/shared/authguard';
import { ForgotPasswordComponent } from './authentication/forgot-password/forgot-password.component';
import { TwoFactorAuthenticationComponent } from './authentication/mfa/two-factor-authentication/two-factor-authentication.component';
import { TwoFactorOtpComponent } from './authentication/mfa/two-factor-otp/two-factor-otp.component';
import { CustomerBasicComponent } from './customer-layout/profile-management/customer-onboarding/customer-basic/customer-basic.component';
import { CustomerEmailComponent } from './customer-layout/profile-management/customer-onboarding/customer-email/customer-email.component';
import { CustomerPackagePaymentComponent } from './customer-layout/profile-management/customer-onboarding/customer-package-payment/customer-package-payment.component';
import { CustomerPackageSelectionComponent } from './customer-layout/profile-management/customer-onboarding/customer-package-selection/customer-package-selection.component';
import { CustomerPasswordComponent } from './customer-layout/profile-management/customer-onboarding/customer-password/customer-password.component';
import { CustomerProfilePictureComponent } from './customer-layout/profile-management/customer-onboarding/customer-profile-picture/customer-profile-picture.component';
import { CustomerReferalCodeComponent } from './customer-layout/profile-management/customer-onboarding/customer-referal-code/customer-referal-code.component';
import { CustomerRegistrationComponent } from './customer-layout/profile-management/customer-onboarding/customer-registration/customer-registration.component';
import { CustomerVerificationComponent } from './customer-layout/profile-management/customer-onboarding/customer-verification/customer-verification.component';
import { PaymentCallbackComponent } from './customer-layout/profile-management/customer-onboarding/payment-callback/payment-callback.component';
import { CustomerCoApplicantComponent } from './customer-layout/profile-management/customer-onboarding/customer-co-applicant/customer-co-applicant.component';
import { CustomerLocationComponent } from './customer-layout/profile-management/customer-onboarding/customer-location/customer-location.component';
import { OtpForgotPasswordComponent } from './authentication/otp-forgot-password/otp-forgot-password.component';
import { ResetPasswordComponent } from './authentication/reset-password/reset-password.component';
import { HomeComponent } from './customer-layout/home/<USER>';
import { MyBookingsComponent } from './customer-layout/my-bookings/my-bookings.component';
import { MyPaymentsComponent } from './customer-layout/my-payments/my-payments.component';
import { MySubscriptionComponent } from './customer-layout/my-subscription/my-subscription.component';
import { CustomerAccountComponent } from './customer-layout/customer-account/customer-account.component';
import { SplashComponent } from './authentication/splash/splash.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'splash',
    pathMatch: 'full'
  },
  {
    path: 'splash',
    component: SplashComponent,
    // canActivate: [AuthGuard],
    // data: { roles: ['ROLE_ANONYMOUS'] },
  },
  {
    path: 'dashboard',
    component: HomeComponent,
    // canActivate: [AuthGuard],
    // data: { roles: ['ROLE_CUSTOMER'] },
  },
  {
    path: 'account/verification',
    component: TwoFactorOtpComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] },
  },
  {
    path: 'account/forgot/password',
    component: ForgotPasswordComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] },
  },
  {
    path: 'account/reset/otp',
    component: OtpForgotPasswordComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] },
  },
  {
    path: 'account/reset/password',
    component: ResetPasswordComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] },
  },
  {
    path: "account/register",
    component: CustomerRegistrationComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: "account/register/basic",
    component: CustomerBasicComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "account/register/location",
    component: CustomerLocationComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "account/register/email",
    component: CustomerEmailComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "account/register/picture",
    component: CustomerProfilePictureComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "account/register/password",
    component: CustomerPasswordComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: "account/register/referral",
    component: CustomerReferalCodeComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "account/register/verification",
    component: CustomerVerificationComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: "account/dashboard/otp/verification",
    component: CustomerVerificationComponent,
    // canActivate: [AuthGuard],
    //data: { roles: ['ROLE_ANONYMOUS'] }
  },
  {
    path: "account/register/co/applicant",
    component: CustomerCoApplicantComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "account/register/package/selection",
    component: CustomerPackageSelectionComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "account/register/package/:id/:fromScreen/pay",
    component: CustomerPackagePaymentComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "account/register/package/success",
    component: PaymentCallbackComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "account/register/package/failure",
    component: PaymentCallbackComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  // {
  //   path: 'register',
  //   loadChildren: () => import('./customer-layout/customer-layout.module').then(m => m.CustomerLayoutModule)
  // },
  // {
  //   path: 'account/two/factor',
  //   component: TwoFactorAuthenticationComponent,
  //   canActivate: [AuthGuard],
  //   data: { roles: ['ROLE_CUSTOMER'] },
  // },

  {
    path: 'sale-portal',
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_SALES_PERSON', 'ROLE_SALES_HEAD', 'ROLE_SALES_MANAGER', 'ROLE_COLLECTION_PERSON',] },
    loadChildren: () => import('./sale-layout/sale-layout.module').then(m => m.SaleLayoutModule)
  },
  {
    path: 'portal',
    // canActivate: [AuthGuard],
    // data: { roles: ['ROLE_CUSTOMER'] },
    loadChildren: () => import('./customer-layout/customer-layout.module').then(m => m.CustomerLayoutModule)
  },
  {
    path: "account1",
    component: CustomerAccountComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "membership",
    component: MySubscriptionComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "booking",
    component: MyBookingsComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  },
  {
    path: "payment",
    component: MyPaymentsComponent,
    canActivate: [AuthGuard],
    data: { roles: ['ROLE_CUSTOMER'] }
  }
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
