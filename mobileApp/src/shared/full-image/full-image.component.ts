import { Component, Input, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-full-image',
  templateUrl: './full-image.component.html',
  styleUrls: ['./full-image.component.scss'],
})
export class FullImageComponent  implements OnInit {
  @Input() imageUrl: string | null = null;

  constructor(private modalCtrl: ModalController) {}

  ngOnInit() {
      
  }

  close() {
    this.modalCtrl.dismiss();
  }

}
