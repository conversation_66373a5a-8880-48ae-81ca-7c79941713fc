import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { NgxMaskPipe } from 'ngx-mask';
import { CustomMaskDirective } from './custom.mask.directive';
import { ValidationMessageComponent } from './validation-message/validation-message.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule
  ],
  declarations: [
    CustomMaskDirective,
    ValidationMessageComponent,
  ],
  exports: [
    CustomMaskDirective,
    ValidationMessageComponent,
  ],
  providers: [
    NgxMaskPipe
  ]
})
export class SharedModuleModule { }
