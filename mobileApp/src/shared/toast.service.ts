import { Injectable } from '@angular/core';
import { ShowOptions, Toast } from '@capacitor/toast';
import { ToastController } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class ToastService {

  constructor(private toastController: ToastController) {
  }

  async show(message: string): Promise<void> {
    // console.log("message:::",message);
    // const option = {} as ShowOptions;
    // option.text = message;
    // option.duration = 'long';
    // option.position = 'bottom';
    // Toast.show(option)
    //   .then(() => {
    //   });

    const toast = await this.toastController.create({
      message: message,
      duration: 1500,
      position: 'bottom',
      mode: 'md',
      animated: true,
      swipeGesture: "vertical"
    });

    await toast.present();
  }
}
