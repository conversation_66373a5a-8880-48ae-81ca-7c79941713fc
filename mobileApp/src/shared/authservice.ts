import { Injectable } from '@angular/core';
import { EventService } from './event.service';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  constructor(private readonly localStorageService: LocalStorageService,
    private readonly eventService: EventService) { }

  getToken(): any {
    const token: any = this.localStorageService.getObject('temp-token');
    if (token) {
      return token;
    }
    const tempToken: any = this.localStorageService.getObject('token');
    return tempToken;
  }

  getUser() {
    const tempUser: any = this.localStorageService.getObject('temp-user');
    if (tempUser) {
      return tempUser;
    }
    const user: any = this.localStorageService.getObject('user');
    return user;
  }

  getRoles(): Array<string> {
    const user = this.getUser();
    if (!user) {
      return new Array<string>();
    }
    return user.roles;
  }

  isCustomer() {
    return this.hasRoles(["ROLE_CUSTOMER"]);
  }

  isSale() {
    return this.hasRoles(["ROLE_SALE", "ROLE_HEAD", "ROLE_MANAGER"]);
  }

  logout() {
    const uuid = this.localStorageService.get("device-uuid");
    this.eventService.publish({ key: 'http:logout', value: uuid });
    this.localStorageService.remove("device-uuid");
    //  setTimeout(() => {
    this.localStorageService.remove('temp-token');
    this.localStorageService.remove('temp-user');
    this.localStorageService.remove('twoFactorLoginData');
    this.localStorageService.remove('token');
    this.localStorageService.remove('user');
    this.localStorageService.remove('panCard');
    this.localStorageService.remove('skipPanCard');
    this.localStorageService.remove('fromMembershipPage');
    this.localStorageService.remove('firstTimePackagePurchasing');
    this.localStorageService.remove('profileImageUrl');
    this.localStorageService.remove('activeSubscriptionData');
    this.localStorageService.remove('coApplicantDetail');
    this.localStorageService.remove('resetGuestFormDetails');
    this.localStorageService.remove('search-input');
    this.localStorageService.remove('hotelDetails');
    this.localStorageService.remove('roomDetails');
    this.localStorageService.remove('preBookingResponse');
    this.localStorageService.remove('guestPreviewDetails');
    this.localStorageService.remove('fetchHotelDetailById');
    this.localStorageService.remove('confirmBookingResponse');
    this.localStorageService.remove('bookingTypeNoLogin');
    this.localStorageService.remove('subscribeNowWithoutLogin');
    this.localStorageService.remove('packageDetail');  
    this.localStorageService.remove('bookingWithoutLogin');
    this.localStorageService.remove('redirectAfterLogin');
    this.localStorageService.remove('campaignId');
    
    //   this.localStorageService.clearAll();
    //  }, 500);
  }

  hasRoles(roles: Array<string>) {
    if (!roles || roles.length === 0) {
      return true;
    }
    const userRoles: Array<string> = this.getRoles();
    return roles.some((e) => {
      return userRoles.indexOf(e) >= 0;
    });
  }

  hasValidToken(): boolean {
    const token: any = this.getToken();
    if (!token) {
      return false;
    }
    return token.accessToken && token.expires_at && token.expires_at > new Date().getTime();
  }

  isAuthorizedUser(roles: Array<string>) {
    if (!this.hasValidToken()) {
      this.logout();
    }
    return { hasAccess: this.hasValidToken(), hasRoleAccess: roles.some(x => this.getRoles().indexOf(x) !== -1) };
  }
}
