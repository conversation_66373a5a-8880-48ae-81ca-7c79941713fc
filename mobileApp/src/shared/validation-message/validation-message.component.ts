import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-validation-message',
  templateUrl: './validation-message.component.html',
  styleUrls: ['./validation-message.component.scss']
})
export class ValidationMessageComponent {

  @Input() field: any;
  @Input() onClickValidation: boolean = false;
  @Input() customErrorMessage?: string;
  @Input() comparableField: any;
  @Input() customClass?: string;
  @Input() fieldErrorMessage?: string;
  @Input() customPatternMessage?: string;
  @Input() customMaskMessage?: string;
  @Input() panCardPatternMessage?: string;

  constructor() { }

  get isFieldInvalid() {
    return this.field && this.field.invalid && this.onClickValidation;
  }

  get isFieldMismatch() {
    return this.field && this.comparableField && this.field.value !== this.comparableField.value;
  }

  getMaskErrorMessage() {
    if (this.field?.errors?.mask) {
      const requiredMask = this.field.errors.mask.requiredMask || '';
      return this.customMaskMessage ? this.customMaskMessage : `Please provide a valid value. Value will be like: ${requiredMask}`;
    }
    return '';
  }
}
