import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot } from '@angular/router';
import { NavController } from '@ionic/angular';
import { AuthService } from './authservice';
import { CommonService } from 'src/services/common.service';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {

  constructor(private readonly authService: AuthService,
    private readonly navController: NavController,
    private readonly commonService: CommonService,
    private readonly localStorageService: LocalStorageService,

  ) {
  }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    const roles = route.data['roles'] as Array<string>;
    const response: any = this.authService.isAuthorizedUser(roles);
    if (roles.indexOf('ROLE_ANONYMOUS') !== -1) {
      if (response?.hasAccess) {
        if (this.authService.isCustomer()) {
          const user = this.authService.getUser();

          if (user && !user.isOnBoardingComplete) {
            this.navController.navigateRoot("/account/register/basic", { animated: true });
            return false;
          }

          this.navController.navigateForward("/dashboard");
          return false;
        }
        this.navController.navigateForward("/sale-portal/dashboard");
        return false;
      }
      return true;
    }
    if (!response.hasAccess || !response.hasRoleAccess) {
      this.authService.logout();
      const redirectPath = state.url;
      this.localStorageService.set('redirectAfterLogin', redirectPath);
      this.navController.navigateRoot("/dashboard", { queryParams: { redirectTo: redirectPath }, animated: true });
      this.commonService.openLoginModal();
      this.localStorageService.set('redirectAfterLogin', redirectPath);
      return false;
    }
    if (!response.hasRoleAccess) {
      this.navController.navigateRoot("403");
      return false;
    }
    return true;
  }

}


