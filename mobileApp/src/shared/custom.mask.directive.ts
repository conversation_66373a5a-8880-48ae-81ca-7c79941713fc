import { Directive, Input } from '@angular/core';
import { IonInput } from '@ionic/angular';
import { NgxMaskPipe } from 'ngx-mask';

@Directive({
  // eslint-disable-next-line @angular-eslint/directive-selector
  selector: 'ion-input'
})
export class CustomMaskDirective {
  @Input() mask: any;
  @Input() thousandSeparator: any;
  constructor(
    private control: IonInput,
    private maskPipe: NgxMaskPipe
    //private maskService: NgxMaskApplierService,
  ) {
    this.control.ionInput.subscribe((r: string) => {
      this.control.value = !this.mask || !this.control.value ?
        this.control.value
        : this.transformValue(this.control.value);
    });
  }

  transformValue(value: any): string {
    if (this.thousandSeparator) {
      return this.maskPipe.transform(value?.toString(), this.mask, { thousandSeparator: this.thousandSeparator });
    }
    return this.maskPipe.transform(value?.toString(), this.mask);
  }
}
