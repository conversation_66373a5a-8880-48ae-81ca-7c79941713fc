{"name": "forbcorp", "version": "0.0.0", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^18.0.0", "@angular/common": "^18.0.0", "@angular/compiler": "^18.0.0", "@angular/core": "^18.0.0", "@angular/forms": "^18.0.0", "@angular/platform-browser": "^18.0.0", "@angular/platform-browser-dynamic": "^18.0.0", "@angular/router": "^18.0.0", "@capacitor/android": "6.1.1", "@capacitor/app": "6.0.0", "@capacitor/camera": "^6.0.2", "@capacitor/core": "^6.2.0", "@capacitor/device": "^6.0.0", "@capacitor/filesystem": "^6.0.2", "@capacitor/geolocation": "^6.0.1", "@capacitor/haptics": "6.0.0", "@capacitor/ios": "6.1.1", "@capacitor/keyboard": "6.0.1", "@capacitor/push-notifications": "^6.0.2", "@capacitor/share": "^6.0.3", "@capacitor/splash-screen": "^6.0.2", "@capacitor/status-bar": "6.0.0", "@capacitor/toast": "^6.0.1", "@capawesome/capacitor-badge": "^6.0.0", "@capgo/nativegeocoder": "^6.1.16", "@ionic/angular": "^8.0.0", "@ionic/pwa-elements": "^3.3.0", "@ionic/storage-angular": "^4.0.0", "@maskito/angular": "^3.0.1", "@maskito/core": "^3.0.1", "@maskito/kit": "^3.1.1", "@maskito/phone": "^3.1.1", "@pantrist/capacitor-date-picker": "^6.0.0", "angular-feather": "^6.5.1", "apexcharts": "^3.51.0", "capacitor-razorpay": "github:razorpay/razorpay-capacitor", "country-state-city": "^3.2.1", "feather-icons": "^4.29.2", "hammerjs": "^2.0.8", "ionicons": "^7.0.0", "libphonenumber-js": "^1.11.12", "ng-apexcharts": "^1.11.0", "ngx-image-cropper": "^7.0.2", "ngx-mask": "^17.1.8", "node": "^20.15.1", "rxjs": "~7.8.0", "swiper": "^8.3.2", "time-ago-pipe": "^1.3.2", "tobii": "^2.0.0-alpha", "tslib": "^2.3.0", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^18.0.0", "@angular-eslint/builder": "^18.0.0", "@angular-eslint/eslint-plugin": "^18.0.0", "@angular-eslint/eslint-plugin-template": "^18.0.0", "@angular-eslint/schematics": "^18.0.0", "@angular-eslint/template-parser": "^18.0.0", "@angular/cli": "^18.0.0", "@angular/compiler-cli": "^18.0.0", "@angular/language-service": "^18.0.0", "@capacitor/assets": "^3.0.5", "@capacitor/cli": "6.1.1", "@ionic/angular-toolkit": "^11.0.1", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.0"}}