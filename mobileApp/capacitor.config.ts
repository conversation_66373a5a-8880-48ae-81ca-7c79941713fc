import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.forbcorp.forbclub',
  appName: 'ZFORB',
  webDir: 'www',
  server: {
    androidScheme: "http",
    cleartext: true,
    allowNavigation: [
      "localhost"
    ]
  },
  plugins: {
    SplashScreen: {
      launchAutoHide: false,
      showSpinner: false,
      splashFullScreen: true,
      backgroundColor: "#ffffff"
    },
    PushNotifications: {
      presentationOptions: ["badge", "sound", "alert"],
    },
    Badge: {
      persist: true,
      autoClear: false
    }
  }
};

export default config;
